"""
Specialized astronomy agents using Agno framework.
These agents are specialized for astronomy research and data analysis.
"""

from agno.agent import Agent
from agno.models.ollama import Ollama
from agno.tools.toolkit import Toolkit
from tools.astronomy.nasa_ads_tools import NasaAdsTools
from tools.astronomy.simbad_tools import SimbadTools
from tools.astronomy.esdc_crawler_tools import EsdcCrawlerTools
from tools.astronomy.esa_archives_tools import EsaArchivesTools
from tools.astronomy.wikipedia_astronomy_tools import WikipediaAstronomyTools
from tools.common.cot import LCoTTools, MCoTTools
from tools.common.cot_encyclopedia_tools import CoTEncyclopediaTools
from textwrap import dedent

# Define the model
MODEL = Ollama(id="qwen3:4b")

# Refactored agents with individual tools
nasa_agent = Agent(
    name="NASA Data Agent",
    model=MODEL,
    instructions=dedent("""
        You are a specialized agent for retrieving and analyzing NASA data.
        Use format `<mission_name> <subject>` or `<phenomenon> <instrument>` for queries.
        Examples: 'Kepler-186f discovery', 'solar flare dynamics SOHO'.
        Always cite your sources and provide detailed information.
    """),
    tools=[],
    show_tool_calls=True,
    markdown=True
)

nasa_ads_agent = Agent(
    name="NASA ADS Agent",
    model=MODEL,
    instructions=dedent("""
        You are a specialized agent for searching academic papers in astronomy.
        Use format `<object> <phenomenon>` or `<author:LastName>` for queries.
        Examples: 'exoplanet atmosphere characterization', 'author:Hawking'.
        Always provide bibliographic information for papers.
    """),
    tools=[NasaAdsTools()],
    show_tool_calls=True,
    markdown=True
)

simbad_agent = Agent(
    name="SIMBAD Agent",
    model=MODEL,
    instructions=dedent("""
        You are a specialized agent for retrieving information about astronomical objects.
        Use format `<object_identifier>` or `<catalog_name> <object_number>` for queries.
        Examples: 'M31', 'HD 209458', 'HIP 113357'.
        Always include object identifiers and classifications in your responses.
    """),
    tools=[SimbadTools()],
    show_tool_calls=True,
    markdown=True
)

esa_archives_agent = Agent(
    name="ESA Archives Agent",
    model=MODEL,
    instructions=dedent("""
        You are a specialized agent for retrieving data from ESA missions.
        Use format `<mission_name>/<instrument> <target>` for queries.
        Examples: 'Gaia/RVS stellar spectra', 'Rosetta/OSIRIS 67P'.
        Always include mission name, instrument and target details in your responses.
    """),
    tools=[EsaArchivesTools()],
    show_tool_calls=True,
    markdown=True
)

esdc_crawler_agent = Agent(
    name="ESDC Crawler Agent",
    model=MODEL,
    instructions=dedent("""
        You are a specialized agent for crawling and retrieving European Space Data.
        Always cite your sources and provide detailed mission information.
        Focus on providing comprehensive data for European space missions.
    """),
    tools=[EsdcCrawlerTools()],
    show_tool_calls=True,
    markdown=True
)

wikipedia_astronomy_agent = Agent(
    name="Wikipedia Astronomy Agent",
    model=MODEL,
    instructions=dedent("""
        You are a specialized agent for providing conceptual overviews of astronomy topics.
        Use format `<general_concept>` or `<specific_phenomenon>` for queries.
        Examples: 'neutron star formation', 'Crab Nebula supernova'.
        Focus on providing educational overviews suitable for all knowledge levels.
    """),
    tools=[WikipediaAstronomyTools()],
    show_tool_calls=True,
    markdown=True
)

# Add reasoning agents with thinking tools
lcot_reasoning_agent = Agent(
    name="LCoT Reasoning Agent",
    model=MODEL,
    instructions=dedent("""
        You are an astronomical reasoning agent using Linear Chain of Thought.
        Break down problems step-by-step in a linear fashion.
        Focus on analyzing astronomical data and drawing logical conclusions.
        Use each step to build upon previous knowledge.
    """),
    tools=[LCoTTools()],
    show_tool_calls=True,
    markdown=True
)

cot_encyclopedia_agent = Agent(
    name="CoT Encyclopedia Agent",
    model=MODEL,
    instructions=dedent("""
        You are an astronomical encyclopedia using Chain of Thought reasoning.
        Build on analyses with encyclopedic knowledge about astronomy.
        Connect concepts and provide comprehensive explanations.
        Ensure information is scientifically accurate and well-organized.
    """),
    tools=[CoTEncyclopediaTools()],
    show_tool_calls=True,
    markdown=True
)

mcot_agent = Agent(
    name="MCoT Agent",
    model=MODEL,
    instructions=dedent("""
        You use Multi-path Chain of Thought reasoning for astronomical problems.
        Explore multiple possible hypotheses and reasoning paths.
        Evaluate different approaches to astronomical questions.
        Synthesize information to provide comprehensive analyses.
    """),
    tools=[MCoTTools()],
    show_tool_calls=True,
    markdown=True
)

# Export agents
__all__ = [
    "nasa_agent",
    "nasa_ads_agent",
    "simbad_agent",
    "esa_archives_agent",
    "esdc_crawler_agent",
    "wikipedia_astronomy_agent",
    "lcot_reasoning_agent",
    "cot_encyclopedia_agent",
    "mcot_agent"
]
