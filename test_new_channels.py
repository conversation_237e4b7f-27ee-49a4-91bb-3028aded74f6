#!/usr/bin/env python3
"""
Test script cho 3 kênh mới: Ocean Mysteries, Archaeology, Aviation
"""

import asyncio
import sys
import os

# Add tools directory to path
sys.path.append('tools')

async def test_ocean_mysteries():
    """Test Ocean Mysteries tools"""
    print("=== Testing Ocean Mysteries ===")

    try:
        from ocean_mysteries.deep_sea_exploration_tools import DeepSeaExplorationTool
        tool = DeepSeaExplorationTool()
        result = await tool.search_deep_sea_discoveries("hydrothermal vents", limit=2)
        print("✅ Deep Sea Exploration Tool works!")
        print(f"Result type: {type(result)}")

        from ocean_mysteries.marine_archaeology_tools import MarineArchaeologyTool
        tool = MarineArchaeologyTool()
        result = await tool.search_shipwrecks("titanic", limit=2)
        print("✅ Marine Archaeology Tool works!")

        from ocean_mysteries.marine_biology_tools import MarineBiologyTool
        tool = MarineBiologyTool()
        result = await tool.search_deep_sea_creatures("anglerfish", limit=2)
        print("✅ Marine Biology Tool works!")

    except Exception as e:
        print(f"❌ Ocean Mysteries error: {e}")

async def test_archaeology():
    """Test Archaeology tools"""
    print("\n=== Testing Archaeology ===")

    try:
        from archaeology.ancient_civilizations_tools import AncientCivilizationsTool
        tool = AncientCivilizationsTool()
        result = await tool.search_civilizations("egyptian", limit=2)
        print("✅ Ancient Civilizations Tool works!")
        print(f"Result type: {type(result)}")

        from archaeology.artifact_analysis_tools import ArtifactAnalysisTool
        tool = ArtifactAnalysisTool()
        result = await tool.analyze_artifacts("pottery", limit=2)
        print("✅ Artifact Analysis Tool works!")

    except Exception as e:
        print(f"❌ Archaeology error: {e}")

async def test_aviation():
    """Test Aviation tools"""
    print("\n=== Testing Aviation ===")

    try:
        from aviation.aerospace_engineering_tools import AerospaceEngineeringTool
        tool = AerospaceEngineeringTool()
        result = await tool.search_aerospace_design("aerodynamics", limit=2)
        print("✅ Aerospace Engineering Tool works!")
        print(f"Result type: {type(result)}")

        from aviation.aircraft_technology_tools import AircraftTechnologyTool
        tool = AircraftTechnologyTool()
        result = await tool.search_aircraft_systems("avionics", limit=2)
        print("✅ Aircraft Technology Tool works!")

    except Exception as e:
        print(f"❌ Aviation error: {e}")

async def main():
    """Run all tests"""
    await test_ocean_mysteries()
    await test_archaeology()
    await test_aviation()
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    asyncio.run(main())
