#!/usr/bin/env python3
"""
Enhanced Demo cho Optimized Cosmology Facebook Team
Showcase tất cả features: text cleaning, chunking, reranking, Qdrant integration
"""

import sys
import time
import logging

# Add current directory to path
sys.path.append('.')

# Setup logging
logging.basicConfig(level=logging.INFO)

def enhanced_demo():
    """Demo với tất cả optimization features"""
    print("🌌 ENHANCED COSMOLOGY FACEBOOK TEAM DEMO")
    print("="*60)
    
    try:
        from cosmology_fb import CosmologyFbWorkflow
        
        # Tạo workflow
        print("📝 Initializing Enhanced Cosmology Workflow...")
        workflow = CosmologyFbWorkflow()
        print("✅ Workflow initialized successfully!")
        
        # Show current settings
        print(f"\n⚙️ OPTIMIZATION SETTINGS:")
        print(f"  Chunk Size: {workflow.chunk_size}")
        print(f"  Chunk Overlap: {workflow.chunk_overlap}")
        print(f"  Rerank Top K: {workflow.rerank_top_k}")
        print(f"  Qdrant Enabled: {workflow.use_qdrant}")
        
        # Option to enable Qdrant
        print(f"\n🔧 QDRANT INTEGRATION:")
        enable_qdrant = input("Enable Qdrant storage? (y/n): ").lower().strip()
        if enable_qdrant == 'y':
            print("🔄 Setting up Qdrant...")
            workflow.enable_qdrant("cosmology_demo")
            print(f"✅ Qdrant enabled: {workflow.use_qdrant}")
        else:
            print("⏭️ Qdrant disabled - using basic storage only")
        
        # Demo queries với độ phức tạp khác nhau
        demo_queries = [
            {
                "name": "Short English Query",
                "query": "What is dark matter?",
                "description": "Test basic text cleaning"
            },
            {
                "name": "Long Vietnamese Query", 
                "query": "Viết 1 status fb ít nhất 1000 tokens, chủ đề: Năng lượng tối ảnh hưởng gì đến sự mở rộng của vũ trụ và tương lai của nó?",
                "description": "Test chunking + reranking + Vietnamese support"
            },
            {
                "name": "Complex Physics Query",
                "query": "Explain how quantum fluctuations in the early universe led to the cosmic microwave background patterns we observe today, and create an engaging Facebook post about it",
                "description": "Test complex content optimization"
            }
        ]
        
        print(f"\n🎯 DEMO QUERIES:")
        for i, query_info in enumerate(demo_queries, 1):
            print(f"{i}. {query_info['name']}: {query_info['description']}")
        
        print(f"\n💡 You can also enter custom queries!")
        print(f"📝 Type 'demo X' to run demo query X (1-{len(demo_queries)})")
        print(f"🚪 Type 'exit' to quit")
        print("="*60)
        
        while True:
            try:
                user_input = input("\n🌌 Enhanced Query: ").strip()
                
                if user_input.lower() == "exit":
                    print("👋 Thanks for testing the enhanced cosmology team!")
                    break
                
                # Handle demo commands
                if user_input.lower().startswith("demo "):
                    try:
                        demo_num = int(user_input.split()[1])
                        if 1 <= demo_num <= len(demo_queries):
                            query_info = demo_queries[demo_num - 1]
                            user_input = query_info["query"]
                            print(f"🎯 Running Demo {demo_num}: {query_info['name']}")
                            print(f"📝 Description: {query_info['description']}")
                            print(f"❓ Query: {user_input}")
                        else:
                            print(f"❌ Invalid demo number. Use 1-{len(demo_queries)}")
                            continue
                    except (IndexError, ValueError):
                        print(f"❌ Invalid format. Use 'demo X' where X is 1-{len(demo_queries)}")
                        continue
                
                if not user_input:
                    print("❌ Please enter a query or 'exit' to quit")
                    continue
                
                print(f"\n🔍 Processing with enhanced optimizations...")
                print("⏳ This includes: text cleaning → chunking → reranking → storage")
                print("-" * 60)
                
                # Track processing
                start_time = time.time()
                
                try:
                    # Run enhanced workflow
                    result = workflow.process_cosmology_query(user_input)
                    
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    # Display results
                    print("📄 ENHANCED RESULT:")
                    print(result)
                    
                    # Show optimization stats
                    print(f"\n📊 OPTIMIZATION STATS:")
                    print(f"  Processing Time: {processing_time:.2f}s")
                    print(f"  Result Length: {len(result)} characters")
                    print(f"  Text Cleaned: {'✅' if '<think>' not in result else '❌'}")
                    print(f"  Qdrant Storage: {'✅' if workflow.use_qdrant else '⏭️ Disabled'}")
                    
                    # Analyze content quality
                    quality_score = analyze_content_quality(result)
                    print(f"  Content Quality: {quality_score:.1f}/10")
                    
                except Exception as e:
                    print(f"❌ Error during enhanced processing: {e}")
                    print("💡 This might be due to API limitations or network issues.")
                
                print("-" * 60)
                print("✅ Enhanced query completed!")
                
            except KeyboardInterrupt:
                print("\n\n👋 Demo interrupted by user. Goodbye!")
                break
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
                print("🔄 You can try again or ask a different question.")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed")
        return False
    except Exception as e:
        print(f"❌ Enhanced demo initialization error: {e}")
        return False
    
    return True

def analyze_content_quality(content: str) -> float:
    """Phân tích chất lượng content"""
    score = 0
    
    # Basic checks
    if len(content) > 100:
        score += 2
    if len(content) > 500:
        score += 1
    
    # Content features
    if any(emoji_char in content for emoji_char in "🌌🔬⭐🚀💫🌟"):
        score += 1.5  # Has science emojis
    if "#" in content:
        score += 1  # Has hashtags
    if any(word in content.lower() for word in ["dark", "energy", "matter", "universe", "cosmic"]):
        score += 2  # Has relevant keywords
    if any(phrase in content.lower() for phrase in ["comment", "share", "what do you think"]):
        score += 1  # Has engagement
    if "**" in content or "*" in content:
        score += 0.5  # Has formatting
    if not ("<think>" in content or "</think>" in content):
        score += 1  # Clean output
    
    return min(score, 10)  # Cap at 10

def show_enhanced_features():
    """Hiển thị thông tin về enhanced features"""
    print("\n" + "="*60)
    print("🚀 ENHANCED COSMOLOGY TEAM FEATURES")
    print("="*60)
    print("""
🧹 TEXT CLEANING:
├── Remove <think>...</think> tags from Qwen output
├── Clean HTML entities and artifacts
├── Normalize whitespace and formatting
└── Prepare content for embedding

🔄 SMART CHUNKING:
├── Auto-detect document type (text, markdown, HTML)
├── Language detection (English, Vietnamese, etc.)
├── Multiple strategies: semantic, recursive, token-based
└── Intelligent overlap for context preservation

🎯 RERANKING:
├── Sentence-transformers with all-MiniLM-L6-v2
├── Relevance scoring with cosine similarity
├── MMR (Maximal Marginal Relevance) algorithm
└── Hybrid strategy combining relevance + diversity

🗄️ QDRANT INTEGRATION:
├── Vector database for semantic search
├── Automatic embedding generation
├── Metadata storage with timestamps
└── Collection management

⚡ OPTIMIZATION FLOW:
1. Team research (unchanged)
2. Writer creates content (unchanged)
3. Text cleaning (remove artifacts)
4. Smart chunking (if content is long)
5. Reranking (select best chunks)
6. Qdrant storage (if enabled)
7. Return optimized content

🎯 BENEFITS:
• Cleaner output without thinking artifacts
• Better content quality through reranking
• Semantic search capabilities with Qdrant
• Multi-language support
• Scalable for large content volumes
    """)
    print("="*60)

def main():
    """Main function"""
    show_enhanced_features()
    
    print("\n🚀 Starting Enhanced Cosmology Demo...")
    success = enhanced_demo()
    
    if success:
        print("\n🎉 Enhanced demo completed successfully!")
    else:
        print("\n❌ Enhanced demo encountered issues.")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 Enhanced demo interrupted by user. Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)
