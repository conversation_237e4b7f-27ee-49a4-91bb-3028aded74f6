#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho c<PERSON><PERSON> hàm get_recent/trending mới đ<PERSON><PERSON><PERSON> thêm vào conspiracy tools.
"""

import sys
import os
import json

# Thêm thư mục gốc vào Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_above_top_secret():
    """Test Above Top Secret tools."""
    print("=== Testing Above Top Secret Tools ===")
    try:
        from tools.conspiracy.above_top_secret_tools import AboveTopSecretTools

        tool = AboveTopSecretTools()

        # Test regular search
        print("--- Regular Search ---")
        result1 = tool.search_discussions("UFO sightings", 3)
        print("Search result:", result1[:200] + "..." if len(result1) > 200 else result1)

        # Test recent discussions
        print("\n--- Recent Discussions ---")
        result2 = tool.get_recent_discussions(3, 7, "UFOs")
        print("Recent discussions result:", result2[:200] + "..." if len(result2) > 200 else result2)

        # Test trending topics
        print("\n--- Trending Topics ---")
        result3 = tool.get_trending_topics(3, "week")
        print("Trending topics result:", result3[:200] + "..." if len(result3) > 200 else result3)

    except Exception as e:
        print(f"Error testing Above Top Secret: {e}")
    print()

def test_conspiracy_search_toolkit():
    """Test Conspiracy Search Toolkit."""
    print("=== Testing Conspiracy Search Toolkit ===")
    try:
        from tools.conspiracy.conspiracy_search_toolkit import ConspiracySearchToolkit

        toolkit = ConspiracySearchToolkit()

        # Test regular keyword generation
        print("--- Above Top Secret Keywords ---")
        result1 = toolkit.generate_above_top_secret_keywords("UFO sightings", "UFOs")
        print(result1)

        print("\n--- Biblioteca Pleyades Keywords ---")
        result2 = toolkit.generate_biblioteca_pleyades_keywords("ancient aliens", "ancient mysteries")
        print(result2)

        print("\n--- CIA FOIA Keywords ---")
        result3 = toolkit.generate_cia_foia_keywords("MKUltra", "mind control")
        print(result3)

        print("\n--- Conspiracy Archive Keywords ---")
        result4 = toolkit.generate_conspiracy_archive_keywords("9/11 truth", "Government")
        print(result4)

        print("\n--- Wikipedia Conspiracy Keywords ---")
        result5 = toolkit.generate_wikipedia_conspiracy_keywords("JFK assassination")
        print(result5)

        # Test recent/trending keyword generation
        print("\n--- Above Top Secret Trending Keywords ---")
        result6 = toolkit.generate_above_top_secret_trending_keywords("week", "Conspiracy Theories")
        print(result6)

        print("\n--- Biblioteca Pleyades Recent Keywords ---")
        result7 = toolkit.generate_biblioteca_pleyades_recent_keywords("ancient mysteries", 30)
        print(result7)

        print("\n--- CIA FOIA Recent Keywords ---")
        result8 = toolkit.generate_cia_foia_recent_keywords("declassified", 30)
        print(result8)

        print("\n--- Conspiracy Archive Trending Keywords ---")
        result9 = toolkit.generate_conspiracy_archive_trending_keywords("Government", "month")
        print(result9)

        print("\n--- Wikipedia Conspiracy Recent Keywords ---")
        result10 = toolkit.generate_wikipedia_conspiracy_recent_keywords(30, "en")
        print(result10)

    except Exception as e:
        print(f"Error testing Search Toolkit: {e}")
    print()

def test_other_tools():
    """Test other conspiracy tools briefly."""
    print("=== Testing Other Conspiracy Tools ===")

    # Test Biblioteca Pleyades (if available)
    try:
        print("--- Biblioteca Pleyades Tools ---")
        from tools.conspiracy.bibliotecapleyades_tools import BibliotecaPleyadesTools
        tool = BibliotecaPleyadesTools()
        print("Biblioteca Pleyades tools loaded successfully")
    except Exception as e:
        print(f"Biblioteca Pleyades tools error: {e}")

    # Test CIA FOIA (if available)
    try:
        print("--- CIA FOIA Tools ---")
        from tools.conspiracy.cia_foia_conspiracy_tools import CIAFOIAConspiracyTools
        tool = CIAFOIAConspiracyTools()
        print("CIA FOIA tools loaded successfully")
    except Exception as e:
        print(f"CIA FOIA tools error: {e}")

    # Test Conspiracy Archive (if available)
    try:
        print("--- Conspiracy Archive Tools ---")
        from tools.conspiracy.conspiracy_archive_tools import ConspiracyArchiveTool
        tool = ConspiracyArchiveTool()
        print("Conspiracy Archive tools loaded successfully")
    except Exception as e:
        print(f"Conspiracy Archive tools error: {e}")

    # Test Wikipedia Conspiracy (if available)
    try:
        print("--- Wikipedia Conspiracy Tools ---")
        from tools.conspiracy.wikipedia_conspiracy_tools import WikipediaConspiracyTool
        tool = WikipediaConspiracyTool()
        print("Wikipedia Conspiracy tools loaded successfully")
    except Exception as e:
        print(f"Wikipedia Conspiracy tools error: {e}")

    print()

def test_package_import():
    """Test package-level imports."""
    print("=== Testing Package Import ===")
    try:
        from tools.conspiracy import (
            AboveTopSecretTools,
            BibliotecaPleyadesTools,
            CIAFOIAConspiracyTools,
            ConspiracyArchiveTools,
            WikipediaConspiracyTools,
            ConspiracySearchToolkit
        )
        print("✅ All package imports successful")

        # Test instantiation
        tools = [
            AboveTopSecretTools(),
            ConspiracySearchToolkit()
        ]
        print("✅ Tool instantiation successful")

    except Exception as e:
        print(f"❌ Package import error: {e}")
    print()

def main():
    """Chạy tất cả các test."""
    print("Testing New Conspiracy Tools Functions")
    print("=" * 50)
    print()

    # Test các tool đã cải tiến
    test_above_top_secret()
    test_conspiracy_search_toolkit()
    test_other_tools()
    test_package_import()

    print("=" * 50)
    print("Testing completed!")

if __name__ == "__main__":
    main()
