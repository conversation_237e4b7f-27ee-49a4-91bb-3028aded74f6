from typing import Dict, Any, Optional
from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.common.cot import LCoTTools
from ..config import MODEL_CONFIG
import logging

logger = logging.getLogger(__name__)

class AnalysisAgent(Agent):
    """Agent phân tích câu hỏi và xác định từ khóa tìm kiếm."""
    
    def __init__(self, **kwargs):
        super().__init__(
            model=<PERSON>lla<PERSON>(
                id=MODEL_CONFIG["base_model"],
            ),
            name="Analysis Agent",
            description="""
            Bạn là một chuyên gia phân tích câu hỏi về thiên văn học. 
            Nhiệm vụ của bạn là phân tích câu hỏi và xác định các từ khóa chính 
            để tìm kiếm tài liệu khoa học phù hợp.
            """,
            **kwargs
        )
        
        # Thêm LCoTTools
        self.lcot_tools = LCoTTools(
            add_instructions=True,
            add_few_shot=True,
            num_steps=5
        )
        self.tools = [self.lcot_tools]
        
        logger.info("Khởi tạo Analysis Agent thành công")
    
    async def arun(self, message: str, **kwargs) -> dict:
        """
        Phân tích câu hỏi và trả về các từ khóa tìm kiếm.
        Chỉ chuẩn bị prompt và trả về, không gọi trực tiếp model.
        """
        logger.info(f"Phân tích câu hỏi: {message}")
        # Sử dụng LCoT để phân tích câu hỏi
        lcot_result = await self.lcot_tools.lcot_reason(message, self)
        # Chuẩn bị prompt để trích xuất từ khóa (Team/Workflow sẽ xử lý tiếp)
        prompt = (
            f"Dựa trên phân tích sau, hãy liệt kê các từ khóa chính để tìm kiếm tài liệu khoa học. "
            f"Chỉ trả về danh sách các từ khóa, phân tách bằng dấu phẩy.\n\n"
            f"Phân tích:\n{lcot_result}\n\nTừ khóa:"
        )
        # Trả về prompt và phân tích để Team/Workflow xử lý tiếp
        return {
            "original_query": message,
            "analysis": lcot_result,
            "keyword_prompt": prompt
        }

    async def run(self, message: str, **kwargs) -> dict:
        return await self.arun(message, **kwargs)
