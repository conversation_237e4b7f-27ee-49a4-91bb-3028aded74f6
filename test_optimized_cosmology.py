#!/usr/bin/env python3
"""
Test script cho optimized Cosmology Facebook Team
Test với text cleaning, chunking, và reranking
"""

import sys
import time
import logging

# Add current directory to path
sys.path.append('.')

# Setup logging
logging.basicConfig(level=logging.INFO)

def test_optimized_workflow():
    """Test workflow với optimization features"""
    print("🧪 TESTING OPTIMIZED COSMOLOGY WORKFLOW")
    print("="*60)
    
    try:
        from cosmology_fb import CosmologyFbWorkflow
        
        # Tạo workflow
        print("📝 Initializing Optimized Cosmology Workflow...")
        workflow = CosmologyFbWorkflow()
        print("✅ Workflow initialized successfully!")
        
        # Test cases với optimization
        test_cases = [
            {
                "name": "Simple Dark Matter Query",
                "query": "What is dark matter?",
                "expected_features": ["dark matter", "invisible", "gravity"]
            },
            {
                "name": "Complex Quantum Cosmology",
                "query": "How do quantum fluctuations affect cosmic structure formation?",
                "expected_features": ["quantum", "fluctuations", "cosmic", "structure"]
            },
            {
                "name": "Vietnamese Query",
                "query": "<PERSON>ăng lượng tối ảnh hưởng gì đến vũ trụ?",
                "expected_features": ["dark energy", "universe", "expansion"]
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔬 TEST {i}/{len(test_cases)}: {test_case['name']}")
            print(f"❓ Query: {test_case['query']}")
            print("⏳ Processing with optimization...")
            
            start_time = time.time()
            
            try:
                # Test workflow
                result = workflow.process_cosmology_query(test_case['query'])
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                # Analyze result
                analysis = analyze_optimized_result(result, test_case['expected_features'])
                
                test_result = {
                    "test_name": test_case['name'],
                    "query": test_case['query'],
                    "processing_time": processing_time,
                    "result_length": len(result),
                    "has_clean_output": analysis['has_clean_output'],
                    "feature_coverage": analysis['feature_coverage'],
                    "optimization_score": analysis['optimization_score'],
                    "result_preview": result[:300] + "..." if len(result) > 300 else result
                }
                
                results.append(test_result)
                
                print(f"✅ Completed in {processing_time:.2f}s")
                print(f"📊 Optimization Score: {test_result['optimization_score']:.1f}/10")
                print(f"🧹 Clean Output: {'✅' if analysis['has_clean_output'] else '❌'}")
                print(f"📝 Result Preview: {test_result['result_preview']}")
                
            except Exception as e:
                print(f"❌ Test failed: {e}")
                results.append({
                    "test_name": test_case['name'],
                    "query": test_case['query'],
                    "error": str(e),
                    "optimization_score": 0
                })
        
        # Generate optimization report
        generate_optimization_report(results)
        
        return results
        
    except Exception as e:
        print(f"❌ Optimized workflow test failed: {e}")
        return []

def analyze_optimized_result(result: str, expected_features: list) -> dict:
    """Phân tích kết quả với focus vào optimization"""
    
    # Check if output is clean (no <think> tags)
    has_clean_output = "<think>" not in result and "</think>" not in result
    
    # Check feature coverage
    result_lower = result.lower()
    found_features = [feature for feature in expected_features if feature.lower() in result_lower]
    feature_coverage = len(found_features) / len(expected_features) if expected_features else 1.0
    
    # Check optimization indicators
    has_structure = any(indicator in result for indicator in ["**", "#", "•", "-", "1.", "2."])
    has_emojis = any(ord(char) > 127 for char in result)
    has_hashtags = "#" in result
    reasonable_length = 200 <= len(result) <= 3000
    
    # Calculate optimization score
    score = 0
    if has_clean_output:
        score += 3  # Clean output is crucial
    score += feature_coverage * 3  # Feature coverage
    if has_structure:
        score += 1
    if has_emojis:
        score += 1
    if has_hashtags:
        score += 1
    if reasonable_length:
        score += 1
    
    return {
        'has_clean_output': has_clean_output,
        'feature_coverage': feature_coverage,
        'found_features': found_features,
        'has_structure': has_structure,
        'has_emojis': has_emojis,
        'has_hashtags': has_hashtags,
        'reasonable_length': reasonable_length,
        'optimization_score': score
    }

def generate_optimization_report(results: list):
    """Tạo báo cáo optimization"""
    print("\n" + "="*60)
    print("📋 OPTIMIZATION TEST REPORT")
    print("="*60)
    
    if not results:
        print("❌ No test results to analyze")
        return
    
    # Filter successful tests
    successful_tests = [r for r in results if 'error' not in r]
    failed_tests = [r for r in results if 'error' in r]
    
    print(f"📊 OVERALL STATISTICS:")
    print(f"✅ Successful Tests: {len(successful_tests)}/{len(results)}")
    print(f"❌ Failed Tests: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        avg_optimization_score = sum(r['optimization_score'] for r in successful_tests) / len(successful_tests)
        avg_time = sum(r['processing_time'] for r in successful_tests) / len(successful_tests)
        
        print(f"🎯 Average Optimization Score: {avg_optimization_score:.1f}/10")
        print(f"⏱️ Average Processing Time: {avg_time:.1f}s")
        
        # Optimization features analysis
        clean_output_rate = sum(1 for r in successful_tests if r['has_clean_output']) / len(successful_tests)
        avg_feature_coverage = sum(r['feature_coverage'] for r in successful_tests) / len(successful_tests)
        
        print(f"\n🧹 OPTIMIZATION FEATURES:")
        print(f"  Clean Output Rate: {clean_output_rate:.1%}")
        print(f"  Average Feature Coverage: {avg_feature_coverage:.1%}")
        
        # Best and worst performing tests
        best_test = max(successful_tests, key=lambda x: x['optimization_score'])
        worst_test = min(successful_tests, key=lambda x: x['optimization_score'])
        
        print(f"\n🏆 BEST OPTIMIZED TEST:")
        print(f"  Test: {best_test['test_name']}")
        print(f"  Score: {best_test['optimization_score']:.1f}/10")
        print(f"  Time: {best_test['processing_time']:.1f}s")
        
        print(f"\n⚠️ LOWEST OPTIMIZED TEST:")
        print(f"  Test: {worst_test['test_name']}")
        print(f"  Score: {worst_test['optimization_score']:.1f}/10")
        print(f"  Time: {worst_test['processing_time']:.1f}s")
    
    # Failed tests details
    if failed_tests:
        print(f"\n❌ FAILED TESTS:")
        for test in failed_tests:
            print(f"  {test['test_name']}: {test['error']}")
    
    # Optimization recommendations
    print(f"\n💡 OPTIMIZATION RECOMMENDATIONS:")
    if successful_tests:
        if avg_optimization_score >= 8:
            print("  🎉 Excellent optimization! All features working well.")
        elif avg_optimization_score >= 6:
            print("  👍 Good optimization with room for improvement.")
            if clean_output_rate < 0.9:
                print("  🧹 Improve text cleaning to remove more <think> tags.")
            if avg_feature_coverage < 0.8:
                print("  🎯 Improve feature coverage in responses.")
        else:
            print("  ⚠️ Optimization needs significant improvement.")
            print("  🔧 Check text cleaning, chunking, and reranking functions.")
        
        if avg_time > 60:
            print("  ⏱️ Consider optimizing processing time.")

def main():
    """Main function"""
    print("🚀 Starting Optimized Cosmology Team Test\n")
    
    results = test_optimized_workflow()
    
    if results:
        print("\n🎉 Optimization test completed!")
        return 0
    else:
        print("\n❌ Optimization test failed.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)
