#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script cho Ocean Mysteries Tools
"""

import sys
import os
import json
import random
import asyncio
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_noaa_ocean_tools():
    """Test NOAA Ocean Tools"""
    print("🌊 Testing NOAA Ocean Tools...")
    try:
        from tools.ocean_mysteries.noaa_ocean_tools import NOAAOceanTool
        
        noaa_tool = NOAAOceanTool()
        
        print("  - Testing NOAA instantiation...")
        print("    ✅ NOAA Ocean Tools instantiated")
        
        # Test get_top_new
        print("  - Testing NOAA get_top_new...")
        assert hasattr(noaa_tool, 'get_top_new')
        print("    ✅ NOAA get_top_new method exists")
        
        return True
        
    except Exception as e:
        print(f"    ❌ NOAA Ocean Tools failed: {str(e)}")
        return False

def test_ocean_mysteries_search_toolkit():
    """Test Ocean Mysteries Search Toolkit"""
    print("🔍 Testing Ocean Mysteries Search Toolkit...")
    try:
        from tools.ocean_mysteries.ocean_mysteries_search_toolkit import OceanMysteriesSearchToolkit
        
        toolkit = OceanMysteriesSearchToolkit()
        
        print("  - Testing ocean mysteries search...")
        result = toolkit.search_ocean_mysteries("unexplained_sounds", "bermuda_triangle", "strong", "active")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Ocean mysteries search works")
        
        print("  - Testing deep sea discoveries search...")
        result = toolkit.search_deep_sea_discoveries("new_species", "deep", "high", "recent")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Deep sea discoveries search works")
        
        print("  - Testing marine phenomena search...")
        result = toolkit.search_marine_phenomena("physical", "regional", "rare", "unexplained")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Marine phenomena search works")
        
        print("  - Testing comprehensive ocean search...")
        result = toolkit.comprehensive_ocean_search("atlantis", "all", "ancient", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive ocean search works")
        
        print("  - Testing underwater archaeology search...")
        result = toolkit.search_underwater_archaeology("shipwreck", "ancient", "good", "diving")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Underwater archaeology search works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Ocean Mysteries Search Toolkit failed: {str(e)}")
        return False

def test_other_ocean_mysteries_tools():
    """Test other ocean mysteries tools"""
    print("🐙 Testing Other Ocean Mysteries Tools...")
    try:
        # Test Deep Sea Exploration Tools
        from tools.ocean_mysteries.deep_sea_exploration_tools import DeepSeaExplorationTool
        
        deep_sea_tool = DeepSeaExplorationTool()
        
        print("  - Testing Deep Sea Exploration Tools...")
        print("    ✅ Deep Sea Exploration Tools instantiated")
        
        # Test Marine Archaeology Tools
        from tools.ocean_mysteries.marine_archaeology_tools import MarineArchaeologyTool
        
        archaeology_tool = MarineArchaeologyTool()
        
        print("  - Testing Marine Archaeology Tools...")
        print("    ✅ Marine Archaeology Tools instantiated")
        
        # Test Marine Biology Tools
        from tools.ocean_mysteries.marine_biology_tools import MarineBiologyTool
        
        biology_tool = MarineBiologyTool()
        
        print("  - Testing Marine Biology Tools...")
        print("    ✅ Marine Biology Tools instantiated")
        
        # Test Ocean Phenomena Tools
        from tools.ocean_mysteries.ocean_phenomena_tools import OceanPhenomenaTool
        
        phenomena_tool = OceanPhenomenaTool()
        
        print("  - Testing Ocean Phenomena Tools...")
        print("    ✅ Ocean Phenomena Tools instantiated")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Other Ocean Mysteries Tools failed: {str(e)}")
        return False

def test_random_ocean_mysteries_functionality():
    """Test random ocean mysteries functionality"""
    print("\n🎲 Testing Random Ocean Mysteries Functionality...")
    
    try:
        # Random ocean mysteries test
        from tools.ocean_mysteries.ocean_mysteries_search_toolkit import OceanMysteriesSearchToolkit
        toolkit = OceanMysteriesSearchToolkit()
        
        mystery_types = ["unexplained_sounds", "anomalous_formations", "missing_vessels", "strange_creatures"]
        mystery_type = random.choice(mystery_types)
        result = toolkit.search_ocean_mysteries(mystery_type, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random ocean mysteries {mystery_type} search test passed")
        
        # Random deep sea discoveries test
        discovery_types = ["new_species", "geological_formation", "ecosystem", "technology"]
        discovery_type = random.choice(discovery_types)
        result = toolkit.search_deep_sea_discoveries(discovery_type, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random deep sea discoveries {discovery_type} test passed")
        
        # Random marine phenomena test
        phenomenon_types = ["physical", "biological", "chemical", "acoustic"]
        phenomenon_type = random.choice(phenomenon_types)
        result = toolkit.search_marine_phenomena(phenomenon_type, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random marine phenomena {phenomenon_type} test passed")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Random Ocean Mysteries Functionality failed: {str(e)}")
        return False

def test_ocean_mysteries_search_variations():
    """Test various ocean mysteries search variations"""
    print("\n🌀 Testing Ocean Mysteries Search Variations...")
    
    try:
        from tools.ocean_mysteries.ocean_mysteries_search_toolkit import OceanMysteriesSearchToolkit
        toolkit = OceanMysteriesSearchToolkit()
        
        # Test comprehensive search with different parameters
        print("  - Testing comprehensive search variations...")
        result = toolkit.comprehensive_ocean_search("kraken", "mysteries", "deep_sea", "advanced")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive search variations work")
        
        # Test underwater archaeology with different parameters
        print("  - Testing underwater archaeology variations...")
        result = toolkit.search_underwater_archaeology("", "medieval", "excellent", "submersible")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Underwater archaeology variations work")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Ocean Mysteries Search Variations failed: {str(e)}")
        return False

async def test_async_noaa():
    """Test async NOAA functionality"""
    print("\n⚡ Testing Async NOAA...")
    
    try:
        from tools.ocean_mysteries.noaa_ocean_tools import NOAAOceanTool
        
        noaa_tool = NOAAOceanTool()
        
        # Test async get_top_new
        print("  - Testing async get_top_new...")
        result = await noaa_tool.get_top_new("discoveries", 5, "month", "deep_sea")
        assert result["status"] == "success"
        print("    ✅ Async get_top_new works")
        
        # Test async search_ocean_data
        print("  - Testing async search_ocean_data...")
        result = await noaa_tool.search_ocean_data("temperature", "temperature", 5)
        assert "status" in result
        print("    ✅ Async search_ocean_data works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Async NOAA failed: {str(e)}")
        return False

def test_noaa_get_top_new_content_types():
    """Test NOAA get_top_new with different content types"""
    print("\n🔬 Testing NOAA get_top_new Content Types...")
    
    try:
        from tools.ocean_mysteries.noaa_ocean_tools import NOAAOceanTool
        
        noaa_tool = NOAAOceanTool()
        
        # Test get_top_new with different content types
        content_types = ["discoveries", "expeditions", "research", "species", "phenomena"]
        
        async def test_content_type(content_type):
            print(f"  - Testing get_top_new for {content_type}...")
            result = await noaa_tool.get_top_new(content_type, 3, "week", "marine")
            assert result["status"] == "success"
            print(f"    ✅ get_top_new for {content_type} works")
        
        # Run async tests
        async def run_all_tests():
            for content_type in content_types:
                await test_content_type(content_type)
        
        asyncio.run(run_all_tests())
        
        return True
        
    except Exception as e:
        print(f"    ❌ NOAA get_top_new Content Types failed: {str(e)}")
        return False

async def test_async_deep_sea_tools():
    """Test async deep sea tools functionality"""
    print("\n🌊 Testing Async Deep Sea Tools...")
    
    try:
        from tools.ocean_mysteries.deep_sea_exploration_tools import DeepSeaExplorationTool
        
        deep_sea_tool = DeepSeaExplorationTool()
        
        # Test async search_deep_sea_discoveries
        print("  - Testing async deep sea discoveries...")
        result = await deep_sea_tool.search_deep_sea_discoveries("hydrothermal vent", "deep", 3)
        assert result["status"] == "success"
        print("    ✅ Async deep sea discoveries works")
        
        # Test async search_underwater_mysteries
        print("  - Testing async underwater mysteries...")
        result = await deep_sea_tool.search_underwater_mysteries("unexplained_sounds", "pacific", 3)
        assert result["status"] == "success"
        print("    ✅ Async underwater mysteries works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Async Deep Sea Tools failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 OCEAN MYSTERIES TOOLS TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing Ocean Mysteries channel tools...")
    print()
    
    test_results = []
    
    # Test all ocean mysteries tools
    test_functions = [
        ("NOAA Ocean Tools", test_noaa_ocean_tools),
        ("Ocean Mysteries Search Toolkit", test_ocean_mysteries_search_toolkit),
        ("Other Ocean Mysteries Tools", test_other_ocean_mysteries_tools),
        ("Random Ocean Mysteries Functionality", test_random_ocean_mysteries_functionality),
        ("Ocean Mysteries Search Variations", test_ocean_mysteries_search_variations),
        ("NOAA get_top_new Content Types", test_noaa_get_top_new_content_types)
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()
    
    # Test async functionality
    print(f"\n{'='*20} Async Tests {'='*20}")
    try:
        async_result1 = asyncio.run(test_async_noaa())
        test_results.append(("Async NOAA", async_result1))
        
        async_result2 = asyncio.run(test_async_deep_sea_tools())
        test_results.append(("Async Deep Sea Tools", async_result2))
    except Exception as e:
        print(f"❌ Async tests failed: {str(e)}")
        test_results.append(("Async NOAA", False))
        test_results.append(("Async Deep Sea Tools", False))
    
    # Summary
    print("\n" + "="*60)
    print("📋 OCEAN MYSTERIES TOOLS TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All ocean mysteries tools are working correctly!")
        print("✨ Ocean Mysteries channel fully functional!")
        print("🌊 Ready for deep sea exploration and underwater mysteries!")
    elif passed >= total * 0.8:
        print("✅ Excellent performance - most functionality working!")
    elif passed >= total * 0.6:
        print("✅ Good performance - majority working!")
    else:
        print("⚠️  Some issues detected. Please check the error messages above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
