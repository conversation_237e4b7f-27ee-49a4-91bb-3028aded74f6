"""
Team module initialization.
This module defines the base team classes and provides import shortcuts.
"""

from agno.team import Team
from agno.models.ollama import Ollama

# Export các lớp và hàm cần thiết
__all__ = [
    "Team",
    "<PERSON>llama",
    "create_route_team",
    "create_coordinate_team",
    "create_collaborate_team"
]

def create_route_team(name, members, description=None, instructions=None):
    """
    Create a team with route mode using Ollama Qwen3:4B model.
    
    Args:
        name (str): Name of the team
        members (list): List of agent members
        description (str, optional): Team description
        instructions (list, optional): List of instruction strings
        
    Returns:
        Team: An initialized Agno team with route mode
    """
    return Team(
        name=name,
        mode="route",
        model=Ollama(id="qwen3:4b"),
        members=members,
        description=description or f"{name} - Router Team",
        instructions=instructions or [],
        show_tool_calls=True,
        markdown=True
    )

def create_coordinate_team(name, members, description=None, instructions=None):
    """
    Create a team with coordinate mode using Ollama Qwen3:4B model.
    
    Args:
        name (str): Name of the team
        members (list): List of agent members
        description (str, optional): Team description
        instructions (list, optional): List of instruction strings
        
    Returns:
        Team: An initialized Agno team with coordinate mode
    """
    return Team(
        name=name,
        mode="coordinate",
        model=Ollama(id="qwen3:4b"),
        members=members,
        description=description or f"{name} - Coordinate Team",
        instructions=instructions or [],
        show_tool_calls=True,
        markdown=True
    )

def create_collaborate_team(name, members, description=None, instructions=None):
    """
    Create a team with collaborate mode using Ollama Qwen3:4B model.
    
    Args:
        name (str): Name of the team
        members (list): List of agent members
        description (str, optional): Team description
        instructions (list, optional): List of instruction strings
        
    Returns:
        Team: An initialized Agno team with collaborate mode
    """
    return Team(
        name=name,
        mode="collaborate",
        model=Ollama(id="qwen3:4b"),
        members=members,
        description=description or f"{name} - Collaborate Team",
        instructions=instructions or [],
        show_tool_calls=True,
        markdown=True
    )