from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import re

class UNEPGreenTool(Toolkit):
    """
    UNEP Green Tool cho tìm kiếm tài li<PERSON>, b<PERSON><PERSON> c<PERSON>o, h<PERSON><PERSON><PERSON> dẫn sống xanh, ph<PERSON>t triển bền vững từ UNEP (Chương trình Môi trường Liên H<PERSON>uốc).
    """

    def __init__(self):
        super().__init__(
            name="UNEP Green Living Search Tool",
            description="Tool cho tìm kiếm tài liệu, b<PERSON><PERSON> c<PERSON>o, hư<PERSON>ng dẫn sống xanh, ph<PERSON>t triển bền vững từ UNEP.",
            tools=[self.search_unep_green]
        )

    async def search_unep_green(self, query: str, topic: Optional[str] = None, year: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm UNEP cho tà<PERSON>, b<PERSON><PERSON>, h<PERSON><PERSON><PERSON> dẫn sống xanh, ph<PERSON><PERSON> triển bền vững.

        Parameters:
        - query: <PERSON><PERSON> khóa tìm kiếm (ví dụ: 'zero waste', 'plastic pollution', 'green energy', 'sustainable fashion', 'eco-friendly tips')
        - topic: Chủ đề cụ thể (ví dụ: 'energy', 'waste', 'climate', 'lifestyle')
        - year: Năm hoặc khoảng năm (ví dụ: '2022', '2018-2022')
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với tiêu đề, mô tả, chủ đề, năm, link UNEP
        """
        logger.info(f"Tìm kiếm UNEP Green: query={query}, topic={topic}, year={year}, limit={limit}")

        try:
            # UNEP Publications search endpoint (giả lập, thực tế có thể dùng https://www.unep.org/resources/publications)
            search_url = "https://www.unep.org/api/v1/search"
            params = {
                "q": query,
                "type": "publication",
                "limit": limit
            }
            if topic:
                params["topic"] = topic
            if year:
                params["year"] = year

            headers = {
                "User-Agent": "Mozilla/5.0 (compatible; UNEPGreenBot/1.0)"
            }
            response = requests.get(search_url, params=params, headers=headers, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "UNEP",
                    "message": f"UNEP API trả về mã lỗi {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for item in data.get("results", []):
                title = item.get("title")
                description = item.get("description")
                year_val = item.get("year") or item.get("datePublished")
                subjects = item.get("subjects") or item.get("tags")
                unep_url = item.get("url") or item.get("link")
                results.append({
                    "title": title,
                    "description": description,
                    "year": year_val,
                    "subjects": subjects,
                    "unep_url": unep_url
                })

            return {
                "status": "success",
                "source": "UNEP",
                "query": query,
                "topic": topic,
                "year": year,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "zero waste",
                    "plastic pollution",
                    "green energy",
                    "sustainable fashion",
                    "eco-friendly tips",
                    "recycling",
                    "renewable energy",
                    "climate action",
                    "biodiversity",
                    "lifestyle guide"
                ],
                "official_data_url": "https://www.unep.org/resources/publications"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm UNEP Green: {str(e)}")
            return {
                "status": "error",
                "source": "UNEP",
                "message": str(e),
                "query": query
            }
