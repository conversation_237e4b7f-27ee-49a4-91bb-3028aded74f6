# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
from datetime import datetime

class MilitarySearchToolkit(Toolkit):
    """
    Military Search Toolkit cho tìm kiếm và tổng hợp thông tin quân sự từ nhiều nguồn.
    """

    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(
            name="military_search_toolkit",
            **kwargs
        )

        self.search_sources = {
            "janes": "Jane's Defence Intelligence",
            "sipri": "Stockholm International Peace Research Institute",
            "globalsecurity": "GlobalSecurity.org",
            "military_factory": "Military Factory Database",
            "wikipedia_military": "Wikipedia Military Articles",
            "defense_databases": "Various defense databases"
        }

        if enable_search:
            self.register(self.search_military_conflicts)
            self.register(self.search_weapons_systems)
            self.register(self.search_military_units)
            self.register(self.comprehensive_military_search)
            self.register(self.search_military_leaders)

    def search_military_conflicts(self, conflict_name: str, time_period: str = "all",
                                region: str = "global", search_depth: str = "standard") -> str:
        """
        Tìm kiếm thông tin về xung đột quân sự.

        Args:
        - conflict_name: Tên xung đột hoặc chiến tranh
        - time_period: Khoảng thời gian ('ancient', 'medieval', 'modern', 'contemporary', 'all')
        - region: Khu vực địa lý ('global', 'europe', 'asia', 'africa', 'americas', 'middle_east')
        - search_depth: Mức độ tìm kiếm ('basic', 'standard', 'comprehensive')

        Returns:
        - JSON string với kết quả tìm kiếm xung đột quân sự
        """
        log_debug(f"Searching military conflicts for: {conflict_name}")

        try:
            # Conflict search across sources
            conflict_results = self._search_across_military_databases(conflict_name, "conflicts", time_period)

            # Battle analysis
            battle_analysis = self._analyze_major_battles(conflict_results, conflict_name)

            # Strategic assessment
            strategic_assessment = self._assess_strategic_context(conflict_results, region)

            # Casualties and impact
            casualties_impact = self._analyze_casualties_and_impact(conflict_results)

            # Historical significance
            historical_significance = self._assess_historical_significance(conflict_results) if search_depth != "basic" else {}

            # Lessons learned
            lessons_learned = self._extract_military_lessons(conflict_results, battle_analysis)

            result = {
                "search_parameters": {
                    "conflict_name": conflict_name,
                    "time_period": time_period,
                    "region": region,
                    "search_depth": search_depth,
                    "sources_searched": list(self.search_sources.keys())
                },
                "conflict_results": conflict_results,
                "battle_analysis": battle_analysis,
                "strategic_assessment": strategic_assessment,
                "casualties_impact": casualties_impact,
                "historical_significance": historical_significance,
                "lessons_learned": lessons_learned,
                "search_quality": self._assess_search_quality(conflict_results, search_depth),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error searching military conflicts: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_weapons_systems(self, weapon_type: str, country: str = "",
                             era: str = "modern", specifications: bool = True) -> str:
        """
        Tìm kiếm thông tin về hệ thống vũ khí.

        Args:
        - weapon_type: Loại vũ khí ('aircraft', 'naval', 'land_systems', 'missiles', 'small_arms')
        - country: Quốc gia sản xuất hoặc sử dụng
        - era: Thời đại ('wwi', 'wwii', 'cold_war', 'modern', 'contemporary')
        - specifications: Có bao gồm thông số kỹ thuật không

        Returns:
        - JSON string với thông tin hệ thống vũ khí
        """
        log_debug(f"Searching weapons systems for: {weapon_type}")

        try:
            # Weapons search
            weapons_data = self._search_weapons_comprehensive(weapon_type, country, era)

            # Technical specifications
            technical_specs = self._gather_technical_specifications(weapons_data) if specifications else {}

            # Operational history
            operational_history = self._analyze_operational_history(weapons_data, weapon_type)

            # Comparative analysis
            comparative_analysis = self._compare_weapon_systems(weapons_data, weapon_type)

            # Development timeline
            development_timeline = self._trace_development_timeline(weapons_data, era)

            # Export and variants
            export_variants = self._analyze_exports_and_variants(weapons_data, country)

            result = {
                "search_parameters": {
                    "weapon_type": weapon_type,
                    "country": country or "All countries",
                    "era": era,
                    "specifications": specifications,
                    "search_scope": "Comprehensive weapons analysis"
                },
                "weapons_data": weapons_data,
                "technical_specs": technical_specs,
                "operational_history": operational_history,
                "comparative_analysis": comparative_analysis,
                "development_timeline": development_timeline,
                "export_variants": export_variants,
                "military_significance": self._assess_military_significance(weapons_data, operational_history),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error searching weapons systems: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_military_units(self, unit_name: str, unit_type: str = "all",
                            country: str = "", time_period: str = "all") -> str:
        """
        Tìm kiếm thông tin về đơn vị quân sự.

        Args:
        - unit_name: Tên đơn vị quân sự
        - unit_type: Loại đơn vị ('army', 'navy', 'air_force', 'special_forces', 'all')
        - country: Quốc gia
        - time_period: Thời kỳ hoạt động

        Returns:
        - JSON string với thông tin đơn vị quân sự
        """
        log_debug(f"Searching military units for: {unit_name}")

        try:
            # Unit search
            unit_data = self._search_military_units_comprehensive(unit_name, unit_type, country, time_period)

            # Organization structure
            organization_structure = self._analyze_unit_organization(unit_data, unit_type)

            # Combat history
            combat_history = self._trace_combat_history(unit_data, unit_name)

            # Equipment and capabilities
            equipment_capabilities = self._analyze_unit_equipment(unit_data, unit_type)

            # Notable operations
            notable_operations = self._identify_notable_operations(combat_history)

            # Current status
            current_status = self._assess_current_unit_status(unit_data, time_period)

            result = {
                "search_parameters": {
                    "unit_name": unit_name,
                    "unit_type": unit_type,
                    "country": country or "All countries",
                    "time_period": time_period,
                    "search_focus": "Military unit analysis"
                },
                "unit_data": unit_data,
                "organization_structure": organization_structure,
                "combat_history": combat_history,
                "equipment_capabilities": equipment_capabilities,
                "notable_operations": notable_operations,
                "current_status": current_status,
                "unit_significance": self._assess_unit_significance(combat_history, notable_operations),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error searching military units: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def comprehensive_military_search(self, search_query: str, search_scope: str = "all",
                                   historical_period: str = "all", military_branch: str = "all") -> str:
        """
        Tìm kiếm quân sự toàn diện từ nhiều nguồn.

        Args:
        - search_query: Từ khóa tìm kiếm
        - search_scope: Phạm vi tìm kiếm ('all', 'conflicts', 'weapons', 'units', 'leaders', 'strategy')
        - historical_period: Thời kỳ lịch sử ('ancient', 'medieval', 'modern', 'contemporary', 'all')
        - military_branch: Quân chủng ('army', 'navy', 'air_force', 'marines', 'all')

        Returns:
        - JSON string với kết quả tìm kiếm toàn diện
        """
        log_debug(f"Comprehensive military search for: {search_query}")

        try:
            # Multi-source search results
            search_results = {}

            if search_scope in ["all", "conflicts"]:
                search_results["conflicts"] = self._search_military_conflicts_comprehensive(search_query, historical_period)

            if search_scope in ["all", "weapons"]:
                search_results["weapons"] = self._search_weapons_by_query(search_query, military_branch)

            if search_scope in ["all", "units"]:
                search_results["units"] = self._search_units_by_query(search_query, military_branch)

            if search_scope in ["all", "leaders"]:
                search_results["leaders"] = self._search_military_leaders(search_query, historical_period)

            if search_scope in ["all", "strategy"]:
                search_results["strategy"] = self._search_military_strategy(search_query, historical_period)

            # Information synthesis
            information_synthesis = self._synthesize_military_information(search_results)

            # Strategic insights
            strategic_insights = self._generate_strategic_insights(search_results, search_query)

            # Historical context
            historical_context = self._provide_historical_context(search_results, historical_period)

            # Cross-references
            cross_references = self._identify_cross_references(search_results)

            result = {
                "search_parameters": {
                    "search_query": search_query,
                    "search_scope": search_scope,
                    "historical_period": historical_period,
                    "military_branch": military_branch,
                    "sources_consulted": list(self.search_sources.keys())
                },
                "search_results": search_results,
                "information_synthesis": information_synthesis,
                "strategic_insights": strategic_insights,
                "historical_context": historical_context,
                "cross_references": cross_references,
                "search_statistics": self._generate_military_search_statistics(search_results),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error in comprehensive military search: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_military_leaders(self, leader_name: str, rank: str = "",
                              country: str = "", time_period: str = "all") -> str:
        """
        Tìm kiếm thông tin về lãnh đạo quân sự.

        Args:
        - leader_name: Tên lãnh đạo quân sự
        - rank: Cấp bậc quân sự
        - country: Quốc gia phục vụ
        - time_period: Thời kỳ phục vụ

        Returns:
        - JSON string với thông tin về lãnh đạo quân sự
        """
        log_debug(f"Searching military leaders: {leader_name}")

        try:
            # Leader search
            leader_data = self._search_military_leaders_comprehensive(leader_name, rank, country, time_period)

            # Military career
            military_career = self._analyze_military_career(leader_data, leader_name)

            # Major campaigns
            major_campaigns = self._identify_major_campaigns(leader_data, leader_name)

            # Leadership style
            leadership_style = self._analyze_leadership_style(leader_data, major_campaigns)

            # Strategic contributions
            strategic_contributions = self._assess_strategic_contributions(leader_data, major_campaigns)

            # Historical legacy
            historical_legacy = self._evaluate_historical_legacy(leader_data, strategic_contributions)

            result = {
                "search_parameters": {
                    "leader_name": leader_name,
                    "rank": rank or "All ranks",
                    "country": country or "All countries",
                    "time_period": time_period,
                    "search_focus": "Military leadership analysis"
                },
                "leader_data": leader_data,
                "military_career": military_career,
                "major_campaigns": major_campaigns,
                "leadership_style": leadership_style,
                "strategic_contributions": strategic_contributions,
                "historical_legacy": historical_legacy,
                "leadership_significance": self._assess_leadership_significance(strategic_contributions, historical_legacy),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error searching military leaders: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    # Helper methods (simplified implementations)
    def _search_across_military_databases(self, query: str, search_type: str, period: str) -> dict:
        """Search across military databases."""
        return {
            "janes_results": [
                {"title": f"Military Analysis: {query}", "source": "Jane's", "relevance": "High"}
                for i in range(3)
            ],
            "sipri_results": [
                {"title": f"Defense Data: {query}", "source": "SIPRI", "type": "Statistical"}
                for i in range(2)
            ],
            "total_results": 5,
            "search_quality": "High"
        }

    def _analyze_major_battles(self, results: dict, conflict: str) -> dict:
        """Analyze major battles in conflict."""
        return {
            "major_battles": [f"Battle of {conflict} #{i+1}" for i in range(3)],
            "battle_outcomes": {"victories": 2, "defeats": 1, "stalemates": 0},
            "strategic_importance": "High",
            "tactical_innovations": ["Combined arms", "Air support", "Electronic warfare"]
        }

    def _assess_strategic_context(self, results: dict, region: str) -> dict:
        """Assess strategic context."""
        return {
            "strategic_objectives": ["Territorial control", "Resource access", "Political influence"],
            "regional_impact": f"Significant impact on {region} stability",
            "international_involvement": "Multiple powers involved",
            "geopolitical_consequences": "Long-term regional realignment"
        }

    def _analyze_casualties_and_impact(self, results: dict) -> dict:
        """Analyze casualties and impact."""
        return {
            "military_casualties": "Estimated 100,000+",
            "civilian_casualties": "Estimated 50,000+",
            "economic_impact": "Severe regional economic disruption",
            "social_impact": "Massive population displacement",
            "infrastructure_damage": "Extensive destruction of key infrastructure"
        }

    def _assess_historical_significance(self, results: dict) -> dict:
        """Assess historical significance."""
        return {
            "historical_importance": "Major turning point",
            "military_innovations": ["New tactics", "Technology advancement"],
            "political_consequences": "Changed regional power balance",
            "long_term_effects": "Influenced subsequent conflicts"
        }

    def _extract_military_lessons(self, results: dict, battles: dict) -> list:
        """Extract military lessons learned."""
        return [
            "Importance of air superiority",
            "Value of intelligence gathering",
            "Need for logistics planning",
            "Significance of coalition warfare",
            "Impact of technology on warfare"
        ]

    def _assess_search_quality(self, results: dict, depth: str) -> dict:
        """Assess search result quality."""
        return {
            "completeness": "High" if depth == "comprehensive" else "Medium",
            "source_diversity": "Multiple authoritative sources",
            "information_currency": "Up-to-date",
            "reliability": "High confidence"
        }

    def _search_weapons_comprehensive(self, weapon_type: str, country: str, era: str) -> dict:
        """Search weapons comprehensively."""
        return {
            "weapon_systems": [
                {"name": f"{weapon_type.title()} System {i+1}", "country": country or "Various", "era": era}
                for i in range(5)
            ],
            "total_systems": 5,
            "categories": [weapon_type],
            "time_span": era
        }

    def _gather_technical_specifications(self, data: dict) -> dict:
        """Gather technical specifications."""
        return {
            "performance_specs": {"speed": "Mach 2.0", "range": "1000 km", "payload": "2000 kg"},
            "dimensions": {"length": "15 m", "wingspan": "10 m", "height": "4 m"},
            "weight": {"empty": "8000 kg", "loaded": "12000 kg", "max_takeoff": "15000 kg"},
            "powerplant": "Twin turbofan engines"
        }

    def _analyze_operational_history(self, data: dict, weapon_type: str) -> dict:
        """Analyze operational history."""
        return {
            "first_deployment": "1985",
            "major_conflicts": ["Gulf War", "Kosovo", "Afghanistan"],
            "operational_record": "Highly successful",
            "combat_effectiveness": "Proven in multiple theaters"
        }

    def _compare_weapon_systems(self, data: dict, weapon_type: str) -> dict:
        """Compare weapon systems."""
        return {
            "peer_competitors": [f"Competitor {i+1}" for i in range(3)],
            "advantages": ["Superior range", "Advanced avionics", "Proven reliability"],
            "disadvantages": ["Higher cost", "Complex maintenance"],
            "overall_ranking": "Top tier"
        }

    def _trace_development_timeline(self, data: dict, era: str) -> dict:
        """Trace development timeline."""
        return {
            "concept_phase": "1975-1980",
            "development_phase": "1980-1985",
            "testing_phase": "1985-1990",
            "production_phase": "1990-present",
            "major_upgrades": ["Block I (1995)", "Block II (2005)", "Block III (2015)"]
        }

    def _analyze_exports_and_variants(self, data: dict, country: str) -> dict:
        """Analyze exports and variants."""
        return {
            "export_customers": ["Allied Nation 1", "Allied Nation 2", "Allied Nation 3"],
            "variants": ["Standard", "Export", "Upgraded"],
            "production_numbers": "500+ units",
            "export_restrictions": "Limited to allied nations"
        }

    def _assess_military_significance(self, weapons: dict, history: dict) -> dict:
        """Assess military significance."""
        return {
            "strategic_importance": "High",
            "tactical_value": "Force multiplier",
            "deterrent_effect": "Significant",
            "influence_on_doctrine": "Shaped modern warfare concepts"
        }

    def _search_military_units_comprehensive(self, unit: str, unit_type: str, country: str, period: str) -> dict:
        """Search military units comprehensively."""
        return {
            "unit_profile": {
                "name": unit,
                "type": unit_type,
                "country": country or "Various",
                "active_period": period
            },
            "organizational_data": {"size": "Brigade", "personnel": "3000", "structure": "Combined arms"},
            "equipment_inventory": ["Main battle tanks", "Infantry fighting vehicles", "Artillery"],
            "operational_status": "Active"
        }

    def _analyze_unit_organization(self, data: dict, unit_type: str) -> dict:
        """Analyze unit organization."""
        return {
            "command_structure": "Hierarchical",
            "sub_units": ["Battalion A", "Battalion B", "Support Battalion"],
            "personnel_strength": "3000 active personnel",
            "organizational_type": unit_type
        }

    def _trace_combat_history(self, data: dict, unit: str) -> dict:
        """Trace combat history."""
        return {
            "major_engagements": [f"Operation {i+1}" for i in range(4)],
            "combat_record": "Distinguished service",
            "battle_honors": ["Campaign Medal", "Unit Citation"],
            "casualties": "Moderate losses in major operations"
        }

    def _analyze_unit_equipment(self, data: dict, unit_type: str) -> dict:
        """Analyze unit equipment."""
        return {
            "primary_equipment": ["M1A2 Abrams", "M2 Bradley", "M109 Paladin"],
            "support_equipment": ["HMMWV", "M88 Recovery", "Communication systems"],
            "modernization_status": "Recently upgraded",
            "readiness_level": "High"
        }

    def _identify_notable_operations(self, history: dict) -> list:
        """Identify notable operations."""
        return [
            {"operation": "Desert Storm", "year": "1991", "role": "Spearhead assault"},
            {"operation": "Iraqi Freedom", "year": "2003", "role": "Urban combat"},
            {"operation": "Enduring Freedom", "year": "2001-2014", "role": "Counterinsurgency"}
        ]

    def _assess_current_unit_status(self, data: dict, period: str) -> dict:
        """Assess current unit status."""
        return {
            "current_status": "Active" if period == "all" else "Historical",
            "location": "Fort Hood, Texas",
            "readiness": "Combat ready",
            "next_deployment": "Scheduled for 2024"
        }
