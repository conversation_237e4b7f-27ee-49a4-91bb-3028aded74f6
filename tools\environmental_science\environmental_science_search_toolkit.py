# -*- coding: utf-8 -*-
from typing import List, Dict, Any
import json
from agno.tools import Toolkit
from agno.utils.log import logger

class EnvironmentalScienceSearchToolkit(Toolkit):
    """A custom Toolkit for generating search keywords for environmental science databases.

    This toolkit provides functions to generate search keywords for NASA EarthData,
    NOAA Climate, Global Forest Watch, UNEP Publications, and Wikipedia Environmental, tailored for environmental research.
    """

    # == Detailed Instructions for the Agent ==
    instruction = [
        "Bạn là một trợ lý nghiên cứu khoa học môi trường, chuyên cung cấp từ khóa tìm kiếm tối ưu cho các cơ sở dữ liệu môi trường.",
        "<PERSON><PERSON> sử dụng các công cụ trong EnvironmentalScienceSearchToolkit, tuân thủ các định dạng từ khóa được chỉ định như sau:",
        "- NASA EarthData: Sử dụng định dạng 'satellite dataset' (ví dụ: 'MODIS NDVI', 'Landsat surface temperature', 'VIIRS nighttime lights').",
        "- NOAA Climate: Sử dụng định dạng 'climate variable location' (ví dụ: 'temperature anomaly USA', 'precipitation trends Pacific', 'sea level rise').",
        "- Global Forest Watch: Sử dụng định dạng 'forest monitoring' (ví dụ: 'deforestation alerts Brazil', 'forest cover change Amazon', 'tree loss Indonesia').",
        "- UNEP Publications: Sử dụng định dạng 'environmental topic' (ví dụ: 'climate change adaptation', 'biodiversity conservation', 'sustainable development').",
        "- Wikipedia Environmental: Sử dụng định dạng 'environmental concept' (ví dụ: 'greenhouse effect', 'ecosystem services', 'carbon cycle').",
        "Ngoài ra, toolkit cũng hỗ trợ tạo từ khóa cho việc tìm kiếm nội dung mới nhất và trending:",
        "- NASA EarthData Recent: Tạo từ khóa cho satellite data và environmental topics mới theo data type.",
        "- NOAA Climate Recent: Tạo từ khóa cho climate data và extreme weather events mới theo region.",
        "- Global Forest Watch Recent: Tạo từ khóa cho deforestation alerts và forest trends mới theo country.",
        "- UNEP Publications Recent: Tạo từ khóa cho environmental reports và sustainability topics mới theo theme.",
        "- Wikipedia Environmental Recent: Tạo từ khóa cho bài viết environmental mới được tạo hoặc cập nhật.",
        "Kiểm tra tính hợp lệ của tham số đầu vào và trả về từ khóa phù hợp với từng cơ sở dữ liệu.",
        "Trả về kết quả dưới dạng JSON với trạng thái ('status'), danh sách từ khóa ('keywords'), và thông báo ('message').",
        "Nếu có lỗi, trả về trạng thái 'error' với mô tả lỗi chi tiết."
    ]

    # == Detailed Few-Shot Examples ==
    few_shot_examples = [
        {
            "user": "Tìm thông tin về climate change và global warming.",
            "tool_calls": [
                {
                    "name": "generate_nasa_earthdata_keywords",
                    "arguments": {"dataset": "MODIS temperature", "region": "global", "data_type": "climate"}
                },
                {
                    "name": "generate_noaa_climate_keywords",
                    "arguments": {"variable": "temperature anomaly", "location": "global", "timeframe": "annual"}
                },
                {
                    "name": "generate_wikipedia_environmental_keywords",
                    "arguments": {"concept": "global warming", "field": "climate science"}
                }
            ]
        },
        {
            "user": "Tìm recent satellite data và trending environmental topics.",
            "tool_calls": [
                {
                    "name": "generate_nasa_earthdata_recent_keywords",
                    "arguments": {"data_type": "MODIS", "days_back": 7}
                },
                {
                    "name": "generate_noaa_climate_recent_keywords",
                    "arguments": {"region": "North America", "days_back": 30}
                },
                {
                    "name": "generate_global_forest_watch_recent_keywords",
                    "arguments": {"country": "Brazil", "days_back": 14}
                }
            ]
        },
        {
            "user": "Tìm nghiên cứu về deforestation và biodiversity loss.",
            "tool_calls": [
                {
                    "name": "generate_global_forest_watch_keywords",
                    "arguments": {"monitoring_type": "deforestation", "country": "Indonesia", "forest_type": "tropical"}
                },
                {
                    "name": "generate_unep_publications_keywords",
                    "arguments": {"topic": "biodiversity conservation", "theme": "ecosystem protection", "region": "tropical"}
                },
                {
                    "name": "generate_wikipedia_environmental_keywords",
                    "arguments": {"concept": "biodiversity loss", "field": "conservation biology"}
                }
            ]
        }
    ]

    def __init__(self):
        """Initializes the EnvironmentalScienceSearchToolkit."""
        super().__init__(
            name="environmental_science_search_toolkit",
            tools=[
                self.generate_nasa_earthdata_keywords,
                self.generate_noaa_climate_keywords,
                self.generate_global_forest_watch_keywords,
                self.generate_unep_publications_keywords,
                self.generate_wikipedia_environmental_keywords,
                self.generate_nasa_earthdata_recent_keywords,
                self.generate_noaa_climate_recent_keywords,
                self.generate_global_forest_watch_recent_keywords,
                self.generate_unep_publications_recent_keywords,
                self.generate_wikipedia_environmental_recent_keywords
            ],
            instructions=self.instruction
        )
        self.few_shot_examples = self.few_shot_examples
        logger.info("EnvironmentalScienceSearchToolkit initialized.")

    def generate_nasa_earthdata_keywords(self, dataset: str, region: str = None, data_type: str = None) -> str:
        """Generates search keywords for NASA EarthData.

        Args:
            dataset: The satellite dataset (e.g., 'MODIS NDVI', 'Landsat surface temperature').
            region: Optional region filter (e.g., 'global', 'North America', 'Amazon').
            data_type: Optional data type (e.g., 'climate', 'vegetation', 'ocean', 'atmosphere').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating NASA EarthData keywords for dataset: '{dataset}', region: '{region}', data_type: '{data_type}'")
        try:
            if not dataset.strip():
                raise ValueError("Dataset cannot be empty.")

            # Tạo từ khóa cho NASA EarthData
            keywords = [dataset]
            if region:
                keywords.append(f"{dataset} {region}")
            if data_type:
                keywords.append(f"{dataset} {data_type}")

            # Thêm từ khóa mở rộng
            keywords.extend([
                f"{dataset} satellite", f"{dataset} remote sensing", f"{dataset} earth observation",
                f"{dataset} analysis", f"{dataset} monitoring"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated NASA EarthData keywords for dataset '{dataset}', region '{region or 'all'}', data_type '{data_type or 'all'}'.",
                "search_type": "satellite_data",
                "parameters": {
                    "dataset": dataset,
                    "region": region,
                    "data_type": data_type
                }
            }
            logger.debug(f"NASA EarthData keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating NASA EarthData keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate NASA EarthData keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_noaa_climate_keywords(self, variable: str, location: str = None, timeframe: str = None) -> str:
        """Generates search keywords for NOAA Climate."""
        logger.info(f"Generating NOAA Climate keywords for variable: '{variable}', location: '{location}', timeframe: '{timeframe}'")
        try:
            if not variable.strip():
                raise ValueError("Variable cannot be empty.")

            keywords = [variable]
            if location:
                keywords.append(f"{variable} {location}")
            if timeframe:
                keywords.append(f"{variable} {timeframe}")
                if location:
                    keywords.append(f"{variable} {location} {timeframe}")

            keywords.extend([
                f"{variable} data", f"{variable} trends", f"{variable} anomaly",
                f"{variable} climate", f"{variable} weather"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated NOAA Climate keywords for variable '{variable}', location '{location or 'all'}', timeframe '{timeframe or 'all'}'.",
                "search_type": "climate_data",
                "parameters": {"variable": variable, "location": location, "timeframe": timeframe}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate NOAA Climate keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_global_forest_watch_keywords(self, monitoring_type: str, country: str = None, forest_type: str = None) -> str:
        """Generates search keywords for Global Forest Watch."""
        logger.info(f"Generating Global Forest Watch keywords for monitoring_type: '{monitoring_type}', country: '{country}', forest_type: '{forest_type}'")
        try:
            if not monitoring_type.strip():
                raise ValueError("Monitoring type cannot be empty.")

            keywords = [monitoring_type]
            if country:
                keywords.append(f"{monitoring_type} {country}")
            if forest_type:
                keywords.append(f"{monitoring_type} {forest_type}")
                if country:
                    keywords.append(f"{monitoring_type} {country} {forest_type}")

            keywords.extend([
                f"{monitoring_type} alerts", f"{monitoring_type} monitoring", f"{monitoring_type} analysis",
                f"forest {monitoring_type}", f"{monitoring_type} detection"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Global Forest Watch keywords for monitoring_type '{monitoring_type}', country '{country or 'all'}', forest_type '{forest_type or 'all'}'.",
                "search_type": "forest_monitoring",
                "parameters": {"monitoring_type": monitoring_type, "country": country, "forest_type": forest_type}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Global Forest Watch keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_unep_publications_keywords(self, topic: str, theme: str = None, region: str = None) -> str:
        """Generates search keywords for UNEP Publications."""
        logger.info(f"Generating UNEP Publications keywords for topic: '{topic}', theme: '{theme}', region: '{region}'")
        try:
            if not topic.strip():
                raise ValueError("Topic cannot be empty.")

            keywords = [topic]
            if theme:
                keywords.append(f"{topic} {theme}")
            if region:
                keywords.append(f"{topic} {region}")
                if theme:
                    keywords.append(f"{topic} {theme} {region}")

            keywords.extend([
                f"{topic} report", f"{topic} assessment", f"{topic} policy",
                f"{topic} sustainable", f"{topic} environment"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated UNEP Publications keywords for topic '{topic}', theme '{theme or 'general'}', region '{region or 'global'}'.",
                "search_type": "environmental_publications",
                "parameters": {"topic": topic, "theme": theme, "region": region}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate UNEP Publications keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_environmental_keywords(self, concept: str, field: str = None) -> str:
        """Generates search keywords for Wikipedia environmental topics."""
        logger.info(f"Generating Wikipedia environmental keywords for concept: '{concept}', field: '{field}'")
        try:
            if not concept.strip():
                raise ValueError("Concept cannot be empty.")

            keywords = [concept]
            if field:
                keywords.append(f"{concept} {field}")

            keywords.extend([
                f"{concept} environmental", f"{concept} ecology", f"{concept} science",
                f"{concept} definition", f"{concept} impact"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia environmental keywords for concept '{concept}' and field '{field or 'general'}'.",
                "search_type": "environmental_concepts",
                "parameters": {"concept": concept, "field": field}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia environmental keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    # == Recent/Trending Keywords Functions ==

    def generate_nasa_earthdata_recent_keywords(self, data_type: str = None, days_back: int = 7) -> str:
        """Generates keywords for recent satellite data on NASA EarthData."""
        logger.info(f"Generating NASA EarthData recent keywords for data_type: '{data_type}', days_back: {days_back}")
        try:
            keywords = ["recent satellite data", "latest earth observation", "new remote sensing"]
            if data_type:
                keywords.extend([f"recent {data_type}", f"latest {data_type}", f"new {data_type}"])
            keywords.extend([
                "recent environmental data", "latest satellite imagery", "new earth data",
                f"last {days_back} days", "recent acquisitions"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated NASA EarthData recent keywords for data_type '{data_type or 'all'}' within last {days_back} days.",
                "search_type": "recent_satellite_data",
                "parameters": {"data_type": data_type, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate NASA EarthData recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_noaa_climate_recent_keywords(self, region: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent climate data on NOAA."""
        logger.info(f"Generating NOAA Climate recent keywords for region: '{region}', days_back: {days_back}")
        try:
            keywords = ["recent climate data", "latest weather patterns", "new climate observations"]
            if region:
                keywords.extend([f"recent {region}", f"latest {region}", f"{region} climate"])
            keywords.extend([
                "recent temperature data", "latest precipitation", "new climate records",
                f"last {days_back} days", "recent climate updates"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated NOAA Climate recent keywords for region '{region or 'all'}' within last {days_back} days.",
                "search_type": "recent_climate_data",
                "parameters": {"region": region, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate NOAA Climate recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_global_forest_watch_recent_keywords(self, country: str = None, days_back: int = 14) -> str:
        """Generates keywords for recent forest monitoring data."""
        logger.info(f"Generating Global Forest Watch recent keywords for country: '{country}', days_back: {days_back}")
        try:
            keywords = ["recent deforestation alerts", "latest forest loss", "new forest monitoring"]
            if country:
                keywords.extend([f"recent {country}", f"latest {country}", f"{country} forest"])
            keywords.extend([
                "recent forest changes", "latest tree loss", "new forest alerts",
                f"last {days_back} days", "recent forest updates"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Global Forest Watch recent keywords for country '{country or 'all'}' within last {days_back} days.",
                "search_type": "recent_forest_data",
                "parameters": {"country": country, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Global Forest Watch recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_unep_publications_recent_keywords(self, theme: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent UNEP publications."""
        logger.info(f"Generating UNEP Publications recent keywords for theme: '{theme}', days_back: {days_back}")
        try:
            keywords = ["recent environmental reports", "latest sustainability publications", "new UNEP studies"]
            if theme:
                keywords.extend([f"recent {theme}", f"latest {theme}", f"{theme} reports"])
            keywords.extend([
                "recent environmental assessments", "latest policy reports", "new environmental studies",
                f"last {days_back} days", "recent publications"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated UNEP Publications recent keywords for theme '{theme or 'all'}' within last {days_back} days.",
                "search_type": "recent_environmental_publications",
                "parameters": {"theme": theme, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate UNEP Publications recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_environmental_recent_keywords(self, days_back: int = 30, language: str = "en") -> str:
        """Generates keywords for recent environmental articles on Wikipedia."""
        logger.info(f"Generating Wikipedia environmental recent keywords for days_back: {days_back}, language: '{language}'")
        try:
            keywords = [
                "recent environmental articles", "new climate science", "latest environmental research",
                "recent sustainability topics", "new environmental policies", "latest conservation efforts",
                f"last {days_back} days environmental", "recent environmental updates"
            ]

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia environmental recent keywords within last {days_back} days for language '{language}'.",
                "search_type": "recent_environmental_articles",
                "parameters": {"days_back": days_back, "language": language}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia environmental recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)