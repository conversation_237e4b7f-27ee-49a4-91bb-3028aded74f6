from typing import Dict, Any, Optional, List, Tuple
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger, log_warning
import aiohttp
import asyncio
from datetime import datetime, timedelta
from urllib.parse import urlencode
import json

# Cache kết quả tìm kiếm trong 1 giờ
SEARCH_CACHE = {}
CACHE_EXPIRY = 3600  # 1 giây

class CacheEntry:
    def __init__(self, data: Any):
        self.data = data
        self.timestamp = datetime.now()
    
    def is_expired(self) -> bool:
        return (datetime.now() - self.timestamp).total_seconds() > CACHE_EXPIRY

def cache_key(query: str, country: Optional[str], year: Optional[str], limit: int) -> str:
    """Tạo khóa cache duy nhất cho mỗi yêu cầu tìm kiếm"""
    return f"{query}:{country or 'all'}:{year or 'all'}:{limit}"

class AgriOpenDataTools(Toolkit):
    """
    Agri Open Data Tool cho tìm kiếm dữ liệu mở về nông nghiệp, s<PERSON><PERSON> xu<PERSON>, c<PERSON>y trồng, chăn nuôi, công nghệ nông nghiệp.
    Hỗ trợ caching, retry tự động và xử lý bất đồng bộ để tối ưu hiệu suất.
    """

    def __init__(self, **kwargs):
        super().__init__(
            name="agri_open_data_tools",
        )
        self.register(self.search_agri_open_data)
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=15, connect=5)
        self.retry_attempts = 3
        self.retry_delay = 1  # giây

    async def _get_session(self) -> aiohttp.ClientSession:
        """Tạo hoặc trả về session hiện có"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self.timeout)
        return self.session

    async def _make_request(self, url: str, params: Dict[str, Any], attempt: int = 1) -> Dict[str, Any]:
        """Thực hiện HTTP request với cơ chế retry"""
        session = await self._get_session()
        
        try:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    return {"status": "success", "data": await response.json()}
                elif response.status == 429 and attempt <= self.retry_attempts:  # Too Many Requests
                    retry_after = int(response.headers.get('Retry-After', self.retry_delay))
                    await asyncio.sleep(retry_after)
                    return await self._make_request(url, params, attempt + 1)
                else:
                    return {"status": "error", "message": f"HTTP {response.status}"}
        except asyncio.TimeoutError:
            if attempt <= self.retry_attempts:
                await asyncio.sleep(self.retry_delay * attempt)
                return await self._make_request(url, params, attempt + 1)
            return {"status": "error", "message": "Request timeout after multiple retries"}
        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def search_agri_open_data(
        self,
        query: str,
        country: Optional[str] = None,
        year: Optional[str] = None,
        limit: int = 10
    ) -> str:
        """
        Tìm kiếm dữ liệu mở về nông nghiệp với hiệu suất cao.

        Parameters:
        - query: Từ khóa về chủ đề nông nghiệp (ví dụ: 'rice yield', 'precision agriculture', 'organic farming', 'crop disease')
        - country: Tên quốc gia hoặc mã ISO (ví dụ: 'Vietnam', 'USA')
        - year: Năm hoặc khoảng năm (ví dụ: '2020', '2015-2022')
        - limit: Số lượng kết quả tối đa (default: 10, tối đa 100)

        Returns:
        - JSON string chứa kết quả tìm kiếm hoặc thông báo lỗi
        """
        # Kiểm tra cache trước
        cache_key_str = cache_key(query, country, year, limit)
        if cache_key_str in SEARCH_CACHE and not SEARCH_CACHE[cache_key_str].is_expired():
            logger.info(f"Lấy kết quả từ cache cho: {query}")
            return json.dumps(SEARCH_CACHE[cache_key_str].data)

        logger.info(f"Tìm kiếm Agri Open Data: query={query}, country={country}, year={year}, limit={limit}")

        try:
            # Sử dụng CKAN API của agriopendata.org
            api_url = "https://agriopendata.org/api/3/action/package_search"
            params = {
                "q": f"{query} {country or ''} {year or ''}".strip(),
                "rows": min(limit, 100)
            }

            result = await self._make_request(api_url, params)
            
            if result["status"] != "success":
                return json.dumps({"status": "error", "message": "Không thể lấy dữ liệu từ Agri Open Data"})

            # Lưu vào cache
            response_data = {
                "status": "success",
                "query": query,
                "country": country,
                "year": year,
                "results": result.get("data", {}).get("results", [])
            }
            SEARCH_CACHE[cache_key_str] = CacheEntry(response_data)
            
            return json.dumps(response_data)

        except Exception as e:
            error_msg = f"Lỗi khi tìm kiếm dữ liệu: {str(e)}"
            logger.error(error_msg)
            return json.dumps({"status": "error", "message": error_msg})

# Tạo instance của tool để import
agri_open_data_tool = AgriOpenDataTools()