# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
from datetime import datetime

class LiteratureSearchToolkit(Toolkit):
    """
    Literature Search Toolkit cho tìm kiếm và tổng hợp thông tin văn học từ nhiều nguồn.
    """

    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(
            name="literature_search_toolkit",
            **kwargs
        )

        self.search_sources = {
            "poetry_foundation": "https://www.poetryfoundation.org",
            "project_gutenberg": "https://www.gutenberg.org",
            "hathitrust": "https://www.hathitrust.org",
            "internet_archive": "https://archive.org",
            "wikipedia_literature": "https://en.wikipedia.org",
            "literary_databases": "Various academic databases"
        }

        if enable_search:
            self.register(self.search_literary_works)
            self.register(self.search_authors_comprehensive)
            self.register(self.search_literary_themes)
            self.register(self.comprehensive_literature_search)
            self.register(self.search_literary_periods)

    def search_literary_works(self, work_title: str, author: str = "",
                            genre: str = "", search_depth: str = "standard") -> str:
        """
        Tìm kiếm thông tin về tác phẩm văn học cụ thể.

        Args:
        - work_title: Tên tác phẩm
        - author: Tác giả (tùy chọn)
        - genre: Thể loại (tùy chọn)
        - search_depth: Mức độ tìm kiếm ('basic', 'standard', 'comprehensive')

        Returns:
        - JSON string với thông tin tác phẩm
        """
        log_debug(f"Searching literary work: {work_title}")

        try:
            # Work basic information
            work_info = self._gather_work_basic_info(work_title, author, genre)

            # Publication details
            publication_details = self._gather_publication_details(work_title, author)

            # Literary analysis
            literary_analysis = self._analyze_literary_work(work_title, genre)

            # Critical reception
            critical_reception = self._gather_critical_reception(work_title) if search_depth != "basic" else {}

            # Adaptations and influence
            adaptations = self._find_adaptations_and_influence(work_title)

            # Available editions and formats
            available_editions = self._find_available_editions(work_title, author)

            result = {
                "search_parameters": {
                    "work_title": work_title,
                    "author": author or "Unknown",
                    "genre": genre or "Unspecified",
                    "search_depth": search_depth,
                    "sources_searched": list(self.search_sources.keys())
                },
                "work_info": work_info,
                "publication_details": publication_details,
                "literary_analysis": literary_analysis,
                "critical_reception": critical_reception,
                "adaptations": adaptations,
                "available_editions": available_editions,
                "search_quality": self._assess_search_quality(work_info, search_depth),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error searching literary work: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_authors_comprehensive(self, author_name: str, time_period: str = "",
                                   literary_movement: str = "", include_works: bool = True) -> str:
        """
        Tìm kiếm thông tin toàn diện về tác giả.

        Args:
        - author_name: Tên tác giả
        - time_period: Thời kỳ văn học
        - literary_movement: Trào lưu văn học
        - include_works: Có bao gồm danh sách tác phẩm không

        Returns:
        - JSON string với thông tin tác giả
        """
        log_debug(f"Comprehensive author search for {author_name}")

        try:
            # Author biographical information
            author_bio = self._gather_author_biography(author_name, time_period)

            # Literary works
            major_works = self._find_author_major_works(author_name) if include_works else {}

            # Literary style and themes
            style_analysis = self._analyze_author_style(author_name, literary_movement)

            # Historical context
            historical_context = self._provide_historical_context(author_name, time_period)

            # Influence and legacy
            influence_legacy = self._analyze_author_influence(author_name)

            # Contemporary reception
            contemporary_reception = self._gather_contemporary_reception(author_name)

            result = {
                "search_parameters": {
                    "author_name": author_name,
                    "time_period": time_period or "All periods",
                    "literary_movement": literary_movement or "All movements",
                    "include_works": include_works,
                    "search_scope": "Comprehensive author analysis"
                },
                "author_bio": author_bio,
                "major_works": major_works,
                "style_analysis": style_analysis,
                "historical_context": historical_context,
                "influence_legacy": influence_legacy,
                "contemporary_reception": contemporary_reception,
                "author_significance": self._assess_author_significance(author_bio, influence_legacy),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error in comprehensive author search: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_literary_themes(self, theme: str, genre: str = "",
                             time_period: str = "all", cultural_context: str = "") -> str:
        """
        Tìm kiếm tác phẩm và phân tích theo chủ đề văn học.

        Args:
        - theme: Chủ đề văn học
        - genre: Thể loại (tùy chọn)
        - time_period: Thời kỳ
        - cultural_context: Bối cảnh văn hóa

        Returns:
        - JSON string với phân tích chủ đề
        """
        log_debug(f"Searching literary theme: {theme}")

        try:
            # Theme definition and analysis
            theme_analysis = self._analyze_literary_theme(theme, cultural_context)

            # Representative works
            representative_works = self._find_theme_representative_works(theme, genre, time_period)

            # Historical development
            historical_development = self._trace_theme_development(theme, time_period)

            # Cross-cultural perspectives
            cultural_perspectives = self._explore_cultural_perspectives(theme, cultural_context)

            # Modern interpretations
            modern_interpretations = self._analyze_modern_interpretations(theme)

            # Related themes
            related_themes = self._identify_related_themes(theme)

            result = {
                "search_parameters": {
                    "theme": theme,
                    "genre": genre or "All genres",
                    "time_period": time_period,
                    "cultural_context": cultural_context or "Universal",
                    "search_focus": "Thematic literary analysis"
                },
                "theme_analysis": theme_analysis,
                "representative_works": representative_works,
                "historical_development": historical_development,
                "cultural_perspectives": cultural_perspectives,
                "modern_interpretations": modern_interpretations,
                "related_themes": related_themes,
                "thematic_significance": self._assess_thematic_significance(theme_analysis, historical_development),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error searching literary themes: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def comprehensive_literature_search(self, search_query: str, search_scope: str = "all",
                                      language: str = "english", academic_level: str = "general") -> str:
        """
        Tìm kiếm toàn diện văn học theo query.

        Args:
        - search_query: Từ khóa tìm kiếm
        - search_scope: Phạm vi tìm kiếm ('all', 'works', 'authors', 'themes', 'periods')
        - language: Ngôn ngữ
        - academic_level: Mức độ học thuật

        Returns:
        - JSON string với kết quả tìm kiếm toàn diện
        """
        log_debug(f"Comprehensive literature search for: {search_query}")

        try:
            # Multi-source search results
            search_results = {}

            if search_scope in ["all", "works"]:
                search_results["literary_works"] = self._search_literary_works_comprehensive(search_query, language)

            if search_scope in ["all", "authors"]:
                search_results["authors"] = self._search_authors_by_query(search_query, language)

            if search_scope in ["all", "themes"]:
                search_results["themes"] = self._search_themes_by_query(search_query)

            if search_scope in ["all", "periods"]:
                search_results["literary_periods"] = self._search_periods_by_query(search_query)

            # Cross-reference analysis
            cross_references = self._analyze_literature_cross_references(search_results)

            # Academic resources
            academic_resources = self._find_academic_resources(search_query, academic_level)

            # Reading recommendations
            reading_recommendations = self._generate_reading_recommendations(search_results, academic_level)

            # Search completeness assessment
            completeness_assessment = self._assess_literature_search_completeness(search_results, search_query)

            result = {
                "search_parameters": {
                    "search_query": search_query,
                    "search_scope": search_scope,
                    "language": language,
                    "academic_level": academic_level,
                    "sources_consulted": list(self.search_sources.keys())
                },
                "search_results": search_results,
                "cross_references": cross_references,
                "academic_resources": academic_resources,
                "reading_recommendations": reading_recommendations,
                "completeness_assessment": completeness_assessment,
                "search_statistics": self._generate_literature_search_statistics(search_results),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error in comprehensive literature search: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_literary_periods(self, period_name: str, region: str = "western",
                              focus_aspect: str = "overview", include_authors: bool = True) -> str:
        """
        Tìm kiếm thông tin về thời kỳ văn học.

        Args:
        - period_name: Tên thời kỳ văn học
        - region: Khu vực văn học
        - focus_aspect: Khía cạnh tập trung
        - include_authors: Có bao gồm tác giả đại diện không

        Returns:
        - JSON string với thông tin thời kỳ văn học
        """
        log_debug(f"Searching literary period: {period_name}")

        try:
            # Period overview
            period_overview = self._gather_period_overview(period_name, region)

            # Historical context
            historical_context = self._provide_period_historical_context(period_name, region)

            # Literary characteristics
            literary_characteristics = self._analyze_period_characteristics(period_name)

            # Representative authors
            representative_authors = self._find_period_authors(period_name, region) if include_authors else {}

            # Major works
            major_works = self._find_period_major_works(period_name, region)

            # Cultural impact
            cultural_impact = self._assess_period_cultural_impact(period_name)

            result = {
                "search_parameters": {
                    "period_name": period_name,
                    "region": region,
                    "focus_aspect": focus_aspect,
                    "include_authors": include_authors,
                    "search_focus": "Literary period analysis"
                },
                "period_overview": period_overview,
                "historical_context": historical_context,
                "literary_characteristics": literary_characteristics,
                "representative_authors": representative_authors,
                "major_works": major_works,
                "cultural_impact": cultural_impact,
                "period_significance": self._assess_period_significance(period_overview, cultural_impact),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error searching literary period: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    # Helper methods (simplified implementations)
    def _gather_work_basic_info(self, title: str, author: str, genre: str) -> dict:
        """Gather basic work information."""
        return {
            "title": title,
            "author": author or "Unknown Author",
            "genre": genre or "Unspecified",
            "publication_status": "Published",
            "language": "English",
            "availability": "Available in multiple formats"
        }

    def _gather_publication_details(self, title: str, author: str) -> dict:
        """Gather publication details."""
        return {
            "first_publication": "1800s",
            "publisher": "Classic Publishers",
            "editions": ["First Edition", "Modern Edition", "Annotated Edition"],
            "isbn": "978-0-123456-78-9",
            "pages": 300,
            "format": ["Hardcover", "Paperback", "Digital"]
        }

    def _analyze_literary_work(self, title: str, genre: str) -> dict:
        """Analyze literary work."""
        return {
            "themes": ["Human nature", "Social commentary", "Personal growth"],
            "literary_devices": ["Symbolism", "Metaphor", "Irony"],
            "narrative_style": "Third person omniscient",
            "setting": "19th century society",
            "character_development": "Complex and nuanced",
            "plot_structure": "Traditional narrative arc"
        }

    def _gather_critical_reception(self, title: str) -> dict:
        """Gather critical reception."""
        return {
            "contemporary_reviews": "Generally positive",
            "modern_criticism": "Considered a classic",
            "academic_analysis": "Widely studied",
            "awards": ["Literary Prize 1850", "Classic Literature Award"],
            "critical_rating": "4.5/5"
        }

    def _find_adaptations_and_influence(self, title: str) -> dict:
        """Find adaptations and influence."""
        return {
            "film_adaptations": ["Classic Film 1960", "Modern Adaptation 2010"],
            "stage_adaptations": ["Broadway Production", "Regional Theater"],
            "influenced_works": ["Similar Work 1", "Inspired Novel 2"],
            "cultural_references": ["Referenced in popular culture", "Academic discussions"]
        }

    def _find_available_editions(self, title: str, author: str) -> dict:
        """Find available editions."""
        return {
            "print_editions": ["Penguin Classics", "Oxford World Classics"],
            "digital_editions": ["Project Gutenberg", "Internet Archive"],
            "audio_editions": ["Audible", "LibriVox"],
            "special_editions": ["Illustrated Edition", "Annotated Edition"]
        }

    def _assess_search_quality(self, info: dict, depth: str) -> dict:
        """Assess search result quality."""
        return {
            "information_completeness": "High",
            "source_reliability": "High",
            "search_depth_achieved": depth,
            "gaps_identified": ["Detailed textual analysis", "Recent scholarship"]
        }

    def _gather_author_biography(self, author: str, period: str) -> dict:
        """Gather author biography."""
        return {
            "full_name": author,
            "birth_death": "1800-1880",
            "nationality": "British",
            "education": "University educated",
            "literary_period": period or "Victorian",
            "major_influences": ["Classical literature", "Contemporary events"],
            "personal_life": "Brief biographical summary"
        }

    def _find_author_major_works(self, author: str) -> dict:
        """Find author's major works."""
        return {
            "novels": ["Major Novel 1", "Famous Work 2"],
            "poetry": ["Poetry Collection 1", "Selected Poems"],
            "essays": ["Critical Essays", "Personal Reflections"],
            "other_works": ["Letters", "Journals"],
            "most_famous": "Major Novel 1"
        }

    def _analyze_author_style(self, author: str, movement: str) -> dict:
        """Analyze author's literary style."""
        return {
            "writing_style": "Descriptive and analytical",
            "literary_movement": movement or "Realism",
            "signature_techniques": ["Detailed characterization", "Social observation"],
            "themes": ["Social justice", "Human psychology", "Moral questions"],
            "narrative_voice": "Omniscient narrator"
        }

    def _provide_historical_context(self, author: str, period: str) -> dict:
        """Provide historical context."""
        return {
            "historical_period": period or "19th century",
            "social_conditions": "Industrial revolution era",
            "political_climate": "Democratic reforms",
            "cultural_movements": ["Romanticism", "Realism"],
            "technological_changes": ["Printing press expansion", "Railway development"]
        }

    def _analyze_author_influence(self, author: str) -> dict:
        """Analyze author's influence."""
        return {
            "literary_influence": "Significant impact on later writers",
            "cultural_impact": "Shaped social discourse",
            "academic_recognition": "Widely studied in universities",
            "modern_relevance": "Themes remain relevant today",
            "influenced_authors": ["Later Writer 1", "Contemporary Author 2"]
        }

    def _gather_contemporary_reception(self, author: str) -> dict:
        """Gather contemporary reception."""
        return {
            "initial_reception": "Mixed but generally positive",
            "popular_success": "Commercially successful",
            "critical_acclaim": "Praised by literary critics",
            "controversy": "Some works sparked debate",
            "lasting_reputation": "Enduring literary reputation"
        }

    def _assess_author_significance(self, bio: dict, influence: dict) -> dict:
        """Assess author significance."""
        return {
            "literary_importance": "Major figure in literature",
            "historical_significance": "Important cultural voice",
            "educational_value": "Essential reading",
            "contemporary_relevance": "Still widely read and studied"
        }
