import json
import logging
from textwrap import dedent
from agno.tools import Toolkit
from typing import Optional
import random

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WriterYoutubeTools(Toolkit):
    """Toolkit for generating detailed YouTube video scripts with strong hooks, storytelling, and engagement."""
    def __init__(self, min_duration_min: int = 20):
        super().__init__(name="writer_youtube")
        self.description = "Toolkit for generating full-length, engaging YouTube video scripts (20+ minutes) with hooks, storytelling, and audience retention techniques."
        self.min_duration_min = min_duration_min
        self.register(self.generate_script, name="generate_script")
        self.register(self.generate_hook, name="generate_hook")
        self.register(self.generate_section, name="generate_section")
        self.register(self.suggest_title, name="suggest_title")
        self.register(self.suggest_tags, name="suggest_tags")
        self.register(self.suggest_thumbnail_ideas, name="suggest_thumbnail_ideas")
        self.register(self.generate_chapter_markers, name="generate_chapter_markers")
        self.register(self.generate_engagement_questions, name="generate_engagement_questions")
        self.register(self.generate_viral_title, name="generate_viral_title")
        self.register(self.generate_debate_points, name="generate_debate_points")
        self.register(self.generate_interview_questions, name="generate_interview_questions")
        self.register(self.generate_reaction_script, name="generate_reaction_script")
        self.register(self.generate_tutorial_steps, name="generate_tutorial_steps")
        self.register(self.generate_challenge_ideas, name="generate_challenge_ideas")
        self.register(self.generate_trend_script, name="generate_trend_script")
        self.register(self.generate_parody_script, name="generate_parody_script")
        self.register(self.generate_poetic_script, name="generate_poetic_script")
        self.register(self.generate_minimalist_script, name="generate_minimalist_script")
        self.register(self.generate_documentary_outline, name="generate_documentary_outline")
        self.register(self.generate_cinematic_script, name="generate_cinematic_script")
        self.register(self.generate_emotional_script, name="generate_emotional_script")
        self.register(self.generate_shocking_script, name="generate_shocking_script")
        self.register(self.generate_educational_script, name="generate_educational_script")
        self.few_shot_examples = dedent("""
            ## VÍ DỤ KỊCH BẢN YOUTUBE ĐẦY ĐỦ:
            
            ### 1. Hook mạnh đầu video:
            "Bạn có biết, chỉ 1% người làm điều này đã thay đổi hoàn toàn cuộc sống? Hãy xem đến cuối để khám phá bí mật!"
            
            ### 2. Dẫn chuyện:
            "Chào mừng các bạn đến với kênh... Hôm nay chúng ta sẽ cùng tìm hiểu về..."
            
            ### 3. Phát triển nội dung:
            - Chia nhỏ thành 5-7 phần, mỗi phần 2-4 phút, có chuyển cảnh, ví dụ, câu hỏi tương tác.
            
            ### 4. Call-to-action:
            "Nếu bạn thấy nội dung này hữu ích, đừng quên like, share và subscribe để ủng hộ kênh nhé!"
            
            ### 5. Kết thúc ấn tượng:
            "Cảm ơn bạn đã xem đến cuối. Đừng bỏ lỡ video tiếp theo với chủ đề cực hot!"
            
            ### 6. Debate:
            "Chủ đề: AI thay thế con người?\n- Phe 1: AI giúp tăng năng suất.\n- Phe 2: AI gây thất nghiệp."
            
            ### 7. Interview:
            "Câu hỏi cho khách mời: Điều gì truyền cảm hứng cho bạn trong lĩnh vực này?"
            
            ### 8. Reaction:
            "Phản ứng đầu tiên khi xem video: Không thể tin nổi!"
            
            ### 9. Tutorial:
            "Bước 1: Chuẩn bị nguyên liệu.\nBước 2: Thực hiện từng bước theo hướng dẫn."
            
            ### 10. Challenge:
            "Thử thách 24h không dùng mạng xã hội. Ai dám thử?"
            
            ### 11. Trend:
            "Bắt trend: Biến hình trong 3 giây!"
            
            ### 12. Parody:
            "Chế lời bài hát nổi tiếng thành chủ đề học tập."
            
            ### 13. Poetic:
            "Mỗi ngày là một vần thơ, mỗi khoảnh khắc là một cảm xúc."
            
            ### 14. Minimalist:
            "Chỉ cần một ý tưởng, một hình ảnh, một thông điệp."
            
            ### 15. Documentary:
            "Khám phá lịch sử qua từng giai đoạn, từng nhân vật."
            
            ### 16. Cinematic:
            "Cảnh quay chậm, nhạc nền sâu lắng, lời dẫn truyền cảm."
            
            ### 17. Emotional:
            "Chia sẻ câu chuyện khiến mình rơi nước mắt."
            
            ### 18. Shocking:
            "Sự thật gây sốc: 90% người không biết điều này!"
            
            ### 19. Educational:
            "Giải thích khái niệm phức tạp bằng ví dụ đơn giản."
        """)

    def generate_hook(self, topic: str, style: Optional[str] = None) -> str:
        """Generate a strong hook for the start of a YouTube video."""
        hooks = [
            f"Bạn sẽ bất ngờ với sự thật về '{topic}'!", 
            f"Chỉ 1% người biết điều này về '{topic}'. Bạn có nằm trong số đó?", 
            f"Đừng bỏ lỡ! {topic} có thể thay đổi cuộc sống bạn.",
            f"Bạn đã từng tự hỏi tại sao... {topic}? Hãy xem đến cuối để biết câu trả lời!",
            f"Nếu bạn từng gặp khó khăn với '{topic}', video này là dành cho bạn!"
        ]
        if style == "giật tít":
            hooks.append(f"Sốc: {topic.upper()} và sự thật phía sau khiến ai cũng bất ngờ! 😱")
        if style == "mysterious":
            hooks.append(f"Ẩn số đằng sau '{topic}' là gì? Đừng rời mắt khỏi màn hình!")
            hooks.append(f"Có một bí ẩn về '{topic}' mà ít ai biết...")
        if style == "suspenseful":
            hooks.append(f"Chuyện gì sẽ xảy ra nếu... {topic}? Hãy theo dõi đến phút cuối!")
            hooks.append(f"Một sự kiện bất ngờ liên quan đến '{topic}' sắp được tiết lộ!")
        if style == "dramatic":
            hooks.append(f"Khoảnh khắc định mệnh: {topic} đã thay đổi tất cả!")
            hooks.append(f"Câu chuyện đầy cảm xúc về '{topic}' sẽ khiến bạn không thể rời mắt!")
        return json.dumps({"hook": random.choice(hooks), "topic": topic, "style": style})

    def generate_section(self, section_title: str, main_points: list, duration_min: int = 3, style: Optional[str] = None) -> str:
        """Generate a detailed section for a YouTube script."""
        content = f"---\n{section_title.upper()} ({duration_min} phút)\n"
        for idx, point in enumerate(main_points, 1):
            content += f"{idx}. {point}\n"
        if style == "story":
            content += "\nKể một câu chuyện thực tế hoặc ví dụ minh họa để tăng tính hấp dẫn."
        elif style == "giải trí":
            content += "\nThêm yếu tố hài hước, meme, hoặc câu hỏi vui cho khán giả."
        elif style == "học thuật":
            content += "\nTrích dẫn nghiên cứu, số liệu, hoặc chuyên gia uy tín."
        elif style == "mysterious":
            content += "\nGợi mở bí ẩn, đặt câu hỏi chưa có lời giải, tạo cảm giác tò mò."
        elif style == "suspenseful":
            content += "\nTạo cao trào, dừng lại ở điểm gay cấn, khuyến khích người xem đoán tiếp."
        elif style == "dramatic":
            content += "\nNhấn mạnh cảm xúc, mô tả tình huống kịch tính, dùng ngôn từ mạnh mẽ."
        return json.dumps({"section": content, "section_title": section_title, "main_points": main_points, "duration_min": duration_min, "style": style})

    def generate_script(self, topic: str, outline: Optional[list] = None, style: Optional[str] = None, cta: Optional[str] = None, viral: bool = True, retention: bool = True, genz: bool = False, entertaining: bool = False, academic: bool = False, mysterious: bool = False, suspenseful: bool = False, dramatic: bool = False) -> str:
        """Generate a full YouTube video script (20+ min) with viral/retention logic, hooks, sections, CTA, outro, and style options."""
        # 1. Viral Hook
        hook = json.loads(self.generate_hook(topic, style))["hook"]
        if viral:
            viral_hooks = [
                f"Bạn sẽ không tin điều số 5!", f"Câu chuyện này sẽ thay đổi cách bạn nghĩ về {topic}.",
                f"Ở cuối video có phần quà bất ngờ cho bạn!", f"Chỉ 1% người xem hết video này! Bạn có nằm trong số đó?"
            ]
            hook = random.choice(viral_hooks) + " " + hook
        # 2. Intro
        intro = f"Chào mừng các bạn đến với kênh! Hôm nay chúng ta sẽ cùng khám phá: {topic}."
        if genz:
            intro += " Đừng quên thả like và comment 'tới công chuyện' nếu bạn thấy hay nhé! 😎"
        if mysterious:
            intro += " Video này sẽ hé lộ những bí ẩn chưa từng được tiết lộ!"
        if suspenseful:
            intro += " Hãy theo dõi đến cuối để không bỏ lỡ khoảnh khắc gay cấn nhất!"
        if dramatic:
            intro += " Câu chuyện hôm nay sẽ khiến bạn xúc động mạnh!"
        # 3. Outline/Sections
        if not outline:
            outline = [
                "Giới thiệu vấn đề", "Bối cảnh & lý do quan trọng", "Các khái niệm chính", "Phân tích chuyên sâu", "Ví dụ thực tế & câu chuyện", "Sai lầm thường gặp", "Giải pháp & lời khuyên", "Kết luận & tổng kết"
            ]
        total_sections = max(len(outline), 6)
        min_sec = max(2, self.min_duration_min // total_sections)
        script_sections = []
        for idx, section in enumerate(outline):
            points = [f"Ý chính {i+1} của {section}" for i in range(2)]
            section_style = style
            if mysterious:
                section_style = "mysterious"
            elif suspenseful:
                section_style = "suspenseful"
            elif dramatic:
                section_style = "dramatic"
            section_text = json.loads(self.generate_section(section, points, duration_min=min_sec, style=section_style))["section"]
            if retention:
                teasers = [
                    "Đừng rời mắt, phần tiếp theo sẽ cực kỳ thú vị!",
                    "Bạn đoán xem điều gì sẽ xảy ra tiếp theo? Comment bên dưới nhé!",
                    "Ở phần sau sẽ có một bí mật lớn được bật mí!",
                    "Bạn đã từng gặp trường hợp này chưa? Hãy chia sẻ trải nghiệm của bạn!"
                ]
                if mysterious:
                    teasers.append("Sắp tới sẽ có một tiết lộ gây sốc về bí ẩn này!")
                if suspenseful:
                    teasers.append("Cao trào đang đến gần, bạn có đoán được điều gì sẽ xảy ra?")
                if dramatic:
                    teasers.append("Khoảnh khắc xúc động nhất sẽ xuất hiện ở phần sau!")
                section_text += "\n" + random.choice(teasers)
            script_sections.append(section_text)
        # 4. CTA
        cta_text = cta or "Nếu bạn thấy video này hữu ích, hãy like, share và subscribe để ủng hộ kênh nhé!"
        if viral:
            cta_text += " Đừng quên bật chuông để không bỏ lỡ video cực hot tiếp theo!"
        # 5. Outro
        outro = "Cảm ơn bạn đã xem đến cuối. Đừng bỏ lỡ video tiếp theo với chủ đề cực hot!"
        if entertaining:
            outro += " Comment chủ đề bạn muốn mình làm video tiếp nhé! 🥳"
        if mysterious:
            outro += " Bí ẩn vẫn còn đó, bạn có đoán ra sự thật không?"
        if suspenseful:
            outro += " Cao trào đã qua, nhưng còn nhiều điều bất ngờ phía trước!"
        if dramatic:
            outro += " Cảm ơn vì đã đồng hành cùng cảm xúc của mình!"
        script = f"HOOK: {hook}\n\n{intro}\n\n" + "\n\n".join(script_sections) + f"\n\nCALL TO ACTION: {cta_text}\n\nOUTRO: {outro}"
        return json.dumps({"script": script, "topic": topic, "outline": outline, "style": style, "min_duration_min": self.min_duration_min, "viral": viral, "retention": retention, "genz": genz, "entertaining": entertaining, "academic": academic, "mysterious": mysterious, "suspenseful": suspenseful, "dramatic": dramatic})

    def generate_chapter_markers(self, outline: list) -> str:
        """Generate YouTube chapter markers for the script outline."""
        chapters = []
        time = 0
        min_per_chap = max(2, self.min_duration_min // max(len(outline), 6))
        for idx, section in enumerate(outline):
            mm = str(time).zfill(2)
            chapters.append(f"{mm}:00 - {section}")
            time += min_per_chap
        return json.dumps({"chapters": chapters, "outline": outline})

    def generate_engagement_questions(self, topic: str, n: int = 3) -> str:
        """Suggest interactive questions to keep viewers engaged throughout the video."""
        questions = [
            f"Bạn nghĩ gì về '{topic}'? Comment bên dưới nhé!",
            f"Bạn đã từng gặp tình huống này chưa? Chia sẻ trải nghiệm của bạn!",
            f"Theo bạn, đâu là giải pháp tốt nhất cho '{topic}'?",
            f"Nếu là bạn, bạn sẽ làm gì trong trường hợp này?",
            f"Bạn có muốn mình làm video về chủ đề nào tiếp theo không?"
        ]
        return json.dumps({"questions": random.sample(questions, min(n, len(questions))), "topic": topic})

    def generate_viral_title(self, topic: str) -> str:
        """Suggest a viral YouTube video title."""
        viral_titles = [
            f"SỐC: {topic.upper()} và sự thật phía sau khiến ai cũng bất ngờ! 😱",
            f"{topic.upper()} - 5 bí mật không ai nói cho bạn!",
            f"Bạn sẽ thay đổi suy nghĩ về {topic} sau video này!",
            f"{topic}: Đừng bỏ lỡ điều số 3!"
        ]
        return json.dumps({"viral_title": random.choice(viral_titles), "topic": topic})

    def generate_debate_points(self, topic: str, sides: int = 2) -> str:
        """Generate main debate points for a topic (multi-side)."""
        points = {f"Side {i+1}": [f"Lập luận {j+1} cho phía {i+1} về {topic}" for j in range(3)] for i in range(sides)}
        return json.dumps({"debate_points": points, "topic": topic, "sides": sides})

    def generate_interview_questions(self, guest: str, topic: str, n: int = 5) -> str:
        """Generate interview questions for a guest on a topic."""
        questions = [f"Câu hỏi {i+1} cho {guest} về {topic}" for i in range(n)]
        return json.dumps({"questions": questions, "guest": guest, "topic": topic})

    def generate_reaction_script(self, video_title: str, context: Optional[str] = None) -> str:
        """Generate a reaction video script for a given video."""
        script = f"Giới thiệu video: {video_title}\n"
        if context:
            script += f"Bối cảnh: {context}\n"
        script += "Phản ứng cá nhân, bình luận hài hước, phân tích cảm xúc, kêu gọi comment cảm nghĩ."
        return json.dumps({"reaction_script": script, "video_title": video_title, "context": context})

    def generate_tutorial_steps(self, topic: str, steps: int = 5) -> str:
        """Generate step-by-step tutorial for a topic."""
        tutorial = [f"Bước {i+1}: Hướng dẫn chi tiết về {topic}" for i in range(steps)]
        return json.dumps({"tutorial_steps": tutorial, "topic": topic})

    def generate_challenge_ideas(self, topic: str, n: int = 3) -> str:
        """Generate challenge video ideas for a topic."""
        ideas = [f"Thử thách {i+1} liên quan đến {topic}" for i in range(n)]
        return json.dumps({"challenge_ideas": ideas, "topic": topic})

    def generate_trend_script(self, trend: str) -> str:
        """Generate a script for a trending topic/video format."""
        script = f"Bắt trend: {trend}\nGiới thiệu, thực hiện trend, bình luận vui nhộn, kêu gọi người xem tham gia."
        return json.dumps({"trend_script": script, "trend": trend})

    def generate_parody_script(self, original: str, parody_topic: str) -> str:
        """Generate a parody script based on an original topic."""
        script = f"Parody {original} với chủ đề {parody_topic}:\nChế lời, thêm yếu tố hài, twist bất ngờ."
        return json.dumps({"parody_script": script, "original": original, "parody_topic": parody_topic})

    def generate_poetic_script(self, topic: str) -> str:
        """Generate a poetic/narrative script for a topic."""
        script = f"{topic} - Một hành trình cảm xúc qua từng vần thơ, hình ảnh, âm nhạc."
        return json.dumps({"poetic_script": script, "topic": topic})

    def generate_minimalist_script(self, topic: str) -> str:
        """Generate a minimalist, concise script for a topic."""
        script = f"{topic}: Chỉ tập trung vào ý chính, hình ảnh tối giản, lời thoại ngắn gọn."
        return json.dumps({"minimalist_script": script, "topic": topic})

    def generate_documentary_outline(self, topic: str) -> str:
        """Generate a documentary-style outline for a topic."""
        outline = [f"Phần {i+1}: Khám phá khía cạnh {i+1} của {topic}" for i in range(5)]
        return json.dumps({"documentary_outline": outline, "topic": topic})

    def generate_cinematic_script(self, topic: str) -> str:
        """Generate a cinematic, visually-driven script for a topic."""
        script = f"{topic}: Mở đầu bằng cảnh quay ấn tượng, chuyển cảnh mượt mà, nhạc nền cảm xúc, lời dẫn truyền cảm."
        return json.dumps({"cinematic_script": script, "topic": topic})

    def generate_emotional_script(self, topic: str) -> str:
        """Generate an emotional, touching script for a topic."""
        script = f"{topic}: Kể lại bằng cảm xúc thật, chia sẻ trải nghiệm cá nhân, khơi gợi sự đồng cảm."
        return json.dumps({"emotional_script": script, "topic": topic})

    def generate_shocking_script(self, topic: str) -> str:
        """Generate a shocking, surprising script for a topic."""
        script = f"{topic}: Hé lộ sự thật gây sốc, twist bất ngờ, giữ bí mật đến phút chót."
        return json.dumps({"shocking_script": script, "topic": topic})

    def generate_educational_script(self, topic: str) -> str:
        """Generate an educational, knowledge-focused script for a topic."""
        script = f"{topic}: Trình bày kiến thức, giải thích khái niệm, ví dụ minh họa, tổng kết bài học."
        return json.dumps({"educational_script": script, "topic": topic})

if __name__ == "__main__":
    toolkit = WriterYoutubeTools()
    print(toolkit.generate_script("Bí quyết học tiếng Anh hiệu quả"))
    print(toolkit.suggest_title("Bí quyết học tiếng Anh hiệu quả"))
    print(toolkit.suggest_tags("Bí quyết học tiếng Anh hiệu quả"))
    print(toolkit.suggest_thumbnail_ideas("Bí quyết học tiếng Anh hiệu quả"))
