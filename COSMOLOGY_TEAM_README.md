# 🌌 Cosmology Facebook Team

Một team nghiên cứu và viết bài Facebook về vũ trụ học sử dụng framework Agno với workflow, memory v2, và nhiều tools chuyên biệt.

## 🎯 Tổng quan

Team Cosmology Facebook được thiết kế để:
- <PERSON><PERSON>ên cứu các chủ đề vũ trụ học từ nhiều nguồn khoa học
- Tạo ra các bài viết Facebook hấp dẫn và dễ hiểu
- Sử dụng các công cụ chuyên biệt để truy cập dữ liệu khoa học
- <PERSON>ối hợp giữa nhiều agent để tạo ra nội dung chất lượng cao

## 🤖 Thành viên Team

### 1. 🔍 Cosmology Sub Question Agent
- **Chức năng**: Tạo ra các câu hỏi phụ dựa trên truy vấn của người dùng
- **Tools**: CosmologySearchToolkit
- **<PERSON><PERSON>ên môn**: <PERSON><PERSON> tích và mở rộng câu hỏi nghiên cứu

### 2. 📚 ArXiv Cosmology Agent  
- **Chức năng**: Tìm kiếm các bài báo khoa học trên ArXiv
- **Tools**: ArXivTools
- **Chuyên môn**: Nghiên cứu các phát triển mới nhất trong vũ trụ học

### 3. ⚛️ CERN Open Data Agent
- **Chức năng**: Truy cập dữ liệu vật lý hạt từ CERN
- **Tools**: CERNOpenDataTools  
- **Chuyên môn**: Dữ liệu thí nghiệm vật lý năng lượng cao

### 4. 🔬 INSPIRE-HEP Agent
- **Chức năng**: Tìm kiếm tài liệu vật lý năng lượng cao
- **Tools**: InspireHEPTools
- **Chuyên môn**: Lý thuyết vũ trụ học và vật lý hạt

### 5. 🚀 NASA ADS Physics Agent
- **Chức năng**: Tìm kiếm các bài báo vật lý thiên văn
- **Tools**: NASAADSPhysicsTools
- **Chuyên môn**: Vũ trụ học quan sát và lý thuyết

### 6. 📖 Wikipedia Physics Agent
- **Chức năng**: Cung cấp giải thích dễ hiểu về các khái niệm
- **Tools**: WikipediaPhysicsTools
- **Chuyên môn**: Làm cho khoa học trở nên dễ tiếp cận

### 7. 🧮 Cosmic Evolution Calculator Agent
- **Chức năng**: Thực hiện các tính toán vũ trụ học
- **Tools**: CosmicEvolutionCalculator
- **Chuyên môn**: Mô hình hóa và tính toán định lượng

### 8. ✍️ Cosmology Writer Agent
- **Chức năng**: Viết bài Facebook hấp dẫn dựa trên nghiên cứu
- **Tools**: WriterFbTools
- **Chuyên môn**: Tạo nội dung truyền thông khoa học

## 🚀 Cách sử dụng

### 1. Chạy Demo
```bash
python demo_cosmology_team.py
```

### 2. Sử dụng trực tiếp
```python
from cosmology_fb import CosmologyFbWorkflow

# Tạo workflow
workflow = CosmologyFbWorkflow()

# Chạy với câu hỏi
result = workflow.run_workflow("What is dark matter?")
```

### 3. Chạy test
```bash
python test_cosmology_team.py
```

## 🎯 Chủ đề được hỗ trợ

- **Dark Matter & Dark Energy**: Vật chất tối và năng lượng tối
- **Cosmic Microwave Background**: Bức xạ nền vũ trụ
- **Big Bang Theory**: Lý thuyết Big Bang và bằng chứng
- **Gravitational Waves**: Sóng hấp dẫn và khám phá
- **Galaxy Formation**: Hình thành và tiến hóa thiên hà
- **Black Holes**: Lỗ đen và hiện tượng cực đoan
- **Cosmic Inflation**: Lạm phát vũ trụ
- **Quantum Cosmology**: Vũ trụ học lượng tử

## 🛠️ Tính năng nổi bật

### Workflow & Memory
- Sử dụng Agno Workflow để điều phối team
- Memory v2 để lưu trữ và chia sẻ thông tin
- Collaborative mode cho phép agents làm việc cùng nhau

### Tools Integration
- Tích hợp 8 tools chuyên biệt
- Fallback data khi API không khả dụng
- Cache để tối ưu hiệu suất

### Content Creation
- Tạo nội dung Facebook hấp dẫn
- Cân bằng giữa tính khoa học và dễ hiểu
- Hashtags và call-to-action phù hợp

## 📝 Ví dụ câu hỏi

1. "What is dark matter and why is it important in cosmology?"
2. "Explain the cosmic microwave background radiation"
3. "What are gravitational waves and how do they help us understand the universe?"
4. "Tell me about the Big Bang theory and recent evidence"
5. "What is dark energy and how does it affect universe expansion?"

## 🔧 Cấu hình

### Database Storage
- Knowledge Base: `tmp/cosmology_research_agent_storage.db`
- Team Storage: `tmp/cosmology_research_team_storage.db`

### Model
- Sử dụng Ollama với model `qwen3:4b`
- Có thể thay đổi model trong code

### Memory Settings
- `delete_memories=True`: Xóa memory cũ
- `clear_memories=True`: Làm sạch memory

## 🎉 Kết quả mong đợi

Team sẽ tạo ra:
1. **Nghiên cứu toàn diện** từ nhiều nguồn khoa học
2. **Bài viết Facebook** hấp dẫn và dễ hiểu
3. **Thông tin chính xác** về vũ trụ học
4. **Nội dung truyền cảm hứng** về khoa học

## 🚨 Lưu ý

- Cần có Ollama và model `qwen3:4b` được cài đặt
- Một số API có thể cần token (đã có fallback data)
- Quá trình có thể mất thời gian do phải truy vấn nhiều nguồn
- Kết quả phụ thuộc vào chất lượng của model và tools

## 🔄 Phát triển tiếp

- Thêm nhiều tools khoa học khác
- Tích hợp với social media APIs
- Cải thiện quality của generated content
- Thêm support cho nhiều ngôn ngữ
