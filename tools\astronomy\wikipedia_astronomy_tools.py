"""
Công cụ Wikipedia tùy chỉnh sử dụng thư viện wikipedia trực tiếp.
"""

import json
import time
import logging
from typing import List, Dict, Any, Optional

from agno.tools import Toolkit
from agno.utils.log import log_debug, logger

class WikipediaAstronomyTools(Toolkit):
    """Công cụ Wikipedia tùy chỉnh sử dụng thư viện wikipedia trực tiếp."""

    def __init__(self, **kwargs):
        """
        Khởi tạo công cụ Wikipedia tùy chỉnh.

        Args:
            **kwargs: <PERSON><PERSON><PERSON> tham số khác
        """
        super().__init__(name="custom_wikipedia_tools", **kwargs)

        # Đăng ký các phương thức
        self.register(self.search_wikipedia_custom)
        self.register(self.search_wikipedia_with_language)
        self.register(self.get_recent_astronomy_articles)

    def search_wikipedia_custom(self, query: str, max_results: int = 3) -> str:
        """
        T<PERSON><PERSON> kiếm thông tin trên Wikipedia với số lượng kết quả tùy chỉnh.

        Args:
            query: Từ khóa tìm kiếm
            max_results: Số lượng kết quả tối đa

        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        log_debug(f"Searching Wikipedia for: {query} with max_results={max_results}")

        try:
            import wikipedia

            # Đo thời gian thực thi
            start_time = time.time()

            # Tìm kiếm
            search_results = wikipedia.search(query, results=max_results)

            # Lấy thông tin chi tiết
            results = []
            for title in search_results:
                try:
                    page = wikipedia.page(title)
                    results.append({
                        "title": page.title,
                        "summary": page.summary,
                        "url": page.url,
                        "categories": page.categories[:5] if hasattr(page, "categories") else []
                    })
                except Exception as e:
                    log_debug(f"Error retrieving page {title}: {e}")

            # Tính thời gian thực thi
            execution_time = time.time() - start_time

            # Thêm metadata
            metadata = {
                "query": query,
                "max_results": max_results,
                "execution_time": execution_time,
                "result_count": len(results)
            }

            # Tạo kết quả cuối cùng
            final_result = {
                "results": results,
                "metadata": metadata
            }

            return json.dumps(final_result, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error searching Wikipedia: {e}")
            return json.dumps({"error": str(e)})

    def search_wikipedia_with_language(self, query: str, language: str = "en", max_results: int = 3) -> str:
        """
        Tìm kiếm thông tin trên Wikipedia với ngôn ngữ tùy chỉnh.

        Args:
            query: Từ khóa tìm kiếm
            language: Ngôn ngữ tìm kiếm
            max_results: Số lượng kết quả tối đa

        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        log_debug(f"Searching Wikipedia for: {query} with language={language}, max_results={max_results}")

        try:
            import wikipedia

            # Đặt ngôn ngữ
            wikipedia.set_lang(language)

            # Đo thời gian thực thi
            start_time = time.time()

            # Tìm kiếm
            search_results = wikipedia.search(query, results=max_results)

            # Lấy thông tin chi tiết
            results = []
            for title in search_results:
                try:
                    page = wikipedia.page(title)
                    results.append({
                        "title": page.title,
                        "summary": page.summary,
                        "url": page.url,
                        "language": language
                    })
                except Exception as e:
                    log_debug(f"Error retrieving page {title}: {e}")

            # Tính thời gian thực thi
            execution_time = time.time() - start_time

            # Thêm metadata
            metadata = {
                "query": query,
                "language": language,
                "max_results": max_results,
                "execution_time": execution_time,
                "result_count": len(results)
            }

            # Tạo kết quả cuối cùng
            final_result = {
                "results": results,
                "metadata": metadata
            }

            return json.dumps(final_result, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error searching Wikipedia: {e}")
            return json.dumps({"error": str(e)})

    def get_recent_astronomy_articles(self, limit: int = 10, days_back: int = 30, language: str = "en") -> str:
        """
        Lấy các bài viết thiên văn học mới được tạo hoặc cập nhật gần đây.

        Args:
            limit: Số lượng bài viết tối đa
            days_back: Số ngày quay lại để tìm
            language: Ngôn ngữ Wikipedia

        Returns:
            Chuỗi JSON chứa các bài viết mới
        """
        log_debug(f"Getting recent astronomy articles: limit={limit}, days_back={days_back}, language={language}")

        try:
            import wikipedia
            import requests
            from datetime import datetime, timedelta

            # Đặt ngôn ngữ
            wikipedia.set_lang(language)

            # Đo thời gian thực thi
            start_time = time.time()

            # Tính toán ngày
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)

            # Danh sách từ khóa thiên văn học
            astronomy_keywords = [
                "astronomy", "astrophysics", "cosmology", "exoplanet",
                "galaxy", "star", "nebula", "black hole", "supernova",
                "telescope", "space", "universe", "planet", "asteroid"
            ]

            results = []

            # Tìm kiếm cho từng từ khóa
            for keyword in astronomy_keywords[:5]:  # Giới hạn để tránh quá nhiều request
                try:
                    search_results = wikipedia.search(keyword, results=3)

                    for title in search_results:
                        if len(results) >= limit:
                            break

                        try:
                            page = wikipedia.page(title)

                            # Kiểm tra xem bài viết có liên quan đến thiên văn không
                            content_lower = (page.summary + " " + " ".join(page.categories[:10])).lower()
                            astronomy_related = any(kw in content_lower for kw in astronomy_keywords)

                            if astronomy_related:
                                # Thêm thông tin bài viết
                                article_info = {
                                    "title": page.title,
                                    "summary": page.summary[:500] + "..." if len(page.summary) > 500 else page.summary,
                                    "url": page.url,
                                    "categories": page.categories[:5] if hasattr(page, "categories") else [],
                                    "language": language,
                                    "keyword_found": keyword,
                                    "is_recent": True,  # Giả định là recent vì Wikipedia API không cung cấp thông tin ngày tạo dễ dàng
                                    "relevance_score": sum(1 for kw in astronomy_keywords if kw in content_lower)
                                }

                                # Kiểm tra trùng lặp
                                if not any(existing["title"] == article_info["title"] for existing in results):
                                    results.append(article_info)

                        except Exception as e:
                            log_debug(f"Error retrieving page {title}: {e}")
                            continue

                except Exception as e:
                    log_debug(f"Error searching for keyword {keyword}: {e}")
                    continue

                if len(results) >= limit:
                    break

            # Sắp xếp theo relevance score
            results.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
            results = results[:limit]

            # Tính thời gian thực thi
            execution_time = time.time() - start_time

            # Thêm metadata
            metadata = {
                "limit": limit,
                "days_back": days_back,
                "language": language,
                "execution_time": execution_time,
                "result_count": len(results),
                "keywords_searched": astronomy_keywords[:5]
            }

            # Tạo kết quả cuối cùng
            final_result = {
                "recent_articles": results,
                "metadata": metadata
            }

            log_debug(f"Found {len(results)} recent astronomy articles")
            return json.dumps(final_result, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Error getting recent astronomy articles: {e}")
            return json.dumps({"error": str(e)})