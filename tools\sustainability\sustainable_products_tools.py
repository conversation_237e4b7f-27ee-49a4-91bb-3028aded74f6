from typing import Dict, Any, List, Optional, Union
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import random
from datetime import datetime
from urllib.parse import quote

class SustainableProductsTool(Toolkit):
    """
    Công cụ tìm kiếm và đánh giá các sản phẩm bền vững, thân thiện môi trường.
    
    <PERSON><PERSON><PERSON> từ khóa tìm kiếm gợi ý:
    - <PERSON><PERSON> dùng gia đình (eco-friendly home products)
    - Thời trang bền vững (sustainable fashion)
    - <PERSON><PERSON> phẩm tự nhiên (natural cosmetics)
    - <PERSON><PERSON> chơi thân thiện môi trường (eco-friendly toys)
    - <PERSON><PERSON><PERSON> li<PERSON><PERSON> tái chế (recycled materials)
    - Sản phẩm không rác thải (zero waste products)
    - <PERSON><PERSON> dùng cá nhân bền vững (sustainable personal care)
    """
    
    def __init__(self):
        super().__init__(
            name="<PERSON><PERSON><PERSON> cụ tìm sản phẩm bền vững",
            tools=[self.search_sustainable_products, self.get_product_sustainability_score]
        )
        self.categories = [
            "thời trang", "gia dụng", "mỹ phẩm", "thực phẩm", 
            "đồ chơi", "văn phòng phẩm", "đồ điện tử"
        ]
        self.certifications = [
            "USDA Organic", "Fair Trade", "GOTS", "FSC", 
            "Energy Star", "Cradle to Cradle", "B Corp"
        ]
        self.brands = [
            "EcoVibe", "GreenLife", "PureHome", "EcoTools",
            "BambooComfort", "GreenChoice", "EcoWise", "SustainableLiving"
        ]

    def _generate_product(self, query: str, category: str) -> Dict[str, Any]:
        """Tạo thông tin sản phẩm mẫu"""
        product_id = f"PRD{random.randint(10000, 99999)}"
        price = round(random.uniform(5, 200), 2)
        rating = round(random.uniform(3.0, 5.0), 1)
        
        # Tạo tên sản phẩm dựa trên truy vấn và danh mục
        name_parts = {
            "thời trang": ["Áo", "Quần", "Váy", "Mũ", "Túi", "Giày"],
            "gia dụng": ["Bàn chải", "Bình nước", "Túi vải", "Hộp đựng", "Khăn tắm"],
            "mỹ phẩm": ["Son", "Kem dưỡng", "Sữa rửa mặt", "Xà phòng", "Dầu gội"],
            "thực phẩm": ["Trà", "Cà phê", "Ngũ cốc", "Đồ hộp", "Đồ khô"],
            "đồ chơi": ["Xếp hình", "Búp bê", "Xe đồ chơi", "Đồ chơi giáo dục"],
            "văn phòng phẩm": ["Sổ tay", "Bút chì", "Túi đựng tài liệu", "Bìa hồ sơ"],
            "đồ điện tử": ["Pin sạc", "Đèn năng lượng mặt trời", "Sạc dự phòng"]
        }
        
        # Chọn danh mục ngẫu nhiên nếu không khớp
        selected_category = category if category in product_categories else random.choice(list(product_categories.keys()))
        name = f"{random.choice(product_categories[selected_category])} {query.capitalize()} Bền Vững"
        
        # Tạo thông tin bền vững
        sustainability_score = random.randint(60, 100)
        materials = ["sợi tre", "bông hữu cơ", "nhựa tái chế", "gỗ bền vững", "vải lanh"][:random.randint(1, 3)]
        certs = random.sample(self.certifications, random.randint(1, 3))
        
        return {
            "product_id": product_id,
            "name": name,
            "brand": random.choice(self.brands),
            "price": price,
            "currency": "VND" if random.random() > 0.5 else "USD",
            "rating": rating,
            "reviews": random.randint(5, 500),
            "category": selected_category,
            "sustainability_score": sustainability_score,
            "materials": materials,
            "certifications": certs,
            "description": f"Sản phẩm {name.lower()} thân thiện với môi trường, làm từ {', '.join(materials)}.",
            "eco_benefits": [
                f"Giảm {random.randint(20, 80)}% lượng khí thải carbon so với sản phẩm thông thường",
                f"Đóng gói bằng vật liệu phân hủy sinh học",
                f"Sản xuất bằng năng lượng tái tạo"
            ][:random.randint(1, 3)],
            "image_url": f"https://example.com/products/{product_id}.jpg",
            "product_url": f"https://example.com/product/{product_id}",
            "in_stock": random.choice([True, True, True, False])
        }

    def search_sustainable_products(self, query: str, category: str = "", 
                                  min_score: int = 0, max_price: float = 0,
                                  limit: int = 10) -> str:
        """
        Tìm kiếm các sản phẩm bền vững dựa trên tiêu chí.
        
        Args:
            query: Từ khóa tìm kiếm sản phẩm
            category: Danh mục sản phẩm (để trống cho tất cả)
            min_score: Điểm bền vững tối thiểu (0-100)
            max_price: Giá tối đa (0 để bỏ qua)
            limit: Số lượng kết quả trả về (tối đa 20)
            
        Returns:
            Chuỗi JSON chứa danh sách sản phẩm phù hợp
        """
        logger.info(f"Đang tìm kiếm sản phẩm bền vững: {query}")
        
        # Xác thực tham số
        limit = max(1, min(limit, 20))
        min_score = max(0, min(100, min_score))
        max_price = max(0, max_price)
        
        if category and category not in self.categories:
            category = ""
        
        try:
            # Tạo danh sách sản phẩm mẫu
            results = []
            for i in range(limit):
                product = self._generate_product(query, category)
                
                # Lọc theo điểm bền vững và giá
                if product["sustainability_score"] >= min_score and \
                   (max_price == 0 or product["price"] <= max_price):
                    results.append(product)
            
            # Sắp xếp theo điểm bền vững giảm dần
            results.sort(key=lambda x: x["sustainability_score"], reverse=True)
            
            # Tạo phản hồi
            response = {
                "status": "success",
                "query": query,
                "filters": {
                    "category": category if category else "Tất cả",
                    "min_sustainability_score": min_score,
                    "max_price": max_price if max_price > 0 else "Không giới hạn"
                },
                "result_count": len(results),
                "results": results,
                "sustainability_metrics": [
                    "Vật liệu tái chế", "Năng lượng tái tạo", 
                    "Không độc hại", "Đạo đức lao động",
                    "Bao bì bền vững"
                ]
            }
            
            return json.dumps(response, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm sản phẩm: {str(e)}")
            error_response = {
                "status": "error",
                "message": f"Không thể tìm kiếm sản phẩm: {str(e)}",
                "query": query,
                "suggestions": [
                    "Thử tìm kiếm với từ khóa khác",
                    "Kiểm tra lại bộ lọc",
                    "Xem danh mục phổ biến"
                ],
                "popular_categories": self.categories[:5]
            }
            return json.dumps(error_response, ensure_ascii=False, indent=2)
    
    def get_product_sustainability_score(self, product_name: str, brand: str = "") -> str:
        """
        Đánh giá điểm bền vững của sản phẩm.
        
        Args:
            product_name: Tên sản phẩm
            brand: Tên thương hiệu (tùy chọn)
            
        Returns:
            Chuỗi JSON chứa đánh giá bền vững của sản phẩm
        """
        logger.info(f"Đang đánh giá điểm bền vững cho sản phẩm: {product_name}")
        
        try:
            # Tạo dữ liệu đánh giá mẫu
            score = random.randint(30, 100)  # Điểm từ 30-100
            
            # Tạo các chỉ số con
            metrics = {
                "vật_liệu": random.randint(20, 100),
                "sản_xuất": random.randint(20, 100),
                "vận_chuyển": random.randint(20, 100),
                "độ_bền": random.randint(20, 100),
                "tái_chế": random.randint(20, 100)
            }
            
            # Tạo đề xuất cải thiện
            improvements = []
            for metric, value in metrics.items():
                if value < 50:
                    improvements.append(f"Cải thiện {metric.replace('_', ' ')} (hiện tại: {value}/100)")
            
            if not improvements:
                improvements = ["Sản phẩm đã đạt tiêu chuẩn bền vững cao"]
            
            # Tạo phản hồi
            response = {
                "status": "success",
                "product": product_name,
                "brand": brand if brand else "Không xác định",
                "sustainability_score": score,
                "rating": "★★★★★"[:score//20] + "☆☆☆☆☆"[score//20:],
                "metrics": metrics,
                "certifications": random.sample(self.certifications, random.randint(0, 3)) or ["Chưa có chứng nhận"],
                "eco_advantages": [
                    "Vật liệu tái chế",
                    "Sản xuất tiết kiệm năng lượng",
                    "Đóng gói thân thiện môi trường"
                ][:random.randint(1, 3)],
                "improvement_opportunities": improvements[:3],
                "similar_sustainable_alternatives": [
                    self._generate_product(product_name.split()[0], "") for _ in range(3)
                ]
            }
            
            return json.dumps(response, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Lỗi khi đánh giá sản phẩm: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Không thể đánh giá sản phẩm: {str(e)}",
                "product": product_name,
                "suggestions": [
                    "Kiểm tra lại tên sản phẩm",
                    "Thêm thông tin thương hiệu nếu có",
                    "Thử tìm kiếm sản phẩm tương tự"
                ]
            }, ensure_ascii=False, indent=2)
