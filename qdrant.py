
import asyncio

from agno.models.ollama import Ollama
from agno.agent import Agent
from agno.knowledge.pdf_url import PDFUrlKnowledgeBase
from agno.vectordb.qdrant import Qdrant
from agno.embedder.ollama import OllamaEmbedder

COLLECTION_NAME = "thai-recipes"

# Initialize Qdrant with local instance
vector_db = Qdrant(
    embedder=OllamaEmbedder(id="nomic-embed-text:latest", dimensions=768),
    collection=COLLECTION_NAME, 
    url="http://localhost:6333"
)

# Create knowledge base
knowledge_base = PDFUrlKnowledgeBase(
    urls=["https://agno-public.s3.amazonaws.com/recipes/ThaiRecipes.pdf"],
    vector_db=vector_db,
)

agent = Agent(
	  model=Ollama(id="qwen3:4B"),
	  knowledge=knowledge_base,
	  show_tool_calls=True
	)

if __name__ == "__main__":
    # Load knowledge base asynchronously
    asyncio.run(knowledge_base.aload(recreate=False))  # Comment out after first run

    # Create and use the agent asynchronously
    asyncio.run(agent.aprint_response("How to make Som Tum", markdown=True))
