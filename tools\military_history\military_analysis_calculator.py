# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math
from datetime import datetime

class MilitaryAnalysisCalculator(Toolkit):
    """
    Military Analysis Calculator cho tính toán battle metrics, force strength và strategic assessments.
    """

    def __init__(self, enable_calculations: bool = True, **kwargs):
        super().__init__(
            name="military_analysis_calculator",
            **kwargs
        )
        
        # Military calculation frameworks
        self.calculation_frameworks = {
            "lanchester_laws": "Mathematical models of combat",
            "combat_power": "Force effectiveness calculations",
            "attrition_models": "Loss rate calculations",
            "logistics_planning": "Supply and support calculations",
            "force_ratios": "Comparative strength analysis"
        }
        
        # Combat effectiveness factors
        self.effectiveness_factors = {
            "training": {"weight": 0.25, "range": [1, 10]},
            "equipment": {"weight": 0.30, "range": [1, 10]},
            "morale": {"weight": 0.20, "range": [1, 10]},
            "leadership": {"weight": 0.15, "range": [1, 10]},
            "logistics": {"weight": 0.10, "range": [1, 10]}
        }
        
        # Strategic value metrics
        self.strategic_metrics = {
            "geographic": "Terrain and position value",
            "economic": "Resource and industrial value",
            "political": "Political and symbolic importance",
            "military": "Direct military significance"
        }
        
        if enable_calculations:
            self.register(self.calculate_battle_metrics)
            self.register(self.analyze_force_strength)
            self.register(self.assess_strategic_value)
            self.register(self.calculate_logistics_requirements)

    def calculate_battle_metrics(self, battle_data: Dict[str, Any], force_a_strength: int = 10000,
                                force_b_strength: int = 8000, terrain_factor: float = 1.0) -> str:
        """
        Tính toán các metrics của trận chiến sử dụng military models.
        
        Args:
            battle_data: Dữ liệu trận chiến
            force_a_strength: Sức mạnh lực lượng A
            force_b_strength: Sức mạnh lực lượng B
            terrain_factor: Hệ số địa hình (0.5-2.0)
            
        Returns:
            Chuỗi JSON chứa tính toán battle metrics
        """
        log_debug(f"Calculating battle metrics for forces {force_a_strength} vs {force_b_strength}")
        
        try:
            # Lanchester's Laws calculations
            lanchester_analysis = self._calculate_lanchester_models(force_a_strength, force_b_strength, terrain_factor)
            
            # Combat power calculations
            combat_power = self._calculate_combat_power(battle_data, force_a_strength, force_b_strength)
            
            # Attrition modeling
            attrition_analysis = self._model_attrition_rates(combat_power, terrain_factor)
            
            # Victory probability
            victory_probability = self._calculate_victory_probability(lanchester_analysis, combat_power)
            
            # Battle duration estimate
            duration_estimate = self._estimate_battle_duration(attrition_analysis, force_a_strength, force_b_strength)
            
            # Casualty projections
            casualty_projections = self._project_casualties(attrition_analysis, duration_estimate)

            result = {
                "calculation_parameters": {
                    "force_a_strength": force_a_strength,
                    "force_b_strength": force_b_strength,
                    "terrain_factor": terrain_factor,
                    "battle_type": battle_data.get("battle_type", "Conventional"),
                    "calculation_date": datetime.now().strftime("%Y-%m-%d")
                },
                "lanchester_analysis": lanchester_analysis,
                "combat_power": combat_power,
                "attrition_analysis": attrition_analysis,
                "victory_probability": victory_probability,
                "duration_estimate": duration_estimate,
                "casualty_projections": casualty_projections,
                "tactical_recommendations": self._generate_tactical_recommendations(victory_probability, combat_power),
                "model_limitations": self._identify_model_limitations(battle_data)
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error calculating battle metrics: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate battle metrics: {str(e)}"
            }, indent=4)

    def analyze_force_strength(self, force_composition: Dict[str, Any], 
                             effectiveness_ratings: Dict[str, float] = None,
                             operational_context: str = "conventional") -> str:
        """
        Phân tích sức mạnh và hiệu quả của lực lượng quân sự.
        
        Args:
            force_composition: Thành phần lực lượng
            effectiveness_ratings: Đánh giá hiệu quả các yếu tố
            operational_context: Bối cảnh tác chiến
            
        Returns:
            Chuỗi JSON chứa phân tích force strength
        """
        log_debug(f"Analyzing force strength in {operational_context} context")
        
        if effectiveness_ratings is None:
            effectiveness_ratings = {factor: 7.0 for factor in self.effectiveness_factors.keys()}
        
        try:
            # Force effectiveness calculation
            force_effectiveness = self._calculate_force_effectiveness(force_composition, effectiveness_ratings)
            
            # Combat multipliers
            combat_multipliers = self._assess_combat_multipliers(force_composition, operational_context)
            
            # Capability assessment
            capability_assessment = self._assess_force_capabilities(force_composition, effectiveness_ratings)
            
            # Readiness analysis
            readiness_analysis = self._analyze_force_readiness(force_composition, effectiveness_ratings)
            
            # Sustainability factors
            sustainability_factors = self._evaluate_sustainability_factors(force_composition)
            
            # Comparative strength
            comparative_strength = self._calculate_comparative_strength(force_effectiveness, combat_multipliers)

            result = {
                "analysis_parameters": {
                    "operational_context": operational_context,
                    "force_size": force_composition.get("total_personnel", "Unknown"),
                    "analysis_framework": "Multi-factor effectiveness model",
                    "analysis_date": datetime.now().strftime("%Y-%m-%d")
                },
                "force_composition": force_composition,
                "force_effectiveness": force_effectiveness,
                "combat_multipliers": combat_multipliers,
                "capability_assessment": capability_assessment,
                "readiness_analysis": readiness_analysis,
                "sustainability_factors": sustainability_factors,
                "comparative_strength": comparative_strength,
                "strength_recommendations": self._generate_strength_recommendations(capability_assessment, readiness_analysis)
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error analyzing force strength: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze force strength: {str(e)}"
            }, indent=4)

    def assess_strategic_value(self, target_data: Dict[str, Any], assessment_criteria: List[str] = None,
                             threat_level: str = "medium", time_horizon: str = "short_term") -> str:
        """
        Đánh giá strategic value của mục tiêu hoặc khu vực.
        
        Args:
            target_data: Dữ liệu về mục tiêu
            assessment_criteria: Tiêu chí đánh giá
            threat_level: Mức độ đe dọa (low, medium, high, critical)
            time_horizon: Phạm vi thời gian (short_term, medium_term, long_term)
            
        Returns:
            Chuỗi JSON chứa đánh giá strategic value
        """
        log_debug(f"Assessing strategic value with {threat_level} threat level")
        
        if assessment_criteria is None:
            assessment_criteria = list(self.strategic_metrics.keys())
        
        try:
            # Strategic importance calculation
            strategic_importance = self._calculate_strategic_importance(target_data, assessment_criteria)
            
            # Threat assessment
            threat_assessment = self._assess_threat_factors(target_data, threat_level)
            
            # Value-threat matrix
            value_threat_matrix = self._create_value_threat_matrix(strategic_importance, threat_assessment)
            
            # Priority ranking
            priority_ranking = self._calculate_priority_ranking(value_threat_matrix, time_horizon)
            
            # Resource allocation
            resource_allocation = self._recommend_resource_allocation(priority_ranking, threat_level)
            
            # Risk analysis
            risk_analysis = self._perform_strategic_risk_analysis(value_threat_matrix, time_horizon)

            result = {
                "assessment_parameters": {
                    "target_name": target_data.get("name", "Unknown Target"),
                    "assessment_criteria": assessment_criteria,
                    "threat_level": threat_level,
                    "time_horizon": time_horizon,
                    "assessment_framework": "Multi-criteria strategic analysis"
                },
                "strategic_importance": strategic_importance,
                "threat_assessment": threat_assessment,
                "value_threat_matrix": value_threat_matrix,
                "priority_ranking": priority_ranking,
                "resource_allocation": resource_allocation,
                "risk_analysis": risk_analysis,
                "strategic_recommendations": self._generate_strategic_recommendations(priority_ranking, risk_analysis),
                "contingency_planning": self._suggest_contingency_planning(threat_assessment, time_horizon)
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error assessing strategic value: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to assess strategic value: {str(e)}"
            }, indent=4)

    def calculate_logistics_requirements(self, operation_data: Dict[str, Any], force_size: int = 5000,
                                       operation_duration: int = 30, supply_distance: float = 500.0) -> str:
        """
        Tính toán yêu cầu logistics cho military operations.
        
        Args:
            operation_data: Dữ liệu tác chiến
            force_size: Quy mô lực lượng
            operation_duration: Thời gian tác chiến (ngày)
            supply_distance: Khoảng cách tiếp tế (km)
            
        Returns:
            Chuỗi JSON chứa tính toán logistics requirements
        """
        log_debug(f"Calculating logistics for {force_size} personnel over {operation_duration} days")
        
        try:
            # Supply requirements calculation
            supply_requirements = self._calculate_supply_requirements(force_size, operation_duration, operation_data)
            
            # Transportation analysis
            transportation_analysis = self._analyze_transportation_requirements(supply_requirements, supply_distance)
            
            # Storage and distribution
            storage_distribution = self._calculate_storage_distribution_needs(supply_requirements, operation_duration)
            
            # Personnel requirements
            personnel_requirements = self._calculate_logistics_personnel(force_size, supply_requirements)
            
            # Cost estimation
            cost_estimation = self._estimate_logistics_costs(supply_requirements, transportation_analysis)
            
            # Risk factors
            logistics_risks = self._assess_logistics_risks(transportation_analysis, supply_distance)

            result = {
                "calculation_parameters": {
                    "force_size": force_size,
                    "operation_duration": operation_duration,
                    "supply_distance": supply_distance,
                    "operation_type": operation_data.get("operation_type", "Standard"),
                    "calculation_date": datetime.now().strftime("%Y-%m-%d")
                },
                "supply_requirements": supply_requirements,
                "transportation_analysis": transportation_analysis,
                "storage_distribution": storage_distribution,
                "personnel_requirements": personnel_requirements,
                "cost_estimation": cost_estimation,
                "logistics_risks": logistics_risks,
                "optimization_recommendations": self._generate_logistics_optimization(supply_requirements, transportation_analysis),
                "contingency_planning": self._develop_logistics_contingencies(logistics_risks, supply_requirements)
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error calculating logistics requirements: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate logistics requirements: {str(e)}"
            }, indent=4)
