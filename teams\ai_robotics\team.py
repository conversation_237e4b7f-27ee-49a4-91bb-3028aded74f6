"""
Team AI, Robotics & Machine Learning - Nhóm agent <PERSON><PERSON><PERSON><PERSON> về AI, Robotics & Machine Learning.
"""

import logging
import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Callable

from agno.team import Team
from agno.agent import Agent

from agents.ai_robotics.deep_researcher_agent import create_ai_robotics_deep_researcher_agent
from agents.ai_robotics.content_creator_agent import create_ai_robotics_content_creator_agent
from agents.ai_robotics.research_agent import create_ai_robotics_research_agent
from agents.ai_robotics.reasoning_agent import create_ai_robotics_reasoning_agent
from agents.ai_robotics.writer_agent import create_ai_robotics_writer_agent
from agents.ai_robotics.social_media_agent import create_ai_robotics_social_media_agent
# <PERSON>ác agent này sẽ được triển khai sau
# from agents.ai_robotics.youtube_agent import create_ai_robotics_youtube_agent
# from agents.ai_robotics.tts_agent import create_ai_robotics_tts_agent

from config.ai_robotics.config import TEAM_CONFIG, TEAM_INSTRUCTIONS

# Các module tối ưu hóa
from utils.memory_optimization import cleanup_memory
from utils.error_handling import handle_errors

# Các module mới
from utils.chunking import chunk_text, chunk_document
from utils.memory_v2 import create_session, add_message, get_messages, set_user_memory, get_user_memory
from utils.reasoning import reason, reason_lcot, reason_mcot, reason_combined
from utils.workflow import create_workflow, create_route_workflow, create_collaborate_workflow, create_coordinate_workflow

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AIRoboticsTeam:
    """Nhóm agent chuyên về AI, Robotics & Machine Learning."""

    def __init__(self, model_id: Optional[str] = None):
        """
        Khởi tạo team AI, Robotics & Machine Learning.

        Args:
            model_id: ID của mô hình ngôn ngữ
        """
        self.model_id = model_id
        self.name = "AI, Robotics & Machine Learning Team"
        logger.info(f"Initializing {self.name}")

        # Khởi tạo session
        self.session_id = create_session()
        logger.info(f"Created session: {self.session_id}")

        # Khởi tạo các agent
        self.agents = self._initialize_agents()

        # Khởi tạo team
        self.team = self._create_team()

        # Khởi tạo workflow
        self.workflow = self._create_workflow()

    def _initialize_agents(self) -> Dict[str, Agent]:
        """
        Khởi tạo các agent cho team.

        Returns:
            Dictionary các agent
        """
        agents = {}

        # Deep Researcher Agent
        try:
            deep_researcher = create_ai_robotics_deep_researcher_agent(model_id=self.model_id)
            agents["deep_researcher"] = deep_researcher
            logger.info(f"Initialized Deep Researcher Agent: {deep_researcher.name}")
        except Exception as e:
            logger.error(f"Error initializing Deep Researcher Agent: {e}")

        # Content Creator Agent
        try:
            content_creator = create_ai_robotics_content_creator_agent(model_id=self.model_id)
            agents["content_creator"] = content_creator
            logger.info(f"Initialized Content Creator Agent: {content_creator.name}")
        except Exception as e:
            logger.error(f"Error initializing Content Creator Agent: {e}")

        # Research Agent
        try:
            research_agent = create_ai_robotics_research_agent(model_id=self.model_id)
            agents["research"] = research_agent
            logger.info(f"Initialized Research Agent: {research_agent.name}")
        except Exception as e:
            logger.error(f"Error initializing Research Agent: {e}")

        # Reasoning Agent
        try:
            reasoning_agent = create_ai_robotics_reasoning_agent(model_id=self.model_id)
            agents["reasoning"] = reasoning_agent
            logger.info(f"Initialized Reasoning Agent: {reasoning_agent.name}")
        except Exception as e:
            logger.error(f"Error initializing Reasoning Agent: {e}")

        # Writer Agent
        try:
            writer_agent = create_ai_robotics_writer_agent(model_id=self.model_id)
            agents["writer"] = writer_agent
            logger.info(f"Initialized Writer Agent: {writer_agent.name}")
        except Exception as e:
            logger.error(f"Error initializing Writer Agent: {e}")

        # Social Media Agent
        try:
            social_media_agent = create_ai_robotics_social_media_agent(model_id=self.model_id)
            agents["social_media"] = social_media_agent
            logger.info(f"Initialized Social Media Agent: {social_media_agent.name}")
        except Exception as e:
            logger.error(f"Error initializing Social Media Agent: {e}")

        logger.info(f"Initialized {len(agents)} agents for {self.name}")
        return agents

    def _create_team(self) -> Team:
        """
        Tạo team Agno.

        Returns:
            Team Agno
        """
        # Lấy danh sách agent
        agent_list = list(self.agents.values())

        # Tạo model Ollama cho team
        from agno.models.ollama import Ollama

        # Đảm bảo model_id không phải là None
        model_id = self.model_id
        if model_id is None:
            model_id = "qwen3:4b"
            logger.warning(f"model_id is None, using default model: {model_id}")

        team_model = Ollama(id=model_id)

        # Tạo team
        team = Team(
            name=self.name,
            members=agent_list,
            instructions=TEAM_INSTRUCTIONS,
            mode=TEAM_CONFIG["mode"],
            success_criteria=TEAM_CONFIG["success_criteria"],
            model=team_model
        )

        logger.info(f"Created team: {self.name} with {len(agent_list)} agents")
        return team

    def _create_workflow(self):
        """
        Tạo workflow cho team.

        Returns:
            Workflow
        """
        # Tạo router agent
        router_agent = self.agents["deep_researcher"]

        # Tạo route workflow
        route_workflow = create_route_workflow(self.agents, router_agent)

        # Tạo collaborate workflow
        collaborate_workflow = create_collaborate_workflow([
            self.agents["research"],
            self.agents["reasoning"],
            self.agents["writer"]
        ])

        # Tạo coordinate workflow
        coordinate_workflow = create_coordinate_workflow(self.agents, self.agents["deep_researcher"])

        # Chọn workflow dựa trên mode
        if TEAM_CONFIG["mode"] == "sequential":
            workflow = route_workflow
        elif TEAM_CONFIG["mode"] == "parallel":
            workflow = collaborate_workflow
        else:
            workflow = coordinate_workflow

        logger.info(f"Created workflow for team: {self.name} with mode: {TEAM_CONFIG['mode']}")
        return workflow

    def get_team(self) -> Team:
        """
        Lấy team Agno.

        Returns:
            Team Agno
        """
        return self.team


@handle_errors
def chat_loop():
    """Vòng lặp chat với team AI, Robotics & Machine Learning."""
    # Khởi tạo team
    ai_robotics_team = AIRoboticsTeam()
    team = ai_robotics_team.get_team()
    workflow = ai_robotics_team.workflow

    print(f"\n{'='*50}")
    print(f"Chào mừng đến với {team.name}!")
    print(f"Nhập 'exit' hoặc 'quit' để thoát.")
    print(f"{'='*50}\n")

    # Tạo session và lưu user_id
    user_id = "user_" + str(int(time.time()))
    set_user_memory(user_id, "last_session", ai_robotics_team.session_id)

    while True:
        try:
            # Nhận input từ người dùng
            user_input = input("\nBạn: ")

            # Kiểm tra thoát
            if user_input.lower() in ["exit", "quit", "thoát"]:
                print("\nCảm ơn bạn đã sử dụng AI, Robotics & Machine Learning Team. Tạm biệt!")
                break

            # Lưu tin nhắn vào session
            add_message("user", user_input)

            # Sử dụng workflow hoặc team tùy thuộc vào độ phức tạp của câu hỏi
            if len(user_input.split()) > 10:  # Câu hỏi phức tạp
                # Sử dụng workflow
                workflow.set_context("user_query", user_input)
                workflow.set_context("user_id", user_id)

                # Thực hiện workflow
                result = workflow.execute()

                # Lấy kết quả
                response = result.get("result", "")
                if not response and "results" in result:
                    # Lấy kết quả từ giai đoạn cuối cùng
                    last_result = result["results"][-1]
                    if "response" in last_result:
                        response = last_result["response"]
            else:
                # Sử dụng team trực tiếp cho câu hỏi đơn giản
                response = team.run(user_input)

            # Đảm bảo response không phải là None
            if response is None:
                response = "Xin lỗi, tôi không thể xử lý yêu cầu của bạn lúc này."

            # Lưu tin nhắn vào session
            add_message("assistant", response)

            # Hiển thị response
            print(f"\nAI, Robotics & Machine Learning Team: {response}")

            # Dọn dẹp bộ nhớ
            cleanup_memory()
        except KeyboardInterrupt:
            print("\nĐã dừng chương trình.")
            break
        except Exception as e:
            logger.error(f"Lỗi trong vòng lặp chat: {e}")
            print(f"\nXin lỗi, đã xảy ra lỗi: {e}")


if __name__ == "__main__":
    # Chạy vòng lặp chat
    chat_loop()