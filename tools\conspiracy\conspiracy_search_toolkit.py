# -*- coding: utf-8 -*-
from typing import List, Dict, Any
import json
from agno.tools import Toolkit
from agno.utils.log import logger

class ConspiracySearchToolkit(Toolkit):
    """A custom Toolkit for generating search keywords for conspiracy theory databases.

    This toolkit provides functions to generate search keywords for Above Top Secret,
    Biblioteca Pleyades, CIA FOIA, Conspiracy Archive, and Wikipedia, tailored for conspiracy research.
    """

    # == Detailed Instructions for the Agent ==
    instruction = [
        "Bạn là một trợ lý nghiên cứu thuyết âm mưu, chuyên cung cấp từ khóa tìm kiếm tối ưu cho các cơ sở dữ liệu conspiracy.",
        "Khi sử dụng các công cụ trong ConspiracySearchToolkit, tuân thủ các định dạng từ khóa được chỉ định như sau:",
        "- Above Top Secret: Sử dụng định dạng 'conspiracy topic' (ví dụ: 'UFO sightings', 'government cover-up').",
        "- Biblioteca Pleyades: Sử dụng định dạng 'ancient topic' (ví dụ: 'ancient aliens', 'secret government').",
        "- CIA FOIA: Sử dụng định dạng 'project name' hoặc 'classified topic' (ví dụ: 'MKUltra', 'Project Blue Book').",
        "- Conspiracy Archive: Sử dụng định dạng 'theory type' (ví dụ: '9/11 truth', 'illuminati', 'new world order').",
        "- Wikipedia Conspiracy: Sử dụng định dạng 'conspiracy theory' (ví dụ: 'JFK assassination conspiracy', 'moon landing hoax').",
        "Ngoài ra, toolkit cũng hỗ trợ tạo từ khóa cho việc tìm kiếm nội dung mới nhất và trending:",
        "- Above Top Secret Recent: Tạo từ khóa cho discussions và topics mới theo forum.",
        "- Biblioteca Pleyades Recent: Tạo từ khóa cho articles và topics mới theo subject.",
        "- CIA FOIA Recent: Tạo từ khóa cho documents và releases mới theo classification.",
        "- Conspiracy Archive Trending: Tạo từ khóa cho theories và posts trending theo category.",
        "- Wikipedia Conspiracy Recent: Tạo từ khóa cho bài viết conspiracy mới được tạo hoặc cập nhật.",
        "Kiểm tra tính hợp lệ của tham số đầu vào và trả về từ khóa phù hợp với từng cơ sở dữ liệu.",
        "Trả về kết quả dưới dạng JSON với trạng thái ('status'), danh sách từ khóa ('keywords'), và thông báo ('message').",
        "Nếu có lỗi, trả về trạng thái 'error' với mô tả lỗi chi tiết.",
        "Lưu ý: Nội dung conspiracy có thể nhạy cảm, cần cân bằng giữa tìm kiếm thông tin và tránh lan truyền thông tin sai lệch."
    ]

    # == Detailed Few-Shot Examples ==
    few_shot_examples = [
        {
            "user": "Tìm thông tin về UFO và government cover-ups.",
            "tool_calls": [
                {
                    "name": "generate_above_top_secret_keywords",
                    "arguments": {"topic": "UFO sightings", "forum": "UFOs"}
                },
                {
                    "name": "generate_cia_foia_keywords",
                    "arguments": {"project": "Project Blue Book", "classification": "declassified"}
                },
                {
                    "name": "generate_wikipedia_conspiracy_keywords",
                    "arguments": {"theory": "UFO conspiracy theories"}
                }
            ]
        },
        {
            "user": "Tìm recent discussions và trending theories.",
            "tool_calls": [
                {
                    "name": "generate_above_top_secret_trending_keywords",
                    "arguments": {"period": "week", "forum": "Conspiracy Theories"}
                },
                {
                    "name": "generate_conspiracy_archive_trending_keywords",
                    "arguments": {"category": "Government", "period": "month"}
                },
                {
                    "name": "generate_cia_foia_recent_keywords",
                    "arguments": {"classification": "recently declassified", "days_back": 30}
                }
            ]
        },
        {
            "user": "Tìm nghiên cứu về mind control và secret societies.",
            "tool_calls": [
                {
                    "name": "generate_cia_foia_keywords",
                    "arguments": {"project": "MKUltra", "classification": "mind control"}
                },
                {
                    "name": "generate_conspiracy_archive_keywords",
                    "arguments": {"theory": "illuminati", "category": "Secret Societies"}
                },
                {
                    "name": "generate_biblioteca_pleyades_keywords",
                    "arguments": {"topic": "secret government", "subject": "ancient mysteries"}
                }
            ]
        }
    ]

    def __init__(self):
        """Initializes the ConspiracySearchToolkit."""
        super().__init__(
            name="conspiracy_search_toolkit",
            tools=[
                self.generate_above_top_secret_keywords,
                self.generate_biblioteca_pleyades_keywords,
                self.generate_cia_foia_keywords,
                self.generate_conspiracy_archive_keywords,
                self.generate_wikipedia_conspiracy_keywords,
                self.generate_above_top_secret_trending_keywords,
                self.generate_biblioteca_pleyades_recent_keywords,
                self.generate_cia_foia_recent_keywords,
                self.generate_conspiracy_archive_trending_keywords,
                self.generate_wikipedia_conspiracy_recent_keywords
            ],
            instructions=self.instruction
        )
        self.few_shot_examples = self.few_shot_examples
        logger.info("ConspiracySearchToolkit initialized.")

    def generate_above_top_secret_keywords(self, topic: str, forum: str = None) -> str:
        """Generates search keywords for Above Top Secret.

        Args:
            topic: The conspiracy topic (e.g., 'UFO sightings', 'government cover-up').
            forum: Optional forum filter (e.g., 'UFOs', 'Conspiracy Theories').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating Above Top Secret keywords for topic: '{topic}', forum: '{forum}'")
        try:
            if not topic.strip():
                raise ValueError("Topic cannot be empty.")

            # Tạo từ khóa cho Above Top Secret
            keywords = [topic]
            if forum:
                keywords.append(f"{topic} {forum}")

            # Thêm từ khóa mở rộng
            keywords.extend([
                f"{topic} discussion", f"{topic} evidence", f"{topic} theory",
                f"{topic} forum", f"{topic} investigation"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Above Top Secret keywords for topic '{topic}' in forum '{forum or 'all'}'.",
                "search_type": "forum_discussions",
                "parameters": {
                    "topic": topic,
                    "forum": forum
                }
            }
            logger.debug(f"Above Top Secret keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating Above Top Secret keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate Above Top Secret keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_biblioteca_pleyades_keywords(self, topic: str, subject: str = None) -> str:
        """Generates search keywords for Biblioteca Pleyades."""
        logger.info(f"Generating Biblioteca Pleyades keywords for topic: '{topic}', subject: '{subject}'")
        try:
            if not topic.strip():
                raise ValueError("Topic cannot be empty.")

            keywords = [topic]
            if subject:
                keywords.append(f"{topic} {subject}")

            keywords.extend([
                f"{topic} ancient", f"{topic} mystery", f"{topic} secret",
                f"ancient {topic}", f"{topic} civilization"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Biblioteca Pleyades keywords for topic '{topic}' and subject '{subject or 'general'}'.",
                "search_type": "ancient_mysteries",
                "parameters": {"topic": topic, "subject": subject}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Biblioteca Pleyades keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_cia_foia_keywords(self, project: str, classification: str = None) -> str:
        """Generates search keywords for CIA FOIA."""
        logger.info(f"Generating CIA FOIA keywords for project: '{project}', classification: '{classification}'")
        try:
            if not project.strip():
                raise ValueError("Project cannot be empty.")

            keywords = [project]
            if classification:
                keywords.append(f"{project} {classification}")

            keywords.extend([
                f"{project} declassified", f"{project} documents", f"{project} files",
                f"project {project}", f"{project} operation"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated CIA FOIA keywords for project '{project}' and classification '{classification or 'all'}'.",
                "search_type": "declassified_documents",
                "parameters": {"project": project, "classification": classification}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate CIA FOIA keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_conspiracy_archive_keywords(self, theory: str, category: str = None) -> str:
        """Generates search keywords for Conspiracy Archive."""
        logger.info(f"Generating Conspiracy Archive keywords for theory: '{theory}', category: '{category}'")
        try:
            if not theory.strip():
                raise ValueError("Theory cannot be empty.")

            keywords = [theory]
            if category:
                keywords.append(f"{theory} {category}")

            keywords.extend([
                f"{theory} conspiracy", f"{theory} truth", f"{theory} evidence",
                f"{theory} investigation", f"{theory} analysis"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Conspiracy Archive keywords for theory '{theory}' and category '{category or 'general'}'.",
                "search_type": "conspiracy_theories",
                "parameters": {"theory": theory, "category": category}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Conspiracy Archive keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_conspiracy_keywords(self, theory: str) -> str:
        """Generates search keywords for Wikipedia conspiracy topics."""
        logger.info(f"Generating Wikipedia conspiracy keywords for theory: '{theory}'")
        try:
            if not theory.strip():
                raise ValueError("Theory cannot be empty.")

            keywords = [theory]
            if "conspiracy" not in theory.lower():
                keywords.append(f"{theory} conspiracy theory")
            keywords.extend([
                f"{theory} allegations", f"{theory} claims", f"{theory} debunked",
                f"{theory} evidence", f"{theory} investigation"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia conspiracy keywords for theory '{theory}'.",
                "search_type": "encyclopedia_articles",
                "parameters": {"theory": theory}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia conspiracy keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    # == Recent/Trending Keywords Functions ==

    def generate_above_top_secret_trending_keywords(self, period: str = "week", forum: str = None) -> str:
        """Generates keywords for trending topics on Above Top Secret."""
        logger.info(f"Generating Above Top Secret trending keywords for period: '{period}', forum: '{forum}'")
        try:
            keywords = [f"trending {period}", f"popular {period}", f"hot topics"]
            if forum:
                keywords.extend([f"trending {forum}", f"popular {forum}", f"{forum} hot"])
            keywords.extend([
                f"recent discussions", f"active threads", f"most viewed",
                f"latest {period}", f"top {period}"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Above Top Secret trending keywords for {period} in forum '{forum or 'all'}'.",
                "search_type": "trending_discussions",
                "parameters": {"period": period, "forum": forum}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Above Top Secret trending keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_biblioteca_pleyades_recent_keywords(self, subject: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent articles on Biblioteca Pleyades."""
        logger.info(f"Generating Biblioteca Pleyades recent keywords for subject: '{subject}', days_back: {days_back}")
        try:
            keywords = ["recent articles", "new content", "latest updates"]
            if subject:
                keywords.extend([f"recent {subject}", f"new {subject}", f"latest {subject}"])
            keywords.extend([
                "recent discoveries", "new research", "latest findings",
                f"last {days_back} days", "recent publications"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Biblioteca Pleyades recent keywords for subject '{subject or 'all'}' within last {days_back} days.",
                "search_type": "recent_articles",
                "parameters": {"subject": subject, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Biblioteca Pleyades recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_cia_foia_recent_keywords(self, classification: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent CIA FOIA releases."""
        logger.info(f"Generating CIA FOIA recent keywords for classification: '{classification}', days_back: {days_back}")
        try:
            keywords = ["recent releases", "new declassified", "latest documents"]
            if classification:
                keywords.extend([f"recent {classification}", f"new {classification}", f"latest {classification}"])
            keywords.extend([
                "recently declassified", "new FOIA", "latest releases",
                f"last {days_back} days", "recent disclosures"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated CIA FOIA recent keywords for classification '{classification or 'all'}' within last {days_back} days.",
                "search_type": "recent_releases",
                "parameters": {"classification": classification, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate CIA FOIA recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_conspiracy_archive_trending_keywords(self, category: str = None, period: str = "month") -> str:
        """Generates keywords for trending theories on Conspiracy Archive."""
        logger.info(f"Generating Conspiracy Archive trending keywords for category: '{category}', period: '{period}'")
        try:
            keywords = [f"trending {period}", f"popular theories", f"hot topics"]
            if category:
                keywords.extend([f"trending {category}", f"popular {category}", f"{category} hot"])
            keywords.extend([
                "viral theories", "most discussed", "top theories",
                f"latest {period}", f"trending conspiracy"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Conspiracy Archive trending keywords for category '{category or 'all'}' within last {period}.",
                "search_type": "trending_theories",
                "parameters": {"category": category, "period": period}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Conspiracy Archive trending keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_conspiracy_recent_keywords(self, days_back: int = 30, language: str = "en") -> str:
        """Generates keywords for recent conspiracy articles on Wikipedia."""
        logger.info(f"Generating Wikipedia conspiracy recent keywords for days_back: {days_back}, language: '{language}'")
        try:
            keywords = [
                "recent conspiracy articles", "new conspiracy theories", "latest conspiracy claims",
                "recent conspiracy investigations", "new conspiracy evidence", "latest conspiracy debunking",
                f"last {days_back} days conspiracy", "recent conspiracy research"
            ]

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia conspiracy recent keywords within last {days_back} days for language '{language}'.",
                "search_type": "recent_articles",
                "parameters": {"days_back": days_back, "language": language}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia conspiracy recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)