from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class StanfordEncyclopediaTool(Toolkit):
    """
    Stanford Encyclopedia Tool for searching philosophy topics from the Stanford Encyclopedia of Philosophy (SEP).
    """

    def __init__(self):
        super().__init__(
            name="Stanford Encyclopedia of Philosophy Search Tool",
            description="Tool for searching philosophy topics, thinkers, and concepts from the Stanford Encyclopedia of Philosophy (SEP).",
            tools=[self.search_stanford_encyclopedia]
        )

    async def search_stanford_encyclopedia(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Search Stanford Encyclopedia of Philosophy (SEP) for philosophy topics, thinkers, and concepts.

        Parameters:
        - query: Term, thinker, or concept (e.g., 'existentialism', 'Kant ethics', 'free will')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with search results including title, summary, author, and SEP URLs
        """
        logger.info(f"Searching Stanford Encyclopedia of Philosophy for: {query}")

        try:
            # SEP không có API chính thức, dùng Google Custom Search hoặc scraping đơn giản
            # Ở đây mô phỏng tìm kiếm bằng Google Custom Search (nếu có key thì dùng thật)
            search_url = "https://www.googleapis.com/customsearch/v1"
            api_key = "YOUR_GOOGLE_API_KEY"  # Thay bằng API key thật nếu có
            cx = "YOUR_CUSTOM_SEARCH_ENGINE_ID"  # Thay bằng CSE ID thật nếu có

            params = {
                "q": f"site:plato.stanford.edu {query}",
                "key": api_key,
                "cx": cx,
                "num": limit
            }

            response = requests.get(search_url, params=params)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Stanford Encyclopedia of Philosophy",
                    "message": f"Search API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            items = data.get("items", [])
            results = []
            for item in items:
                results.append({
                    "title": item.get("title"),
                    "snippet": item.get("snippet"),
                    "link": item.get("link"),
                    "displayLink": item.get("displayLink"),
                    "sep_url": item.get("link")
                })

            return {
                "status": "success",
                "source": "Stanford Encyclopedia of Philosophy",
                "query": query,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "existentialism",
                    "Kant ethics",
                    "free will",
                    "Plato Republic",
                    "phenomenology",
                    "utilitarianism",
                    "Nietzsche",
                    "Daoism",
                    "logic",
                    "philosophy of mind"
                ],
                "official_data_url": "https://plato.stanford.edu/"
            }

        except Exception as e:
            log_debug(f"Error searching Stanford Encyclopedia of Philosophy: {str(e)}")
            return {
                "status": "error",
                "source": "Stanford Encyclopedia of Philosophy",
                "message": str(e),
                "query": query
            }
