#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Basic Test Script cho 3 kênh còn lại:
- Literature (Search Toolkit + Analyzer)
- Military History (Search Toolkit + Analyzer)  
- Medical Science (Search Toolkit + Analyzer)
"""

import sys
import os
import json
import random
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_literature_basic():
    """Test Literature Basic Tools"""
    print("📚 Testing Literature Basic Tools...")
    try:
        # Test literature search toolkit
        from tools.literature.literature_search_toolkit import LiteratureSearchToolkit
        
        toolkit = LiteratureSearchToolkit()
        
        print("  - Testing Literature Search Toolkit...")
        result = toolkit.search_literary_works("Shakespeare", "", "drama", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Literature Search Toolkit works")
        
        # Test literature analyzer
        from tools.literature.literature_analyzer import LiteratureAnalyzer
        
        analyzer = LiteratureAnalyzer()
        
        print("  - Testing Literature Analyzer...")
        result = analyzer.analyze_literary_movements("Romanticism", "19th century", "poetry", "standard")
        data = json.loads(result)
        assert "analysis_parameters" in data
        print("    ✅ Literature Analyzer works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Literature Basic Tools failed: {str(e)}")
        return False

def test_military_basic():
    """Test Military History Basic Tools"""
    print("⚔️ Testing Military History Basic Tools...")
    try:
        # Test military search toolkit
        from tools.military_history.military_search_toolkit import MilitarySearchToolkit
        
        toolkit = MilitarySearchToolkit()
        
        print("  - Testing Military Search Toolkit...")
        result = toolkit.search_military_conflicts("World War II", "all", "global", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Military Search Toolkit works")
        
        # Test military analyzer
        from tools.military_history.military_analyzer import MilitaryAnalyzer
        
        analyzer = MilitaryAnalyzer()
        
        print("  - Testing Military Analyzer...")
        result = analyzer.analyze_military_technology("tanks", "WWII", "armor", "standard")
        data = json.loads(result)
        assert "analysis_parameters" in data
        print("    ✅ Military Analyzer works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Military History Basic Tools failed: {str(e)}")
        return False

def test_medical_basic():
    """Test Medical Science Basic Tools"""
    print("🏥 Testing Medical Science Basic Tools...")
    try:
        # Test medical search toolkit
        from tools.medical_science.medical_search_toolkit import MedicalSearchToolkit
        
        toolkit = MedicalSearchToolkit()
        
        print("  - Testing Medical Search Toolkit...")
        result = toolkit.search_medical_literature("diabetes", "all", "5years", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Medical Search Toolkit works")
        
        # Test medical analyzer
        from tools.medical_science.medical_analyzer import MedicalAnalyzer
        
        analyzer = MedicalAnalyzer()
        
        print("  - Testing Medical Analyzer...")
        result = analyzer.analyze_disease_trends("diabetes", "global", "chronic", "standard")
        data = json.loads(result)
        assert "analysis_parameters" in data
        print("    ✅ Medical Analyzer works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Medical Science Basic Tools failed: {str(e)}")
        return False

def test_comprehensive_searches():
    """Test comprehensive search functions"""
    print("\n🔍 Testing Comprehensive Search Functions...")
    
    try:
        # Literature comprehensive search
        from tools.literature.literature_search_toolkit import LiteratureSearchToolkit
        lit_toolkit = LiteratureSearchToolkit()
        
        print("  - Testing Literature Comprehensive Search...")
        result = lit_toolkit.comprehensive_literature_search("Victorian novel", "all", "english", "general")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Literature Comprehensive Search works")
        
        # Military comprehensive search
        from tools.military_history.military_search_toolkit import MilitarySearchToolkit
        mil_toolkit = MilitarySearchToolkit()
        
        print("  - Testing Military Comprehensive Search...")
        result = mil_toolkit.comprehensive_military_search("World War II", "WWII", "all")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Military Comprehensive Search works")
        
        # Medical comprehensive search
        from tools.medical_science.medical_search_toolkit import MedicalSearchToolkit
        med_toolkit = MedicalSearchToolkit()
        
        print("  - Testing Medical Comprehensive Search...")
        result = med_toolkit.comprehensive_medical_search("cancer treatment", "all", "high", "oncology")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Medical Comprehensive Search works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Comprehensive Searches failed: {str(e)}")
        return False

def test_random_functionality():
    """Test random functionality across channels"""
    print("\n🎲 Testing Random Functionality...")
    
    try:
        # Random literature test
        from tools.literature.literature_search_toolkit import LiteratureSearchToolkit
        lit_toolkit = LiteratureSearchToolkit()
        
        authors = ["Shakespeare", "Dickens", "Austen", "Hemingway"]
        author = random.choice(authors)
        result = lit_toolkit.search_authors_comprehensive(author, "", "", True)
        data = json.loads(result)
        assert "search_parameters" in data
        print("  🎯 Random Literature test passed")
        
        # Random military test
        from tools.military_history.military_search_toolkit import MilitarySearchToolkit
        mil_toolkit = MilitarySearchToolkit()
        
        conflicts = ["World War I", "World War II", "Vietnam War", "Korean War"]
        conflict = random.choice(conflicts)
        result = mil_toolkit.search_military_conflicts(conflict, "all", "global", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("  🎯 Random Military test passed")
        
        # Random medical test
        from tools.medical_science.medical_search_toolkit import MedicalSearchToolkit
        med_toolkit = MedicalSearchToolkit()
        
        conditions = ["diabetes", "cancer", "heart disease", "alzheimer"]
        condition = random.choice(conditions)
        result = med_toolkit.search_medical_conditions(condition, "comprehensive", True, True)
        data = json.loads(result)
        assert "search_parameters" in data
        print("  🎯 Random Medical test passed")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Random Functionality failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 THREE CHANNELS BASIC TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing basic functionality of Literature, Military History, and Medical Science...")
    print()
    
    test_results = []
    
    # Test basic functionality
    test_functions = [
        ("Literature Basic Tools", test_literature_basic),
        ("Military History Basic Tools", test_military_basic),
        ("Medical Science Basic Tools", test_medical_basic),
        ("Comprehensive Searches", test_comprehensive_searches),
        ("Random Functionality", test_random_functionality)
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()
    
    # Summary
    print("\n" + "="*60)
    print("📋 THREE CHANNELS BASIC TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All three channels basic functionality working correctly!")
        print("✨ Literature, Military History, and Medical Science tools verified!")
    elif passed >= total * 0.6:
        print("✅ Most functionality working - minor issues with some components")
    else:
        print("⚠️  Significant issues detected. Please check the error messages above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
