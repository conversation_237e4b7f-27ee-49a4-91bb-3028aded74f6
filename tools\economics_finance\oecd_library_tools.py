from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class OECDLibraryTool(Toolkit):
    """
    OECD Library Tool cho tìm kiếm tài li<PERSON>u, dữ liệu kinh tế, b<PERSON><PERSON> c<PERSON><PERSON> từ OECD Library.
    """

    def __init__(self):
        super().__init__(
            name="OECD Library Search Tool",
            tools=[self.search_oecd_library]
        )

    async def search_oecd_library(self, query: str, language: str = "en", limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm OECD Library cho tài liệu, dữ liệu kinh tế, b<PERSON><PERSON> c<PERSON>o, thống kê.

        Parameters:
        - query: Từ khóa tìm kiếm (ví dụ: 'GDP growth', 'Vietnam education', 'tax policy', 'inequality')
        - language: <PERSON><PERSON> ngôn ngữ (ví dụ: 'en', 'fr')
        - limit: <PERSON><PERSON> lư<PERSON><PERSON> kết quả tối đa (default: 5)

        Returns:
        - JSO<PERSON> với tiêu đề, mô tả, nă<PERSON>, chủ đề, link OECD Library
        """
        logger.info(f"Tìm kiếm OECD Library: query={query}, language={language}, limit={limit}")

        try:
            # OECD iLibrary OpenSearch API (hoặc endpoint REST)
            # Ví dụ: https://www.oecd-ilibrary.org/search?value1=GDP+growth&option1=quicksearch&sortField=Date&sortDescending=true&page=1&pageSize=5
            search_url = "https://www.oecd-ilibrary.org/search"
            params = {
                "value1": query,
                "option1": "quicksearch",
                "sortField": "Date",
                "sortDescending": "true",
                "page": 1,
                "pageSize": limit,
                "lang": language
            }
            headers = {
                "User-Agent": "OECDLibraryBot/1.0"
            }
            response = requests.get(search_url, params=params, headers=headers, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "OECD Library",
                    "message": f"OECD Library search returned status code {response.status_code}",
                    "query": query
                }

            # Đơn giản: lấy các link kết quả đầu tiên (dùng regex)
            import re
            results = []
            # Mỗi kết quả nằm trong <a class="result-title" href="...">...</a>
            for match in re.finditer(r'<a class="result-title" href="([^"]+)"[^>]*>(.*?)</a>', response.text):
                if len(results) >= limit:
                    break
                url = match.group(1)
                title = re.sub(r'<.*?>', '', match.group(2)).strip()
                # Lấy mô tả nếu có
                desc_match = re.search(rf'<a class="result-title" href="{re.escape(url)}"[^>]*>.*?</a>.*?<div class="result-desc">(.*?)</div>', response.text, re.DOTALL)
                description = re.sub(r'<.*?>', '', desc_match.group(1)).strip() if desc_match else None
                # Chuẩn hóa url
                if url.startswith("/"):
                    url = f"https://www.oecd-ilibrary.org{url}"
                results.append({
                    "title": title,
                    "description": description,
                    "oecd_url": url
                })

            return {
                "status": "success",
                "source": "OECD Library",
                "query": query,
                "language": language,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "GDP growth <country>",
                    "tax policy <country>",
                    "education <country>",
                    "inequality <region>",
                    "economic outlook",
                    "environmental statistics",
                    "public finance",
                    "trade data",
                    "employment report"
                ],
                "official_data_url": "https://www.oecd-ilibrary.org/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm OECD Library: {str(e)}")
            return {
                "status": "error",
                "source": "OECD Library",
                "message": str(e),
                "query": query
            }
