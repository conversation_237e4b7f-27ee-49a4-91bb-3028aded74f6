import requests
import json
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
from bs4 import BeautifulSoup
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class GSMARenaTools(Toolkit):
    """
    Công cụ tìm kiếm và tra cứu thông số kỹ thuật điện thoại di động từ GSMArena.
    
    Cung cấp thông tin chi tiết về điện thoại thông minh, m<PERSON><PERSON> t<PERSON>h bảng, 
    smartwatch và các thiết bị di động khác.
    
    Keyword gợi ý: "điện thoại iPhone 15 Pro Max", "thông số Samsung Galaxy S24",
    "so s<PERSON>h Xiaomi 13T với Vivo X100", "đánh giá Realme 11 Pro+", "máy tính bảng giá rẻ"
    """
    
    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(name="gsmarena_tools", **kwargs)
        self.base_url = "https://www.gsmarena.com"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        if enable_search:
            self.register(self.search_devices)
            self.register(self.get_device_specs)
    
    def search_devices(self, query: str, max_results: int = 5) -> str:
        """
        Tìm kiếm thiết bị di động trên GSMArena.
        
        Args:
            query (str): Từ khóa tìm kiếm (tên hãng, model, hoặc từ khóa liên quan)
            max_results (int, optional): Số lượng kết quả tối đa. Mặc định: 5.
            
        Returns:
            str: Chuỗi JSON chứa danh sách thiết bị phù hợp
            
        Ví dụ:
            search_devices("iPhone 15 Pro Max", 3)
            search_devices("Samsung Galaxy S24 Ultra", 5)
        """
        log_debug(f"Tìm kiếm thiết bị trên GSMArena: {query}")
        
        try:
            search_url = f"{self.base_url}/res.php3"
            params = {
                "sSearch": query
            }
            
            response = requests.get(
                search_url,
                params=params,
                headers=self.headers,
                timeout=15
            )
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # Lấy các kết quả tìm kiếm
            device_list = soup.select('.makers li')
            
            for device in device_list[:max_results]:
                link_elem = device.find('a')
                if not link_elem:
                    continue
                    
                title = link_elem.get('title', '')
                url = link_elem.get('href', '')
                if url and not url.startswith('http'):
                    url = f"{self.base_url}/{url}"
                
                # Lấy ảnh thiết bị
                img_elem = link_elem.find('img')
                img_url = img_elem.get('src') if img_elem else ""
                if img_url and not img_url.startswith('http'):
                    img_url = f"{self.base_url}/{img_url}"
                
                # Lấy thông tin cơ bản
                info_elems = device.select('.article-info-line span')
                specs = [elem.get_text(strip=True) for elem in info_elems]
                
                results.append({
                    "title": title,
                    "url": url,
                    "image": img_url,
                    "specs": specs,
                    "source": "GSMArena"
                })
            
            # Nếu không có kết quả, trả về kết quả mặc định
            if not results:
                return self._get_default_results(query)
            
            return json.dumps({
                "status": "success",
                "source": "GSMArena",
                "query": query,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)
            
        except requests.RequestException as e:
            logger.error(f"Lỗi khi truy vấn GSMArena: {e}")
            return self._get_error_response(query, str(e))
    
    def get_device_specs(self, device_url: str) -> str:
        """
        Lấy thông số kỹ thuật chi tiết của một thiết bị.
        
        Args:
            device_url (str): URL của trang thông tin thiết bị trên GSMArena
            
        Returns:
            str: Chuỗi JSON chứa thông số kỹ thuật chi tiết
            
        Ví dụ:
            get_device_specs("https://www.gsmarena.com/apple_iphone_15_pro_max-12559.php")
        """
        log_debug(f"Lấy thông số thiết bị từ: {device_url}")
        
        try:
            # Đảm bảo URL đầy đủ
            if not device_url.startswith('http'):
                device_url = f"{self.base_url}/{device_url}"
            
            response = requests.get(device_url, headers=self.headers, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Lấy thông tin cơ bản
            device_info = {
                "name": soup.select_one('.specs-phone-name-title').get_text(strip=True) if soup.select_one('.specs-phone-name-title') else "",
                "image": soup.select_one('.specs-photo-main img')['src'] if soup.select_one('.specs-photo-main img') else "",
                "release_date": soup.select_one('.specs-preview-body .specs-spotlight-features time')
                                .get_text(strip=True) if soup.select_one('.specs-preview-body .specs-spotlight-features time') else "",
                "specs": {}
            }
            
            # Lấy các nhóm thông số kỹ thuật
            spec_sections = soup.select('#specs-list table')
            
            for section in spec_sections:
                section_title = section.find_previous('th', class_='ttl')
                if not section_title:
                    continue
                    
                section_name = section_title.get_text(strip=True)
                device_info["specs"][section_name] = {}
                
                # Lấy các thông số trong nhóm
                rows = section.select('tr')
                for row in rows:
                    cells = row.select('td')
                    if len(cells) >= 2:
                        spec_name = cells[0].get_text(strip=True)
                        spec_value = cells[1].get_text(strip=True, separator=' ')
                        device_info["specs"][section_name][spec_name] = spec_value
            
            return json.dumps({
                "status": "success",
                "source": "GSMArena",
                "device_info": device_info,
                "result_count": len(device_info["specs"]) if device_info["specs"] else 0
            }, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"Lỗi khi lấy thông số thiết bị: {e}")
            return json.dumps({
                "status": "error",
                "source": "GSMArena",
                "device_url": device_url,
                "message": str(e),
                "device_info": {}
            }, indent=2, ensure_ascii=False)