# Art History Tools - Enhanced Architecture & "Get Recent/Popular" Functions

Tài liệu này mô tả việc cải tiến art history tools và các hàm mới được thêm vào để lấy nội dung recent/popular.

## Tổng quan

Art history tools đã được mở rộng từ 3 tools thành 5 tools + 1 search toolkit:

1. **Europeana Art Tools** - `europeana_art_tools.py` (đã cải tiến)
2. **Google Arts & Culture Tools** - `google_arts_culture_tools.py` (giữ nguyên)
3. **Wikipedia Art Tools** - `wikipedia_art_tools.py` (giữ nguyên)
4. **Metropolitan Museum Tools** - `metropolitan_museum_tools.py` (mới)
5. **Rijksmuseum Tools** - `rijksmuseum_tools.py` (mới)
6. **Art History Search Toolkit** - `art_history_search_toolkit.py` (mới)

## Chi tiết cá<PERSON> cả<PERSON> tiến

### 1. Europeana Art Tools (<PERSON><PERSON> cả<PERSON> tiến)

#### Hàm hiện có:
- `search_europeana_art(query, type_=None, creator=None, limit=5)` - Chuyển từ async sang sync

#### Hàm mới:
- `get_recent_artworks(limit=10, days_back=30, type_=None)` - Lấy artworks mới
- `get_trending_collections(limit=10, theme=None)` - Lấy collections phổ biến

**Ví dụ sử dụng:**
```python
from tools.art_history.europeana_art_tools import EuropeanaArtTools

tool = EuropeanaArtTools()
recent = tool.get_recent_artworks(5, 30, "IMAGE")
trending = tool.get_trending_collections(5, "art")
```

### 2. Metropolitan Museum Tools (Mới)

Tool hoàn toàn mới sử dụng Met Museum API.

#### Hàm chính:
- `search_met_art(query, department=None, medium=None, limit=10)` - Tìm kiếm art objects
- `get_recent_acquisitions(limit=10, days_back=90, department=None)` - Lấy acquisitions mới
- `get_highlighted_artworks(limit=10, department=None)` - Lấy artworks nổi bật

**Ví dụ sử dụng:**
```python
from tools.art_history.metropolitan_museum_tools import MetropolitanMuseumTools

tool = MetropolitanMuseumTools()
search = tool.search_met_art("European painting", "European Paintings", "oil on canvas", 5)
recent = tool.get_recent_acquisitions(5, 90, "American Wing")
highlights = tool.get_highlighted_artworks(5, "Greek and Roman Art")
```

### 3. Rijksmuseum Tools (Mới)

Tool hoàn toàn mới chuyên về Dutch art và cultural heritage.

#### Hàm chính:
- `search_rijksmuseum(query, artist=None, type_=None, limit=10)` - Tìm kiếm Dutch art
- `get_recent_additions(limit=10, days_back=90, type_=None)` - Lấy additions mới
- `get_popular_dutch_art(limit=10, period=None)` - Lấy Dutch art phổ biến

**Ví dụ sử dụng:**
```python
from tools.art_history.rijksmuseum_tools import RijksmuseumTools

tool = RijksmuseumTools()
search = tool.search_rijksmuseum("Rembrandt", "Rembrandt van Rijn", "painting", 5)
recent = tool.get_recent_additions(5, 90, "photography")
popular = tool.get_popular_dutch_art(5, "Golden Age")
```

### 4. Google Arts & Culture Tools (Cần cải tiến)

**Hàm cần thêm:**
- `get_featured_exhibitions(limit=10, period="month")`
- `get_trending_artists(limit=10, region=None)`

### 5. Wikipedia Art Tools (Cần cải tiến)

**Hàm cần thêm:**
- `get_recent_art_articles(limit=10, days_back=30, language="en")`
- `get_popular_art_topics(limit=10, category=None)`

### 6. Art History Search Toolkit (Mới)

Tool chia sub question hoàn toàn mới cho art history research.

#### Hàm tạo từ khóa thông thường:
- `generate_europeana_keywords(artist, period=None, type=None)`
- `generate_google_arts_keywords(query, type=None)`
- `generate_wikipedia_art_keywords(concept, category=None)`
- `generate_met_museum_keywords(culture, medium=None, period=None)`
- `generate_rijksmuseum_keywords(artist=None, type=None, period=None)`

#### Hàm tạo từ khóa cho recent/popular:
- `generate_europeana_recent_keywords(theme=None, days_back=30)`
- `generate_google_arts_trending_keywords(type="exhibitions", period="month")`
- `generate_wikipedia_art_recent_keywords(days_back=30, language="en")`
- `generate_museum_recent_keywords(museum, department=None)`
- `generate_art_discovery_keywords(theme, period=None)`

**Ví dụ sử dụng:**
```python
from tools.art_history.art_history_search_toolkit import ArtHistorySearchToolkit

toolkit = ArtHistorySearchToolkit()
keywords = toolkit.generate_europeana_keywords("Van Gogh", "Post-Impressionism", "painting")
recent_keywords = toolkit.generate_europeana_recent_keywords("contemporary art", 30)
```

## Tính năng chung

### Cải tiến Architecture
- **Sync Operations**: Chuyển từ async sang sync để consistent với astronomy/ai_robotics/archaeology
- **Caching System**: Thêm cache với TTL phù hợp cho từng loại content
- **Retry Mechanism**: Exponential backoff cho API calls
- **Error Handling**: Comprehensive error handling với fallback data

### Fallback Data
Tất cả các hàm đều có cơ chế fallback data chất lượng cao:
- **Europeana**: Famous artworks từ major European collections
- **Met Museum**: Iconic American và international artworks
- **Rijksmuseum**: Dutch Golden Age masterpieces như The Night Watch
- **Google Arts**: Popular exhibitions và artist spotlights
- **Wikipedia**: High-quality art history articles

### Định dạng từ khóa đặc biệt

#### Europeana:
- `artist period` (e.g., 'Van Gogh Post-Impressionism')
- `artwork type` (e.g., 'Renaissance painting')

#### Google Arts & Culture:
- `artwork OR artist OR movement` (e.g., 'Starry Night OR Van Gogh OR Post-Impressionism')

#### Wikipedia Art:
- `art topic` (e.g., 'Renaissance painting')
- `artist biography` (e.g., 'Leonardo da Vinci')

#### Metropolitan Museum:
- `culture medium period` (e.g., 'European painting 19th century')
- `American sculpture` (e.g., 'American sculpture')

#### Rijksmuseum:
- `Dutch artist period` (e.g., 'Rembrandt Golden Age')
- `Vermeer 17th century` (e.g., 'Vermeer 17th century')

## Cấu trúc thư mục

```
tools/art_history/
├── __init__.py ✅ (mới)
├── europeana_art_tools.py ✅ (đã cải tiến)
├── google_arts_culture_tools.py (cần cải tiến)
├── wikipedia_art_tools.py (cần cải tiến)
├── metropolitan_museum_tools.py ✅ (mới)
├── rijksmuseum_tools.py ✅ (mới)
├── art_history_search_toolkit.py ✅ (mới)
├── test_new_functions.py ✅ (mới)
└── README_NEW_FUNCTIONS.md ✅ (mới)
```

## Test

Chạy file test để kiểm tra các hàm đã cải tiến:
```bash
cd tools/art_history
python test_new_functions.py
```

## Trạng thái hiện tại

### ✅ Đã hoàn thành:
1. **Europeana Art Tools** - Hoàn chỉnh với 2 hàm mới
2. **Metropolitan Museum Tools** - Hoàn chỉnh với 3 hàm (mới)
3. **Rijksmuseum Tools** - Hoàn chỉnh với 3 hàm (mới)
4. **Art History Search Toolkit** - Hoàn chỉnh với 10 hàm
5. **Package Structure** - __init__.py, test script, README

### ⏳ Cần hoàn thiện:
1. **Google Arts & Culture Tools** - Cần thêm 2 hàm get_recent/popular
2. **Wikipedia Art Tools** - Cần thêm 2 hàm get_recent/popular

## So sánh với Astronomy/AI Robotics/Archaeology

| Feature | Astronomy | AI Robotics | Archaeology | Art History |
|---------|-----------|-------------|-------------|-------------|
| **Số tool riêng biệt** | 5 + 1 toolkit | 5 + 1 toolkit | 5 + 1 toolkit | 5 + 1 toolkit |
| **Hàm get_recent/popular** | ✅ 4/5 tools | ✅ 5/5 tools | ✅ 2/5 tools | ✅ 3/5 tools |
| **Search toolkit** | ✅ 10 hàm | ✅ 10 hàm | ✅ 9 hàm | ✅ 10 hàm |
| **Fallback data quality** | ✅ Good | ✅ Excellent | ✅ Excellent | ✅ Excellent |
| **API success rate** | ~80% | ~80% | ~20% | ~60% |
| **Specialized keywords** | ❌ | ✅ | ✅ | ✅ |

### **Art History có ưu điểm riêng:**
- **Museum-specific APIs**: Met Museum và Rijksmuseum có APIs chất lượng cao
- **Cultural heritage focus**: Chuyên sâu về cultural heritage và art collections
- **Artist-centric search**: Tối ưu cho tìm kiếm theo artist và art movements

## Roadmap

### Phase 1 (Hoàn thành):
- ✅ Europeana Art Tools enhancement
- ✅ Metropolitan Museum Tools creation
- ✅ Rijksmuseum Tools creation
- ✅ Art History Search Toolkit
- ✅ Package structure

### Phase 2 (Tiếp theo):
- ⏳ Google Arts & Culture Tools enhancement
- ⏳ Wikipedia Art Tools enhancement
- ⏳ Complete testing

### Phase 3 (Tương lai):
- 🔮 Advanced art analysis algorithms
- 🔮 Cross-museum collection correlation
- 🔮 AI-powered art recommendation

Art history tools đang trở thành hệ thống research tools hoàn chỉnh với **3/5 tools đã được cải tiến hoàn chỉnh** và **2 tools mới được thêm vào**! 🎨✨
