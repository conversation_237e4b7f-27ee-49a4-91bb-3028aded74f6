#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script cho Tech Reviews Tools
"""

import sys
import os
import json
import random
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_techcrunch_tools():
    """Test TechCrunch Tools"""
    print("📰 Testing TechCrunch Tools...")
    try:
        from tools.tech_reviews.techcrunch_tools import TechCrunchTools

        tc_tool = TechCrunchTools()

        print("  - Testing TechCrunch instantiation...")
        print("    ✅ TechCrunch Tools instantiated")

        # Test get_top_new
        print("  - Testing TechCrunch get_top_new...")
        assert hasattr(tc_tool, 'get_top_new')
        print("    ✅ TechCrunch get_top_new method exists")

        # Test basic functionality
        print("  - Testing basic TechCrunch functionality...")
        result = tc_tool.search_articles("AI startup", 3)
        data = json.loads(result)
        assert "status" in data
        print("    ✅ Basic TechCrunch functionality works")

        return True

    except Exception as e:
        print(f"    ❌ TechCrunch Tools failed: {str(e)}")
        return False

def test_tech_reviews_search_toolkit():
    """Test Tech Reviews Search Toolkit"""
    print("🔍 Testing Tech Reviews Search Toolkit...")
    try:
        from tools.tech_reviews.tech_reviews_search_toolkit import TechReviewsSearchToolkit

        toolkit = TechReviewsSearchToolkit()

        print("  - Testing product reviews search...")
        result = toolkit.search_product_reviews("smartphone", "apple", "premium", "excellent")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Product reviews search works")

        print("  - Testing startup news search...")
        result = toolkit.search_startup_news("ai", "series_a", "silicon_valley", "funding")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Startup news search works")

        print("  - Testing tech analysis search...")
        result = toolkit.search_tech_analysis("ai", "market", "short_term", "business")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Tech analysis search works")

        print("  - Testing comprehensive tech search...")
        result = toolkit.comprehensive_tech_search("iPhone 15", "all", "consumer", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive tech search works")

        print("  - Testing device comparisons search...")
        result = toolkit.search_device_comparisons("smartphone", "head_to_head", "performance", "premium")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Device comparisons search works")

        return True

    except Exception as e:
        print(f"    ❌ Tech Reviews Search Toolkit failed: {str(e)}")
        return False

def test_other_tech_reviews_tools():
    """Test other tech reviews tools"""
    print("📱 Testing Other Tech Reviews Tools...")
    try:
        # Test GSMArena Tools
        from tools.tech_reviews.gsmarena_tools import GSMARenaTools

        gsm_tool = GSMARenaTools()

        print("  - Testing GSMArena Tools...")
        print("    ✅ GSMArena Tools instantiated")

        # Test The Verge Tools
        from tools.tech_reviews.theverge_tools import TheVergeTools

        verge_tool = TheVergeTools()

        print("  - Testing The Verge Tools...")
        print("    ✅ The Verge Tools instantiated")

        # Test CNET Tools
        from tools.tech_reviews.cnet_tools import CNETTools

        cnet_tool = CNETTools()

        print("  - Testing CNET Tools...")
        print("    ✅ CNET Tools instantiated")

        return True

    except Exception as e:
        print(f"    ❌ Other Tech Reviews Tools failed: {str(e)}")
        return False

def test_random_tech_reviews_functionality():
    """Test random tech reviews functionality"""
    print("\n🎲 Testing Random Tech Reviews Functionality...")

    try:
        # Random product reviews test
        from tools.tech_reviews.tech_reviews_search_toolkit import TechReviewsSearchToolkit
        toolkit = TechReviewsSearchToolkit()

        product_types = ["smartphone", "laptop", "tablet", "wearable", "smart_home"]
        product_type = random.choice(product_types)
        result = toolkit.search_product_reviews(product_type, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random product reviews {product_type} search test passed")

        # Random startup news test
        industries = ["ai", "fintech", "healthtech", "edtech", "cleantech"]
        industry = random.choice(industries)
        result = toolkit.search_startup_news(industry, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random startup news {industry} test passed")

        # Random tech analysis test
        technologies = ["ai", "blockchain", "quantum", "iot", "5g"]
        technology = random.choice(technologies)
        result = toolkit.search_tech_analysis(technology, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random tech analysis {technology} test passed")

        return True

    except Exception as e:
        print(f"    ❌ Random Tech Reviews Functionality failed: {str(e)}")
        return False

def test_tech_reviews_search_variations():
    """Test various tech reviews search variations"""
    print("\n📊 Testing Tech Reviews Search Variations...")

    try:
        from tools.tech_reviews.tech_reviews_search_toolkit import TechReviewsSearchToolkit
        toolkit = TechReviewsSearchToolkit()

        # Test comprehensive search with different parameters
        print("  - Testing comprehensive search variations...")
        result = toolkit.comprehensive_tech_search("MacBook Pro", "reviews", "consumer", "advanced")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive search variations work")

        # Test device comparisons with different parameters
        print("  - Testing device comparisons variations...")
        result = toolkit.search_device_comparisons("", "category_roundup", "camera", "mid_range")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Device comparisons variations work")

        return True

    except Exception as e:
        print(f"    ❌ Tech Reviews Search Variations failed: {str(e)}")
        return False

def test_techcrunch_get_top_new():
    """Test TechCrunch get_top_new functionality"""
    print("\n⚡ Testing TechCrunch get_top_new...")

    try:
        from tools.tech_reviews.techcrunch_tools import TechCrunchTools

        tc_tool = TechCrunchTools()

        # Test get_top_new with different content types
        content_types = ["articles", "startups", "funding", "reviews", "events"]

        for content_type in content_types:
            print(f"  - Testing get_top_new for {content_type}...")
            result = tc_tool.get_top_new(content_type, 5, "week", "ai")
            data = json.loads(result)
            assert data["status"] == "success"
            print(f"    ✅ get_top_new for {content_type} works")

        return True

    except Exception as e:
        print(f"    ❌ TechCrunch get_top_new failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 TECH REVIEWS TOOLS TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing Tech Reviews channel tools...")
    print()

    test_results = []

    # Test all tech reviews tools
    test_functions = [
        ("TechCrunch Tools", test_techcrunch_tools),
        ("Tech Reviews Search Toolkit", test_tech_reviews_search_toolkit),
        ("Other Tech Reviews Tools", test_other_tech_reviews_tools),
        ("Random Tech Reviews Functionality", test_random_tech_reviews_functionality),
        ("Tech Reviews Search Variations", test_tech_reviews_search_variations),
        ("TechCrunch get_top_new", test_techcrunch_get_top_new)
    ]

    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()

    # Summary
    print("\n" + "="*60)
    print("📋 TECH REVIEWS TOOLS TEST SUMMARY")
    print("="*60)

    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")

    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 All tech reviews tools are working correctly!")
        print("✨ Tech Reviews channel fully functional!")
        print("📱 Ready for tech analysis and product reviews!")
    elif passed >= total * 0.8:
        print("✅ Excellent performance - most functionality working!")
    elif passed >= total * 0.6:
        print("✅ Good performance - majority working!")
    else:
        print("⚠️  Some issues detected. Please check the error messages above.")

    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
