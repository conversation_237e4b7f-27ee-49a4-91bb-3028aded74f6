#!/usr/bin/env python3
"""
Simple test để debug cosmology workflow
"""

import sys
import logging

# Add current directory to path
sys.path.append('.')

# Setup logging
logging.basicConfig(level=logging.INFO)

def test_simple_workflow():
    """Test workflow đơn giản"""
    print("🧪 Testing Simple Cosmology Workflow")
    
    try:
        from cosmology_fb import CosmologyFbWorkflow
        
        # Tạo workflow
        print("📝 Creating workflow...")
        workflow = CosmologyFbWorkflow()
        print("✅ Workflow created successfully!")
        
        # Test team riêng biệt
        print("🔬 Testing team research...")
        team_result = workflow.cosmology_research_team.run(
            "What is dark matter? Provide a brief explanation."
        )
        
        print("📄 Team result type:", type(team_result))
        if hasattr(team_result, 'content'):
            print("📄 Team result content:", team_result.content[:200] + "..." if len(team_result.content) > 200 else team_result.content)
        else:
            print("📄 Team result:", str(team_result)[:200] + "..." if len(str(team_result)) > 200 else str(team_result))
        
        print("✅ Team test completed!")
        
        # Test writer riêng biệt
        print("✍️ Testing writer agent...")
        writer_result = workflow.writer_agent.run(
            "Create a short Facebook post about dark matter being mysterious invisible matter in the universe."
        )
        
        print("📄 Writer result type:", type(writer_result))
        if hasattr(writer_result, '__iter__') and not isinstance(writer_result, str):
            print("📄 Writer result is iterable")
            for i, response in enumerate(writer_result):
                if i > 2:  # Limit to first 3 responses
                    print("📄 ... (more responses)")
                    break
                print(f"📄 Response {i+1}:", str(response)[:100] + "..." if len(str(response)) > 100 else str(response))
        else:
            print("📄 Writer result:", str(writer_result)[:200] + "..." if len(str(writer_result)) > 200 else str(writer_result))
        
        print("✅ Writer test completed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🚀 Starting Simple Cosmology Test\n")
    
    success = test_simple_workflow()
    
    if success:
        print("\n🎉 Simple test completed successfully!")
    else:
        print("\n❌ Simple test failed.")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)
