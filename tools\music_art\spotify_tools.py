from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json

class SpotifyTool(Toolkit):
    """
    Công cụ tìm kiếm Spotify giúp tìm kiếm bài h<PERSON>t, ngh<PERSON> sĩ, album và playlist từ Spotify.
    
    <PERSON><PERSON><PERSON> từ khóa tìm kiếm gợi ý:
    - <PERSON><PERSON><PERSON> b<PERSON><PERSON>, nghệ sĩ, album
    - Thể loại nh<PERSON> (pop, rock, hip hop, v.v.)
    - Playlist theo tâm trạng (chill, workout, focus)
    - <PERSON><PERSON><PERSON> hát theo năm phát hành
    - <PERSON><PERSON><PERSON> xế<PERSON> hạng (top hits, viral 50)
    """
    def __init__(self):
        super().__init__(
            name="Công cụ tìm kiếm Spotify",
            tools=[self.search_spotify]
        )
        self.api_url = "https://api.spotify.com/v1"
        self.search_types = ["track", "artist", "album", "playlist"]

    def search_spotify(self, query: str, type_: str = "track", limit: int = 5) -> str:
        """
        Tìm kiếm trên Spotify theo từ khóa và loại kết quả.
        
        Args:
            query: Từ khóa tìm kiếm (tên bài hát, nghệ sĩ, album...)
            type_: Loại kết quả (track, artist, album, playlist)
            limit: Số lượng kết quả trả về (tối đa 50)
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm Spotify với từ khóa: {query}, loại: {type_}")
        
        if type_ not in self.search_types:
            type_ = "track"
            
        limit = max(1, min(limit, 50))  # Giới hạn trong khoảng 1-50
        
        try:
            # Giả lập kết quả tìm kiếm
            results = []
            
            if type_ == "track":
                results = [
                    {
                        "title": f"{query} - Single Version",
                        "artist": f"Nghệ sĩ của {query}",
                        "album": f"Album của {query}",
                        "release_date": "2025-01-01",
                        "duration_ms": 180000,
                        "popularity": 85,
                        "preview_url": f"https://p.scdn.co/mp3-preview/abc123",
                        "external_url": f"https://open.spotify.com/track/abc123",
                        "image_url": "https://i.scdn.co/image/abc123"
                    } for _ in range(limit)
                ]
            elif type_ == "artist":
                results = [
                    {
                        "name": f"{query} {i+1}",
                        "genres": ["Pop", "R&B"],
                        "followers": 1000000,
                        "popularity": 90,
                        "external_url": f"https://open.spotify.com/artist/abc{i}",
                        "image_url": "https://i.scdn.co/image/abc123"
                    } for i in range(limit)
                ]
            elif type_ == "album":
                results = [
                    {
                        "name": f"{query} (Deluxe Edition)",
                        "artist": f"Nghệ sĩ của {query}",
                        "release_date": "2025-01-01",
                        "total_tracks": 12,
                        "album_type": "album",
                        "external_url": f"https://open.spotify.com/album/abc{i}",
                        "image_url": "https://i.scdn.co/image/abc123"
                    } for i in range(limit)
                ]
            elif type_ == "playlist":
                results = [
                    {
                        "name": f"{query} Mix {i+1}",
                        "owner": "Spotify",
                        "description": f"Tuyển tập các bài hát {query} hay nhất",
                        "tracks_count": 50,
                        "external_url": f"https://open.spotify.com/playlist/abc{i}",
                        "image_url": "https://i.scdn.co/image/abc123"
                    } for i in range(limit)
                ]
            
            result = {
                "status": "success",
                "source": "Spotify",
                "query": query,
                "type": type_,
                "limit": limit,
                "results": results
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Spotify: {str(e)}")
            result = {
                "status": "error",
                "source": "Spotify",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"{query} - Single Version",
                        "artist": f"Nghệ sĩ của {query}",
                        "url": "https://open.spotify.com/track/xyz",
                        "summary": f"Bài hát {query} phổ biến trên Spotify"
                    },
                    {
                        "title": f"Top bài hát {query}",
                        "url": f"https://open.spotify.com/search/{query}/tracks",
                        "summary": f"Danh sách các bài hát {query} phổ biến nhất"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
