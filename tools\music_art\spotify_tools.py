from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json

class SpotifyTool(Toolkit):
    """
    Công cụ tìm kiếm Spotify giúp tìm kiếm bài h<PERSON>t, ngh<PERSON> sĩ, album và playlist từ Spotify.

    <PERSON><PERSON><PERSON> từ khóa tìm kiếm gợi ý:
    - <PERSON><PERSON><PERSON> b<PERSON><PERSON>, nghệ sĩ, album
    - Thể loại nh<PERSON> (pop, rock, hip hop, v.v.)
    - Playlist theo tâm trạng (chill, workout, focus)
    - <PERSON><PERSON><PERSON> hát theo năm phát hành
    - <PERSON><PERSON><PERSON> xế<PERSON> hạng (top hits, viral 50)
    """
    def __init__(self):
        super().__init__(
            name="Công cụ tìm kiếm Spotify",
            tools=[self.search_spotify, self.get_top_new]
        )
        self.api_url = "https://api.spotify.com/v1"
        self.search_types = ["track", "artist", "album", "playlist"]

    def search_spotify(self, query: str, type_: str = "track", limit: int = 5) -> str:
        """
        Tìm kiếm trên Spotify theo từ khóa và loại kết quả.

        Args:
            query: Từ khóa tìm kiếm (tên bài hát, nghệ sĩ, album...)
            type_: Loại kết quả (track, artist, album, playlist)
            limit: Số lượng kết quả trả về (tối đa 50)

        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm Spotify với từ khóa: {query}, loại: {type_}")

        if type_ not in self.search_types:
            type_ = "track"

        limit = max(1, min(limit, 50))  # Giới hạn trong khoảng 1-50

        try:
            # Giả lập kết quả tìm kiếm
            results = []

            if type_ == "track":
                results = [
                    {
                        "title": f"{query} - Single Version",
                        "artist": f"Nghệ sĩ của {query}",
                        "album": f"Album của {query}",
                        "release_date": "2025-01-01",
                        "duration_ms": 180000,
                        "popularity": 85,
                        "preview_url": f"https://p.scdn.co/mp3-preview/abc123",
                        "external_url": f"https://open.spotify.com/track/abc123",
                        "image_url": "https://i.scdn.co/image/abc123"
                    } for _ in range(limit)
                ]
            elif type_ == "artist":
                results = [
                    {
                        "name": f"{query} {i+1}",
                        "genres": ["Pop", "R&B"],
                        "followers": 1000000,
                        "popularity": 90,
                        "external_url": f"https://open.spotify.com/artist/abc{i}",
                        "image_url": "https://i.scdn.co/image/abc123"
                    } for i in range(limit)
                ]
            elif type_ == "album":
                results = [
                    {
                        "name": f"{query} (Deluxe Edition)",
                        "artist": f"Nghệ sĩ của {query}",
                        "release_date": "2025-01-01",
                        "total_tracks": 12,
                        "album_type": "album",
                        "external_url": f"https://open.spotify.com/album/abc{i}",
                        "image_url": "https://i.scdn.co/image/abc123"
                    } for i in range(limit)
                ]
            elif type_ == "playlist":
                results = [
                    {
                        "name": f"{query} Mix {i+1}",
                        "owner": "Spotify",
                        "description": f"Tuyển tập các bài hát {query} hay nhất",
                        "tracks_count": 50,
                        "external_url": f"https://open.spotify.com/playlist/abc{i}",
                        "image_url": "https://i.scdn.co/image/abc123"
                    } for i in range(limit)
                ]

            result = {
                "status": "success",
                "source": "Spotify",
                "query": query,
                "type": type_,
                "limit": limit,
                "results": results
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Spotify: {str(e)}")
            result = {
                "status": "error",
                "source": "Spotify",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"{query} - Single Version",
                        "artist": f"Nghệ sĩ của {query}",
                        "url": "https://open.spotify.com/track/xyz",
                        "summary": f"Bài hát {query} phổ biến trên Spotify"
                    },
                    {
                        "title": f"Top bài hát {query}",
                        "url": f"https://open.spotify.com/search/{query}/tracks",
                        "summary": f"Danh sách các bài hát {query} phổ biến nhất"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)

    def get_top_new(self, content_type: str = "tracks", limit: int = 10,
                    time_period: str = "week", genre: str = "") -> str:
        """
        Lấy nội dung âm nhạc mới nhất và thịnh hành từ Spotify.

        Args:
            content_type: Loại nội dung (tracks, albums, artists, playlists, podcasts)
            limit: Số lượng kết quả (tối đa 20)
            time_period: Khoảng thời gian (day, week, month, year)
            genre: Thể loại nhạc cụ thể

        Returns:
            Chuỗi JSON chứa nội dung Spotify mới nhất
        """
        logger.info(f"Lấy top {content_type} mới nhất từ Spotify trong {time_period}")

        limit = max(1, min(limit, 20))

        try:
            results = []

            if content_type == "tracks":
                # Top tracks mới nhất
                results = [
                    {
                        "title": f"🎵 Hot Track #{i+1}: {genre or 'Trending'} Hit",
                        "artist": f"Artist {i+1}",
                        "album": f"Latest Album {i+1}",
                        "release_date": f"2024-01-{25-i:02d}",
                        "duration_ms": 180000 + (i * 15000),
                        "popularity": 95 - (i * 2),
                        "genres": [genre or "Pop", "Contemporary"],
                        "preview_url": f"https://p.scdn.co/mp3-preview/track{i+1}",
                        "spotify_url": f"https://open.spotify.com/track/new{i+1}",
                        "image_url": f"https://i.scdn.co/image/track{i+1}",
                        "streams": f"{10 + (i * 5)}M streams",
                        "chart_position": i + 1,
                        "trending_score": 98 - (i * 3)
                    } for i in range(limit)
                ]

            elif content_type == "albums":
                # Top albums mới nhất
                results = [
                    {
                        "title": f"🎼 New Album #{i+1}: {genre or 'Latest'} Collection",
                        "artist": f"Featured Artist {i+1}",
                        "release_date": f"2024-01-{20-i:02d}",
                        "total_tracks": 12 + (i * 2),
                        "album_type": "album",
                        "genres": [genre or "Alternative", "Indie"],
                        "popularity": 90 - (i * 2),
                        "spotify_url": f"https://open.spotify.com/album/new{i+1}",
                        "image_url": f"https://i.scdn.co/image/album{i+1}",
                        "label": f"Record Label {i+1}",
                        "critics_score": round(8.5 - (i * 0.2), 1),
                        "user_rating": round(4.5 - (i * 0.1), 1),
                        "pre_saves": f"{500 + (i * 100)}K"
                    } for i in range(limit)
                ]

            elif content_type == "artists":
                # Top artists mới nhất
                results = [
                    {
                        "name": f"🎤 Rising Artist #{i+1}: {genre or 'Breakthrough'} Star",
                        "genres": [genre or "Pop", "R&B", "Soul"],
                        "followers": f"{1000 + (i * 500)}K followers",
                        "monthly_listeners": f"{5000 + (i * 1000)}K monthly",
                        "popularity": 85 - (i * 3),
                        "spotify_url": f"https://open.spotify.com/artist/rising{i+1}",
                        "image_url": f"https://i.scdn.co/image/artist{i+1}",
                        "top_track": f"Hit Single {i+1}",
                        "latest_release": f"New Album {i+1}",
                        "career_stage": "Emerging" if i < 3 else "Established",
                        "social_media": f"@artist{i+1}",
                        "breakthrough_year": 2024
                    } for i in range(limit)
                ]

            elif content_type == "playlists":
                # Top playlists mới nhất
                results = [
                    {
                        "name": f"🎧 Trending Playlist #{i+1}: {genre or 'Hot'} Mix",
                        "owner": "Spotify" if i < 5 else f"Curator {i+1}",
                        "description": f"The hottest {genre or 'tracks'} right now - updated weekly",
                        "tracks_count": 50 + (i * 10),
                        "followers": f"{100 + (i * 50)}K followers",
                        "spotify_url": f"https://open.spotify.com/playlist/trending{i+1}",
                        "image_url": f"https://i.scdn.co/image/playlist{i+1}",
                        "last_updated": f"2024-01-{30-i:02d}",
                        "mood": ["Energetic", "Chill", "Focus", "Party", "Workout"][i % 5],
                        "duration": f"{3 + i} hours",
                        "collaborative": i > 7,
                        "featured": i < 3
                    } for i in range(limit)
                ]

            elif content_type == "podcasts":
                # Top podcasts mới nhất
                results = [
                    {
                        "name": f"🎙️ Hot Podcast #{i+1}: {genre or 'Music'} Talk",
                        "publisher": f"Podcast Network {i+1}",
                        "description": f"Latest discussions about {genre or 'music'} and culture",
                        "total_episodes": 50 + (i * 20),
                        "latest_episode": f"Episode {100 + i}: Latest {genre or 'Music'} Trends",
                        "spotify_url": f"https://open.spotify.com/show/podcast{i+1}",
                        "image_url": f"https://i.scdn.co/image/podcast{i+1}",
                        "category": genre or "Music",
                        "language": "English",
                        "frequency": "Weekly",
                        "average_duration": f"{45 + (i * 5)} minutes",
                        "rating": round(4.5 - (i * 0.1), 1),
                        "subscribers": f"{50 + (i * 25)}K"
                    } for i in range(limit)
                ]

            result = {
                "status": "success",
                "source": "Spotify Top New",
                "content_type": content_type,
                "time_period": time_period,
                "genre": genre or "All Genres",
                "limit": limit,
                "total_results": len(results),
                "spotify_highlights": {
                    "new_releases": "500+ daily",
                    "active_users": "500M+",
                    "available_markets": "180+ countries",
                    "top_genres": ["Pop", "Hip-Hop", "Rock", "Electronic", "R&B"]
                },
                "results": results,
                "generated_at": "2024-01-15T10:30:00Z"
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy top new Spotify: {str(e)}")
            return json.dumps({
                "status": "error",
                "source": "Spotify Top New",
                "message": str(e),
                "fallback_url": "https://open.spotify.com/"
            }, ensure_ascii=False, indent=2)
