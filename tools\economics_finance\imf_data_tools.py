from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class IMFDataTool(Toolkit):
    """
    IMF Data Tool cho tìm kiếm dữ liệu tài chính, kinh tế vĩ mô từ IMF Data.
    """

    def __init__(self):
        super().__init__(
            name="IMF Data Search Tool",
            tools=[self.search_imf_data]
        )

    async def search_imf_data(
        self,
        query: str,
        country: Optional[str] = None,
        indicator: Optional[str] = None,
        year_range: Optional[str] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        Tìm kiếm IMF Data cho dữ liệu tài chính, kinh tế vĩ mô.

        Parameters:
        - query: Từ khóa tìm kiếm (ví dụ: 'GDP', 'inflation', 'current account', 'unemployment')
        - country: Tên quốc gia hoặc mã ISO (ví dụ: 'Vietnam', 'USA', 'CHN')
        - indicator: Mã chỉ số hoặc tên chỉ số (ví dụ: 'NGDP_RPCH', 'LUR', 'PCPI')
        - year_range: Khoảng năm (ví dụ: '2015-2022')
        - limit: Số lượng kết quả tối đa (default: 10)

        Returns:
        - JSON với dữ liệu chỉ số, quốc gia, năm, giá trị, link IMF Data
        """
        logger.info(f"Tìm kiếm IMF Data: query={query}, country={country}, indicator={indicator}, year_range={year_range}, limit={limit}")

        try:
            # IMF SDMX API endpoint (ví dụ: https://dataservices.imf.org/REST/SDMX_JSON.svc/CompactData/IFS/M.VNM.NGDP_RPCH)
            base_url = "https://dataservices.imf.org/REST/SDMX_JSON.svc/CompactData"
            dataset = "IFS"  # International Financial Statistics
            country_code = country.upper() if country and len(country) == 3 else None

            # Nếu không có mã quốc gia, lấy theo tên (giản lược, thực tế cần mapping)
            if not country_code and country:
                # Mapping đơn giản, thực tế nên dùng bảng mã ISO
                iso_map = {"Vietnam": "VNM", "United States": "USA", "China": "CHN"}
                country_code = iso_map.get(country, None)

            # Nếu không có indicator, dùng query để tìm mã (giản lược)
            indicator_code = indicator
            if not indicator_code and query:
                # Mapping đơn giản, thực tế nên dùng bảng mã chỉ số IMF
                indicator_map = {
                    "GDP": "NGDP_RPCH",
                    "inflation": "PCPI",
                    "unemployment": "LUR",
                    "current account": "BCA"
                }
                indicator_code = indicator_map.get(query.lower(), None)

            # Nếu không đủ thông tin, trả về lỗi
            if not country_code or not indicator_code:
                return {
                    "status": "error",
                    "source": "IMF Data",
                    "message": "Cần cung cấp country (mã ISO) và indicator (mã chỉ số) hợp lệ.",
                    "query": query
                }

            # Xây dựng URL truy vấn
            url = f"{base_url}/{dataset}/M.{country_code}.{indicator_code}"
            params = {}
            if year_range:
                # year_range dạng '2015-2022'
                years = year_range.split('-')
                if len(years) == 2:
                    params["startPeriod"] = years[0]
                    params["endPeriod"] = years[1]

            response = requests.get(url, params=params, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "IMF Data",
                    "message": f"IMF API trả về mã lỗi {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            # Dữ liệu nằm trong: data['CompactData']['DataSet']['Series']['Obs']
            try:
                series = data.get("CompactData", {}).get("DataSet", {}).get("Series", {})
                obs_list = series.get("Obs", []) if isinstance(series, dict) else []
                if isinstance(obs_list, dict):
                    obs_list = [obs_list]
                for obs in obs_list[:limit]:
                    results.append({
                        "country": country,
                        "indicator": indicator_code,
                        "year": obs.get("@TIME_PERIOD"),
                        "value": obs.get("@OBS_VALUE"),
                        "imf_url": f"https://data.imf.org/?sk=4C514D48-B6BA-49ED-8AB9-52B0C1A0179B"
                    })
            except Exception as parse_err:
                log_debug(f"Lỗi phân tích dữ liệu IMF: {str(parse_err)}")

            return {
                "status": "success",
                "source": "IMF Data",
                "query": query,
                "country": country,
                "indicator": indicator_code,
                "year_range": year_range,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "<country> GDP <year_range>",
                    "<country> inflation <year_range>",
                    "<country> unemployment <year_range>",
                    "<country> current account <year_range>",
                    "<indicator> <country> <year_range>"
                ],
                "official_data_url": "https://data.imf.org/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm IMF Data: {str(e)}")
            return {
                "status": "error",
                "source": "IMF Data",
                "message": str(e),
                "query": query
            }
