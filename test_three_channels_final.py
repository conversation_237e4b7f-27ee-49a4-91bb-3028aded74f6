#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final Test Script cho 3 kênh còn lại với đúng method names:
- Literature
- Military History  
- Medical Science
"""

import sys
import os
import json
import random
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_literature_final():
    """Test Literature Tools với đúng method names"""
    print("📚 Testing Literature Tools...")
    try:
        # Test literature search toolkit
        from tools.literature.literature_search_toolkit import LiteratureSearchToolkit
        
        toolkit = LiteratureSearchToolkit()
        
        print("  - Testing Literature Search Toolkit...")
        result = toolkit.search_literary_works("Shakespeare", "", "drama", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Literature Search Toolkit works")
        
        # Test literature analyzer với đúng method name
        from tools.literature.literature_analyzer import LiteratureAnalyzer
        
        analyzer = LiteratureAnalyzer()
        
        print("  - Testing Literature Analyzer...")
        result = analyzer.analyze_literary_trends("20th_century", "western", "poetry", "movements")
        data = json.loads(result)
        assert "analysis_parameters" in data
        print("    ✅ Literature Analyzer works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Literature Tools failed: {str(e)}")
        return False

def test_military_final():
    """Test Military History Tools với đúng method names"""
    print("⚔️ Testing Military History Tools...")
    try:
        # Test military search toolkit
        from tools.military_history.military_search_toolkit import MilitarySearchToolkit
        
        toolkit = MilitarySearchToolkit()
        
        print("  - Testing Military Search Toolkit...")
        result = toolkit.search_military_conflicts("World War II", "all", "global", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Military Search Toolkit works")
        
        # Test military analyzer với đúng method name
        from tools.military_history.military_analyzer import MilitaryAnalyzer
        
        analyzer = MilitaryAnalyzer()
        
        print("  - Testing Military Analyzer...")
        result = analyzer.analyze_warfare_evolution("modern", "20th_century", "technology", "comprehensive")
        data = json.loads(result)
        assert "analysis_parameters" in data
        print("    ✅ Military Analyzer works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Military History Tools failed: {str(e)}")
        return False

def test_medical_final():
    """Test Medical Science Tools với đúng method names"""
    print("🏥 Testing Medical Science Tools...")
    try:
        # Test medical search toolkit
        from tools.medical_science.medical_search_toolkit import MedicalSearchToolkit
        
        toolkit = MedicalSearchToolkit()
        
        print("  - Testing Medical Search Toolkit...")
        result = toolkit.search_medical_literature("diabetes", "all", "5years", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Medical Search Toolkit works")
        
        # Test medical analyzer với đúng method name
        from tools.medical_science.medical_analyzer import MedicalAnalyzer
        
        analyzer = MedicalAnalyzer()
        
        print("  - Testing Medical Analyzer...")
        result = analyzer.analyze_medical_trends("diabetes", "global", "chronic_diseases", "comprehensive")
        data = json.loads(result)
        assert "analysis_parameters" in data
        print("    ✅ Medical Analyzer works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Medical Science Tools failed: {str(e)}")
        return False

def test_comprehensive_functionality():
    """Test comprehensive functionality"""
    print("\n🔍 Testing Comprehensive Functionality...")
    
    try:
        # Literature comprehensive search
        from tools.literature.literature_search_toolkit import LiteratureSearchToolkit
        lit_toolkit = LiteratureSearchToolkit()
        
        print("  - Testing Literature Comprehensive Search...")
        result = lit_toolkit.comprehensive_literature_search("Victorian novel", "all", "english", "general")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Literature Comprehensive Search works")
        
        # Medical comprehensive search
        from tools.medical_science.medical_search_toolkit import MedicalSearchToolkit
        med_toolkit = MedicalSearchToolkit()
        
        print("  - Testing Medical Comprehensive Search...")
        result = med_toolkit.comprehensive_medical_search("cancer treatment", "all", "high", "oncology")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Medical Comprehensive Search works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Comprehensive Functionality failed: {str(e)}")
        return False

def test_random_cross_functionality():
    """Test random cross-functionality"""
    print("\n🎲 Testing Random Cross-Functionality...")
    
    try:
        # Random literature test
        from tools.literature.literature_search_toolkit import LiteratureSearchToolkit
        lit_toolkit = LiteratureSearchToolkit()
        
        authors = ["Shakespeare", "Dickens", "Austen", "Hemingway"]
        author = random.choice(authors)
        result = lit_toolkit.search_authors_comprehensive(author, "", "", True)
        data = json.loads(result)
        assert "search_parameters" in data
        print("  🎯 Random Literature test passed")
        
        # Random military test
        from tools.military_history.military_search_toolkit import MilitarySearchToolkit
        mil_toolkit = MilitarySearchToolkit()
        
        conflicts = ["World War I", "World War II", "Vietnam War", "Korean War"]
        conflict = random.choice(conflicts)
        result = mil_toolkit.search_military_conflicts(conflict, "all", "global", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("  🎯 Random Military test passed")
        
        # Random medical test
        from tools.medical_science.medical_search_toolkit import MedicalSearchToolkit
        med_toolkit = MedicalSearchToolkit()
        
        conditions = ["diabetes", "cancer", "heart disease", "alzheimer"]
        condition = random.choice(conditions)
        result = med_toolkit.search_medical_conditions(condition, "comprehensive", True, True)
        data = json.loads(result)
        assert "search_parameters" in data
        print("  🎯 Random Medical test passed")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Random Cross-Functionality failed: {str(e)}")
        return False

def test_analyzer_insights():
    """Test analyzer insights generation"""
    print("\n💡 Testing Analyzer Insights...")
    
    try:
        # Literature insights
        from tools.literature.literature_analyzer import LiteratureAnalyzer
        lit_analyzer = LiteratureAnalyzer()
        
        print("  - Testing Literature Insights...")
        result = lit_analyzer.generate_literary_insights("comprehensive", "patterns", "modern", "multicultural")
        data = json.loads(result)
        assert "insight_generation" in data
        print("    ✅ Literature Insights work")
        
        # Medical insights
        from tools.medical_science.medical_analyzer import MedicalAnalyzer
        med_analyzer = MedicalAnalyzer()
        
        print("  - Testing Medical Insights...")
        result = med_analyzer.generate_medical_insights("comprehensive", "trends", "current", "global")
        data = json.loads(result)
        assert "insight_generation" in data
        print("    ✅ Medical Insights work")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Analyzer Insights failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 THREE CHANNELS FINAL TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Final testing of Literature, Military History, and Medical Science...")
    print()
    
    test_results = []
    
    # Test all functionality
    test_functions = [
        ("Literature Tools", test_literature_final),
        ("Military History Tools", test_military_final),
        ("Medical Science Tools", test_medical_final),
        ("Comprehensive Functionality", test_comprehensive_functionality),
        ("Random Cross-Functionality", test_random_cross_functionality),
        ("Analyzer Insights", test_analyzer_insights)
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()
    
    # Summary
    print("\n" + "="*60)
    print("📋 THREE CHANNELS FINAL TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All three channels working perfectly!")
        print("✨ Literature, Military History, and Medical Science fully verified!")
    elif passed >= total * 0.8:
        print("✅ Excellent performance - most functionality working correctly!")
    elif passed >= total * 0.6:
        print("✅ Good performance - majority of functionality working!")
    else:
        print("⚠️  Some issues detected. Please check the error messages above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
