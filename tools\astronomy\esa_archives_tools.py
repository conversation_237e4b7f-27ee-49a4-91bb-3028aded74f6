import json
import time
import requests
from typing import Optional, Dict, List, Any
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class EsaArchivesTools(Toolkit):
    def __init__(self, search_hubble_papers: bool = True, timeout: int = 10,
                 max_retries: int = 3, **kwargs):
        super().__init__(name="esa_archives_tools", **kwargs)
        # Cập nhật URL chính xác
        self.base_url = "https://hst.esac.esa.int/ehst-sl-server/servlet/data-action"
        self.timeout = timeout
        self.max_retries = max_retries

        # Khởi tạo cache đơn giản
        self.cache = {}

        if search_hubble_papers:
            self.register(self.search_esa_hubble)
            self.register(self.get_recent_observations)

    def search_esa_hubble(self, query: str, max_results: int = 5) -> str:
        """
        Search the ESA Hubble Science Archive using a keyword.
        Args:
            query (str): Keyword to search for.
            max_results (int): Number of results to return (default: 5).
        Returns:
            str: JSON string of basic results.
        """
        log_debug(f"Searching ESA Hubble Archive for: {query}")

        # Kiểm tra cache
        cache_key = f"{query}_{max_results}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for: {query}")
            return self.cache[cache_key]

        # Fallback data nếu API không hoạt động
        fallback_data = [
            {
                "observation_id": "fallback_data",
                "target_name": query,
                "start_time": "",
                "instrument": "Fallback Data",
                "exposure": "",
                "link": "https://archives.esac.esa.int/ehst/",
                "note": "This is fallback data due to API unavailability"
            }
        ]

        params = {
            "REQUEST": "doQuery",
            "LANG": "ADQL",
            "QUERY": f"SELECT * FROM ehst.science WHERE target_name LIKE '%{query}%'",
            "FORMAT": "json"
        }

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"ESA Hubble Archive attempt {attempt+1}/{self.max_retries}")
                response = requests.get(
                    self.base_url,
                    params=params,
                    timeout=self.timeout
                )
                response.raise_for_status()
                results = response.json()

                filtered_results = results.get("data", [])[:max_results]
                output = []
                for item in filtered_results:
                    output.append({
                        "observation_id": item.get("observation_id"),
                        "target_name": item.get("target_name"),
                        "start_time": item.get("start_time"),
                        "instrument": item.get("instrument_name"),
                        "exposure": item.get("exposure_time"),
                        "link": f"https://archives.esac.esa.int/ehst/#obsid={item.get('observation_id')}"
                    })

                result_json = json.dumps(output, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"ESA Hubble Archive timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"ESA Hubble Archive request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"ESA Hubble Archive unexpected error: {e}")
                break

        # Trả về dữ liệu fallback nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to search ESA Hubble Archive failed for query: {query}")
        logger.info(f"Returning fallback data for query: {query}")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json  # Cache fallback data
        return fallback_json

    def get_recent_observations(self, limit: int = 10, days_back: int = 30, instrument: str = None) -> str:
        """
        Get recent observations from ESA Hubble Science Archive.
        Args:
            limit (int): Number of observations to return (default: 10).
            days_back (int): Number of days to look back (default: 30).
            instrument (str): Optional instrument filter (e.g., 'WFC3', 'ACS').
        Returns:
            str: JSON string of recent observations.
        """
        log_debug(f"Getting recent observations: limit={limit}, days_back={days_back}, instrument={instrument}")

        # Tạo cache key
        cache_key = f"recent_obs_{limit}_{days_back}_{instrument or 'all'}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for recent observations")
            return self.cache[cache_key]

        # Tạo fallback data cho recent observations
        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)

        fallback_data = [
            {
                "observation_id": f"recent_obs_{i+1}",
                "target_name": f"Recent Target {i+1}",
                "start_time": (end_date - timedelta(days=i*2)).strftime("%Y-%m-%d"),
                "instrument": instrument or "WFC3",
                "exposure": f"{300 + i*100}s",
                "link": "https://archives.esac.esa.int/ehst/",
                "note": "This is fallback data for recent observations",
                "is_recent": True,
                "days_ago": i*2
            }
            for i in range(min(limit, 5))
        ]

        # Tạo query cho observations gần đây
        date_filter = f"start_time >= '{start_date.strftime('%Y-%m-%d')}'"
        if instrument:
            instrument_filter = f" AND instrument_name = '{instrument}'"
        else:
            instrument_filter = ""

        params = {
            "REQUEST": "doQuery",
            "LANG": "ADQL",
            "QUERY": f"SELECT * FROM ehst.science WHERE {date_filter}{instrument_filter} ORDER BY start_time DESC",
            "FORMAT": "json"
        }

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"ESA recent observations attempt {attempt+1}/{self.max_retries}")
                response = requests.get(
                    self.base_url,
                    params=params,
                    timeout=self.timeout
                )
                response.raise_for_status()
                results = response.json()

                filtered_results = results.get("data", [])[:limit]
                output = []
                for item in filtered_results:
                    # Tính số ngày từ observation
                    try:
                        obs_date = datetime.strptime(item.get("start_time", ""), "%Y-%m-%d")
                        days_ago = (end_date - obs_date).days
                    except:
                        days_ago = None

                    output.append({
                        "observation_id": item.get("observation_id"),
                        "target_name": item.get("target_name"),
                        "start_time": item.get("start_time"),
                        "instrument": item.get("instrument_name"),
                        "exposure": item.get("exposure_time"),
                        "link": f"https://archives.esac.esa.int/ehst/#obsid={item.get('observation_id')}",
                        "is_recent": True,
                        "days_ago": days_ago,
                        "filter_applied": f"last_{days_back}_days"
                    })

                result_json = json.dumps(output, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                log_debug(f"Found {len(output)} recent observations")
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"ESA recent observations timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"ESA recent observations request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"ESA recent observations unexpected error: {e}")
                break

        # Trả về dữ liệu fallback nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to get recent observations from ESA failed")
        logger.info(f"Returning fallback data for recent observations")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json  # Cache fallback data
        return fallback_json