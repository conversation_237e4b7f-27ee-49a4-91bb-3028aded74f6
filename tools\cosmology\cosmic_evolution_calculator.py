# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math
from datetime import datetime

class CosmicEvolutionCalculator(Toolkit):
    """
    Cosmic Evolution Calculator cho tính toán cosmic structure evolution, galaxy formation và universe development.
    """

    def __init__(self, enable_calculations: bool = True, **kwargs):
        super().__init__(
            name="cosmic_evolution_calculator",
            **kwargs
        )
        
        # Cosmic timeline (billion years ago)
        self.cosmic_timeline = {
            "big_bang": 13.8,
            "inflation": 13.8,
            "nucleosynthesis": 13.8,
            "recombination": 13.42,
            "first_stars": 13.6,
            "first_galaxies": 13.2,
            "galaxy_mergers": 10.0,
            "solar_system": 4.6,
            "present": 0.0
        }
        
        # Evolution rates (per billion years)
        self.evolution_rates = {
            "galaxy_formation": 0.5,
            "star_formation": 0.3,
            "heavy_element_production": 0.4,
            "structure_growth": 0.2,
            "dark_matter_clustering": 0.1,
            "cosmic_expansion": 0.07
        }
        
        # Cosmic structure scales
        self.structure_scales = {
            "quantum_fluctuations": {"size_mpc": 1e-30, "mass_solar": 1e-20},
            "dark_matter_halos": {"size_mpc": 0.1, "mass_solar": 1e12},
            "galaxies": {"size_mpc": 0.03, "mass_solar": 1e11},
            "galaxy_groups": {"size_mpc": 1, "mass_solar": 1e13},
            "galaxy_clusters": {"size_mpc": 3, "mass_solar": 1e15},
            "cosmic_web": {"size_mpc": 100, "mass_solar": 1e17}
        }
        
        if enable_calculations:
            self.register(self.calculate_cosmic_structure_evolution)
            self.register(self.estimate_galaxy_formation)
            self.register(self.analyze_universe_development)
            self.register(self.predict_cosmic_future)

    def calculate_cosmic_structure_evolution(self, structure1: str, structure2: str, 
                                           time_separation_gyr: float = None,
                                           evolution_aspect: str = "mass") -> str:
        """
        Tính toán cosmic structure evolution và gravitational hierarchy development.
        
        Args:
            structure1: Cấu trúc vũ trụ thứ nhất
            structure2: Cấu trúc vũ trụ thứ hai
            time_separation_gyr: Khoảng cách thời gian (billion years)
            evolution_aspect: Khía cạnh evolution ('mass', 'size', 'complexity')
            
        Returns:
            Chuỗi JSON chứa tính toán cosmic structure evolution
        """
        log_debug(f"Calculating cosmic structure evolution between {structure1} and {structure2}")
        
        try:
            if time_separation_gyr is None:
                time_separation_gyr = 5.0  # Default 5 billion years
            
            # Get structure parameters
            struct1_params = self.structure_scales.get(structure1, self.structure_scales["galaxies"])
            struct2_params = self.structure_scales.get(structure2, self.structure_scales["galaxy_clusters"])
            
            # Calculate evolution metrics
            evolution_rate = self.evolution_rates.get(f"{evolution_aspect}_evolution", 0.3)
            growth_factor = (1 + evolution_rate) ** time_separation_gyr
            
            # Hierarchical structure formation
            mass_ratio = struct2_params["mass_solar"] / struct1_params["mass_solar"]
            size_ratio = struct2_params["size_mpc"] / struct1_params["size_mpc"]
            
            # Dark matter evolution
            dark_matter_growth = growth_factor * 0.85  # 85% dark matter
            baryonic_matter_growth = growth_factor * 0.15  # 15% baryonic matter
            
            # Gravitational dynamics
            gravitational_binding = math.log10(mass_ratio) * 0.5
            virial_equilibrium = "Achieved" if mass_ratio > 100 else "Developing"
            
            # Structure formation mechanisms
            formation_mechanisms = {
                "gravitational_collapse": round(time_separation_gyr * 0.2, 2),
                "hierarchical_merging": round(time_separation_gyr * 0.3, 2),
                "gas_accretion": round(time_separation_gyr * 0.15, 2),
                "feedback_processes": round(time_separation_gyr * 0.1, 2)
            }
            
            # Cosmic environment effects
            environmental_factors = {
                "cosmic_web_influence": 0.7,
                "dark_energy_effects": 0.3 + (time_separation_gyr * 0.05),
                "primordial_fluctuations": 0.9 - (time_separation_gyr * 0.05),
                "reionization_impact": 0.6 if time_separation_gyr > 10 else 0.2
            }
            
            result = {
                "structure_comparison": {
                    "structure1": structure1,
                    "structure2": structure2,
                    "time_separation_gyr": time_separation_gyr,
                    "evolution_aspect": evolution_aspect
                },
                "evolution_metrics": {
                    "growth_factor": round(growth_factor, 2),
                    "mass_ratio": f"{mass_ratio:.2e}",
                    "size_ratio": round(size_ratio, 1),
                    "evolution_rate": evolution_rate
                },
                "matter_composition": {
                    "dark_matter_growth": round(dark_matter_growth, 2),
                    "baryonic_matter_growth": round(baryonic_matter_growth, 2),
                    "dark_matter_fraction": 0.85,
                    "baryonic_fraction": 0.15
                },
                "gravitational_dynamics": {
                    "gravitational_binding": round(gravitational_binding, 2),
                    "virial_equilibrium": virial_equilibrium,
                    "dynamical_time": f"{(struct2_params['size_mpc'] / 100):.1f} Gyr"
                },
                "formation_mechanisms": formation_mechanisms,
                "environmental_factors": environmental_factors,
                "observational_signatures": self._generate_observational_signatures(structure1, structure2),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error calculating cosmic structure evolution: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate cosmic structure evolution: {str(e)}"
            }, indent=4)

    def estimate_galaxy_formation(self, galaxy_type: str, formation_redshift: float = 3.0,
                                current_age_gyr: float = 10.0) -> str:
        """
        Ước tính galaxy formation và stellar evolution.
        
        Args:
            galaxy_type: Loại galaxy ('spiral', 'elliptical', 'irregular')
            formation_redshift: Redshift khi galaxy hình thành
            current_age_gyr: Tuổi hiện tại (billion years)
            
        Returns:
            Chuỗi JSON chứa ước tính galaxy formation
        """
        log_debug(f"Estimating galaxy formation for {galaxy_type} galaxy")
        
        try:
            # Galaxy formation parameters
            galaxy_params = {
                "spiral": {"star_formation_rate": 0.5, "gas_fraction": 0.3, "merger_rate": 0.1},
                "elliptical": {"star_formation_rate": 0.1, "gas_fraction": 0.05, "merger_rate": 0.3},
                "irregular": {"star_formation_rate": 0.8, "gas_fraction": 0.5, "merger_rate": 0.05}
            }
            
            params = galaxy_params.get(galaxy_type, galaxy_params["spiral"])
            
            # Calculate formation timeline
            formation_time_gyr = 13.8 - (formation_redshift * 2)  # Rough conversion
            evolution_time = current_age_gyr - formation_time_gyr
            
            # Stellar population evolution
            stellar_generations = max(1, int(evolution_time / 2))  # New generation every 2 Gyr
            metallicity_evolution = min(0.02, stellar_generations * 0.003)  # Solar metallicity = 0.02
            
            # Star formation history
            sf_history = []
            for gen in range(stellar_generations):
                gen_time = gen * 2
                sf_rate = params["star_formation_rate"] * math.exp(-gen_time / 5)  # Exponential decline
                sf_history.append({
                    "generation": gen + 1,
                    "time_gyr": gen_time,
                    "star_formation_rate": round(sf_rate, 2),
                    "metallicity": round(gen * 0.003, 4),
                    "dominant_stellar_type": self._get_dominant_stellar_type(gen)
                })
            
            # Chemical evolution
            chemical_evolution = {
                "initial_metallicity": 0.0,
                "current_metallicity": round(metallicity_evolution, 4),
                "alpha_enhancement": round(0.3 - (evolution_time * 0.02), 2),
                "heavy_element_production": round(stellar_generations * 0.1, 2)
            }
            
            # Morphological evolution
            morphology_evolution = {
                "initial_morphology": "Irregular/Protogalaxy",
                "current_morphology": galaxy_type.title(),
                "disk_formation": "Complete" if galaxy_type == "spiral" else "Minimal",
                "bulge_development": "Significant" if galaxy_type == "elliptical" else "Moderate"
            }
            
            # Environmental influences
            environment_effects = {
                "dark_matter_halo_mass": f"{1e12 * (1 + formation_redshift):.1e} solar masses",
                "gas_accretion_rate": round(params["gas_fraction"] * 10, 1),
                "merger_history": f"{int(evolution_time * params['merger_rate'])} major mergers",
                "cosmic_web_position": "Filament" if galaxy_type == "spiral" else "Node"
            }
            
            result = {
                "galaxy_formation": {
                    "galaxy_type": galaxy_type,
                    "formation_redshift": formation_redshift,
                    "formation_time_gyr": round(formation_time_gyr, 1),
                    "evolution_time_gyr": round(evolution_time, 1)
                },
                "stellar_evolution": {
                    "stellar_generations": stellar_generations,
                    "current_metallicity": round(metallicity_evolution, 4),
                    "star_formation_efficiency": params["star_formation_rate"]
                },
                "star_formation_history": sf_history,
                "chemical_evolution": chemical_evolution,
                "morphological_evolution": morphology_evolution,
                "environmental_influences": environment_effects,
                "observational_properties": self._generate_galaxy_observables(galaxy_type, evolution_time),
                "future_evolution": self._predict_galaxy_future(galaxy_type, params),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error estimating galaxy formation: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to estimate galaxy formation: {str(e)}"
            }, indent=4)

    def analyze_universe_development(self, cosmic_epoch: str, 
                                   development_span_gyr: float = 5.0,
                                   focus_aspect: str = "structure_formation") -> str:
        """
        Phân tích universe development qua cosmic epochs.
        """
        log_debug(f"Analyzing universe development during {cosmic_epoch}")
        
        try:
            # Cosmic epoch parameters
            epoch_params = {
                "inflation": {"temperature_k": 1e28, "density_ratio": 1e60, "duration_s": 1e-32},
                "nucleosynthesis": {"temperature_k": 1e9, "density_ratio": 1e12, "duration_s": 1200},
                "recombination": {"temperature_k": 3000, "density_ratio": 1e3, "duration_s": 1e13},
                "reionization": {"temperature_k": 1e4, "density_ratio": 1e2, "duration_s": 1e15},
                "structure_formation": {"temperature_k": 10, "density_ratio": 10, "duration_s": 1e17}
            }
            
            params = epoch_params.get(cosmic_epoch, epoch_params["structure_formation"])
            
            # Development phases
            development_phases = {
                "early_phase": {
                    "duration_fraction": 0.3,
                    "key_processes": ["Initial conditions", "Linear growth", "Density fluctuations"],
                    "characteristic_scale": "Horizon scale"
                },
                "growth_phase": {
                    "duration_fraction": 0.5,
                    "key_processes": ["Nonlinear collapse", "Halo formation", "Gas dynamics"],
                    "characteristic_scale": "Jeans scale"
                },
                "maturation_phase": {
                    "duration_fraction": 0.2,
                    "key_processes": ["Feedback effects", "Environmental processing", "Secular evolution"],
                    "characteristic_scale": "Virial scale"
                }
            }
            
            # Physical processes
            physical_processes = {
                "gravitational_dynamics": self._analyze_gravitational_processes(cosmic_epoch),
                "thermodynamic_evolution": self._analyze_thermodynamic_processes(cosmic_epoch),
                "electromagnetic_processes": self._analyze_electromagnetic_processes(cosmic_epoch),
                "nuclear_processes": self._analyze_nuclear_processes(cosmic_epoch)
            }
            
            result = {
                "universe_development": {
                    "cosmic_epoch": cosmic_epoch,
                    "development_span_gyr": development_span_gyr,
                    "focus_aspect": focus_aspect,
                    "epoch_temperature_k": params["temperature_k"]
                },
                "development_phases": development_phases,
                "physical_processes": physical_processes,
                "cosmic_parameters": self._calculate_cosmic_parameters(cosmic_epoch, development_span_gyr),
                "observational_consequences": self._predict_observational_consequences(cosmic_epoch),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error analyzing universe development: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze universe development: {str(e)}"
            }, indent=4)

    def predict_cosmic_future(self, prediction_timeframe_gyr: float = 100.0,
                            cosmological_model: str = "lambda_cdm") -> str:
        """
        Dự đoán cosmic future và ultimate fate of universe.
        """
        log_debug(f"Predicting cosmic future over {prediction_timeframe_gyr} Gyr")
        
        try:
            # Future timeline milestones
            future_milestones = {
                "andromeda_collision": {"time_gyr": 4.5, "impact": "Galaxy merger"},
                "sun_red_giant": {"time_gyr": 5.0, "impact": "Solar system disruption"},
                "stellar_formation_peak": {"time_gyr": 10.0, "impact": "Peak star formation ends"},
                "white_dwarf_era": {"time_gyr": 100.0, "impact": "Main sequence stars end"},
                "brown_dwarf_era": {"time_gyr": 1000.0, "impact": "Stellar remnants dominate"},
                "black_hole_era": {"time_gyr": 1e12, "impact": "Only black holes remain"}
            }
            
            # Cosmic evolution predictions
            evolution_predictions = {
                "expansion_fate": "Accelerating expansion" if cosmological_model == "lambda_cdm" else "Unknown",
                "structure_evolution": "Increasing isolation of bound systems",
                "star_formation": "Exponential decline",
                "galaxy_evolution": "Merger-driven growth followed by isolation"
            }
            
            # Timeline projections
            timeline_projections = []
            for milestone, data in future_milestones.items():
                if data["time_gyr"] <= prediction_timeframe_gyr:
                    timeline_projections.append({
                        "milestone": milestone.replace("_", " ").title(),
                        "time_gyr": data["time_gyr"],
                        "impact": data["impact"],
                        "probability": "High" if data["time_gyr"] < 50 else "Medium"
                    })
            
            result = {
                "prediction_parameters": {
                    "timeframe_gyr": prediction_timeframe_gyr,
                    "cosmological_model": cosmological_model,
                    "current_universe_age_gyr": 13.8
                },
                "evolution_predictions": evolution_predictions,
                "timeline_projections": timeline_projections,
                "ultimate_fate": self._predict_ultimate_fate(cosmological_model),
                "uncertainty_factors": [
                    "Dark energy equation of state",
                    "Quantum gravity effects",
                    "Vacuum stability",
                    "Unknown physics"
                ],
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error predicting cosmic future: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to predict cosmic future: {str(e)}"
            }, indent=4)

    # Helper methods
    def _generate_observational_signatures(self, struct1: str, struct2: str) -> List[str]:
        """Generate observational signatures for structure evolution."""
        return [
            "Gravitational lensing effects",
            "X-ray emission from hot gas",
            "Radio emission from cosmic rays",
            "Optical galaxy distributions"
        ]

    def _get_dominant_stellar_type(self, generation: int) -> str:
        """Get dominant stellar type for generation."""
        types = ["Population III", "Population II", "Population I"]
        return types[min(generation, len(types)-1)]

    def _generate_galaxy_observables(self, galaxy_type: str, age: float) -> Dict[str, str]:
        """Generate observable properties for galaxy."""
        return {
            "color": "Blue" if galaxy_type == "irregular" else "Red",
            "luminosity": "High" if age < 5 else "Moderate",
            "gas_content": "Rich" if galaxy_type == "spiral" else "Poor",
            "stellar_populations": "Mixed ages" if galaxy_type == "spiral" else "Old"
        }

    def _predict_galaxy_future(self, galaxy_type: str, params: Dict) -> Dict[str, str]:
        """Predict future evolution of galaxy."""
        return {
            "star_formation": "Declining" if params["gas_fraction"] < 0.2 else "Continuing",
            "morphology": "Stable" if galaxy_type == "elliptical" else "Evolving",
            "merger_fate": "Likely" if params["merger_rate"] > 0.2 else "Unlikely"
        }

    def _analyze_gravitational_processes(self, epoch: str) -> str:
        """Analyze gravitational processes for cosmic epoch."""
        return "Dominant structure formation mechanism"

    def _analyze_thermodynamic_processes(self, epoch: str) -> str:
        """Analyze thermodynamic processes for cosmic epoch."""
        return "Cooling and heating balance"

    def _analyze_electromagnetic_processes(self, epoch: str) -> str:
        """Analyze electromagnetic processes for cosmic epoch."""
        return "Radiation-matter interactions"

    def _analyze_nuclear_processes(self, epoch: str) -> str:
        """Analyze nuclear processes for cosmic epoch."""
        return "Element synthesis" if epoch == "nucleosynthesis" else "Stellar nucleosynthesis"

    def _calculate_cosmic_parameters(self, epoch: str, span: float) -> Dict[str, float]:
        """Calculate cosmic parameters for epoch."""
        return {
            "hubble_parameter": 70.0,
            "matter_density": 0.3,
            "dark_energy_density": 0.7,
            "baryon_density": 0.05
        }

    def _predict_observational_consequences(self, epoch: str) -> List[str]:
        """Predict observational consequences of cosmic epoch."""
        return [
            "Cosmic microwave background signatures",
            "Large-scale structure patterns",
            "Primordial nucleosynthesis abundances",
            "Galaxy formation history"
        ]

    def _predict_ultimate_fate(self, model: str) -> Dict[str, str]:
        """Predict ultimate fate of universe."""
        if model == "lambda_cdm":
            return {
                "expansion": "Eternal acceleration",
                "temperature": "Approaches absolute zero",
                "entropy": "Maximum entropy state",
                "timeline": "Heat death in ~10^100 years"
            }
        return {"fate": "Model-dependent"}
