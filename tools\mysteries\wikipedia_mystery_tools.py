import requests
import json
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
from datetime import datetime
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class WikipediaMysteryTools(Toolkit):
    """
    Công cụ tìm kiếm và truy xuất thông tin về các b<PERSON>, hiện tượng kỳ lạ chưa giải thích được từ Wikipedia.
    
    Cung cấp thông tin về các vụ mất tích b<PERSON>, hiện tượ<PERSON>, đ<PERSON><PERSON> đi<PERSON><PERSON><PERSON>,
    và các sự kiện khó giải thích khác trên khắp thế giới.
    
    Keyword gợi ý: "tam giác quỷ Bermuda", "vụ án Dyatlov Pass", "thành phố Atlantis",
    "hồ Loch Ness", "vòng tròn trên đồng lú<PERSON>", "hiện tượng UFO"
    """
    
    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(name="wikipedia_mystery_tools", **kwargs)
        self.base_url = "https://en.wikipedia.org/w/api.php"
        self.headers = {
            "User-Agent": "MysteryResearchBot/1.0 (https://example.com; <EMAIL>)"
        }
        if enable_search:
            self.register(self.search_mysteries)
            self.register(self.get_mystery_details)
    
    def search_mysteries(self, query: str, max_results: int = 5) -> str:
        """
        Tìm kiếm các bài viết về bí ẩn chưa giải thích được trên Wikipedia.
        
        Args:
            query (str): Từ khóa tìm kiếm (ví dụ: "tam giác quỷ", "vụ án Dyatlov")
            max_results (int, optional): Số lượng kết quả tối đa. Mặc định: 5.
            
        Returns:
            str: Chuỗi JSON chứa kết quả tìm kiếm
            
        Ví dụ:
            search_mysteries("tam giác quỷ", 3)
        """
        log_debug(f"Tìm kiếm bí ẩn trên Wikipedia: {query}")
        
        try:
            params = {
                "action": "query",
                "format": "json",
                "list": "search",
                "srsearch": f"{query} mystery OR unexplained OR paranormal OR supernatural",
                "srlimit": max_results,
                "srprop": "snippet|titlesnippet",
                "utf8": "",
                "origin": "*"
            }
            
            response = requests.get(
                self.base_url,
                params=params,
                headers=self.headers,
                timeout=15
            )
            response.raise_for_status()
            data = response.json()
            
            results = []
            if 'query' in data and 'search' in data['query']:
                for item in data['query']['search']:
                    title = item.get('title', '')
                    pageid = item.get('pageid', 0)
                    snippet = item.get('snippet', '').replace('<span class="searchmatch">', '').replace('</span>', '')
                    
                    # Lấy URL đầy đủ của bài viết
                    url = f"https://en.wikipedia.org/wiki/{quote_plus(title.replace(' ', '_'))}"
                    
                    results.append({
                        "title": title,
                        "pageid": pageid,
                        "url": url,
                        "snippet": snippet,
                        "source": "Wikipedia"
                    })
            
            # Nếu không có kết quả, trả về kết quả mặc định
            if not results:
                return self._get_default_results(query)
            
            return json.dumps({
                "status": "success",
                "source": "Wikipedia",
                "query": query,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)
            
        except requests.RequestException as e:
            logger.error(f"Lỗi khi tìm kiếm trên Wikipedia: {e}")
            return self._get_error_response(query, str(e))
    
    def get_mystery_details(self, page_title: str) -> str:
        """
        Lấy thông tin chi tiết về một bí ẩn cụ thể từ Wikipedia.
        
        Args:
            page_title (str): Tiêu đề chính xác của bài viết Wikipedia
            
        Returns:
            str: Chuỗi JSON chứa thông tin chi tiết về bí ẩn
            
        Ví dụ:
            get_mystery_details("Bermuda Triangle")
        """
        log_debug(f"Lấy thông tin chi tiết về bí ẩn: {page_title}")
        
        try:
            # Lấy thông tin cơ bản của bài viết
            params = {
                "action": "query",
                "format": "json",
                "prop": "extracts|pageimages|info",
                "titles": page_title,
                "exintro": True,
                "explaintext": True,
                "inprop": "url",
                "pithumbsize": 500,
                "redirects": 1,
                "origin": "*"
            }
            
            response = requests.get(
                self.base_url,
                params=params,
                headers=self.headers,
                timeout=15
            )
            response.raise_for_status()
            data = response.json()
            
            # Xử lý dữ liệu trả về
            pages = data.get('query', {}).get('pages', {})
            if not pages:
                return self._get_error_response(page_title, "Không tìm thấy bài viết")
            
            page_id = next(iter(pages))
            page_data = pages[page_id]
            
            # Kiểm tra xem bài viết có tồn tại không
            if 'missing' in page_data:
                return self._get_error_response(page_title, "Bài viết không tồn tại")
            
            # Trích xuất thông tin cần thiết
            mystery_info = {
                "title": page_data.get('title', ''),
                "pageid": page_data.get('pageid', 0),
                "url": f"https://en.wikipedia.org/wiki/{quote_plus(page_title.replace(' ', '_'))}",
                "extract": page_data.get('extract', ''),
                "fullurl": page_data.get('fullurl', ''),
                "thumbnail": page_data.get('thumbnail', {}).get('source', '') if 'thumbnail' in page_data else ""
            }
            
            # Lấy thông tin về các danh mục
            params_categories = {
                "action": "query",
                "format": "json",
                "prop": "categories",
                "titles": page_title,
                "clshow": "!hidden",
                "cllimit": 50,
                "origin": "*"
            }
            
            response_cat = requests.get(
                self.base_url,
                params=params_categories,
                headers=self.headers,
                timeout=15
            )
            
            if response_cat.status_code == 200:
                cat_data = response_cat.json()
                pages_cat = cat_data.get('query', {}).get('pages', {})
                if pages_cat:
                    page_cat = next(iter(pages_cat.values()))
                    categories = [cat['title'].replace('Category:', '') 
                                   for cat in page_cat.get('categories', [])]
                    mystery_info["categories"] = categories
            
            return json.dumps({
                "status": "success",
                "source": "Wikipedia",
                "mystery_info": mystery_info,
                "retrieved_at": datetime.utcnow().isoformat()
            }, indent=2, ensure_ascii=False)
            
        except requests.RequestException as e:
            logger.error(f"Lỗi khi lấy thông tin chi tiết từ Wikipedia: {e}")
            return self._get_error_response(page_title, str(e))
    
    def _get_default_results(self, query: str) -> str:
        """Trả về kết quả mặc định khi không tìm thấy kết quả."""
        default_results = [
            {
                "title": "Bermuda Triangle",
                "pageid": 17035,
                "url": "https://en.wikipedia.org/wiki/Bermuda_Triangle",
                "snippet": "The Bermuda Triangle, also known as the Devil's Triangle, is a loosely defined region in the western part of the North Atlantic Ocean where a number of aircraft and ships are said to have disappeared under mysterious circumstances.",
                "source": "Wikipedia"
            },
            {
                "title": "Dyatlov Pass incident",
                "pageid": 1234567,
                "url": "https://en.wikipedia.org/wiki/Dyatlov_Pass_incident",
                "snippet": "The Dyatlov Pass incident was an event in which nine Russian hikers died in the northern Ural Mountains between February 1 and 2, 1959, in uncertain circumstances.",
                "source": "Wikipedia"
            },
            {
                "title": "Loch Ness Monster",
                "pageid": 2345678,
                "url": "https://en.wikipedia.org/wiki/Loch_Ness_Monster",
                "snippet": "In Scottish folklore, the Loch Ness Monster or Nessie is a creature said to inhabit Loch Ness in the Scottish Highlands. It is often described as large in size with a long neck and one or more humps protruding from the water.",
                "source": "Wikipedia"
            }
        ]
        
        return json.dumps({
            "status": "success",
            "source": "Wikipedia",
            "query": query,
            "message": "Không tìm thấy kết quả phù hợp. Dưới đây là một số bí ẩn nổi tiếng.",
            "results": default_results,
            "result_count": len(default_results)
        }, indent=2, ensure_ascii=False)
    
    def _get_error_response(self, query: str, error_msg: str) -> str:
        """Trả về phản hồi lỗi có cấu trúc."""
        return json.dumps({
            "status": "error",
            "source": "Wikipedia",
            "query": query,
            "message": f"Không thể truy xuất kết quả: {error_msg}",
            "results": []
        }, indent=2, ensure_ascii=False)
