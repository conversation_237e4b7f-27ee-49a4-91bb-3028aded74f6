import asyncio
import sys
from pathlib import Path

# Thêm thư mục gốc vào PATH để import module
sys.path.append(str(Path(__file__).parent.absolute()))

from astronomy_team.astronomy_team import AstronomyWorkflow

async def main():
    """Hàm chính chạy chương trình."""
    print("=" * 50)
    print("CHÀO MỪNG ĐẾN VỚI ASTRONOMY TEAM")
    print("Nhập 'exit' hoặc 'quit' để thoất")
    print("=" * 50)
    
    # Khởi tạo workflow
    print("\n🔄 Đang khởi tạo đội ngũ chuyên gia thiên văn...")
    workflow = AstronomyWorkflow()
    print("✅ Đã sẵn sàng!")
    
    while True:
        try:
            # Nhận câu hỏi từ người dùng
            print("\n" + "-" * 50)
            query = input("\nBạn muốn hỏi gì về thiên văn học? ")
            
            # Thoát nếu người dùng nhập exit hoặc quit
            if query.lower() in ['exit', 'quit']:
                print("\n👋 Hẹn gặp lại!")
                break
                
            if not query.strip():
                print("Vui lòng nhập câu hỏi!")
                continue
                
            # Xử lý câu hỏi
            print("\n🔄 Đang xử lý câu hỏi của bạn...")
            response = await workflow.arun(query)
            
            # Hiển thị kết quả
            print("\n" + "=" * 50)
            print("📝 KẾT QUẢ:")
            print("=" * 50)
            print(response.content)

            # Hiển thị nguồn tham khảo nếu có
            if hasattr(response, "metadata") and response.metadata and response.metadata.get("sources"):
                print("\n📚 NGUỒN THAM KHẢO:")
                for i, source in enumerate(response.metadata["sources"], 1):
                    print(f"{i}. {source}")

            # Hiển thị đánh giá chất lượng nếu có
            if hasattr(response, "metadata") and response.metadata and "evaluation" in response.metadata:
                eval_metrics = response.metadata["evaluation"].get("evaluation_metrics", {})
                if eval_metrics:
                    print("\n⭐ ĐÁNH GIÁ CHẤT LƯỢNG:")
                    for metric, score in eval_metrics.items():
                        print(f"- {metric.capitalize()}: {score}/10")
        
        except KeyboardInterrupt:
            print("\n👋 Đã nhận tín hiệu dừng. Đang thoát...")
            break
            
        except Exception as e:
            print(f"\n❌ Đã xảy ra lỗi: {str(e)}")
            print("Vui lòng thử lại với câu hỏi khác hoặc báo cáo lỗi này.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Đã thoát chương trình.")
    except Exception as e:
        print(f"\n❌ Lỗi không mong muốn: {str(e)}")
        print("Vui lòng kiểm tra lại cấu hình và thử lại.")
