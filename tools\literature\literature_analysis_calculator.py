# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import re
import math
from datetime import datetime

class LiteratureAnalysisCalculator(Toolkit):
    """
    Literature Analysis Calculator cho phân tích văn bản, readability, style và so sánh tác giả.
    """

    def __init__(self, enable_calculations: bool = True, **kwargs):
        super().__init__(
            name="literature_analysis_calculator",
            **kwargs
        )

        # Readability formulas and weights
        self.readability_formulas = {
            "flesch_reading_ease": {"weight": 0.3, "range": [0, 100]},
            "flesch_kincaid_grade": {"weight": 0.25, "range": [1, 18]},
            "gunning_fog": {"weight": 0.25, "range": [6, 20]},
            "coleman_liau": {"weight": 0.2, "range": [1, 16]}
        }

        # Literary style metrics
        self.style_metrics = {
            "sentence_complexity": 0.2,
            "vocabulary_richness": 0.25,
            "narrative_structure": 0.2,
            "literary_devices": 0.15,
            "thematic_depth": 0.2
        }

        # Text complexity factors
        self.complexity_factors = {
            "syntactic": 0.3,
            "semantic": 0.3,
            "discourse": 0.2,
            "pragmatic": 0.2
        }

        if enable_calculations:
            self.register(self.analyze_text_complexity)
            self.register(self.calculate_readability_scores)
            self.register(self.analyze_literary_style)
            self.register(self.compare_authors_similarity)

    def analyze_text_complexity(self, text_sample: str, text_type: str = "prose",
                               analysis_depth: str = "standard", language: str = "english") -> str:
        """
        Phân tích độ phức tạp của văn bản.

        Args:
            text_sample: Mẫu văn bản để phân tích
            text_type: Loại văn bản (prose, poetry, drama, essay)
            analysis_depth: Mức độ phân tích (basic, standard, comprehensive)
            language: Ngôn ngữ của văn bản

        Returns:
            Chuỗi JSON chứa phân tích độ phức tạp
        """
        log_debug(f"Analyzing text complexity for {text_type}")

        try:
            # Basic text statistics
            text_stats = self._calculate_basic_text_stats(text_sample)

            # Syntactic complexity
            syntactic_complexity = self._analyze_syntactic_complexity(text_sample, text_type)

            # Semantic complexity
            semantic_complexity = self._analyze_semantic_complexity(text_sample, language)

            # Discourse complexity
            discourse_complexity = self._analyze_discourse_complexity(text_sample, text_type)

            # Pragmatic complexity
            pragmatic_complexity = self._analyze_pragmatic_complexity(text_sample) if analysis_depth != "basic" else {}

            # Overall complexity score
            overall_complexity = self._calculate_overall_complexity(
                syntactic_complexity, semantic_complexity, discourse_complexity, pragmatic_complexity
            )

            result = {
                "analysis_parameters": {
                    "text_type": text_type,
                    "analysis_depth": analysis_depth,
                    "language": language,
                    "sample_length": len(text_sample),
                    "analysis_date": datetime.now().strftime("%Y-%m-%d")
                },
                "text_statistics": text_stats,
                "complexity_analysis": {
                    "syntactic_complexity": syntactic_complexity,
                    "semantic_complexity": semantic_complexity,
                    "discourse_complexity": discourse_complexity,
                    "pragmatic_complexity": pragmatic_complexity
                },
                "overall_complexity": overall_complexity,
                "complexity_classification": self._classify_text_complexity(overall_complexity),
                "educational_recommendations": self._generate_educational_recommendations(overall_complexity, text_type),
                "analysis_confidence": self._assess_analysis_confidence(text_stats, analysis_depth)
            }

            return json.dumps(result, indent=4)

        except Exception as e:
            logger.error(f"Error analyzing text complexity: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze text complexity: {str(e)}"
            }, indent=4)

    def calculate_readability_scores(self, text_sample: str, target_audience: str = "general",
                                   include_detailed_analysis: bool = True) -> str:
        """
        Tính toán các chỉ số readability của văn bản.

        Args:
            text_sample: Mẫu văn bản
            target_audience: Đối tượng đọc mục tiêu
            include_detailed_analysis: Có bao gồm phân tích chi tiết không

        Returns:
            Chuỗi JSON chứa các chỉ số readability
        """
        log_debug(f"Calculating readability scores for {target_audience} audience")

        try:
            # Basic text metrics for readability
            text_metrics = self._extract_readability_metrics(text_sample)

            # Calculate individual readability scores
            flesch_reading_ease = self._calculate_flesch_reading_ease(text_metrics)
            flesch_kincaid_grade = self._calculate_flesch_kincaid_grade(text_metrics)
            gunning_fog = self._calculate_gunning_fog_index(text_metrics)
            coleman_liau = self._calculate_coleman_liau_index(text_metrics)

            # Composite readability score
            composite_score = self._calculate_composite_readability(
                flesch_reading_ease, flesch_kincaid_grade, gunning_fog, coleman_liau
            )

            # Audience appropriateness
            audience_analysis = self._analyze_audience_appropriateness(composite_score, target_audience)

            # Detailed analysis
            detailed_analysis = self._provide_detailed_readability_analysis(
                text_metrics, composite_score
            ) if include_detailed_analysis else {}

            result = {
                "readability_analysis": {
                    "target_audience": target_audience,
                    "text_length": len(text_sample),
                    "analysis_date": datetime.now().strftime("%Y-%m-%d")
                },
                "text_metrics": text_metrics,
                "readability_scores": {
                    "flesch_reading_ease": {
                        "score": round(flesch_reading_ease, 2),
                        "interpretation": self._interpret_flesch_score(flesch_reading_ease)
                    },
                    "flesch_kincaid_grade": {
                        "grade_level": round(flesch_kincaid_grade, 1),
                        "interpretation": f"Grade {flesch_kincaid_grade:.1f} reading level"
                    },
                    "gunning_fog_index": {
                        "score": round(gunning_fog, 2),
                        "interpretation": self._interpret_fog_score(gunning_fog)
                    },
                    "coleman_liau_index": {
                        "score": round(coleman_liau, 2),
                        "interpretation": f"Grade {coleman_liau:.1f} level"
                    }
                },
                "composite_score": composite_score,
                "audience_analysis": audience_analysis,
                "detailed_analysis": detailed_analysis,
                "improvement_suggestions": self._generate_readability_improvements(text_metrics, composite_score)
            }

            return json.dumps(result, indent=4)

        except Exception as e:
            logger.error(f"Error calculating readability scores: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate readability scores: {str(e)}"
            }, indent=4)

    def analyze_literary_style(self, text_sample: str, author_name: str = "",
                             literary_period: str = "", comparison_mode: bool = False) -> str:
        """
        Phân tích phong cách văn học của văn bản.

        Args:
            text_sample: Mẫu văn bản
            author_name: Tên tác giả (nếu biết)
            literary_period: Thời kỳ văn học
            comparison_mode: Có so sánh với các phong cách khác không

        Returns:
            Chuỗi JSON chứa phân tích phong cách văn học
        """
        log_debug(f"Analyzing literary style for {author_name or 'unknown author'}")

        try:
            # Style metrics analysis
            sentence_analysis = self._analyze_sentence_structure(text_sample)
            vocabulary_analysis = self._analyze_vocabulary_richness(text_sample)
            narrative_analysis = self._analyze_narrative_structure(text_sample)
            literary_devices = self._identify_literary_devices(text_sample)
            thematic_elements = self._analyze_thematic_elements(text_sample)

            # Style classification
            style_classification = self._classify_literary_style(
                sentence_analysis, vocabulary_analysis, narrative_analysis, literary_devices
            )

            # Period comparison
            period_comparison = self._compare_with_period_style(
                style_classification, literary_period
            ) if literary_period else {}

            # Author style signature
            author_signature = self._generate_author_style_signature(
                sentence_analysis, vocabulary_analysis, literary_devices
            )

            result = {
                "style_analysis": {
                    "author_name": author_name or "Unknown",
                    "literary_period": literary_period or "Unspecified",
                    "comparison_mode": comparison_mode,
                    "sample_length": len(text_sample)
                },
                "style_metrics": {
                    "sentence_structure": sentence_analysis,
                    "vocabulary_richness": vocabulary_analysis,
                    "narrative_structure": narrative_analysis,
                    "literary_devices": literary_devices,
                    "thematic_elements": thematic_elements
                },
                "style_classification": style_classification,
                "period_comparison": period_comparison,
                "author_signature": author_signature,
                "style_distinctiveness": self._assess_style_distinctiveness(style_classification),
                "literary_influences": self._identify_potential_influences(style_classification, literary_period)
            }

            return json.dumps(result, indent=4)

        except Exception as e:
            logger.error(f"Error analyzing literary style: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze literary style: {str(e)}"
            }, indent=4)

    def compare_authors_similarity(self, author1_sample: str, author2_sample: str,
                                 author1_name: str = "Author 1", author2_name: str = "Author 2",
                                 comparison_aspects: List[str] = None) -> str:
        """
        So sánh độ tương đồng giữa hai tác giả.

        Args:
            author1_sample: Mẫu văn bản của tác giả 1
            author2_sample: Mẫu văn bản của tác giả 2
            author1_name: Tên tác giả 1
            author2_name: Tên tác giả 2
            comparison_aspects: Các khía cạnh so sánh

        Returns:
            Chuỗi JSON chứa so sánh độ tương đồng
        """
        log_debug(f"Comparing similarity between {author1_name} and {author2_name}")

        if comparison_aspects is None:
            comparison_aspects = ["style", "vocabulary", "structure", "themes"]

        try:
            # Analyze both authors
            author1_analysis = self._comprehensive_author_analysis(author1_sample)
            author2_analysis = self._comprehensive_author_analysis(author2_sample)

            # Calculate similarity scores
            similarity_scores = {}

            if "style" in comparison_aspects:
                similarity_scores["style_similarity"] = self._calculate_style_similarity(
                    author1_analysis["style"], author2_analysis["style"]
                )

            if "vocabulary" in comparison_aspects:
                similarity_scores["vocabulary_similarity"] = self._calculate_vocabulary_similarity(
                    author1_analysis["vocabulary"], author2_analysis["vocabulary"]
                )

            if "structure" in comparison_aspects:
                similarity_scores["structure_similarity"] = self._calculate_structure_similarity(
                    author1_analysis["structure"], author2_analysis["structure"]
                )

            if "themes" in comparison_aspects:
                similarity_scores["thematic_similarity"] = self._calculate_thematic_similarity(
                    author1_analysis["themes"], author2_analysis["themes"]
                )

            # Overall similarity
            overall_similarity = self._calculate_overall_similarity(similarity_scores)

            # Distinctive features
            distinctive_features = self._identify_distinctive_features(author1_analysis, author2_analysis)

            result = {
                "comparison_analysis": {
                    "author1_name": author1_name,
                    "author2_name": author2_name,
                    "comparison_aspects": comparison_aspects,
                    "sample_lengths": {
                        "author1": len(author1_sample),
                        "author2": len(author2_sample)
                    }
                },
                "individual_analyses": {
                    "author1_profile": author1_analysis,
                    "author2_profile": author2_analysis
                },
                "similarity_scores": similarity_scores,
                "overall_similarity": overall_similarity,
                "similarity_interpretation": self._interpret_similarity_score(overall_similarity),
                "distinctive_features": distinctive_features,
                "influence_assessment": self._assess_potential_influence(similarity_scores, distinctive_features)
            }

            return json.dumps(result, indent=4)

        except Exception as e:
            logger.error(f"Error comparing authors similarity: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to compare authors similarity: {str(e)}"
            }, indent=4)

    # Helper methods for text analysis
    def _calculate_basic_text_stats(self, text: str) -> dict:
        """Calculate basic text statistics."""
        sentences = re.split(r'[.!?]+', text)
        words = re.findall(r'\b\w+\b', text.lower())
        characters = len(text.replace(' ', ''))

        return {
            "total_characters": len(text),
            "total_words": len(words),
            "total_sentences": len([s for s in sentences if s.strip()]),
            "average_words_per_sentence": len(words) / max(len(sentences), 1),
            "average_characters_per_word": characters / max(len(words), 1),
            "unique_words": len(set(words)),
            "lexical_diversity": len(set(words)) / max(len(words), 1)
        }

    def _analyze_syntactic_complexity(self, text: str, text_type: str) -> dict:
        """Analyze syntactic complexity."""
        sentences = re.split(r'[.!?]+', text)
        avg_sentence_length = sum(len(s.split()) for s in sentences) / max(len(sentences), 1)

        return {
            "average_sentence_length": round(avg_sentence_length, 2),
            "sentence_variety": "High" if avg_sentence_length > 20 else "Medium" if avg_sentence_length > 15 else "Low",
            "complexity_score": min(100, avg_sentence_length * 3),
            "text_type_adjustment": 1.2 if text_type == "poetry" else 1.0
        }

    def _analyze_semantic_complexity(self, text: str, language: str) -> dict:
        """Analyze semantic complexity."""
        words = re.findall(r'\b\w+\b', text.lower())
        long_words = [w for w in words if len(w) > 6]

        return {
            "vocabulary_sophistication": len(long_words) / max(len(words), 1),
            "semantic_density": "High" if len(long_words) / len(words) > 0.3 else "Medium",
            "conceptual_complexity": "Advanced" if len(set(long_words)) > 20 else "Intermediate",
            "language_factor": 1.1 if language != "english" else 1.0
        }

    def _analyze_discourse_complexity(self, text: str, text_type: str) -> dict:
        """Analyze discourse complexity."""
        paragraphs = text.split('\n\n')

        return {
            "discourse_structure": "Complex" if len(paragraphs) > 5 else "Simple",
            "coherence_markers": text.count('however') + text.count('therefore') + text.count('moreover'),
            "narrative_complexity": "High" if text_type in ["novel", "drama"] else "Medium",
            "organizational_score": min(100, len(paragraphs) * 10)
        }

    def _analyze_pragmatic_complexity(self, text: str) -> dict:
        """Analyze pragmatic complexity."""
        return {
            "implied_meaning": "Present" if '?' in text or '"' in text else "Limited",
            "cultural_references": "Multiple" if any(word in text.lower() for word in ["shakespeare", "bible", "mythology"]) else "Few",
            "irony_indicators": text.count('!') + text.count('...'),
            "pragmatic_score": 75  # Simplified score
        }

    def _calculate_overall_complexity(self, syntactic: dict, semantic: dict, discourse: dict, pragmatic: dict) -> dict:
        """Calculate overall complexity score."""
        scores = {
            "syntactic": syntactic.get("complexity_score", 50),
            "semantic": semantic.get("vocabulary_sophistication", 0.3) * 100,
            "discourse": discourse.get("organizational_score", 50),
            "pragmatic": pragmatic.get("pragmatic_score", 50) if pragmatic else 50
        }

        weighted_score = sum(scores[k] * self.complexity_factors[k] for k in scores)

        return {
            "component_scores": scores,
            "weighted_total": round(weighted_score, 2),
            "complexity_level": self._classify_complexity_level(weighted_score),
            "confidence": "High" if pragmatic else "Medium"
        }

    def _classify_complexity_level(self, score: float) -> str:
        """Classify complexity level."""
        if score >= 80:
            return "Very High"
        elif score >= 65:
            return "High"
        elif score >= 50:
            return "Medium"
        elif score >= 35:
            return "Low"
        else:
            return "Very Low"

    def _classify_text_complexity(self, complexity: dict) -> dict:
        """Classify text complexity."""
        level = complexity["complexity_level"]
        return {
            "complexity_level": level,
            "reading_difficulty": "Graduate" if level == "Very High" else "College" if level == "High" else "High School",
            "target_audience": "Academic" if level in ["Very High", "High"] else "General",
            "educational_use": "Advanced courses" if level == "Very High" else "Standard curriculum"
        }

    def _generate_educational_recommendations(self, complexity: dict, text_type: str) -> list:
        """Generate educational recommendations."""
        level = complexity["complexity_level"]
        recommendations = []

        if level == "Very High":
            recommendations.append("Suitable for graduate-level analysis")
            recommendations.append("Requires extensive background knowledge")
        elif level == "High":
            recommendations.append("Appropriate for advanced undergraduate courses")
            recommendations.append("Benefits from guided reading")
        else:
            recommendations.append("Accessible to general readers")
            recommendations.append("Good for introductory literature courses")

        return recommendations

    def _assess_analysis_confidence(self, stats: dict, depth: str) -> dict:
        """Assess analysis confidence."""
        word_count = stats.get("total_words", 0)

        return {
            "confidence_level": "High" if word_count > 500 and depth == "comprehensive" else "Medium",
            "sample_adequacy": "Sufficient" if word_count > 200 else "Limited",
            "reliability_factors": ["Sample size", "Analysis depth", "Text completeness"]
        }
