import requests
import json
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
from bs4 import BeautifulSoup
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class ConspiracyArchiveTools(Toolkit):
    """
    Công cụ tìm kiếm và truy xuất thông tin từ Conspiracy Archive.
    
    Cung cấp quyền truy cập vào các bài viết, tài liệu về các thuyết âm mưu,
    tổ chức bí mật và các sự kiện lịch sử gây tranh cãi.
    
    Keyword gợi ý: "mind control", "secret society", "new world order",
    "illuminati", "false flag", "deep state", "assassination theories"
    """
    
    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(name="conspiracy_archive_tools", **kwargs)
        self.base_url = "https://www.conspiracyarchive.com"
        if enable_search:
            self.register(self.search_articles)
            self.register(self.get_featured_articles)
    
    def search_articles(self, query: str, max_results: int = 5) -> str:
        """
        Tìm kiếm bài viết trên Conspiracy Archive.
        
        Args:
            query (str): Từ khóa tìm kiếm (ví dụ: "mkultra", "new world order")
            max_results (int, optional): Số lượng kết quả tối đa. Mặc định: 5.
            
        Returns:
            str: Chuỗi JSON chứa kết quả tìm kiếm
            
        Ví dụ:
            search_articles("mind control", 3)
        """
        log_debug(f"Tìm kiếm trên Conspiracy Archive: {query}")
        
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        try:
            # Gửi yêu cầu tìm kiếm
            search_url = f"{self.base_url}/search.html"
            params = {"q": query}
            
            response = requests.get(
                search_url,
                params=params,
                headers=headers,
                timeout=15
            )
            response.raise_for_status()
            
            # Phân tích kết quả HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # Lấy các kết quả tìm kiếm
            search_results = soup.select('.search-result')
            
            for result in search_results[:max_results]:
                title_elem = result.select_one('h3 a')
                if not title_elem:
                    continue
                    
                title = title_elem.get_text(strip=True)
                url = title_elem.get('href', '')
                if url and not url.startswith('http'):
                    url = f"{self.base_url}/{url.lstrip('/')}"
                
                # Lấy mô tả nếu có
                desc_elem = result.select_one('.search-snippet')
                description = desc_elem.get_text(strip=True) if desc_elem else ""
                
                results.append({
                    "title": title,
                    "url": url,
                    "summary": description,
                    "source": "Conspiracy Archive"
                })
            
            # Nếu không có kết quả, trả về kết quả mặc định
            if not results:
                return self._get_default_results(query)
            
            return json.dumps({
                "status": "success",
                "source": "Conspiracy Archive",
                "query": query,
                "results": results,
                "result_count": len(results),
                "search_url": response.url
            }, indent=2, ensure_ascii=False)
            
        except requests.RequestException as e:
            logger.error(f"Lỗi khi truy vấn Conspiracy Archive: {e}")
            return self._get_error_response(query, str(e))
    
    def get_featured_articles(self) -> str:
        """
        Lấy danh sách các bài viết nổi bật từ Conspiracy Archive.
        
        Returns:
            str: Chuỗi JSON chứa danh sách bài viết nổi bật
        """
        log_debug("Lấy danh sách bài viết nổi bật từ Conspiracy Archive")
        
        featured_articles = [
            {
                "title": "MKUltra Mind Control",
                "url": "https://www.conspiracyarchive.com/Articles/MKULTRA.htm",
                "summary": "Tài liệu về chương trình kiểm soát tâm trí MKUltra của CIA.",
                "category": "Mind Control"
            },
            {
                "title": "New World Order",
                "url": "https://www.conspiracyarchive.com/NWO.htm",
                "summary": "Các lý thuyết về trật tự thế giới mới và chính phủ toàn cầu.",
                "category": "Secret Societies"
            },
            {
                "title": "9/11 Conspiracy Theories",
                "url": "https://www.conspiracyarchive.com/911/911.htm",
                "summary": "Phân tích các giả thuyết âm mưu xung quanh sự kiện 11/9.",
                "category": "False Flag Operations"
            },
            {
                "title": "The Illuminati",
                "url": "https://www.conspiracyarchive.com/Conspiracy/Illuminati.htm",
                "summary": "Lịch sử và ảnh hưởng của hội kín Illuminati.",
                "category": "Secret Societies"
            },
            {
                "title": "JFK Assassination",
                "url": "https://www.conspiracyarchive.com/Conspiracy/JFK.htm",
                "summary": "Các giả thuyết âm mưu xung quanh vụ ám sát Tổng thống Kennedy.",
                "category": "Assassinations"
            }
        ]
        
        return json.dumps({
            "status": "success",
            "source": "Conspiracy Archive",
            "featured_articles": featured_articles,
            "last_updated": "2023-05-22"
        }, indent=2, ensure_ascii=False)
    
    def _get_default_results(self, query: str) -> str:
        """Trả về kết quả mặc định khi không tìm thấy kết quả."""
        default_results = [
            {
                "title": "MKUltra Mind Control",
                "url": "https://www.conspiracyarchive.com/Articles/MKULTRA.htm",
                "summary": "Tài liệu về chương trình kiểm soát tâm trí MKUltra của CIA.",
                "source": "Conspiracy Archive"
            },
            {
                "title": "New World Order",
                "url": "https://www.conspiracyarchive.com/NWO.htm",
                "summary": "Các lý thuyết về trật tự thế giới mới và chính phủ toàn cầu.",
                "source": "Conspiracy Archive"
            },
            {
                "title": "The Illuminati",
                "url": "https://www.conspiracyarchive.com/Conspiracy/Illuminati.htm",
                "summary": "Lịch sử và ảnh hưởng của hội kín Illuminati.",
                "source": "Conspiracy Archive"
            }
        ]
        
        return json.dumps({
            "status": "success",
            "source": "Conspiracy Archive",
            "query": query,
            "message": "Không tìm thấy kết quả phù hợp. Dưới đây là một số tài nguyên mặc định.",
            "results": default_results,
            "result_count": len(default_results)
        }, indent=2, ensure_ascii=False)
    
    def _get_error_response(self, query: str, error_msg: str) -> str:
        """Trả về phản hồi lỗi có cấu trúc."""
        return json.dumps({
            "status": "error",
            "source": "Conspiracy Archive",
            "query": query,
            "message": f"Không thể truy xuất kết quả: {error_msg}",
            "results": []
        }, indent=2, ensure_ascii=False)
