from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class GlobalSecurityTool(Toolkit):
    """
    GlobalSecurity.org Tool for searching military policy, weapons, and intelligence data.
    """

    def __init__(self):
        super().__init__(
            name="GlobalSecurity.org Search Tool",
            description="Tool for searching military policy, weapons, and intelligence data from GlobalSecurity.org.",
            tools=[self.search_globalsecurity]
        )

    async def search_globalsecurity(self, query: str, category: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Search GlobalSecurity.org for military policy, weapons, and intelligence data.

        Parameters:
        - query: Geopolitical, weapon, or policy keyword (e.g., 'nuclear doctrine', 'defense budget', 'F-35 Lightning II')
        - category: Optional category filter (e.g., 'weapons', 'policy', 'intelligence')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with search results including title, summary, category, and GlobalSecurity.org URLs
        """
        logger.info(f"Searching GlobalSecurity.org for: {query}, category={category}")

        try:
            # GlobalSecurity.org không có API ch<PERSON> thứ<PERSON>, dùng Google Custom Search hoặc scraping đơn giản
            # Ở đây mô phỏng tìm kiếm bằng Google Custom Search (nếu có key thì dùng thật)
            search_url = "https://www.googleapis.com/customsearch/v1"
            api_key = "YOUR_GOOGLE_API_KEY"  # Thay bằng API key thật nếu có
            cx = "YOUR_CUSTOM_SEARCH_ENGINE_ID"  # Thay bằng CSE ID thật nếu có

            params = {
                "q": f"site:globalsecurity.org {query}",
                "key": api_key,
                "cx": cx,
                "num": limit
            }
            if category:
                params["q"] += f" {category}"

            response = requests.get(search_url, params=params)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "GlobalSecurity.org",
                    "message": f"Search API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            items = data.get("items", [])
            results = []
            for item in items:
                results.append({
                    "title": item.get("title"),
                    "snippet": item.get("snippet"),
                    "link": item.get("link"),
                    "displayLink": item.get("displayLink"),
                    "category": category,
                    "globalsecurity_url": item.get("link")
                })

            return {
                "status": "success",
                "source": "GlobalSecurity.org",
                "query": query,
                "category": category,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching GlobalSecurity.org: {str(e)}")
            # Trả về mẫu nếu lỗi
            return {
                "status": "error",
                "source": "GlobalSecurity.org",
                "message": str(e),
                "query": query,
                "results": [
                    {"title": "Nuclear Doctrine", "url": "https://www.globalsecurity.org/wmd/ops/nuclear-doctrine.htm", "summary": "Overview of nuclear doctrine and policy."},
                    {"title": "F-35 Lightning II", "url": "https://www.globalsecurity.org/military/systems/aircraft/f-35.htm", "summary": "Technical and operational details of the F-35 Lightning II."}
                ]
            }
