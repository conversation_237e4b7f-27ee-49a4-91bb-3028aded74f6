from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json

class EOLTool(Toolkit):
    """
    Encyclopedia of Life (EOL) Tool for searching species-level summaries.
    """
    
    def __init__(self):
        super().__init__(
            name="Encyclopedia of Life Search Tool",
            tools=[self.search_eol]
        )
        self.api_key = "1d8bf785cd9c4496602246c8ec9193a3543cf780abf3544db4b05753"  # Default API key for EOL

    async def search_eol(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Search Encyclopedia of Life (EOL) for species information.
        
        Parameters:
        - query: Search query using scientific or common names (e.g., "beetles", "African elephants")
        - limit: Maximum number of results to return (default: 5)
        
        Returns:
        - JSON with search results including species information, descriptions, and media
        """
        logger.info(f"Searching EOL for: {query}")
        
        try:
            # Search EOL for the query
            search_url = "https://eol.org/api/search"
            search_params = {
                "q": query,
                "page": 1,
                "per_page": limit,
                "key": self.api_key
            }
            
            search_response = requests.get(search_url, params=search_params)
            
            if search_response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Encyclopedia of Life",
                    "message": f"Search API returned status code {search_response.status_code}",
                    "query": query
                }
            
            search_data = search_response.json()
            results = []
            
            # For each search result, get detailed information
            for result in search_data.get("results", []):
                page_id = result.get("id")
                if page_id:
                    # Get detailed page information
                    page_url = f"https://eol.org/api/pages/{page_id}"
                    page_params = {
                        "images_per_page": 3,
                        "videos_per_page": 0,
                        "sounds_per_page": 0,
                        "maps_per_page": 0,
                        "texts_per_page": 2,
                        "details": True,
                        "key": self.api_key
                    }
                    
                    page_response = requests.get(page_url, params=page_params)
                    
                    if page_response.status_code == 200:
                        page_data = page_response.json()
                        
                        # Extract scientific name
                        scientific_name = page_data.get("scientificName")
                        
                        # Extract taxonomy
                        taxonomy = {}
                        for ancestor in page_data.get("ancestors", []):
                            rank = ancestor.get("taxonRank", "").lower()
                            if rank in ["kingdom", "phylum", "class", "order", "family", "genus"]:
                                taxonomy[rank] = ancestor.get("scientificName")
                        
                        # Extract descriptions
                        descriptions = []
                        for text in page_data.get("dataObjects", []):
                            if text.get("type") == "http://purl.org/dc/dcmitype/Text":
                                description = {
                                    "title": text.get("title", "Description"),
                                    "text": text.get("description", "").replace("<p>", "").replace("</p>", ""),
                                    "language": text.get("language", "en"),
                                    "source": text.get("source", "")
                                }
                                descriptions.append(description)
                        
                        # Extract images
                        images = []
                        for image in page_data.get("dataObjects", []):
                            if image.get("type") == "http://purl.org/dc/dcmitype/StillImage":
                                img_data = {
                                    "title": image.get("title"),
                                    "url": image.get("mediaURL"),
                                    "license": image.get("license"),
                                    "rights": image.get("rights")
                                }
                                images.append(img_data)
                        
                        # Extract vernacular (common) names
                        common_names = []
                        for vernacular in page_data.get("vernacularNames", []):
                            if vernacular.get("language") == "en":
                                common_names.append(vernacular.get("vernacularName"))
                        
                        species_info = {
                            "id": page_id,
                            "scientific_name": scientific_name,
                            "common_names": common_names,
                            "taxonomy": taxonomy,
                            "descriptions": descriptions,
                            "images": images,
                            "eol_url": f"https://eol.org/pages/{page_id}"
                        }
                        
                        results.append(species_info)
            
            return {
                "status": "success",
                "source": "Encyclopedia of Life",
                "query": query,
                "results_count": len(results),
                "results": results
            }
        
        except Exception as e:
            log_debug(f"Error searching Encyclopedia of Life: {str(e)}")
            return {
                "status": "error",
                "source": "Encyclopedia of Life",
                "message": str(e),
                "query": query
            }