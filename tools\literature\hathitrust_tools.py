from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class HathiTrustTool(Toolkit):
    """
    HathiTrust Tool cho tìm kiếm sách, tài liệu văn học, <PERSON>h<PERSON> b<PERSON>nh, nghi<PERSON>n cứu từ HathiTrust Digital Library.
    """

    def __init__(self):
        super().__init__(
            name="HathiTrust Search Tool",
            tools=[self.search_hathitrust]
        )

    async def search_hathitrust(self, query: str, author: Optional[str] = None, year: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm HathiTrust cho sách, tài liệu văn họ<PERSON>, phê bình, nghiên cứu.

        Parameters:
        - query: Từ khóa tên tác phẩm, chủ đề, thể loại, ph<PERSON> b<PERSON><PERSON> (ví dụ: 'Pride and Prejudice', 'romantic poetry', 'literary criticism')
        - author: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> (ví dụ: '<PERSON>', '<PERSON>')
        - year: <PERSON><PERSON><PERSON> xuất bản hoặc khoảng năm (ví dụ: '1922', '1900-1950')
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với tiêu đề, tác giả, năm, mô tả, link HathiTrust
        """
        logger.info(f"Tìm kiếm HathiTrust: query={query}, author={author}, year={year}, limit={limit}")

        try:
            # HathiTrust Bibliographic API endpoint
            api_url = "https://catalog.hathitrust.org/api/volumes/brief/json/search"
            params = {
                "q1": query,
                "searchtype": "all",
                "limit": limit
            }
            if author:
                params["author"] = author
            if year:
                params["publishDate"] = year

            response = requests.get(api_url, params=params, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "HathiTrust",
                    "message": f"HathiTrust API trả về mã lỗi {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            records = data.get("records", {})
            for record_id, record in list(records.items())[:limit]:
                title = record.get("title")
                authors = record.get("authors")
                publish_date = record.get("publishDate")
                publisher = record.get("publisher")
                description = record.get("description")
                rights = record.get("rights")
                # Lấy link đọc online nếu có
                item_links = []
                for item in record.get("items", []):
                    if item.get("usRightsString") == "Full view":
                        item_links.append(f"https://hdl.handle.net/2027/{item.get('htid')}")
                hathitrust_url = f"https://catalog.hathitrust.org/Record/{record_id}"

                results.append({
                    "title": title,
                    "authors": authors,
                    "publish_date": publish_date,
                    "publisher": publisher,
                    "description": description,
                    "rights": rights,
                    "hathitrust_url": hathitrust_url,
                    "fullview_links": item_links
                })

            return {
                "status": "success",
                "source": "HathiTrust",
                "query": query,
                "author": author,
                "year": year,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "Pride and Prejudice",
                    "romantic poetry",
                    "literary criticism",
                    "Shakespeare",
                    "Emily Dickinson",
                    "Victorian novel",
                    "modernist literature",
                    "American poetry",
                    "French symbolism"
                ],
                "official_data_url": "https://www.hathitrust.org/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm HathiTrust: {str(e)}")
            return {
                "status": "error",
                "source": "HathiTrust",
                "message": str(e),
                "query": query
            }
