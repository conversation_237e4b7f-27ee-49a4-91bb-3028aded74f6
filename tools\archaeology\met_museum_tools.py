from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class MetMuseumTool(Toolkit):
    """
    Met Museum Tool for searching archaeological artifacts and metadata from The Metropolitan Museum of Art.
    """

    def __init__(self):
        super().__init__(
            name="Met Museum Search Tool",
            tools=[self.search_met_museum]
        )

    async def search_met_museum(self, query: str, department: Optional[str] = None, material: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        Search the Met Museum's collection for archaeological artifacts.

        Parameters:
        - query: Search query using artifact name, culture, or accession number (e.g., 'Egyptian statue', '54.3.3')
        - department: Department name or ID (e.g., 'Egyptian Art', 'Ancient Near Eastern Art')
        - material: Material filter (e.g., 'stone', 'bronze')
        - limit: Maximum number of results to return (default: 10)

        Returns:
        - JSON with search results including artifact metadata, images, and Met Museum URLs
        """
        logger.info(f"Searching Met Museum for: {query}, department={department}, material={material}")

        try:
            # Step 1: Search for object IDs
            search_url = "https://collectionapi.metmuseum.org/public/collection/v1/search"
            params = {
                "q": query,
                "hasImages": "true"
            }
            if department:
                # Department IDs are integers; if string, try to map to ID (not implemented here)
                params["departmentId"] = department
            response = requests.get(search_url, params=params)

            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Met Museum",
                    "message": f"Search API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            object_ids = data.get("objectIDs", [])[:limit]
            results = []

            # Step 2: Fetch details for each object
            for obj_id in object_ids:
                obj_url = f"https://collectionapi.metmuseum.org/public/collection/v1/objects/{obj_id}"
                obj_resp = requests.get(obj_url)
                if obj_resp.status_code != 200:
                    continue
                obj_data = obj_resp.json()

                # Filter by material if specified
                if material:
                    obj_materials = obj_data.get("medium", "").lower()
                    if material.lower() not in obj_materials:
                        continue

                results.append({
                    "object_id": obj_id,
                    "title": obj_data.get("title"),
                    "object_name": obj_data.get("objectName"),
                    "culture": obj_data.get("culture"),
                    "period": obj_data.get("period"),
                    "dynasty": obj_data.get("dynasty"),
                    "date": obj_data.get("objectDate"),
                    "medium": obj_data.get("medium"),
                    "dimensions": obj_data.get("dimensions"),
                    "department": obj_data.get("department"),
                    "accession_number": obj_data.get("accessionNumber"),
                    "artist": obj_data.get("artistDisplayName"),
                    "description": obj_data.get("creditLine"),
                    "image_url": obj_data.get("primaryImageSmall"),
                    "met_url": obj_data.get("objectURL")
                })

            return {
                "status": "success",
                "source": "Met Museum",
                "query": query,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching Met Museum: {str(e)}")
            return {
                "status": "error",
                "source": "Met Museum",
                "message": str(e),
                "query": query
            }
