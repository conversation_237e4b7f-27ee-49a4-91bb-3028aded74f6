"""
Hệ thống memory v2 cải tiến.
"""

import json
import time
import uuid
import logging
import re
import hashlib
from typing import Dict, List, Any, Optional, Union, Tuple

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Lưu trữ bộ nhớ
_sessions = {}
_user_memories = {}
_current_session_id = None

def create_session() -> str:
    """
    Tạo một phiên làm việc mới.

    Returns:
        ID của phiên làm việc
    """
    global _current_session_id

    session_id = str(uuid.uuid4())
    _sessions[session_id] = {
        "created_at": time.time(),
        "messages": [],
        "metadata": {},
        "summary": ""
    }

    _current_session_id = session_id
    logger.info(f"Created new session: {session_id}")

    return session_id

def get_current_session_id() -> Optional[str]:
    """
    Lấy ID của phiên làm việc hiện tại.

    Returns:
        ID của phiên làm việc hiện tại hoặc None nếu không có
    """
    return _current_session_id

def set_current_session(session_id: str) -> bool:
    """
    Đặt phiên làm việc hiện tại.

    Args:
        session_id: ID của phiên làm việc

    Returns:
        True nếu thành công, False nếu không
    """
    global _current_session_id

    if session_id in _sessions:
        _current_session_id = session_id
        logger.info(f"Set current session to: {session_id}")
        return True

    logger.warning(f"Session {session_id} not found")
    return False

def add_message(role: str, content: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
    """
    Thêm tin nhắn vào phiên làm việc hiện tại.

    Args:
        role: Vai trò của người gửi (user, assistant, system)
        content: Nội dung tin nhắn
        metadata: Metadata của tin nhắn

    Returns:
        True nếu thành công, False nếu không
    """
    global _current_session_id

    if not _current_session_id:
        logger.warning("No active session. Creating a new one.")
        _current_session_id = create_session()

    # Đảm bảo content không phải là None
    if content is None:
        logger.warning("Content is None, using empty string instead")
        content = ""

    # Đảm bảo content là chuỗi
    if not isinstance(content, str):
        logger.warning(f"Content is not a string, converting from {type(content)}")
        content = str(content)

    message = {
        "role": role,
        "content": content,
        "timestamp": time.time(),
        "metadata": metadata or {}
    }

    _sessions[_current_session_id]["messages"].append(message)
    logger.debug(f"Added message to session {_current_session_id}")

    return True

def get_messages(session_id: Optional[str] = None, limit: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    Lấy danh sách tin nhắn từ phiên làm việc.

    Args:
        session_id: ID của phiên làm việc (mặc định: phiên hiện tại)
        limit: Số lượng tin nhắn tối đa cần lấy

    Returns:
        Danh sách tin nhắn
    """
    global _current_session_id

    session_id = session_id or _current_session_id

    if not session_id:
        logger.warning("No active session")
        return []

    if session_id not in _sessions:
        logger.warning(f"Session {session_id} not found")
        return []

    messages = _sessions[session_id]["messages"]

    if limit:
        messages = messages[-limit:]

    return messages

def update_session_metadata(metadata: Dict[str, Any], session_id: Optional[str] = None) -> bool:
    """
    Cập nhật metadata của phiên làm việc.

    Args:
        metadata: Metadata cần cập nhật
        session_id: ID của phiên làm việc (mặc định: phiên hiện tại)

    Returns:
        True nếu thành công, False nếu không
    """
    global _current_session_id

    session_id = session_id or _current_session_id

    if not session_id:
        logger.warning("No active session")
        return False

    if session_id not in _sessions:
        logger.warning(f"Session {session_id} not found")
        return False

    _sessions[session_id]["metadata"].update(metadata)
    logger.debug(f"Updated metadata for session {session_id}")

    return True

def set_session_summary(summary: str, session_id: Optional[str] = None) -> bool:
    """
    Đặt tóm tắt cho phiên làm việc.

    Args:
        summary: Tóm tắt phiên làm việc
        session_id: ID của phiên làm việc (mặc định: phiên hiện tại)

    Returns:
        True nếu thành công, False nếu không
    """
    global _current_session_id

    session_id = session_id or _current_session_id

    if not session_id:
        logger.warning("No active session")
        return False

    if session_id not in _sessions:
        logger.warning(f"Session {session_id} not found")
        return False

    _sessions[session_id]["summary"] = summary
    logger.debug(f"Set summary for session {session_id}")

    return True

def get_session_summary(session_id: Optional[str] = None, regenerate: bool = False) -> str:
    """
    Lấy tóm tắt của phiên làm việc.

    Args:
        session_id: ID của phiên làm việc (mặc định: phiên hiện tại)
        regenerate: Tạo lại tóm tắt hay không

    Returns:
        Tóm tắt phiên làm việc
    """
    global _current_session_id

    session_id = session_id or _current_session_id

    if not session_id:
        logger.warning("No active session")
        return ""

    if session_id not in _sessions:
        logger.warning(f"Session {session_id} not found")
        return ""

    # Tạo lại tóm tắt nếu cần
    if regenerate or not _sessions[session_id]["summary"]:
        compress_session(session_id, generate_summary_flag=True)

    return _sessions[session_id]["summary"]

def get_session_keywords(session_id: Optional[str] = None) -> List[str]:
    """
    Lấy từ khóa của phiên làm việc.

    Args:
        session_id: ID của phiên làm việc (mặc định: phiên hiện tại)

    Returns:
        Danh sách các từ khóa
    """
    global _current_session_id

    session_id = session_id or _current_session_id

    if not session_id:
        logger.warning("No active session")
        return []

    if session_id not in _sessions:
        logger.warning(f"Session {session_id} not found")
        return []

    # Đảm bảo có từ khóa
    if "metadata" not in _sessions[session_id] or "keywords" not in _sessions[session_id]["metadata"]:
        compress_session(session_id, generate_summary_flag=True)

    return _sessions[session_id].get("metadata", {}).get("keywords", [])

def set_user_memory(user_id: str, key: str, value: Any) -> bool:
    """
    Lưu thông tin người dùng.

    Args:
        user_id: ID của người dùng
        key: Khóa
        value: Giá trị

    Returns:
        True nếu thành công, False nếu không
    """
    if user_id not in _user_memories:
        _user_memories[user_id] = {}

    _user_memories[user_id][key] = value
    logger.debug(f"Set memory for user {user_id}: {key}={value}")

    return True

def get_user_memory(user_id: str, key: str, default: Any = None) -> Any:
    """
    Lấy thông tin người dùng.

    Args:
        user_id: ID của người dùng
        key: Khóa
        default: Giá trị mặc định nếu không tìm thấy

    Returns:
        Giá trị của khóa hoặc giá trị mặc định
    """
    if user_id not in _user_memories:
        return default

    return _user_memories[user_id].get(key, default)

def get_all_user_memories(user_id: str) -> Dict[str, Any]:
    """
    Lấy tất cả thông tin của người dùng.

    Args:
        user_id: ID của người dùng

    Returns:
        Dictionary chứa tất cả thông tin của người dùng
    """
    if user_id not in _user_memories:
        return {}

    return _user_memories[user_id].copy()

def clear_user_memory(user_id: str, key: Optional[str] = None) -> bool:
    """
    Xóa thông tin người dùng.

    Args:
        user_id: ID của người dùng
        key: Khóa cần xóa (None để xóa tất cả)

    Returns:
        True nếu thành công, False nếu không
    """
    if user_id not in _user_memories:
        return False

    if key is None:
        _user_memories.pop(user_id)
        logger.debug(f"Cleared all memories for user {user_id}")
    elif key in _user_memories[user_id]:
        _user_memories[user_id].pop(key)
        logger.debug(f"Cleared memory for user {user_id}: {key}")
    else:
        return False

    return True

def save_memory_to_file(file_path: str) -> bool:
    """
    Lưu bộ nhớ vào file.

    Args:
        file_path: Đường dẫn đến file

    Returns:
        True nếu thành công, False nếu không
    """
    try:
        memory_data = {
            "sessions": _sessions,
            "user_memories": _user_memories,
            "current_session_id": _current_session_id
        }

        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(memory_data, f, indent=2)

        logger.info(f"Saved memory to {file_path}")
        return True
    except Exception as e:
        logger.error(f"Error saving memory to file: {e}")
        return False

def load_memory_from_file(file_path: str) -> bool:
    """
    Tải bộ nhớ từ file.

    Args:
        file_path: Đường dẫn đến file

    Returns:
        True nếu thành công, False nếu không
    """
    global _sessions, _user_memories, _current_session_id

    try:
        with open(file_path, "r", encoding="utf-8") as f:
            memory_data = json.load(f)

        _sessions = memory_data.get("sessions", {})
        _user_memories = memory_data.get("user_memories", {})
        _current_session_id = memory_data.get("current_session_id")

        logger.info(f"Loaded memory from {file_path}")
        return True
    except Exception as e:
        logger.error(f"Error loading memory from file: {e}")
        return False

def generate_summary(messages: List[Dict[str, Any]], max_length: int = 200) -> str:
    """
    Tạo tóm tắt từ danh sách tin nhắn.

    Args:
        messages: Danh sách tin nhắn
        max_length: Độ dài tối đa của tóm tắt

    Returns:
        Tóm tắt
    """
    if not messages:
        return "Empty session"

    # Lấy nội dung từ các tin nhắn
    contents = []
    for msg in messages:
        role = msg.get("role", "unknown")
        content = msg.get("content", "")

        # Đảm bảo content không phải là None
        if content is None:
            content = ""
            logger.warning("Content is None in message, using empty string")

        # Kiểm tra xem content có phải là coroutine không
        if hasattr(content, '__await__'):
            # Nếu là coroutine, sử dụng chuỗi mô tả
            content = f"<coroutine object at {id(content)}>"

        # Đảm bảo content là chuỗi
        if not isinstance(content, str):
            logger.warning(f"Content is not a string, converting from {type(content)}")
            content = str(content)

        # Chỉ lấy 50 ký tự đầu tiên của mỗi tin nhắn
        short_content = content[:50] + ("..." if len(content) > 50 else "")
        contents.append(f"{role}: {short_content}")

    # Tạo tóm tắt
    summary = f"Session with {len(messages)} messages: " + " | ".join(contents[-3:])

    # Giới hạn độ dài tóm tắt
    if len(summary) > max_length:
        summary = summary[:max_length-3] + "..."

    return summary

def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """
    Trích xuất từ khóa từ văn bản.

    Args:
        text: Văn bản cần trích xuất từ khóa
        max_keywords: Số lượng từ khóa tối đa

    Returns:
        Danh sách các từ khóa
    """
    # Đảm bảo text không phải là None
    if text is None:
        logger.warning("Text is None in extract_keywords, using empty string")
        return []

    # Đảm bảo text là chuỗi
    if not isinstance(text, str):
        logger.warning(f"Text is not a string in extract_keywords, converting from {type(text)}")
        text = str(text)

    # Loại bỏ các ký tự đặc biệt
    text = re.sub(r'[^\w\s]', ' ', text.lower())

    # Tách thành các từ
    words = text.split()

    # Loại bỏ các từ dừng (stopwords)
    stopwords = {"a", "an", "the", "and", "or", "but", "is", "are", "was", "were",
                "in", "on", "at", "to", "for", "with", "by", "about", "like",
                "from", "of", "that", "this", "these", "those", "it", "its"}

    # Đếm tần suất các từ
    word_counts = {}
    for word in words:
        if word not in stopwords and len(word) > 2:
            word_counts[word] = word_counts.get(word, 0) + 1

    # Sắp xếp theo tần suất
    sorted_words = sorted(word_counts.items(), key=lambda x: x[1], reverse=True)

    # Lấy các từ khóa
    keywords = [word for word, count in sorted_words[:max_keywords]]

    return keywords

def compress_session(session_id: Optional[str] = None, max_messages: int = 10, generate_summary_flag: bool = True) -> bool:
    """
    Nén phiên làm việc để giảm kích thước bộ nhớ.

    Args:
        session_id: ID của phiên làm việc (mặc định: phiên hiện tại)
        max_messages: Số lượng tin nhắn tối đa giữ lại
        generate_summary_flag: Tạo tóm tắt hay không

    Returns:
        True nếu thành công, False nếu không
    """
    global _current_session_id

    session_id = session_id or _current_session_id

    if not session_id:
        logger.warning("No active session")
        return False

    if session_id not in _sessions:
        logger.warning(f"Session {session_id} not found")
        return False

    # Tạo tóm tắt nếu chưa có hoặc cần tạo lại
    if generate_summary_flag or not _sessions[session_id]["summary"]:
        messages = _sessions[session_id]["messages"]

        if messages:
            # Tạo tóm tắt
            summary = generate_summary(messages)
            _sessions[session_id]["summary"] = summary

            # Trích xuất từ khóa
            try:
                # Đảm bảo content không phải là None và là chuỗi
                message_contents = []
                for msg in messages:
                    content = msg.get("content", "")
                    if content is None:
                        content = ""
                    if not isinstance(content, str):
                        content = str(content)
                    message_contents.append(content)

                all_text = " ".join(message_contents)
                keywords = extract_keywords(all_text)

                # Lưu từ khóa vào metadata
                if "metadata" not in _sessions[session_id]:
                    _sessions[session_id]["metadata"] = {}

                _sessions[session_id]["metadata"]["keywords"] = keywords
            except Exception as e:
                logger.error(f"Error extracting keywords: {e}")
                _sessions[session_id]["metadata"]["keywords"] = []

    # Giữ lại max_messages tin nhắn gần nhất
    if len(_sessions[session_id]["messages"]) > max_messages:
        _sessions[session_id]["messages"] = _sessions[session_id]["messages"][-max_messages:]

    logger.info(f"Compressed session {session_id}")
    return True

def get_memory_v2(db_path: str = "memory.db",
                 enable_session_storage: bool = True,
                 enable_user_memories: bool = True,
                 enable_session_summaries: bool = True) -> Dict[str, Any]:
    """
    Lấy instance của memory v2.

    Args:
        db_path: Đường dẫn đến file cơ sở dữ liệu
        enable_session_storage: Bật/tắt lưu trữ phiên làm việc
        enable_user_memories: Bật/tắt lưu trữ thông tin người dùng
        enable_session_summaries: Bật/tắt tóm tắt phiên làm việc

    Returns:
        Instance của memory v2
    """
    # Tạo phiên làm việc mới
    session_id = create_session()

    return {
        "session_id": session_id,
        "db_path": db_path,
        "enable_session_storage": enable_session_storage,
        "enable_user_memories": enable_user_memories,
        "enable_session_summaries": enable_session_summaries,
        "create_session": create_session,
        "add_message": add_message,
        "get_messages": get_messages,
        "set_user_memory": set_user_memory,
        "get_user_memory": get_user_memory,
        "get_session_summary": get_session_summary,
        "compress_session": compress_session,
        "get_session_keywords": get_session_keywords,
        "get_all_user_memories": get_all_user_memories
    }