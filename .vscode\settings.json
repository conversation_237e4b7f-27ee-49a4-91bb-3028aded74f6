{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "desktop-commander": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-desktop-commander"]}, "context7": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-context7"]}}, "mcp": {"inputs": [], "servers": {"mcp-server-time": {"command": "python", "args": ["-m", "mcp_server_time", "--local-timezone=America/Los_Angeles"], "env": {}}}}, "chat.mcp.discovery.enabled": true, "github.copilot.chat.agent.thinkingTool": true}