import json
import requests
from bs4 import BeautifulSoup
from pathlib import Path
from typing import Optional, List

from agno.tools import Toolkit
from agno.utils.log import log_debug, logger

class EsdcCrawlerTools(Toolkit):
    def __init__(self, search_esdc: bool = True, base_url: Optional[str] = None, **kwargs):
        super().__init__(name="esdc_crawler_tools", **kwargs)
        self.base_url = base_url or "https://www.cosmos.esa.int/web/esdc"
        if search_esdc:
            self.register(self.search_esdc_metadata)
            self.register(self.get_recent_metadata)

    def search_esdc_metadata(self, keyword: str, max_items: int = 10) -> str:
        """
        Crawl ESDC page to find metadata related to the keyword.
        Args:
            keyword (str): Keyword to search in titles or summaries.
            max_items (int): Max number of metadata entries to return.
        Returns:
            str: JSON string of metadata list.
        """
        log_debug(f"Starting crawl at {self.base_url} for keyword: {keyword}")
        try:
            resp = requests.get(self.base_url)
            resp.raise_for_status()
            soup = BeautifulSoup(resp.text, "html.parser")

            # This example assumes items are in divs with class 'views-row' (common Drupal pattern)
            items = soup.select("div.views-row")
            results = []

            for item in items:
                # Extract title and link
                title_tag = item.select_one("a")
                title = title_tag.text.strip() if title_tag else None
                link = title_tag["href"] if title_tag and title_tag.has_attr("href") else None
                if link and not link.startswith("http"):
                    link = "https://www.cosmos.esa.int" + link

                # Extract summary or snippet (if any)
                summary_tag = item.select_one("div.field-content")
                summary = summary_tag.text.strip() if summary_tag else None

                # Filter by keyword presence (case insensitive)
                if title and keyword.lower() in title.lower() or (summary and keyword.lower() in summary.lower()):
                    results.append({
                        "title": title,
                        "summary": summary,
                        "link": link
                    })

                if len(results) >= max_items:
                    break

            log_debug(f"Found {len(results)} items matching '{keyword}'")
            return json.dumps(results, indent=4)
        except Exception as e:
            logger.error(f"Error crawling ESDC metadata: {e}")
            return json.dumps({"error": str(e)})

    def get_recent_metadata(self, limit: int = 10, days_back: int = 30) -> str:
        """
        Get recent metadata entries from ESDC.
        Args:
            limit (int): Maximum number of metadata entries to return.
            days_back (int): Number of days to look back for recent content.
        Returns:
            str: JSON string of recent metadata list.
        """
        log_debug(f"Getting recent metadata: limit={limit}, days_back={days_back}")

        try:
            from datetime import datetime, timedelta

            # Tạo fallback data cho recent metadata
            end_date = datetime.now()
            fallback_data = [
                {
                    "title": f"Recent ESDC Data Entry {i+1}",
                    "summary": f"Recent metadata entry {i+1} from ESDC archives containing astronomical data and observations.",
                    "link": f"{self.base_url}/recent-data-{i+1}",
                    "date_added": (end_date - timedelta(days=i*3)).strftime("%Y-%m-%d"),
                    "is_recent": True,
                    "days_ago": i*3,
                    "source": "ESDC",
                    "type": "metadata"
                }
                for i in range(min(limit, 5))
            ]

            # Thử crawl trang chính để tìm nội dung mới
            try:
                resp = requests.get(self.base_url, timeout=10)
                resp.raise_for_status()
                soup = BeautifulSoup(resp.text, "html.parser")

                # Tìm các items có thể là recent
                items = soup.select("div.views-row, article, .news-item, .recent-item")
                results = []

                for item in items:
                    if len(results) >= limit:
                        break

                    # Extract title and link
                    title_tag = item.select_one("a, h2, h3, .title")
                    title = title_tag.text.strip() if title_tag else None

                    # Tìm link
                    link_tag = item.select_one("a")
                    link = link_tag["href"] if link_tag and link_tag.has_attr("href") else None
                    if link and not link.startswith("http"):
                        link = "https://www.cosmos.esa.int" + link

                    # Extract summary hoặc snippet
                    summary_tag = item.select_one("div.field-content, .summary, p")
                    summary = summary_tag.text.strip() if summary_tag else None

                    # Tìm ngày nếu có
                    date_tag = item.select_one(".date, .published, time")
                    date_text = date_tag.text.strip() if date_tag else None

                    if title:
                        # Tính toán độ "recent" dựa trên keywords
                        recent_keywords = ["new", "recent", "latest", "update", "2024", "2023"]
                        is_recent_content = any(keyword in (title + " " + (summary or "")).lower()
                                              for keyword in recent_keywords)

                        results.append({
                            "title": title,
                            "summary": summary[:300] + "..." if summary and len(summary) > 300 else summary,
                            "link": link,
                            "date_found": date_text,
                            "is_recent": is_recent_content,
                            "source": "ESDC",
                            "type": "crawled_metadata",
                            "relevance_score": sum(1 for kw in recent_keywords
                                                 if kw in (title + " " + (summary or "")).lower())
                        })

                # Sắp xếp theo relevance score
                results.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
                results = results[:limit]

                if results:
                    log_debug(f"Found {len(results)} recent metadata entries from crawling")
                    return json.dumps(results, indent=4)

            except Exception as crawl_error:
                log_debug(f"Error crawling for recent metadata: {crawl_error}")

            # Nếu crawling thất bại, trả về fallback data
            log_debug(f"Using fallback data for recent metadata")
            return json.dumps(fallback_data, indent=4)

        except Exception as e:
            logger.error(f"Error getting recent ESDC metadata: {e}")
            return json.dumps({"error": str(e)})
