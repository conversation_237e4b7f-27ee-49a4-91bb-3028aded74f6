from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import logger
import requests

class InternetArchiveArchaeoTool(Toolkit):
    """
    Internet Archive Archaeo Tool for searching archaeology books, field reports, and artifact studies.
    """

    def __init__(self):
        super().__init__(
            name="Internet Archive Archaeology Search Tool",
            tools=[self.search_internet_archive_archaeo]
        )

    async def search_internet_archive_archaeo(self, query: str, limit: int = 5) -> str:
        """
        Search Internet Archive for archaeology books, field reports, and artifact studies.

        Parameters:
        - query: Search string (e.g., 'pompeii.1912.mau', 'egypt.scarab.faience')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with search results including title, year, authors, description, and archive URLs
        """
        logger.info(f"Searching Internet Archive for archaeology: {query}")

        try:
            search_url = "https://archive.org/advancedsearch.php"
            params = {
                "q": f"({query}) AND (subject:archaeology OR subject:excavation OR subject:artifact OR subject:site)",
                "fl[]": "identifier,title,creator,year,description,subject",
                "rows": limit,
                "output": "json"
            }
            response = requests.get(search_url, params=params)

            if response.status_code != 200:
                response = {
                    "status": "error",
                    "source": "Internet Archive",
                    "message": f"Archive search API returned status code {response.status_code}",
                    "query": query

                }

                return json.dumps(response, ensure_ascii=False, indent=2)

            data = response.json()
            docs = data.get("response", {}).get("docs", [])
            results = []
            for item in docs:
                identifier = item.get("identifier")
                archive_url = f"https://archive.org/details/{identifier}" if identifier else None
                results.append({
                    "identifier": identifier,
                    "title": item.get("title"),
                    "authors": item.get("creator"),
                    "year": item.get("year"),
                    "description": item.get("description"),
                    "subjects": item.get("subject"),
                    "archive_url": archive_url
                })

            response = {
                "status": "success",
                "source": "Internet Archive",
                "query": query,
                "results_count": len(results),
                "results": results


            }


            return json.dumps(response, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Error searching Internet Archive Archaeo: {str(e)}")
            response = {
                "status": "error",
                "source": "Internet Archive",
                "message": str(e),
                "query": query

            }

            return json.dumps(response, ensure_ascii=False, indent=2)
