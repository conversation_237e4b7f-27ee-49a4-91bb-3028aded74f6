# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math

class EnvironmentalImpactCalculator(Toolkit):
    """
    Environmental Impact Calculator cho tính toán carbon footprint, water footprint và các tác động môi trường khác.
    """

    def __init__(self, enable_calculations: bool = True, **kwargs):
        super().__init__(
            name="environmental_impact_calculator",
            **kwargs
        )
        
        # Emission factors (kg CO2 equivalent)
        self.emission_factors = {
            # Transportation (per km)
            "car_gasoline": 0.21,
            "car_diesel": 0.17,
            "car_electric": 0.05,
            "bus": 0.08,
            "train": 0.04,
            "airplane_domestic": 0.25,
            "airplane_international": 0.15,
            "motorcycle": 0.12,
            
            # Energy (per kWh)
            "electricity_coal": 0.82,
            "electricity_natural_gas": 0.35,
            "electricity_renewable": 0.02,
            "heating_natural_gas": 0.18,
            "heating_oil": 0.25,
            
            # Food (per kg)
            "beef": 27.0,
            "pork": 12.1,
            "chicken": 6.9,
            "fish": 6.1,
            "dairy": 3.2,
            "vegetables": 2.0,
            "fruits": 1.1,
            "grains": 2.7
        }
        
        # Water footprint factors (liters per unit)
        self.water_factors = {
            # Food (per kg)
            "beef": 15400,
            "pork": 6000,
            "chicken": 4300,
            "rice": 2500,
            "wheat": 1800,
            "vegetables": 300,
            "fruits": 900,
            "coffee": 18900,  # per kg of coffee beans
            "cotton": 10000,  # per kg of cotton
            
            # Beverages (per liter)
            "beer": 300,
            "wine": 870,
            "soft_drinks": 200
        }
        
        if enable_calculations:
            self.register(self.calculate_carbon_footprint)
            self.register(self.calculate_water_footprint)
            self.register(self.estimate_energy_consumption)
            self.register(self.assess_waste_impact)

    def calculate_carbon_footprint(self, activity: str, amount: float, unit: str = "km", 
                                 fuel_type: str = "gasoline") -> str:
        """
        Tính toán carbon footprint cho các hoạt động khác nhau.
        
        Args:
            activity: Loại hoạt động ('transportation', 'energy', 'food')
            amount: Số lượng (km, kWh, kg)
            unit: Đơn vị đo lường
            fuel_type: Loại nhiên liệu hoặc nguồn năng lượng
            
        Returns:
            Chuỗi JSON chứa kết quả tính toán carbon footprint
        """
        log_debug(f"Calculating carbon footprint for {activity}: {amount} {unit}")
        
        try:
            # Xác định emission factor
            if activity == "transportation":
                factor_key = f"car_{fuel_type}" if fuel_type in ["gasoline", "diesel", "electric"] else fuel_type
            elif activity == "energy":
                factor_key = f"electricity_{fuel_type}" if "electricity" in fuel_type else fuel_type
            elif activity == "food":
                factor_key = fuel_type  # fuel_type sẽ là loại thực phẩm
            else:
                factor_key = f"{activity}_{fuel_type}"
            
            emission_factor = self.emission_factors.get(factor_key, 0.1)  # Default factor
            
            # Tính toán CO2 emissions
            co2_emissions = amount * emission_factor
            
            # Tính toán equivalent comparisons
            trees_needed = co2_emissions / 22  # 1 tree absorbs ~22kg CO2/year
            car_km_equivalent = co2_emissions / 0.21  # Average car emissions
            
            result = {
                "activity": activity,
                "amount": amount,
                "unit": unit,
                "fuel_type": fuel_type,
                "co2_emissions_kg": round(co2_emissions, 2),
                "emission_factor": emission_factor,
                "equivalents": {
                    "trees_needed_to_offset": round(trees_needed, 1),
                    "car_km_equivalent": round(car_km_equivalent, 1),
                    "percentage_of_daily_target": round((co2_emissions / 6.8) * 100, 1)  # 6.8kg/day target
                },
                "recommendations": self._get_carbon_recommendations(activity, co2_emissions),
                "calculation_date": "2024-01-01",
                "methodology": "IPCC Guidelines for National Greenhouse Gas Inventories"
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error calculating carbon footprint: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate carbon footprint: {str(e)}"
            }, indent=4)

    def calculate_water_footprint(self, product: str, quantity: float, unit: str = "kg") -> str:
        """
        Tính toán water footprint cho các sản phẩm.
        
        Args:
            product: Loại sản phẩm
            quantity: Số lượng
            unit: Đơn vị (kg, liter)
            
        Returns:
            Chuỗi JSON chứa kết quả tính toán water footprint
        """
        log_debug(f"Calculating water footprint for {product}: {quantity} {unit}")
        
        try:
            water_factor = self.water_factors.get(product.lower(), 1000)  # Default 1000L/kg
            
            # Tính toán water usage
            water_usage = quantity * water_factor
            
            # Tính toán equivalents
            shower_equivalent = water_usage / 65  # Average shower uses 65L
            bottle_equivalent = water_usage / 0.5  # 500ml bottle
            
            result = {
                "product": product,
                "quantity": quantity,
                "unit": unit,
                "water_usage_liters": round(water_usage, 1),
                "water_factor": water_factor,
                "equivalents": {
                    "shower_equivalent": round(shower_equivalent, 1),
                    "water_bottles_equivalent": round(bottle_equivalent, 0),
                    "daily_drinking_water_equivalent": round(water_usage / 2.5, 1)  # 2.5L/day recommended
                },
                "water_stress_impact": self._assess_water_stress_impact(water_usage),
                "recommendations": self._get_water_recommendations(product, water_usage),
                "calculation_date": "2024-01-01",
                "methodology": "Water Footprint Network Standards"
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error calculating water footprint: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate water footprint: {str(e)}"
            }, indent=4)

    def estimate_energy_consumption(self, appliance: str, usage_hours: float, 
                                  power_rating: float = None) -> str:
        """
        Ước tính mức tiêu thụ năng lượng của thiết bị.
        
        Args:
            appliance: Loại thiết bị
            usage_hours: Số giờ sử dụng
            power_rating: Công suất (watts), nếu không có sẽ dùng giá trị mặc định
            
        Returns:
            Chuỗi JSON chứa ước tính tiêu thụ năng lượng
        """
        log_debug(f"Estimating energy consumption for {appliance}: {usage_hours} hours")
        
        try:
            # Default power ratings (watts)
            default_power = {
                "refrigerator": 150,
                "air_conditioner": 3500,
                "washing_machine": 2000,
                "dishwasher": 1800,
                "television": 100,
                "computer": 300,
                "led_bulb": 10,
                "microwave": 1000,
                "electric_heater": 2000
            }
            
            power = power_rating or default_power.get(appliance.lower(), 500)
            
            # Tính toán energy consumption
            energy_kwh = (power * usage_hours) / 1000
            
            # Tính toán costs và emissions
            electricity_cost = energy_kwh * 0.12  # $0.12/kWh average
            co2_emissions = energy_kwh * 0.5  # 0.5kg CO2/kWh average grid
            
            result = {
                "appliance": appliance,
                "usage_hours": usage_hours,
                "power_rating_watts": power,
                "energy_consumption_kwh": round(energy_kwh, 2),
                "estimated_cost_usd": round(electricity_cost, 2),
                "co2_emissions_kg": round(co2_emissions, 2),
                "efficiency_tips": self._get_efficiency_tips(appliance),
                "annual_projection": {
                    "energy_kwh": round(energy_kwh * 365, 0),
                    "cost_usd": round(electricity_cost * 365, 0),
                    "co2_kg": round(co2_emissions * 365, 0)
                },
                "calculation_date": "2024-01-01"
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error estimating energy consumption: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to estimate energy consumption: {str(e)}"
            }, indent=4)

    def assess_waste_impact(self, waste_type: str, amount: float, unit: str = "kg") -> str:
        """
        Đánh giá tác động môi trường của chất thải.
        
        Args:
            waste_type: Loại chất thải ('plastic', 'paper', 'organic', 'electronic')
            amount: Số lượng chất thải
            unit: Đơn vị (kg, tons)
            
        Returns:
            Chuỗi JSON chứa đánh giá tác động chất thải
        """
        log_debug(f"Assessing waste impact for {waste_type}: {amount} {unit}")
        
        try:
            # Waste impact factors
            decomposition_time = {
                "plastic": "450 years",
                "paper": "2-6 weeks",
                "organic": "2-5 weeks",
                "electronic": "1000+ years",
                "glass": "1 million years",
                "aluminum": "80-200 years"
            }
            
            recycling_potential = {
                "plastic": 0.3,  # 30% typically recycled
                "paper": 0.7,   # 70% recycling rate
                "organic": 0.9, # 90% compostable
                "electronic": 0.2, # 20% properly recycled
                "glass": 0.8,   # 80% recyclable
                "aluminum": 0.9 # 90% recyclable
            }
            
            # Convert to kg if needed
            amount_kg = amount * 1000 if unit == "tons" else amount
            
            # Calculate impacts
            landfill_volume = amount_kg * 0.5  # m³ (rough estimate)
            recyclable_amount = amount_kg * recycling_potential.get(waste_type, 0.3)
            
            result = {
                "waste_type": waste_type,
                "amount": amount,
                "unit": unit,
                "amount_kg": amount_kg,
                "decomposition_time": decomposition_time.get(waste_type, "Unknown"),
                "landfill_volume_m3": round(landfill_volume, 2),
                "recyclable_amount_kg": round(recyclable_amount, 2),
                "recycling_rate": f"{recycling_potential.get(waste_type, 0.3)*100}%",
                "environmental_impact": self._assess_waste_severity(waste_type, amount_kg),
                "reduction_strategies": self._get_waste_reduction_tips(waste_type),
                "disposal_recommendations": self._get_disposal_recommendations(waste_type),
                "calculation_date": "2024-01-01"
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error assessing waste impact: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to assess waste impact: {str(e)}"
            }, indent=4)

    def _get_carbon_recommendations(self, activity: str, emissions: float) -> list:
        """Tạo recommendations để giảm carbon footprint."""
        if activity == "transportation":
            return ["Use public transport", "Consider electric vehicles", "Combine trips", "Work from home when possible"]
        elif activity == "energy":
            return ["Switch to renewable energy", "Improve insulation", "Use energy-efficient appliances", "Reduce heating/cooling"]
        elif activity == "food":
            return ["Reduce meat consumption", "Buy local produce", "Minimize food waste", "Choose organic when possible"]
        return ["General carbon reduction strategies"]

    def _get_water_recommendations(self, product: str, usage: float) -> list:
        """Tạo recommendations để giảm water footprint."""
        return ["Choose water-efficient alternatives", "Reduce consumption", "Support sustainable production", "Reuse and recycle"]

    def _assess_water_stress_impact(self, usage: float) -> str:
        """Đánh giá mức độ stress về nước."""
        if usage < 100:
            return "Low impact"
        elif usage < 1000:
            return "Moderate impact"
        else:
            return "High impact"

    def _get_efficiency_tips(self, appliance: str) -> list:
        """Tạo tips để tăng hiệu quả năng lượng."""
        return ["Use energy-efficient settings", "Regular maintenance", "Optimal usage patterns", "Consider upgrading to newer models"]

    def _assess_waste_severity(self, waste_type: str, amount: float) -> str:
        """Đánh giá mức độ nghiêm trọng của chất thải."""
        if waste_type in ["plastic", "electronic"] and amount > 10:
            return "High environmental impact"
        elif amount > 50:
            return "Moderate environmental impact"
        else:
            return "Low environmental impact"

    def _get_waste_reduction_tips(self, waste_type: str) -> list:
        """Tạo tips để giảm chất thải."""
        return ["Reduce consumption", "Reuse when possible", "Recycle properly", "Choose sustainable alternatives"]

    def _get_disposal_recommendations(self, waste_type: str) -> list:
        """Tạo recommendations cho việc xử lý chất thải."""
        return ["Follow local recycling guidelines", "Use designated disposal facilities", "Consider donation or resale", "Proper hazardous waste disposal"]
