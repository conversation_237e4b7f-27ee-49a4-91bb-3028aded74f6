#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho các economics finance tools đã đư<PERSON>c c<PERSON>i tiến.
"""

import sys
import os
import json

# Thêm thư mục gốc vào Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_yahoo_finance_tools():
    """Test Yahoo Finance tools."""
    print("=== Testing Yahoo Finance Tools ===")
    try:
        from tools.economics_finance.yahoo_finance_tools import YahooFinanceTools
        
        tool = YahooFinanceTools()
        
        # Test regular search
        print("--- Regular Market Data Search ---")
        result1 = tool.search_market_data("AAPL", "1mo", "1d")
        print("Market data result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test recent market data
        print("\n--- Recent Market Data ---")
        result2 = tool.get_recent_market_data(5, "US", 7)
        print("Recent market data result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # Test trending stocks
        print("\n--- Trending Stocks ---")
        result3 = tool.get_trending_stocks(5, "most_active")
        print("Trending stocks result:", result3[:200] + "..." if len(result3) > 200 else result3)
        
        print("✅ Yahoo Finance Tools: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing Yahoo Finance Tools: {e}")
    print()

def test_economics_finance_search_toolkit():
    """Test Economics Finance Search Toolkit."""
    print("=== Testing Economics Finance Search Toolkit ===")
    try:
        from tools.economics_finance.economics_finance_search_toolkit import EconomicsFinanceSearchToolkit
        
        toolkit = EconomicsFinanceSearchToolkit()
        
        # Test regular keyword generation
        print("--- Yahoo Finance Keywords ---")
        result1 = toolkit.generate_yahoo_finance_keywords("AAPL", "US", "stock")
        print(result1)
        
        print("\n--- World Bank Keywords ---")
        result2 = toolkit.generate_world_bank_keywords("GDP", "United States", "growth")
        print(result2)
        
        print("\n--- Trading Economics Keywords ---")
        result3 = toolkit.generate_trading_economics_keywords("United States", "inflation", "monthly")
        print(result3)
        
        print("\n--- Financial News Keywords ---")
        result4 = toolkit.generate_financial_news_keywords("Federal Reserve", "monetary policy")
        print(result4)
        
        print("\n--- Wikipedia Economics Keywords ---")
        result5 = toolkit.generate_wikipedia_economics_keywords("inflation", "macroeconomics")
        print(result5)
        
        # Test recent/trending keyword generation
        print("\n--- Yahoo Finance Recent Keywords ---")
        result6 = toolkit.generate_yahoo_finance_recent_keywords("US", 7)
        print(result6)
        
        print("\n--- World Bank Recent Keywords ---")
        result7 = toolkit.generate_world_bank_recent_keywords("Asia", 30)
        print(result7)
        
        print("\n--- Trading Economics Recent Keywords ---")
        result8 = toolkit.generate_trading_economics_recent_keywords("China", 30)
        print(result8)
        
        print("\n--- Financial News Recent Keywords ---")
        result9 = toolkit.generate_financial_news_recent_keywords("earnings", 7)
        print(result9)
        
        print("\n--- Wikipedia Economics Recent Keywords ---")
        result10 = toolkit.generate_wikipedia_economics_recent_keywords(30, "en")
        print(result10)
        
        print("✅ Economics Finance Search Toolkit: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing Search Toolkit: {e}")
    print()

def test_other_tools():
    """Test other economics finance tools briefly."""
    print("=== Testing Other Economics Finance Tools ===")
    
    # Test World Bank Data (if available)
    try:
        print("--- World Bank Data Tools ---")
        from tools.economics_finance.world_bank_data_tools import WorldBankDataTool
        tool = WorldBankDataTool()
        print("World Bank Data tools loaded successfully")
    except Exception as e:
        print(f"World Bank Data tools error: {e}")
    
    # Test IMF Data (if available)
    try:
        print("--- IMF Data Tools ---")
        from tools.economics_finance.imf_data_tools import IMFDataTool
        tool = IMFDataTool()
        print("IMF Data tools loaded successfully")
    except Exception as e:
        print(f"IMF Data tools error: {e}")
    
    # Test OECD Library (if available)
    try:
        print("--- OECD Library Tools ---")
        from tools.economics_finance.oecd_library_tools import OECDLibraryTool
        tool = OECDLibraryTool()
        print("OECD Library tools loaded successfully")
    except Exception as e:
        print(f"OECD Library tools error: {e}")
    
    # Test Wikipedia Economics (if available)
    try:
        print("--- Wikipedia Economics Tools ---")
        from tools.economics_finance.wikipedia_econ_tools import WikipediaEconTool
        tool = WikipediaEconTool()
        print("Wikipedia Economics tools loaded successfully")
    except Exception as e:
        print(f"Wikipedia Economics tools error: {e}")
    
    print()

def test_package_import():
    """Test package-level imports."""
    print("=== Testing Package Import ===")
    try:
        # Test core tools
        from tools.economics_finance.yahoo_finance_tools import YahooFinanceTools
        from tools.economics_finance.economics_finance_search_toolkit import EconomicsFinanceSearchToolkit
        
        print("✅ Core package imports successful")
        
        # Test instantiation
        tools = [
            YahooFinanceTools(),
            EconomicsFinanceSearchToolkit()
        ]
        print("✅ Core tool instantiation successful")
        
    except Exception as e:
        print(f"❌ Package import error: {e}")
    print()

def main():
    """Chạy tất cả các test."""
    print("Testing Economics Finance Tools Functions")
    print("=" * 50)
    print()
    
    # Test các tool đã cải tiến
    test_yahoo_finance_tools()
    test_economics_finance_search_toolkit()
    test_other_tools()
    test_package_import()
    
    print("=" * 50)
    print("Testing completed!")
    print("\n📊 Summary:")
    print("✅ Working: Yahoo Finance Tools (new), Economics Finance Search Toolkit")
    print("⏳ Need checking: World Bank Data, IMF Data, OECD Library, Wikipedia Economics")

if __name__ == "__main__":
    main()
