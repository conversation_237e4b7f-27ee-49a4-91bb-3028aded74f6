"""
<PERSON><PERSON>ng cụ tìm kiếm NASA ADS cho vật lý thiên văn.
"""

import json
import time
import requests
from typing import Optional, Dict, Any, List

from agno.tools import Toolkit
from agno.utils.log import log_debug, logger

class NASAADSPhysicsTools(Toolkit):
    """Công cụ tìm kiếm NASA ADS cho vật lý thiên văn."""
    
    def __init__(self, search_ads_physics: bool = True, token: Optional[str] = None, 
                 timeout: int = 10, max_retries: int = 3, **kwargs):
        """
        Khởi tạo công cụ tìm kiếm NASA ADS cho vật lý.
        
        Args:
            search_ads_physics: <PERSON><PERSON> đăng ký phương thức tìm kiếm hay không
            token: Token API của NASA ADS
            timeout: Thời gian timeout cho request (giây)
            max_retries: Số lần thử lại tối đa
            **kwargs: <PERSON><PERSON><PERSON> tham số khá<PERSON>
        """
        super().__init__(name="nasa_ads_physics_tools", **kwargs)
        self.token = token or "T81buNnE5s6cdB8Fdl4SXzhurreMvnIgnAIostod"
        self.base_url = "https://api.adsabs.harvard.edu/v1/search/query"
        self.timeout = timeout
        self.max_retries = max_retries
        
        # Danh sách các danh mục vật lý
        self.physics_categories = [
            "physics",
            "gr-qc",
            "hep-ph",
            "hep-th",
            "quant-ph",
            "physics.hist-ph"
        ]
        
        # Khởi tạo cache đơn giản
        self.cache = {}
        
        if search_ads_physics:
            self.register(self.search_nasa_ads_physics_papers)
    
    def search_nasa_ads_physics_papers(self, query: str, rows: int = 5) -> str:
        """
        Tìm kiếm bài báo vật lý trên NASA ADS.
        
        Args:
            query: Từ khóa tìm kiếm
            rows: Số lượng kết quả tối đa
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        log_debug(f"Searching NASA ADS Physics for: {query}")
        
        # Kiểm tra cache
        cache_key = f"{query}_{rows}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for: {query}")
            return self.cache[cache_key]
        
        # Tạo truy vấn với các danh mục vật lý
        physics_filter = " OR ".join([f"database:{cat}" for cat in self.physics_categories])
        full_query = f"({query}) AND ({physics_filter})"
        
        headers = {
            "Authorization": f"Bearer {self.token}"
        }
        params = {
            "q": full_query,
            "fl": "title,author,abstract,pubdate,doi,bibcode,keyword_norm,database",
            "rows": rows,
            "sort": "date desc"
        }
        
        # Fallback data nếu API không hoạt động
        fallback_data = [
            {
                "title": f"Fallback data for query: {query}",
                "authors": ["API Unavailable"],
                "abstract": "This is fallback data due to API unavailability",
                "published": "",
                "doi": None,
                "bibcode": "fallback_bibcode",
                "ads_url": "https://ui.adsabs.harvard.edu/",
                "keywords": ["fallback"],
                "databases": self.physics_categories
            }
        ]
        
        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"NASA ADS Physics attempt {attempt+1}/{self.max_retries}")
                response = requests.get(
                    self.base_url, 
                    headers=headers, 
                    params=params,
                    timeout=self.timeout
                )
                response.raise_for_status()
                data = response.json()
                
                results = []
                for doc in data.get("response", {}).get("docs", []):
                    paper = {
                        "title": doc.get("title", [""])[0],
                        "authors": doc.get("author", [])[:3],  # Giới hạn số lượng tác giả
                        "abstract": self._truncate_text(doc.get("abstract", ""), 500),  # Giới hạn độ dài abstract
                        "published": doc.get("pubdate", ""),
                        "doi": doc.get("doi", [None])[0] if doc.get("doi") else None,
                        "bibcode": doc.get("bibcode", ""),
                        "ads_url": f"https://ui.adsabs.harvard.edu/abs/{doc.get('bibcode', '')}" if doc.get("bibcode") else None,
                        "keywords": doc.get("keyword_norm", [])[:5],  # Giới hạn số lượng từ khóa
                        "databases": doc.get("database", [])
                    }
                    results.append(paper)
                
                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                return result_json
                
            except requests.exceptions.Timeout:
                logger.warning(f"NASA ADS Physics timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)  # Chờ 1 giây trước khi thử lại
            except requests.exceptions.RequestException as e:
                logger.warning(f"NASA ADS Physics request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"NASA ADS Physics unexpected error: {e}")
                break
        
        # Trả về dữ liệu fallback nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to search NASA ADS Physics failed for query: {query}")
        logger.info(f"Returning fallback data for query: {query}")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json  # Cache fallback data
        return fallback_json
    
    def _truncate_text(self, text: str, max_length: int = 500) -> str:
        """Giới hạn độ dài văn bản."""
        if not text or len(text) <= max_length:
            return text
        return text[:max_length] + "..."


if __name__ == "__main__":
    # Test công cụ
    tools = NASAADSPhysicsTools()
    result = tools.search_nasa_ads_physics_papers("relativistic jets", rows=3)
    print(result)