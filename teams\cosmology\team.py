"""
Team cho kênh Cosmology & Physics History.
"""

import logging
import json
import time
import asyncio
from typing import Dict, Any, List, Optional, Callable

from agno.team import Team
from agno.agent import Agent

from agents.cosmology.deep_researcher_agent import create_cosmology_deep_researcher_agent
from agents.cosmology.content_creator_agent import create_cosmology_content_creator_agent

from utils.memory_optimization import cleanup_memory
from utils.error_handling import handle_errors
from utils.memory_v2 import create_session, add_message, get_messages, set_user_memory, get_user_memory
from utils.workflow import create_workflow, create_route_workflow, create_collaborate_workflow, create_coordinate_workflow

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CosmologyTeam:
    """Team cho kênh Cosmology & Physics History."""

    def __init__(self, model_id: Optional[str] = None):
        """
        Khởi tạo team.

        Args:
            model_id: ID của mô hình ngôn ng<PERSON>
        """
        self.model_id = model_id
        self.name = "Cosmology & Physics History Team"
        logger.info(f"Initializing {self.name}")

        # Khởi tạo session
        self.session_id = create_session()
        logger.info(f"Created session: {self.session_id}")

        # Khởi tạo các agent
        self.agents = self._initialize_agents()

        # Khởi tạo team
        self.team = self._create_team()

        # Khởi tạo workflow
        self.workflow = self._create_workflow()

    def _initialize_agents(self) -> Dict[str, Agent]:
        """
        Khởi tạo các agent cho team.

        Returns:
            Dictionary các agent
        """
        agents = {}

        # Deep Researcher Agent
        try:
            deep_researcher = create_cosmology_deep_researcher_agent(model_id=self.model_id)
            agents["deep_researcher"] = deep_researcher
            logger.info(f"Initialized Deep Researcher Agent: {deep_researcher.name}")
        except Exception as e:
            logger.error(f"Error initializing Deep Researcher Agent: {e}")

        # Content Creator Agent
        try:
            content_creator = create_cosmology_content_creator_agent(model_id=self.model_id)
            agents["content_creator"] = content_creator
            logger.info(f"Initialized Content Creator Agent: {content_creator.name}")
        except Exception as e:
            logger.error(f"Error initializing Content Creator Agent: {e}")

        # Research Agent
        try:
            from agents.cosmology.research_agent import create_cosmology_research_agent
            research_agent = create_cosmology_research_agent(model_id=self.model_id)
            agents["research"] = research_agent
            logger.info(f"Initialized Research Agent: {research_agent.name}")
        except Exception as e:
            logger.error(f"Error initializing Research Agent: {e}")

        # Reasoning Agent
        try:
            from agents.cosmology.reasoning_agent import create_cosmology_reasoning_agent
            reasoning_agent = create_cosmology_reasoning_agent(model_id=self.model_id)
            agents["reasoning"] = reasoning_agent
            logger.info(f"Initialized Reasoning Agent: {reasoning_agent.name}")
        except Exception as e:
            logger.error(f"Error initializing Reasoning Agent: {e}")

        # Writer Agent
        try:
            from agents.cosmology.writer_agent import create_cosmology_writer_agent
            writer_agent = create_cosmology_writer_agent(model_id=self.model_id)
            agents["writer"] = writer_agent
            logger.info(f"Initialized Writer Agent: {writer_agent.name}")
        except Exception as e:
            logger.error(f"Error initializing Writer Agent: {e}")

        # Social Media Agent
        try:
            from agents.cosmology.social_media_agent import create_cosmology_social_media_agent
            social_media_agent = create_cosmology_social_media_agent(model_id=self.model_id)
            agents["social_media"] = social_media_agent
            logger.info(f"Initialized Social Media Agent: {social_media_agent.name}")
        except Exception as e:
            logger.error(f"Error initializing Social Media Agent: {e}")

        # YouTube Agent
        try:
            from agents.cosmology.youtube_agent import create_cosmology_youtube_agent
            youtube_agent = create_cosmology_youtube_agent(model_id=self.model_id)
            agents["youtube"] = youtube_agent
            logger.info(f"Initialized YouTube Agent: {youtube_agent.name}")
        except Exception as e:
            logger.error(f"Error initializing YouTube Agent: {e}")

        # TTS Agent
        try:
            from agents.cosmology.tts_agent import create_cosmology_tts_agent
            tts_agent = create_cosmology_tts_agent(model_id=self.model_id)
            agents["tts"] = tts_agent
            logger.info(f"Initialized TTS Agent: {tts_agent.name}")
        except Exception as e:
            logger.error(f"Error initializing TTS Agent: {e}")

        logger.info(f"Initialized {len(agents)} agents for {self.name}")
        return agents

    def _create_team(self) -> Team:
        """
        Tạo team Agno.

        Returns:
            Team Agno
        """
        # Lấy danh sách agent
        agent_list = list(self.agents.values())

        # Tạo team
        team_instructions = """
        Bạn là team chuyên về vũ trụ học và lịch sử vật lý.

        Nhiệm vụ của bạn là:
        1. Phân tích câu hỏi của người dùng để xác định các từ khóa tìm kiếm liên quan
        2. Tìm kiếm thông tin từ nhiều nguồn vũ trụ học và lịch sử vật lý khác nhau
        3. Tạo nội dung chất lượng cao dựa trên thông tin tìm được

        Quy trình làm việc:
        1. Deep Researcher Agent phân tích câu hỏi và tìm kiếm thông tin
        2. Content Creator Agent tạo nội dung dựa trên kết quả tìm kiếm

        Luôn sử dụng tiếng Anh khi tương tác với các công cụ, nhưng trả lời người dùng bằng tiếng Việt.
        """

        # Tạo model Ollama cho team
        from agno.models.ollama import Ollama

        # Đảm bảo model_id không phải là None
        model_id = self.model_id
        if model_id is None:
            model_id = "qwen3:4b"
            logger.warning(f"model_id is None, using default model: {model_id}")

        team_model = Ollama(id=model_id)

        # Tạo team
        team = Team(
            name=self.name,
            members=agent_list,
            instructions=team_instructions,
            mode="sequential",
            model=team_model
        )

        logger.info(f"Created team: {self.name} with {len(agent_list)} agents")
        return team

    def _create_workflow(self):
        """
        Tạo workflow cho team.

        Returns:
            Workflow
        """
        # Tạo router agent
        router_agent = self.agents["deep_researcher"]

        # Tạo route workflow
        route_workflow = create_route_workflow(self.agents, router_agent)

        # Tạo collaborate workflow
        collaborate_workflow = create_collaborate_workflow([
            self.agents["research"],
            self.agents["reasoning"],
            self.agents["writer"]
        ])

        # Tạo coordinate workflow
        coordinate_workflow = create_coordinate_workflow(self.agents, self.agents["deep_researcher"])

        # Chọn workflow dựa trên mode
        workflow = route_workflow

        logger.info(f"Created workflow for team: {self.name} with mode: sequential")
        return workflow

    def get_team(self) -> Team:
        """
        Lấy team Agno.

        Returns:
            Team Agno
        """
        return self.team

@handle_errors
def chat_loop():
    """Vòng lặp chat với team Cosmology & Physics History."""
    # Khởi tạo team
    cosmology_team = CosmologyTeam()
    team = cosmology_team.get_team()
    workflow = cosmology_team.workflow

    print(f"\n{'='*50}")
    print(f"Chào mừng đến với {team.name}!")
    print(f"Nhập 'exit' hoặc 'quit' để thoát.")
    print(f"{'='*50}\n")

    # Tạo session và lưu user_id
    user_id = "user_" + str(int(time.time()))
    set_user_memory(user_id, "last_session", cosmology_team.session_id)

    while True:
        try:
            # Nhận input từ người dùng
            user_input = input("\nBạn: ")

            # Kiểm tra thoát
            if user_input.lower() in ["exit", "quit", "thoát"]:
                print("\nCảm ơn bạn đã sử dụng Cosmology & Physics History Team. Tạm biệt!")
                break

            # Lưu tin nhắn vào session
            add_message("user", user_input)

            # Sử dụng workflow hoặc team tùy thuộc vào độ phức tạp của câu hỏi
            if len(user_input.split()) > 10:  # Câu hỏi phức tạp
                # Sử dụng workflow
                workflow.set_context("user_query", user_input)
                workflow.set_context("user_id", user_id)

                # Thực hiện workflow
                result = workflow.execute()

                # Lấy kết quả
                response = result.get("result", "")
                if not response and "results" in result:
                    # Lấy kết quả từ giai đoạn cuối cùng
                    last_result = result["results"][-1]
                    if "response" in last_result:
                        response = last_result["response"]
            else:
                # Sử dụng team trực tiếp cho câu hỏi đơn giản
                response = team.run(user_input)

            # Đảm bảo response không phải là None
            if response is None:
                response = "Xin lỗi, tôi không thể xử lý yêu cầu của bạn lúc này."

            # Lưu tin nhắn vào session
            add_message("assistant", response)

            # Hiển thị response
            print(f"\nCosmology & Physics History Team: {response}")

            # Dọn dẹp bộ nhớ
            cleanup_memory()
        except KeyboardInterrupt:
            print("\nĐã dừng chương trình.")
            break
        except Exception as e:
            logger.error(f"Lỗi trong vòng lặp chat: {e}")
            print(f"\nXin lỗi, đã xảy ra lỗi: {e}")