from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.astronomy.nasa_ads_tools import NasaAdsTools
from tools.common.cot import LCoTTools, MCoTTools
from agno.memory.v2.memory import Memory
from agno.team.team import Team, RunResponse
from agno.workflow import Workflow

agent_memory = Memory


class AstronomyWorkflow(Workflow):
    description = "Workflow cho dự án nghiên cứu thiên văn học."


analysis_agent = Agent(
    model=Ollama(id="qwen3:4b",),
    name="Analysis Agent",
    tools=[LCoTTools()],
    instructions="""
    Bạn là một chuyên gia phân tích câu hỏi về thiên văn học. 
    Nhiệm vụ của bạn là phân tích câu hỏi và xác định các từ khóa chính 
    để tìm kiếm tài liệu khoa học phù hợp.
    """,
    show_tool_calls=True,
    markdown=True,
)

nasa_ads_agent = Agent(
    model=Ollama(id="qwen3:4b",),
    name="NASA ADS Agent",
    tools=[NasaAdsTools()],
    instructions="""
    Bạn là một chuyên gia tìm kiếm tài liệu thiên văn học từ cơ sở dữ liệu NASA ADS.
    Nhiệm vụ của bạn là tìm kiếm và trả về các bài báo khoa học liên quan đến chủ đề được yêu cầu.
    """,
    show_tool_calls=True,
    markdown=True,
)

quality_control_agent = Agent(
    model=Ollama(id="qwen3:4b",),
    name="Quality Control Agent",
    tools=[MCoTTools()],
    instructions="""
    Bạn là một chuyên gia đánh giá chất lượng thông tin khoa học.
    Nhiệm vụ của bạn là kiểm tra và đánh giá độ chính xác, độ tin cậy 
    và mức độ phù hợp của các tài liệu được cung cấp.
    """,
    show_tool_calls=True,
    markdown=True,
)

writer_agent = Agent(
    model=Ollama(id="qwen3:4b",),
    name="Writer Agent",
    tools=[],
    instructions="""
    Bạn là một nhà văn khoa học chuyên nghiệp.
    Nhiệm vụ của bạn là tổng hợp thông tin từ các nguồn khác nhau 
    và viết câu trả lời rõ ràng, chính xác và dễ hiểu.
    """,
    show_tool_calls=True,
    markdown=True,
)

astronomy_team = Team(
    name="Astronomy Research Team",
    mode="coordinate",
    members=[
        analysis_agent,
        nasa_ads_agent,
        quality_control_agent,
        writer_agent
    ],
    instructions="""
    Bạn là một nhóm nghiên cứu thiên văn học.
    Nhiệm vụ của bạn là phân tích câu hỏi, tìm kiếm tài liệu khoa học,
    và đánh giá chất lượng thông tin.
    """,
    show_tool_calls=True,
    show_members_responses=True,
    markdown=True,
)


if __name__ == "__main__":
    