# -*- coding: utf-8 -*-
from typing import List, Dict, Any
import json
from agno.tools import Toolkit
from agno.utils.log import logger

class EvolutionaryBiologySearchToolkit(Toolkit):
    """A custom Toolkit for generating search keywords for evolutionary biology databases.

    This toolkit provides functions to generate search keywords for NCBI Taxonomy,
    BioLib, Open Tree, Wikipedia Evolutionary, and specialized evolutionary analysis, tailored for evolutionary research.
    """

    # == Detailed Instructions for the Agent ==
    instruction = [
        "Bạn là một trợ lý nghiên cứu sinh học tiến hóa, chuyên cung cấp từ khóa tìm kiếm tối ưu cho các cơ sở dữ liệu tiến hóa.",
        "<PERSON><PERSON> sử dụng các công cụ trong EvolutionaryBiologySearchToolkit, tuân thủ các định dạng từ khóa được chỉ định như sau:",
        "- NCBI Taxonomy: Sử dụng định dạng 'organism[ORGN]' hoặc 'taxid' (ví dụ: 'Homo sapiens[ORGN]', 'taxid:9606', 'Primates[ORGN]').",
        "- BioLib: Sử dụng định dạng 'scientific name' hoặc 'family*' (ví dụ: 'Homo.sapiens', 'Felidae*', 'Drosophila.melanogaster').",
        "- Open Tree: Sử dụng định dạng 'phylogenetic study' (ví dụ: 'mammalian phylogeny', 'plant evolution', 'bacterial tree').",
        "- Wikipedia Evolutionary: Sử dụng định dạng 'evolutionary concept' (ví dụ: 'natural selection', 'speciation', 'molecular evolution').",
        "- Phylogenetics: Sử dụng định dạng 'tree analysis' (ví dụ: 'maximum likelihood', 'bayesian inference', 'molecular clock').",
        "- Genomics: Sử dụng định dạng 'genome evolution' (ví dụ: 'gene duplication', 'horizontal transfer', 'genome rearrangement').",
        "- Molecular Evolution: Sử dụng định dạng 'molecular analysis' (ví dụ: 'protein evolution', 'codon usage', 'selection analysis').",
        "Ngoài ra, toolkit cũng hỗ trợ tạo từ khóa cho việc tìm kiếm nội dung mới nhất và trending:",
        "- NCBI Taxonomy Recent: Tạo từ khóa cho taxonomy changes và popular lineages mới theo rank.",
        "- BioLib Recent: Tạo từ khóa cho taxonomic updates và trending species mới theo category.",
        "- Open Tree Recent: Tạo từ khóa cho phylogenetic studies và evolutionary trees mới theo method.",
        "- Wikipedia Evolutionary Recent: Tạo từ khóa cho evolutionary articles và concepts mới được cập nhật.",
        "- Phylogenetics Recent: Tạo từ khóa cho phylogenetic methods và tree analysis mới theo approach.",
        "Kiểm tra tính hợp lệ của tham số đầu vào và trả về từ khóa phù hợp với từng cơ sở dữ liệu.",
        "Trả về kết quả dưới dạng JSON với trạng thái ('status'), danh sách từ khóa ('keywords'), và thông báo ('message').",
        "Nếu có lỗi, trả về trạng thái 'error' với mô tả lỗi chi tiết."
    ]

    # == Detailed Few-Shot Examples ==
    few_shot_examples = [
        {
            "user": "Tìm thông tin về human evolution và primate phylogeny.",
            "tool_calls": [
                {
                    "name": "generate_ncbi_taxonomy_keywords",
                    "arguments": {"organism": "Homo sapiens", "rank": "species", "lineage": "Primates"}
                },
                {
                    "name": "generate_open_tree_keywords",
                    "arguments": {"study_type": "phylogenetic", "taxa": "primates", "method": "molecular"}
                },
                {
                    "name": "generate_wikipedia_evolutionary_keywords",
                    "arguments": {"concept": "human evolution", "field": "paleoanthropology"}
                }
            ]
        },
        {
            "user": "Tìm recent phylogenetic studies và trending evolutionary topics.",
            "tool_calls": [
                {
                    "name": "generate_ncbi_taxonomy_recent_keywords",
                    "arguments": {"rank": "species", "days_back": 30}
                },
                {
                    "name": "generate_open_tree_recent_keywords",
                    "arguments": {"method": "maximum_likelihood", "days_back": 60}
                },
                {
                    "name": "generate_phylogenetics_recent_keywords",
                    "arguments": {"approach": "bayesian", "days_back": 14}
                }
            ]
        },
        {
            "user": "Tìm nghiên cứu về gene duplication và molecular evolution.",
            "tool_calls": [
                {
                    "name": "generate_genomics_keywords",
                    "arguments": {"analysis_type": "gene duplication", "organism": "mammals", "method": "comparative"}
                },
                {
                    "name": "generate_molecular_evolution_keywords",
                    "arguments": {"analysis": "protein evolution", "approach": "phylogenetic", "data_type": "sequence"}
                },
                {
                    "name": "generate_biolib_keywords",
                    "arguments": {"taxon": "Mammalia", "data_type": "genomic", "analysis": "comparative"}
                }
            ]
        }
    ]

    def __init__(self):
        """Initializes the EvolutionaryBiologySearchToolkit."""
        super().__init__(
            name="evolutionary_biology_search_toolkit",
            tools=[
                self.generate_ncbi_taxonomy_keywords,
                self.generate_biolib_keywords,
                self.generate_open_tree_keywords,
                self.generate_wikipedia_evolutionary_keywords,
                self.generate_phylogenetics_keywords,
                self.generate_genomics_keywords,
                self.generate_molecular_evolution_keywords,
                self.generate_ncbi_taxonomy_recent_keywords,
                self.generate_biolib_recent_keywords,
                self.generate_open_tree_recent_keywords,
                self.generate_wikipedia_evolutionary_recent_keywords,
                self.generate_phylogenetics_recent_keywords
            ],
            instructions=self.instruction
        )
        self.few_shot_examples = self.few_shot_examples
        logger.info("EvolutionaryBiologySearchToolkit initialized.")

    def generate_ncbi_taxonomy_keywords(self, organism: str, rank: str = None, lineage: str = None) -> str:
        """Generates search keywords for NCBI Taxonomy.

        Args:
            organism: The organism name (e.g., 'Homo sapiens', 'Drosophila melanogaster').
            rank: Optional taxonomic rank (e.g., 'species', 'genus', 'family').
            lineage: Optional lineage filter (e.g., 'Primates', 'Vertebrata').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating NCBI Taxonomy keywords for organism: '{organism}', rank: '{rank}', lineage: '{lineage}'")
        try:
            if not organism.strip():
                raise ValueError("Organism cannot be empty.")

            # Tạo từ khóa cho NCBI Taxonomy
            keywords = [f"{organism}[ORGN]"]
            if rank:
                keywords.append(f"{organism} {rank}")
            if lineage:
                keywords.append(f"{organism} {lineage}")

            # Thêm từ khóa mở rộng
            keywords.extend([
                f"{organism} taxonomy", f"{organism} phylogeny", f"{organism} evolution",
                f"{organism} lineage", f"{organism} classification"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated NCBI Taxonomy keywords for organism '{organism}', rank '{rank or 'all'}', lineage '{lineage or 'all'}'.",
                "search_type": "taxonomic_data",
                "parameters": {
                    "organism": organism,
                    "rank": rank,
                    "lineage": lineage
                }
            }
            logger.debug(f"NCBI Taxonomy keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating NCBI Taxonomy keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate NCBI Taxonomy keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_biolib_keywords(self, taxon: str, data_type: str = None, analysis: str = None) -> str:
        """Generates search keywords for BioLib."""
        logger.info(f"Generating BioLib keywords for taxon: '{taxon}', data_type: '{data_type}', analysis: '{analysis}'")
        try:
            if not taxon.strip():
                raise ValueError("Taxon cannot be empty.")

            # Format for BioLib (use dots for species names)
            formatted_taxon = taxon.replace(" ", ".")
            keywords = [formatted_taxon]

            if data_type:
                keywords.append(f"{formatted_taxon} {data_type}")
            if analysis:
                keywords.append(f"{formatted_taxon} {analysis}")
                if data_type:
                    keywords.append(f"{formatted_taxon} {data_type} {analysis}")

            keywords.extend([
                f"{formatted_taxon} ecology", f"{formatted_taxon} morphology", f"{formatted_taxon} distribution",
                f"{formatted_taxon} synonyms", f"{formatted_taxon} taxonomy"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated BioLib keywords for taxon '{taxon}', data_type '{data_type or 'all'}', analysis '{analysis or 'all'}'.",
                "search_type": "taxonomic_ecology",
                "parameters": {"taxon": taxon, "data_type": data_type, "analysis": analysis}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate BioLib keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_open_tree_keywords(self, study_type: str, taxa: str = None, method: str = None) -> str:
        """Generates search keywords for Open Tree of Life."""
        logger.info(f"Generating Open Tree keywords for study_type: '{study_type}', taxa: '{taxa}', method: '{method}'")
        try:
            if not study_type.strip():
                raise ValueError("Study type cannot be empty.")

            keywords = [study_type]
            if taxa:
                keywords.append(f"{study_type} {taxa}")
            if method:
                keywords.append(f"{study_type} {method}")
                if taxa:
                    keywords.append(f"{study_type} {taxa} {method}")

            keywords.extend([
                f"{study_type} tree", f"{study_type} phylogeny", f"{study_type} evolution",
                f"tree of life {study_type}", f"{study_type} relationships"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Open Tree keywords for study_type '{study_type}', taxa '{taxa or 'all'}', method '{method or 'all'}'.",
                "search_type": "phylogenetic_trees",
                "parameters": {"study_type": study_type, "taxa": taxa, "method": method}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Open Tree keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_evolutionary_keywords(self, concept: str, field: str = None) -> str:
        """Generates search keywords for Wikipedia evolutionary topics."""
        logger.info(f"Generating Wikipedia evolutionary keywords for concept: '{concept}', field: '{field}'")
        try:
            if not concept.strip():
                raise ValueError("Concept cannot be empty.")

            keywords = [concept]
            if field:
                keywords.append(f"{concept} {field}")

            keywords.extend([
                f"{concept} evolution", f"{concept} evolutionary", f"{concept} biology",
                f"{concept} theory", f"{concept} mechanism"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia evolutionary keywords for concept '{concept}' and field '{field or 'general'}'.",
                "search_type": "evolutionary_concepts",
                "parameters": {"concept": concept, "field": field}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia evolutionary keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_phylogenetics_keywords(self, analysis: str, approach: str = None, data_type: str = None) -> str:
        """Generates search keywords for phylogenetic analysis."""
        logger.info(f"Generating Phylogenetics keywords for analysis: '{analysis}', approach: '{approach}', data_type: '{data_type}'")
        try:
            if not analysis.strip():
                raise ValueError("Analysis cannot be empty.")

            keywords = [analysis]
            if approach:
                keywords.append(f"{analysis} {approach}")
            if data_type:
                keywords.append(f"{analysis} {data_type}")
                if approach:
                    keywords.append(f"{analysis} {approach} {data_type}")

            keywords.extend([
                f"{analysis} phylogenetic", f"{analysis} tree", f"{analysis} evolutionary",
                f"phylogenetic {analysis}", f"{analysis} reconstruction"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Phylogenetics keywords for analysis '{analysis}', approach '{approach or 'all'}', data_type '{data_type or 'all'}'.",
                "search_type": "phylogenetic_analysis",
                "parameters": {"analysis": analysis, "approach": approach, "data_type": data_type}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Phylogenetics keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_genomics_keywords(self, analysis_type: str, organism: str = None, method: str = None) -> str:
        """Generates search keywords for genomic evolution analysis."""
        logger.info(f"Generating Genomics keywords for analysis_type: '{analysis_type}', organism: '{organism}', method: '{method}'")
        try:
            if not analysis_type.strip():
                raise ValueError("Analysis type cannot be empty.")

            keywords = [analysis_type]
            if organism:
                keywords.append(f"{analysis_type} {organism}")
            if method:
                keywords.append(f"{analysis_type} {method}")
                if organism:
                    keywords.append(f"{analysis_type} {organism} {method}")

            keywords.extend([
                f"{analysis_type} genomic", f"{analysis_type} genome", f"{analysis_type} evolution",
                f"genome {analysis_type}", f"{analysis_type} comparative"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Genomics keywords for analysis_type '{analysis_type}', organism '{organism or 'all'}', method '{method or 'all'}'.",
                "search_type": "genomic_evolution",
                "parameters": {"analysis_type": analysis_type, "organism": organism, "method": method}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Genomics keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_molecular_evolution_keywords(self, analysis: str, approach: str = None, data_type: str = None) -> str:
        """Generates search keywords for molecular evolution analysis."""
        logger.info(f"Generating Molecular Evolution keywords for analysis: '{analysis}', approach: '{approach}', data_type: '{data_type}'")
        try:
            if not analysis.strip():
                raise ValueError("Analysis cannot be empty.")

            keywords = [analysis]
            if approach:
                keywords.append(f"{analysis} {approach}")
            if data_type:
                keywords.append(f"{analysis} {data_type}")
                if approach:
                    keywords.append(f"{analysis} {approach} {data_type}")

            keywords.extend([
                f"{analysis} molecular", f"{analysis} evolution", f"{analysis} sequence",
                f"molecular {analysis}", f"{analysis} evolutionary"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Molecular Evolution keywords for analysis '{analysis}', approach '{approach or 'all'}', data_type '{data_type or 'all'}'.",
                "search_type": "molecular_evolution",
                "parameters": {"analysis": analysis, "approach": approach, "data_type": data_type}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Molecular Evolution keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    # == Recent/Trending Keywords Functions ==

    def generate_ncbi_taxonomy_recent_keywords(self, rank: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent taxonomy changes on NCBI."""
        logger.info(f"Generating NCBI Taxonomy recent keywords for rank: '{rank}', days_back: {days_back}")
        try:
            keywords = ["recent taxonomy changes", "latest classifications", "new taxonomic revisions"]
            if rank:
                keywords.extend([f"recent {rank}", f"latest {rank}", f"new {rank}"])
            keywords.extend([
                "recent lineage updates", "latest phylogenetic revisions", "new taxonomic data",
                f"last {days_back} days", "recent taxonomy"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated NCBI Taxonomy recent keywords for rank '{rank or 'all'}' within last {days_back} days.",
                "search_type": "recent_taxonomy_changes",
                "parameters": {"rank": rank, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate NCBI Taxonomy recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_biolib_recent_keywords(self, category: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent taxonomic updates on BioLib."""
        logger.info(f"Generating BioLib recent keywords for category: '{category}', days_back: {days_back}")
        try:
            keywords = ["recent taxonomic updates", "latest species data", "new ecological information"]
            if category:
                keywords.extend([f"recent {category}", f"latest {category}", f"new {category}"])
            keywords.extend([
                "recent species descriptions", "latest morphological data", "new distribution records",
                f"last {days_back} days", "recent biolib updates"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated BioLib recent keywords for category '{category or 'all'}' within last {days_back} days.",
                "search_type": "recent_taxonomic_updates",
                "parameters": {"category": category, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate BioLib recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_open_tree_recent_keywords(self, method: str = None, days_back: int = 60) -> str:
        """Generates keywords for recent phylogenetic studies."""
        logger.info(f"Generating Open Tree recent keywords for method: '{method}', days_back: {days_back}")
        try:
            keywords = ["recent phylogenetic studies", "latest evolutionary trees", "new phylogenetic analyses"]
            if method:
                keywords.extend([f"recent {method}", f"latest {method}", f"new {method}"])
            keywords.extend([
                "recent tree reconstructions", "latest molecular phylogenies", "new evolutionary relationships",
                f"last {days_back} days", "recent phylogenetic research"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Open Tree recent keywords for method '{method or 'all'}' within last {days_back} days.",
                "search_type": "recent_phylogenetic_studies",
                "parameters": {"method": method, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Open Tree recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_evolutionary_recent_keywords(self, days_back: int = 30, language: str = "en") -> str:
        """Generates keywords for recent evolutionary articles on Wikipedia."""
        logger.info(f"Generating Wikipedia evolutionary recent keywords for days_back: {days_back}, language: '{language}'")
        try:
            keywords = [
                "recent evolutionary articles", "new evolutionary theories", "latest evolutionary research",
                "recent speciation studies", "new phylogenetic discoveries", "latest evolutionary biology",
                f"last {days_back} days evolution", "recent evolutionary updates"
            ]

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia evolutionary recent keywords within last {days_back} days for language '{language}'.",
                "search_type": "recent_evolutionary_articles",
                "parameters": {"days_back": days_back, "language": language}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia evolutionary recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_phylogenetics_recent_keywords(self, approach: str = None, days_back: int = 14) -> str:
        """Generates keywords for recent phylogenetic methods and analyses."""
        logger.info(f"Generating Phylogenetics recent keywords for approach: '{approach}', days_back: {days_back}")
        try:
            keywords = ["recent phylogenetic methods", "latest tree reconstruction", "new evolutionary analysis"]
            if approach:
                keywords.extend([f"recent {approach}", f"latest {approach}", f"new {approach}"])
            keywords.extend([
                "recent molecular clocks", "latest divergence estimates", "new phylogenetic software",
                f"last {days_back} days", "recent phylogenetic advances"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Phylogenetics recent keywords for approach '{approach or 'all'}' within last {days_back} days.",
                "search_type": "recent_phylogenetic_methods",
                "parameters": {"approach": approach, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Phylogenetics recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)