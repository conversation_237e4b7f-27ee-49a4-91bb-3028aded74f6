from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger, log_warning
import aiohttp
import asyncio
import re
import json
from datetime import datetime, timedelta
from bs4 import BeautifulSoup

# Cache kết quả tìm kiếm trong 1 giờ
SEARCH_CACHE = {}
CACHE_EXPIRY = 3600  # 1 giờ

class CacheEntry:
    def __init__(self, data: Any):
        self.data = data
        self.timestamp = datetime.now()
    
    def is_expired(self) -> bool:
        return (datetime.now() - self.timestamp).total_seconds() > CACHE_EXPIRY

def cache_key(query: str, topic: Optional[str], year: Optional[str], limit: int) -> str:
    """Tạo khóa cache duy nhất cho mỗi yêu cầu tìm kiếm"""
    return f"cgiar:{query}:{topic or 'all'}:{year or 'all'}:{limit}"

class CGIARTools(Toolkit):
    """
    CGIAR Tool cho tìm kiếm nghiê<PERSON> c<PERSON>, đ<PERSON><PERSON> mớ<PERSON>, công nghệ nông nghiệp từ CGIAR.
    Hỗ trợ caching, retry tự động và xử lý bất đồng bộ để tối ưu hiệu suất.
    """

    def __init__(self, **kwargs):
        super().__init__(
            name="cgiar_tools",
            **kwargs
        )
        self.register(self.search_cgiar)
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=20, connect=8)
        self.retry_attempts = 3
        self.retry_delay = 2  # giây
        self.base_url = "https://www.cgiar.org"

    async def _get_session(self) -> aiohttp.ClientSession:
        """Tạo hoặc trả về session hiện có"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                timeout=self.timeout,
                headers={
                    "User-Agent": "Mozilla/5.0 (compatible; CGIARSearchBot/1.0)",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                }
            )
        return self.session

    async def _make_request(self, url: str, params: Dict[str, Any], attempt: int = 1) -> Dict[str, Any]:
        """Thực hiện HTTP request với cơ chế retry"""
        session = await self._get_session()
        
        try:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    return {"status": "success", "html": await response.text()}
                elif response.status in [429, 502, 503, 504] and attempt <= self.retry_attempts:
                    retry_after = int(response.headers.get('Retry-After', self.retry_delay * attempt))
                    await asyncio.sleep(retry_after)
                    return await self._make_request(url, params, attempt + 1)
                else:
                    return {"status": "error", "message": f"HTTP {response.status}: {response.reason}"}
        except asyncio.TimeoutError:
            if attempt <= self.retry_attempts:
                await asyncio.sleep(self.retry_delay * attempt)
                return await self._make_request(url, params, attempt + 1)
            return {"status": "error", "message": "Request timeout after multiple retries"}
        except Exception as e:
            if attempt <= self.retry_attempts:
                await asyncio.sleep(self.retry_delay * attempt)
                return await self._make_request(url, params, attempt + 1)
            return {"status": "error", "message": str(e)}

    async def search_cgiar(self, query: str, topic: Optional[str] = None, year: Optional[str] = None, limit: int = 5) -> str:
        """
        Tìm kiếm CGIAR cho nghiên cứu, đổi mới, công nghệ nông nghiệp với hiệu suất cao.

        Parameters:
        - query: Từ khóa tìm kiếm (ví dụ: 'climate resilient crops', 'rice breeding')
        - topic: Chủ đề cụ thể (ví dụ: 'innovation', 'research', 'climate')
        - year: Năm hoặc khoảng năm (ví dụ: '2022', '2018-2022')
        - limit: Số lượng kết quả tối đa (default: 5, tối đa 20)

        Returns:
        - JSON string chứa kết quả tìm kiếm hoặc thông báo lỗi
        """
        # Kiểm tra cache trước
        cache_key_str = cache_key(query, topic, year, limit)
        if cache_key_str in SEARCH_CACHE and not SEARCH_CACHE[cache_key_str].is_expired():
            logger.info(f"Lấy kết quả từ cache cho: {query}")
            return json.dumps(SEARCH_CACHE[cache_key_str].data)

        logger.info(f"Tìm kiếm CGIAR: query={query}, topic={topic}, year={year}, limit={limit}")

        try:
            # Xây dựng URL tìm kiếm
            search_url = f"{self.base_url}/search/"
            params = {
                "s": query,
                "post_type[]": ["research", "publication", "news"]
            }

            if topic:
                params["topics[]"] = topic.lower()
            if year:
                params["years[]"] = year

            # Thực hiện request
            result = await self._make_request(search_url, params)
            
            if result["status"] != "success":
                return json.dumps({"status": "error", "message": "Không thể kết nối đến CGIAR"})

            # Phân tích kết quả HTML
            soup = BeautifulSoup(result["html"], 'html.parser')
            search_results = []
            
            for item in soup.select('.search-results .result-item')[:limit]:
                try:
                    title_elem = item.select_one('.result-title a')
                    if not title_elem:
                        continue
                        
                    title = title_elem.get_text(strip=True)
                    url = title_elem['href']
                    
                    # Làm cho URL đầy đủ nếu cần
                    if not url.startswith('http'):
                        url = f"{self.base_url.rstrip('/')}/{url.lstrip('/')}"
                    
                    # Trích xuất mô tả
                    description = ''
                    desc_elem = item.select_one('.result-excerpt')
                    if desc_elem:
                        description = desc_elem.get_text(strip=True)
                    
                    # Trích xuất loại và ngày
                    meta = item.select_one('.result-meta')
                    item_type = meta.select_one('.result-type').get_text(strip=True) if meta and meta.select_one('.result-type') else 'Unknown'
                    date = meta.select_one('.result-date').get_text(strip=True) if meta and meta.select_one('.result-date') else ''
                    
                    search_results.append({
                        'title': title,
                        'url': url,
                        'description': description,
                        'type': item_type,
                        'date': date
                    })
                except Exception as e:
                    log_warning(f"Lỗi khi xử lý kết quả tìm kiếm: {str(e)}")
                    continue

            # Tạo kết quả và lưu vào cache
            response_data = {
                "status": "success",
                "query": query,
                "topic": topic,
                "year": year,
                "results": search_results,
                "source": "CGIAR",
                "search_url": f"{search_url}?s={query}",
                "timestamp": datetime.now().isoformat()
            }
            
            SEARCH_CACHE[cache_key_str] = CacheEntry(response_data)
            return json.dumps(response_data)

        except Exception as e:
            error_msg = f"Lỗi khi tìm kiếm CGIAR: {str(e)}"
            logger.error(error_msg)
            return json.dumps({"status": "error", "message": error_msg})

# Tạo instance của tool để import
cgiar_tool = CGIARTools()