# Tài liệu Hướng dẫn Sử dụng Agno Framework cho Người Mới Bắt đầu

Chào mừng bạn đến với Agno! Đây là một framework nhẹ và hiệu năng cao để xây dựng các Agent AI. Tài liệu này nhằm mục đích hướng dẫn những người mới bắt đầu cách sử dụng các tiện ích và khái niệm cốt lõi củ<PERSON>, tập trung vào các chức năng bạn đã yêu cầu và một số thành phần quan trọng khác.

Tài liệu được viết bằng tiếng Việt, với các giải thích chi tiết và ví dụ ở mức độ trung bình, phù hợp cho những người đang bắt đầu xây dựng Agent đầu tiên của mình.

## <PERSON><PERSON><PERSON> lụ<PERSON>

1.  [Hướng dẫn sử dụng `run` và `arun`](#hướng-dẫn-sử-dụng-run-và-arun-trong-agno)
2.  [Hướng dẫn sử dụng `print_response` và `aprint_response`](#hướng-dẫn-sử-dụng-print_response-và-aprint_response-trong-agno)
3.  [Hướng dẫn sử dụng Bộ nhớ SQLite (`memory sqlite`)](#hướng-dẫn-sử-dụng-bộ-nhớ-sqlite-memory-sqlite-trong-agno)
4.  [Hướng dẫn sử dụng Trạng thái Phiên (`session_state`)](#hướng-dẫn-sử-dụng-trạng-thái-phiên-session_state-trong-agno)
5.  [Các Khái niệm và Tiện ích Quan trọng Khác](#các-khái-niệm-và-tiện-ích-quan-trọng-khác-cho-người-mới-bắt-đầu-với-agno)

---





---



# Hướng dẫn sử dụng các tiện ích `run` và `arun` trong Agno

Trong framework Agno, các phương thức `run` và `arun` là cốt lõi để thực thi một Agent. Chúng đóng vai trò là điểm khởi đầu cho một lượt tương tác với Agent, nhận đầu vào từ người dùng (hoặc hệ thống khác) và trả về kết quả xử lý của Agent.

## Mục đích và Khác biệt chính

Cả `run` và `arun` đều thực hiện cùng một chức năng cơ bản: chạy Agent với một thông điệp đầu vào và các cấu hình tùy chọn. Sự khác biệt chính nằm ở cách thức thực thi:

*   **`run` (Synchronous - Đồng bộ):** Phương thức này thực thi Agent một cách tuần tự và chặn luồng thực thi chính cho đến khi Agent hoàn thành xử lý và trả về kết quả. Nó phù hợp cho các kịch bản đơn giản hoặc khi bạn cần kết quả ngay lập tức trước khi tiếp tục các tác vụ khác trong cùng một luồng.
*   **`arun` (Asynchronous - Bất đồng bộ):** Phương thức này được thiết kế để chạy trong môi trường bất đồng bộ (sử dụng `asyncio` của Python). Nó không chặn luồng chính, cho phép các tác vụ khác chạy song song trong khi Agent đang xử lý. Điều này đặc biệt hữu ích trong các ứng dụng web, API, hoặc các hệ thống cần xử lý nhiều yêu cầu đồng thời mà không làm giảm hiệu năng.

## Cách sử dụng cơ bản

Việc sử dụng `run` và `arun` khá tương đồng. Bạn cần có một đối tượng `Agent` đã được khởi tạo và sau đó gọi phương thức tương ứng với thông điệp đầu vào.

```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat
import asyncio

# Khởi tạo Agent đơn giản
agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    description="Bạn là một trợ lý AI hữu ích."
)

# --- Sử dụng run (đồng bộ) ---
print("--- Chạy đồng bộ với run ---")
response_sync = agent.run("Xin chào! Bạn có thể giúp gì?")
print(f"Kết quả đồng bộ: {response_sync}")

# --- Sử dụng arun (bất đồng bộ) ---
async def main_async():
    print("\n--- Chạy bất đồng bộ với arun ---")
    response_async = await agent.arun("Kể một câu chuyện cười ngắn.")
    print(f"Kết quả bất đồng bộ: {response_async}")

# Chạy hàm bất đồng bộ
asyncio.run(main_async())
```

Trong ví dụ trên:

1.  Chúng ta khởi tạo một `Agent` cơ bản sử dụng mô hình `gpt-4o` từ OpenAI.
2.  Gọi `agent.run("...")` để thực thi đồng bộ. Kết quả (thường là một chuỗi văn bản phản hồi từ mô hình) được trả về và gán cho `response_sync`.
3.  Để sử dụng `arun`, chúng ta định nghĩa một hàm `async def main_async()` và gọi `await agent.arun("...")` bên trong nó. Từ khóa `await` là cần thiết vì `arun` là một coroutine. Kết quả được gán cho `response_async`.
4.  Cuối cùng, `asyncio.run(main_async())` được dùng để chạy hàm bất đồng bộ và nhận kết quả.

## Các tham số quan trọng

Cả `run` và `arun` đều chấp nhận nhiều tham số tùy chọn để kiểm soát hành vi của Agent trong quá trình thực thi. Dưới đây là một số tham số phổ biến và hữu ích cho người mới bắt đầu:

*   **`message` (bắt buộc):** Thông điệp đầu vào cho Agent. Đây có thể là một chuỗi (`str`), một từ điển (`dict`), hoặc một danh sách (`list`) tùy thuộc vào cách Agent được cấu hình để xử lý đầu vào.
*   **`stream` (`bool`, mặc định: `False`):** Nếu đặt là `True`, kết quả sẽ được trả về dưới dạng một generator (cho `run`) hoặc async generator (cho `arun`), cho phép bạn xử lý từng phần nhỏ của phản hồi khi chúng được tạo ra (streaming). Điều này hữu ích để hiển thị kết quả dần dần cho người dùng thay vì chờ toàn bộ phản hồi.
*   **`user_id` (`str`, tùy chọn):** Định danh người dùng. Thông tin này có thể được sử dụng bởi các thành phần khác như Memory để lưu trữ và truy xuất lịch sử tương tác theo từng người dùng.
*   **`session_id` (`str`, tùy chọn):** Định danh phiên làm việc. Tương tự `user_id`, nó giúp quản lý trạng thái và bộ nhớ trong ngữ cảnh của một phiên tương tác cụ thể.
*   **`show_reasoning` (`bool`, mặc định: `False` trong `Agent`, nhưng có thể được ghi đè):** Khi sử dụng các Agent có khả năng suy luận (Reasoning), đặt tham số này thành `True` sẽ yêu cầu Agent hiển thị các bước suy luận trung gian mà nó thực hiện để đi đến kết quả cuối cùng. Điều này rất hữu ích cho việc gỡ lỗi và hiểu cách Agent "suy nghĩ".
*   **`stream_intermediate_steps` (`bool`, mặc định: `False`):** Khi `stream=True` và Agent có sử dụng tools hoặc reasoning, đặt tham số này thành `True` sẽ stream cả các bước trung gian (như tool calls, tool outputs, reasoning steps) chứ không chỉ stream kết quả cuối cùng.
*   **`tools` (`list`, tùy chọn):** Danh sách các tools (công cụ) mà Agent có thể sử dụng trong lần chạy này. Nó có thể ghi đè danh sách tools mặc định được cấu hình khi khởi tạo Agent.
*   **`instructions` (`list`, tùy chọn):** Danh sách các chỉ dẫn bổ sung cho Agent trong lần chạy này, ghi đè các chỉ dẫn mặc định.

**Ví dụ với tham số `stream` và `show_reasoning`:**

```python
# (Giả sử agent đã được khởi tạo với ReasoningTools và YFinanceTools như ví dụ trong README)

print("\n--- Chạy đồng bộ với stream và reasoning ---")
# Lưu ý: print_response là một tiện ích khác, sẽ được giải thích sau.
# Ở đây chúng ta minh họa các tham số cho run/arun.
# agent.run("Phân tích cổ phiếu NVDA", stream=True, show_full_reasoning=True, stream_intermediate_steps=True)
# Thay vì dùng print_response, ta có thể lặp qua generator trả về từ run:
response_stream = agent.run("Phân tích cổ phiếu NVDA", stream=True, show_full_reasoning=True, stream_intermediate_steps=True)
print("Streaming response:")
for chunk in response_stream:
    print(chunk, end="", flush=True)
print("\n")

# Tương tự với arun
async def main_async_stream():
    print("--- Chạy bất đồng bộ với stream và reasoning ---")
    response_async_stream = agent.arun("Phân tích cổ phiếu AAPL", stream=True, show_full_reasoning=True, stream_intermediate_steps=True)
    print("Streaming async response:")
    async for chunk in response_async_stream:
        print(chunk, end="", flush=True)
    print("\n")

asyncio.run(main_async_stream())
```

## Kết quả trả về

*   **Khi `stream=False` (mặc định):**
    *   `run`: Trả về kết quả cuối cùng của Agent, thường là một chuỗi (`str`) hoặc một đối tượng Pydantic nếu `response_model` được cấu hình.
    *   `arun`: Trả về một coroutine, khi `await` sẽ nhận được kết quả cuối cùng tương tự như `run`.
*   **Khi `stream=True`:**
    *   `run`: Trả về một **iterator** (generator). Bạn có thể lặp qua nó để nhận từng phần (`chunk`) của phản hồi.
    *   `arun`: Trả về một **async iterator** (async generator). Bạn có thể sử dụng `async for` để lặp qua nó và nhận từng phần (`chunk`).

Các `chunk` này có thể là các phần văn bản của phản hồi cuối cùng, hoặc các thông tin về bước trung gian (nếu `stream_intermediate_steps=True`).

## Tương tác với vòng đời Agent

Khi bạn gọi `run` hoặc `arun`, một chuỗi các sự kiện xảy ra bên trong Agent:

1.  **Chuẩn bị:** Agent chuẩn bị ngữ cảnh thực thi, bao gồm thông điệp đầu vào, lịch sử trò chuyện (nếu có), thông tin người dùng/phiên, các chỉ dẫn, và danh sách tools khả dụng.
2.  **Gọi mô hình (Model Call):** Agent gửi yêu cầu đến mô hình ngôn ngữ lớn (LLM) đã cấu hình, kèm theo prompt được xây dựng từ các thông tin ở bước chuẩn bị.
3.  **Xử lý phản hồi mô hình:**
    *   Nếu mô hình trả về một phản hồi trực tiếp, Agent xử lý và trả về kết quả (hoặc bắt đầu stream kết quả).
    *   Nếu mô hình yêu cầu sử dụng một tool (Function Calling/Tool Calling), Agent sẽ:
        *   Phân tích yêu cầu gọi tool.
        *   Thực thi tool tương ứng.
        *   Nhận kết quả từ tool.
        *   Gửi lại kết quả tool cho mô hình để tiếp tục xử lý.
        *   Lặp lại quá trình này nếu mô hình yêu cầu gọi thêm tool.
    *   Nếu Agent có cấu hình Reasoning, các bước suy luận sẽ được thực hiện xen kẽ với các lần gọi mô hình và tool.
4.  **Cập nhật trạng thái:** Agent có thể cập nhật bộ nhớ (Memory) và lưu trữ (Storage) dựa trên kết quả của lượt chạy (ví dụ: lưu lịch sử trò chuyện, cập nhật thông tin người dùng).
5.  **Trả kết quả:** Agent trả về kết quả cuối cùng hoặc stream các phần kết quả/bước trung gian.

Việc hiểu rõ luồng thực thi này giúp bạn cấu hình Agent hiệu quả hơn và gỡ lỗi khi cần thiết.

`run` và `arun` là hai phương thức nền tảng để làm việc với Agent trong Agno. Việc lựa chọn giữa chúng phụ thuộc vào yêu cầu về đồng bộ/bất đồng bộ của ứng dụng bạn đang xây dựng. Nắm vững cách sử dụng và các tham số của chúng là bước đầu tiên quan trọng để khai thác sức mạnh của Agno.

# Hướng dẫn sử dụng `print_response` và `aprint_response` trong Agno

Trong khi `run` và `arun` cung cấp cơ chế cốt lõi để thực thi Agent và trả về kết quả (dưới dạng giá trị cuối cùng hoặc stream), Agno cung cấp thêm hai phương thức tiện ích là `print_response` và `aprint_response` để đơn giản hóa việc hiển thị kết quả, đặc biệt là khi sử dụng chế độ streaming.

## Mục đích

`print_response` và `aprint_response` về cơ bản là các hàm bao (wrapper) tiện lợi xung quanh `run` và `arun` tương ứng. Mục đích chính của chúng là tự động xử lý việc lặp qua các phần (chunks) của phản hồi được stream và in chúng ra console (hoặc một luồng đầu ra khác) theo thời gian thực. Điều này giúp bạn dễ dàng hiển thị phản hồi của Agent cho người dùng cuối mà không cần viết mã lặp thủ công.

Giống như `run` và `arun`, sự khác biệt chính giữa chúng là:

*   **`print_response` (Synchronous - Đồng bộ):** Gọi `run` bên trong và in kết quả stream một cách đồng bộ. Nó sẽ chặn luồng thực thi cho đến khi toàn bộ phản hồi được in ra.
*   **`aprint_response` (Asynchronous - Bất đồng bộ):** Gọi `arun` bên trong và in kết quả stream một cách bất đồng bộ. Nó được sử dụng trong môi trường `asyncio` và không chặn luồng chính.

## Cách sử dụng cơ bản

Cách sử dụng rất giống với `run` và `arun`, nhưng thay vì nhận lại giá trị trả về, các phương thức này sẽ trực tiếp in kết quả ra màn hình.

```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat
import asyncio

# Khởi tạo Agent đơn giản
agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    description="Bạn là một trợ lý AI hữu ích.",
    markdown=True # Thường hữu ích khi dùng print_response để định dạng markdown
)

# --- Sử dụng print_response (đồng bộ) ---
print("--- In phản hồi đồng bộ với print_response (stream=True mặc định) ---")
agent.print_response("Kể một câu chuyện ngắn về một chú mèo thám hiểm.")

# --- Sử dụng aprint_response (bất đồng bộ) ---
async def main_async_print():
    print("\n--- In phản hồi bất đồng bộ với aprint_response (stream=True mặc định) ---")
    await agent.aprint_response("Giải thích ngắn gọn về lỗ đen.")

# Chạy hàm bất đồng bộ
asyncio.run(main_async_print())
```

Lưu ý quan trọng: Mặc định, cả `print_response` và `aprint_response` đều **bật chế độ stream (`stream=True`)**. Do đó, bạn sẽ thấy kết quả được in ra từng phần một.

## Các tham số quan trọng

Các phương thức này chấp nhận hầu hết các tham số giống như `run` và `arun` (`message`, `user_id`, `session_id`, `tools`, `instructions`, `show_reasoning`, `stream_intermediate_steps`, v.v.) vì chúng chỉ đơn giản là truyền các tham số này xuống các hàm cốt lõi.

Tuy nhiên, có một số tham số đặc biệt liên quan đến việc định dạng và hiển thị:

*   **`message` (bắt buộc):** Thông điệp đầu vào cho Agent.
*   **`stream` (`bool`, mặc định: `True`):** Kiểm soát việc có stream kết quả hay không. Nếu bạn đặt `stream=False`, nó sẽ chờ Agent chạy xong hoàn toàn rồi mới in toàn bộ kết quả cuối cùng ra một lượt (ít phổ biến hơn cho các phương thức `print_*` này).
*   **`markdown` (`bool`, mặc định lấy từ cấu hình Agent, thường là `False` nếu không đặt):** Nếu `True`, nó sẽ cố gắng hiển thị các khối mã và định dạng Markdown khác một cách trực quan hơn trong console (sử dụng thư viện `rich`). Điều này làm cho kết quả dễ đọc hơn, đặc biệt khi Agent trả về mã nguồn hoặc văn bản có cấu trúc.
*   **`show_full_reasoning` (`bool`, mặc định: `False`):** Tương tự `show_reasoning` trong `run`/`arun`, nhưng thường được dùng kết hợp với `stream=True` và `stream_intermediate_steps=True` để in cả các bước suy luận trung gian.
*   **`stream_intermediate_steps` (`bool`, mặc định: `False`):** Khi `stream=True`, tham số này quyết định có in cả các bước trung gian (tool calls, reasoning) ra console hay không.
*   **`max_tokens` (`int`, tùy chọn):** Giới hạn số lượng token tối đa mà mô hình được phép tạo ra trong phản hồi.
*   **`response_model` (Pydantic Model, tùy chọn):** Nếu bạn muốn Agent trả về một cấu trúc dữ liệu cụ thể (được định nghĩa bằng Pydantic), bạn có thể cung cấp model đó ở đây. `print_response` sẽ in ra biểu diễn chuỗi của đối tượng model đó.

**Ví dụ với `markdown` và `show_full_reasoning`:**

```python
# (Giả sử agent đã được khởi tạo với ReasoningTools, YFinanceTools và markdown=True)

print("\n--- In phản hồi đồng bộ với markdown và reasoning ---")
agent.print_response(
    "Viết một báo cáo ngắn về NVDA, bao gồm giá cổ phiếu và tin tức gần đây. Sử dụng định dạng markdown.",
    show_full_reasoning=True,
    stream_intermediate_steps=True
)

# Tương tự với aprint_response
async def main_async_print_detailed():
    print("\n--- In phản hồi bất đồng bộ với markdown và reasoning ---")
    await agent.aprint_response(
        "Viết một báo cáo ngắn về AAPL, bao gồm giá cổ phiếu và tin tức gần đây. Sử dụng định dạng markdown.",
        show_full_reasoning=True,
        stream_intermediate_steps=True
    )

asyncio.run(main_async_print_detailed())
```

Trong ví dụ này, việc đặt `markdown=True` khi khởi tạo Agent (hoặc truyền trực tiếp vào `print_response`) và sử dụng `show_full_reasoning=True`, `stream_intermediate_steps=True` sẽ giúp hiển thị kết quả một cách rõ ràng, bao gồm cả các bảng dữ liệu, các bước suy luận và lệnh gọi tool, được định dạng đẹp mắt trên console.

## Khi nào nên sử dụng?

*   Sử dụng `print_response` và `aprint_response` khi bạn muốn **nhanh chóng hiển thị kết quả** của Agent ra console cho mục đích demo, gỡ lỗi, hoặc trong các ứng dụng giao diện dòng lệnh (CLI) đơn giản.
*   Chúng đặc biệt hữu ích khi bạn muốn **hiển thị kết quả stream** mà không cần tự quản lý iterator/async iterator trả về từ `run`/`arun`.
*   Khi bạn cần **kiểm soát hoàn toàn cách xử lý kết quả** (ví dụ: gửi qua API, lưu vào cơ sở dữ liệu, hiển thị trên giao diện web phức tạp), bạn nên sử dụng `run` hoặc `arun` và tự xử lý kết quả trả về.

Tóm lại, `print_response` và `aprint_response` là những tiện ích giúp đơn giản hóa việc quan sát hoạt động của Agent bằng cách tự động in các phản hồi (đặc biệt là phản hồi stream) ra console.

---



# Hướng dẫn sử dụng Bộ nhớ SQLite (`memory sqlite`) trong Agno

Một trong những khả năng mạnh mẽ của Agent là khả năng ghi nhớ các tương tác trước đó, cho phép chúng duy trì ngữ cảnh và đưa ra phản hồi phù hợp hơn theo thời gian. Agno cung cấp một hệ thống Bộ nhớ (Memory) linh hoạt, và một trong những trình điều khiển (driver) bộ nhớ tích hợp sẵn đơn giản và hiệu quả cho người mới bắt đầu là SQLite.

SQLite là một hệ quản trị cơ sở dữ liệu quan hệ nhẹ, lưu trữ toàn bộ cơ sở dữ liệu trong một file duy nhất trên đĩa. Điều này làm cho nó trở nên lý tưởng cho các ứng dụng không yêu cầu khả năng mở rộng lớn hoặc truy cập đồng thời cao, phù hợp cho việc phát triển và thử nghiệm Agent cục bộ.

## Mục đích của Bộ nhớ Agent

Bộ nhớ trong Agno (Agent Memory) phục vụ các mục đích chính sau:

1.  **Lưu trữ Lịch sử Tương tác:** Ghi lại các tin nhắn trao đổi giữa người dùng và Agent trong một phiên làm việc (session) hoặc qua nhiều phiên.
2.  **Duy trì Ngữ cảnh:** Cung cấp lịch sử trò chuyện cho mô hình ngôn ngữ lớn (LLM) trong các lượt tương tác tiếp theo, giúp mô hình hiểu rõ bối cảnh và đưa ra phản hồi liên quan.
3.  **Tóm tắt Phiên (Session Summaries):** Một số loại bộ nhớ có thể tự động tạo tóm tắt cho các cuộc trò chuyện dài, giúp giảm lượng thông tin cần gửi đến LLM mà vẫn giữ được ngữ cảnh chính.

## Kích hoạt và Cấu hình Bộ nhớ SQLite

Để sử dụng bộ nhớ SQLite cho Agent, bạn cần thực hiện hai bước chính:

1.  **Import lớp `SqliteMemory`:** Lớp này nằm trong module `agno.memory.sqlite`.
2.  **Khởi tạo và gán cho Agent:** Tạo một instance của `SqliteMemory` và truyền nó vào tham số `memory` khi khởi tạo `Agent`.

```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.memory.sqlite import SqliteMemory # Bước 1: Import
import os

# Đảm bảo thư mục chứa file db tồn tại
memory_dir = "/home/<USER>/agno_memory"
os.makedirs(memory_dir, exist_ok=True)

# Đường dẫn đến file cơ sở dữ liệu SQLite
# Agno sẽ tự động tạo file này nếu chưa tồn tại.
sqlite_db_file = os.path.join(memory_dir, "agent_memory.db")

# Bước 2: Khởi tạo SqliteMemory
agent_memory = SqliteMemory(
    db_file=sqlite_db_file,
    # table_name="my_chat_history" # Tùy chọn: Đặt tên bảng khác nếu muốn
)

# Khởi tạo Agent với bộ nhớ đã cấu hình
agent_with_memory = Agent(
    model=OpenAIChat(id="gpt-4o"),
    description="Bạn là một trợ lý AI có khả năng ghi nhớ cuộc trò chuyện.",
    memory=agent_memory, # Gán đối tượng memory
    # enable_agentic_memory=True, # Mặc định là True khi memory được cung cấp
    add_history_to_messages=True # Quan trọng: Đảm bảo lịch sử được thêm vào prompt
)

# Sử dụng Agent như bình thường
print("--- Tương tác với Agent có bộ nhớ SQLite ---")

# Lần tương tác 1
user_id_1 = "user_abc"
session_id_1 = "session_123"
print(f"\n[User: {user_id_1}, Session: {session_id_1}] Xin chào! Tôi tên là An.")
agent_with_memory.print_response("Xin chào! Tôi tên là An.", user_id=user_id_1, session_id=session_id_1)

# Lần tương tác 2 (cùng user, cùng session)
print(f"\n[User: {user_id_1}, Session: {session_id_1}] Bạn có nhớ tên tôi không?")
agent_with_memory.print_response("Bạn có nhớ tên tôi không?", user_id=user_id_1, session_id=session_id_1)

# Lần tương tác 3 (user khác)
user_id_2 = "user_xyz"
session_id_2 = "session_456"
print(f"\n[User: {user_id_2}, Session: {session_id_2}] Xin chào! Tôi là Bình.")
agent_with_memory.print_response("Xin chào! Tôi là Bình.", user_id=user_id_2, session_id=session_id_2)

# Lần tương tác 4 (quay lại user 1, session 1)
print(f"\n[User: {user_id_1}, Session: {session_id_1}] Tóm tắt lại cuộc trò chuyện của chúng ta.")
agent_with_memory.print_response("Tóm tắt lại cuộc trò chuyện của chúng ta.", user_id=user_id_1, session_id=session_id_1)

print(f"\nKiểm tra file cơ sở dữ liệu tại: {sqlite_db_file}")
```

**Giải thích ví dụ:**

1.  **Import và Khởi tạo:** Chúng ta import `SqliteMemory` và tạo một instance của nó, chỉ định đường dẫn đến file `agent_memory.db`. Agno sẽ tự động tạo file và bảng cần thiết nếu chúng chưa tồn tại.
2.  **Gán vào Agent:** Instance `agent_memory` được truyền vào tham số `memory` của `Agent`.
3.  **`add_history_to_messages=True`:** Tham số này rất quan trọng. Nó bảo Agent tự động lấy lịch sử trò chuyện từ bộ nhớ (`SqliteMemory`) và thêm vào danh sách tin nhắn gửi đến LLM. Nếu không có tham số này, bộ nhớ chỉ lưu trữ lịch sử mà không sử dụng nó để duy trì ngữ cảnh.
4.  **`user_id` và `session_id`:** Khi gọi `run`, `arun`, `print_response`, hoặc `aprint_response`, bạn cần cung cấp `user_id` và/hoặc `session_id`. `SqliteMemory` sử dụng các định danh này để phân biệt và lưu trữ lịch sử cho từng người dùng và/hoặc từng phiên làm việc riêng biệt. Trong ví dụ, Agent có thể nhớ tên "An" trong `session_123` nhưng không biết tên "An" khi tương tác với `user_xyz` trong `session_456`.
5.  **Lưu trữ:** Sau mỗi lượt tương tác, `SqliteMemory` sẽ tự động lưu tin nhắn của người dùng và phản hồi của Agent vào file `agent_memory.db` được liên kết với `user_id` và `session_id` tương ứng.

## Cấu trúc Bảng SQLite

`SqliteMemory` thường tạo ra một bảng (mặc định tên là `agno_memory`) trong file SQLite với các cột cơ bản như:

*   `id`: Khóa chính, tự động tăng.
*   `run_id`: Định danh duy nhất cho mỗi lượt chạy `run`/`arun`.
*   `session_id`: Định danh phiên làm việc.
*   `user_id`: Định danh người dùng.
*   `role`: Vai trò của tin nhắn (\'user\' hoặc \'assistant\').
*   `name`: Tên (nếu có, ví dụ tên của tool).
*   `content`: Nội dung của tin nhắn.
*   `timestamp`: Dấu thời gian khi tin nhắn được lưu.
*   `metadata`: Trường JSON để lưu trữ các thông tin bổ sung (ví dụ: token usage, latency).

Bạn có thể sử dụng các công cụ quản lý SQLite (như DB Browser for SQLite) để mở file `.db` và xem trực tiếp lịch sử được lưu trữ.

## Các tham số cấu hình `SqliteMemory`

Ngoài `db_file`, `SqliteMemory` còn có một số tham số cấu hình khác:

*   **`table_name` (`str`, mặc định: `\'agno_memory\'`):** Tên của bảng trong cơ sở dữ liệu SQLite để lưu trữ lịch sử.
*   **`db_schema` (`str`, tùy chọn):** Tên schema của cơ sở dữ liệu (ít dùng với SQLite).
*   **`memory_type` (`str`, mặc định: `\'chat\'`):** Loại bộ nhớ, hiện tại chủ yếu là \'chat\'.
*   **`prune_interval` (`int`, mặc định: `100`):** Số lượt chạy `run`/`arun` trước khi thực hiện việc cắt tỉa (prune) bộ nhớ cũ (nếu được cấu hình).
*   **`prune_threshold` (`int`, tùy chọn):** Số lượng tin nhắn tối đa giữ lại cho mỗi session/user khi cắt tỉa. Nếu không đặt, việc cắt tỉa sẽ không diễn ra.

## Giới hạn và Lưu ý

*   **Hiệu năng với dữ liệu lớn:** SQLite hoạt động tốt với lượng dữ liệu vừa phải. Nếu lịch sử trò chuyện trở nên quá lớn (hàng triệu bản ghi), hiệu năng truy vấn có thể giảm.
*   **Truy cập đồng thời:** SQLite không được thiết kế cho việc ghi đồng thời từ nhiều tiến trình hoặc luồng. Nếu bạn cần triển khai Agent trong môi trường có nhiều worker cùng truy cập bộ nhớ, các giải pháp khác như PostgreSQL hoặc các cơ sở dữ liệu chuyên dụng có thể phù hợp hơn.
*   **Đường dẫn file:** Đảm bảo tiến trình chạy Agent có quyền ghi vào thư mục chứa file SQLite.

Bộ nhớ SQLite là một lựa chọn tuyệt vời để bắt đầu tích hợp khả năng ghi nhớ cho Agno Agent của bạn một cách nhanh chóng và đơn giản, đặc biệt trong môi trường phát triển và thử nghiệm cục bộ.

---



# Hướng dẫn sử dụng Trạng thái Phiên (`session_state`) trong Agno

Trong quá trình xây dựng các Agent phức tạp hơn, đặc biệt là các Agent tương tác qua nhiều lượt hoặc thực hiện các quy trình công việc (workflows) có nhiều bước, việc quản lý trạng thái của phiên làm việc (session) trở nên quan trọng. Agno cung cấp cơ chế `session_state` để lưu trữ và truy xuất dữ liệu tùy chỉnh liên quan đến một phiên tương tác cụ thể.

**Lưu ý:** Dựa trên phân tích mã nguồn Agno (phiên bản tại thời điểm viết tài liệu này), khái niệm "session stage" (giai đoạn phiên) dường như không được triển khai trực tiếp như một tính năng riêng biệt với API rõ ràng. Thay vào đó, Agno cung cấp một cơ chế linh hoạt hơn là **`session_state`**, một từ điển (dictionary) mà bạn có thể sử dụng để lưu trữ bất kỳ dữ liệu trạng thái nào liên quan đến phiên hiện tại. Bạn có thể tự mình sử dụng `session_state` để triển khai logic quản lý "giai đoạn" (stage) nếu cần.

## Mục đích của `session_state`

`session_state` là một thuộc tính của đối tượng `Agent` cho phép bạn:

1.  **Lưu trữ dữ liệu tùy chỉnh:** Giữ lại thông tin cụ thể về phiên làm việc hiện tại, ví dụ như các bước đã hoàn thành trong một quy trình, thông tin người dùng đã thu thập, hoặc kết quả trung gian.
2.  **Duy trì trạng thái qua các lượt chạy:** Dữ liệu trong `session_state` được liên kết với một `session_id` cụ thể và (thường) được lưu trữ bền bỉ thông qua hệ thống Bộ nhớ (Memory) hoặc Lưu trữ (Storage) của Agent. Điều này có nghĩa là trạng thái có thể được khôi phục và sử dụng trong các lần gọi `run`/`arun` tiếp theo trong cùng một phiên.
3.  **Điều khiển luồng Agent:** Dựa vào thông tin trong `session_state`, bạn có thể thay đổi hành vi của Agent, ví dụ như quyết định bước tiếp theo trong một quy trình, hoặc điều chỉnh câu trả lời dựa trên trạng thái hiện tại.

## Cách sử dụng `session_state`

Việc sử dụng `session_state` liên quan chặt chẽ đến hệ thống Memory/Storage của Agent, vì đây là nơi trạng thái thường được lưu trữ và truy xuất.

1.  **Kích hoạt Bộ nhớ/Lưu trữ:** Bạn cần cấu hình Agent với một hệ thống Memory (như `SqliteMemory`) hoặc Storage có khả năng lưu trữ `session_state`. Bộ nhớ SQLite được đề cập trước đó có hỗ trợ lưu trữ `session_state`.
2.  **Truy cập và Cập nhật:** Bên trong logic của Agent (ví dụ, trong các hàm tool tùy chỉnh hoặc các hàm hook), bạn có thể truy cập và sửa đổi `agent.session_state`. `session_state` hoạt động như một từ điển Python thông thường.
3.  **Cung cấp `session_id`:** Khi gọi `run`/`arun`, việc cung cấp một `session_id` nhất quán là rất quan trọng để Agent có thể truy xuất đúng trạng thái cho phiên đó.

**Ví dụ: Triển khai logic "stage" đơn giản bằng `session_state`**

Giả sử chúng ta muốn tạo một Agent đặt pizza đơn giản có các giai đoạn: `CHAO_HOI`, `CHON_LOAI_PIZZA`, `CHON_KICH_CO`, `XAC_NHAN`.

```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.memory.sqlite import SqliteMemory
import os

# --- Cấu hình Bộ nhớ --- (Tương tự ví dụ trước)
memory_dir = "/home/<USER>/agno_memory"
os.makedirs(memory_dir, exist_ok=True)
sqlite_db_file = os.path.join(memory_dir, "pizza_agent_memory.db")
agent_memory = SqliteMemory(db_file=sqlite_db_file)

# --- Định nghĩa Agent --- 
pizza_agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    description="Bạn là Agent đặt pizza. Hãy hỏi người dùng từng bước một: loại pizza, kích cỡ, rồi xác nhận.",
    memory=agent_memory,
    add_history_to_messages=True,
    # Chúng ta sẽ không dùng tool ở đây, mà dựa vào prompt và session_state
    # Trong thực tế, bạn có thể dùng tool để cập nhật state
)

# --- Hàm chạy tương tác --- 
def interact(agent, message, user_id, session_id):
    print(f"\n[User: {user_id}, Session: {session_id}] {message}")
    
    # Lấy session_state hiện tại (nếu có) từ bộ nhớ
    current_session = agent.memory.get_session(session_id=session_id)
    initial_state = current_session.session_state if current_session else {}
    agent.session_state = initial_state # Gán state vào agent cho lần chạy này
    
    print(f"DEBUG: Trạng thái trước khi chạy: {agent.session_state}")

    # Xác định giai đoạn hiện tại từ state
    current_stage = agent.session_state.get("stage", "CHAO_HOI")
    
    # Xây dựng prompt dựa trên giai đoạn
    prompt_prefix = f"[Giai đoạn hiện tại: {current_stage}]\n"
    full_message = prompt_prefix + message

    # Chạy agent
    response = agent.run(full_message, user_id=user_id, session_id=session_id)
    print(f"[Agent: {agent.agent_id}] {response}")

    # --- Cập nhật state dựa trên phản hồi (logic ví dụ đơn giản) ---
    # Trong thực tế, logic này có thể phức tạp hơn, dựa vào phân tích phản hồi của LLM hoặc tool
    new_stage = current_stage
    if current_stage == "CHAO_HOI" and "loại pizza" in response.lower():
        new_stage = "CHON_LOAI_PIZZA"
        agent.session_state["stage"] = new_stage
        agent.session_state["user_query"] = message # Lưu lại yêu cầu ban đầu
    elif current_stage == "CHON_LOAI_PIZZA" and ("cỡ" in response.lower() or "kích thước" in response.lower()):
        new_stage = "CHON_KICH_CO"
        agent.session_state["stage"] = new_stage
        # Giả sử agent đã hỏi và người dùng trả lời loại pizza
        agent.session_state["pizza_type"] = message # Lưu loại pizza người dùng chọn
    elif current_stage == "CHON_KICH_CO" and "xác nhận" in response.lower():
        new_stage = "XAC_NHAN"
        agent.session_state["stage"] = new_stage
        agent.session_state["pizza_size"] = message # Lưu kích cỡ người dùng chọn
    elif current_stage == "XAC_NHAN" and ("đã đặt" in response.lower() or "hoàn tất" in response.lower()):
         new_stage = "HOAN_TAT"
         agent.session_state["stage"] = new_stage
         agent.session_state["confirmation"] = message # Lưu xác nhận

    # Lưu state mới vào bộ nhớ (quan trọng!)
    # Lưu ý: agent.run đã tự động lưu tin nhắn, nhưng chúng ta cần lưu state đã cập nhật
    # Cách chuẩn tắc hơn là dùng Storage hoặc hook, nhưng đây là ví dụ đơn giản
    if new_stage != current_stage:
         print(f"DEBUG: Cập nhật trạng thái thành: {agent.session_state}")
         agent.memory.update_session_state(session_id=session_id, session_state=agent.session_state)

# --- Mô phỏng cuộc trò chuyện --- 
user = "user_pizza_lover"
session = "pizza_order_001"

interact(pizza_agent, "Tôi muốn đặt pizza.", user, session)
interact(pizza_agent, "Hải sản.", user, session) # Trả lời câu hỏi về loại pizza
interact(pizza_agent, "Cỡ lớn nhé.", user, session) # Trả lời câu hỏi về kích cỡ
interact(pizza_agent, "Đúng rồi, xác nhận đi.", user, session) # Trả lời câu hỏi xác nhận

print(f"\nKiểm tra file cơ sở dữ liệu tại: {sqlite_db_file}")
# Bạn có thể mở file DB để xem session_state được lưu như thế nào
```

**Giải thích ví dụ:**

1.  **Lấy và Gán State:** Trước mỗi lần gọi `run`, chúng ta lấy `session_state` hiện có từ `agent.memory` (nếu phiên đã tồn tại) và gán nó vào `agent.session_state`. Điều này đảm bảo Agent có thông tin trạng thái mới nhất cho lần chạy này.
2.  **Xác định Giai đoạn:** Chúng ta đọc giá trị của khóa `"stage"` từ `agent.session_state` để biết Agent đang ở giai đoạn nào của quy trình đặt hàng (mặc định là `CHAO_HOI`).
3.  **Điều chỉnh Prompt:** Thông tin giai đoạn được thêm vào đầu `message` để cung cấp ngữ cảnh rõ ràng hơn cho LLM.
4.  **Cập nhật State:** Sau khi Agent phản hồi, chúng ta thực hiện một logic đơn giản (dựa trên từ khóa trong phản hồi của Agent và câu trả lời của người dùng) để xác định xem có nên chuyển sang giai đoạn tiếp theo hay không và lưu các thông tin liên quan (loại pizza, kích cỡ) vào `session_state`.
5.  **Lưu State:** Nếu `session_state` bị thay đổi, chúng ta gọi `agent.memory.update_session_state()` để lưu trạng thái mới vào cơ sở dữ liệu SQLite, liên kết với `session_id`.

**Quan trọng:** Ví dụ trên minh họa cách *sử dụng* `session_state` để quản lý giai đoạn. Logic cập nhật state ở đây rất đơn giản. Trong ứng dụng thực tế, bạn có thể cần:
    *   Sử dụng các **Tools** để thực hiện các hành động cụ thể và cập nhật state một cách đáng tin cậy hơn.
    *   Sử dụng **Reasoning** để Agent tự quyết định khi nào nên chuyển giai đoạn.
    *   Sử dụng các **Hooks** (ví dụ: `pre_run_hook`, `post_run_hook`) để quản lý việc lấy và lưu state một cách tập trung hơn.

## Lưu trữ `session_state`

Khi sử dụng `SqliteMemory`, `session_state` (là một dictionary) thường được serialize (ví dụ: thành chuỗi JSON) và lưu vào một cột riêng trong bảng quản lý phiên (thường là bảng `agno_sessions` hoặc tương tự, khác với bảng `agno_memory` lưu tin nhắn). Khi cần, nó sẽ được deserialize trở lại thành dictionary.

## Kết luận

`session_state` là một cơ chế linh hoạt trong Agno để lưu trữ và quản lý dữ liệu trạng thái tùy chỉnh cho mỗi phiên làm việc. Mặc dù không có khái niệm "session stage" được định nghĩa sẵn, bạn hoàn toàn có thể sử dụng `session_state` kết hợp với Memory/Storage và logic tùy chỉnh (trong tools, hooks, hoặc prompt) để xây dựng các Agent có khả năng thực hiện các quy trình nhiều bước hoặc duy trì trạng thái phức tạp qua các lượt tương tác.

---



# Các Khái niệm và Tiện ích Quan trọng Khác cho Người Mới Bắt đầu với Agno

Ngoài các chức năng thực thi (`run`, `arun`), hiển thị (`print_response`, `aprint_response`), bộ nhớ (`SqliteMemory`), và quản lý trạng thái (`session_state`) đã đề cập, có một số khái niệm và tiện ích cốt lõi khác trong Agno mà người mới bắt đầu nên làm quen.

## 1. Khởi tạo Agent (`agno.agent.Agent`)

Lớp `Agent` là trung tâm của framework Agno. Việc hiểu cách khởi tạo và cấu hình một Agent là bước đầu tiên.

```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat
# from agno.tools.duckduckgo import DuckDuckGoTools # Ví dụ về import tool
# from agno.memory.sqlite import SqliteMemory # Ví dụ về import memory

# Khởi tạo Agent với các tham số cơ bản
my_agent = Agent(
    # --- Tham số cốt lõi ---
    model=OpenAIChat(id="gpt-4o"), # Bắt buộc: Mô hình LLM sẽ sử dụng
    description="Bạn là một trợ lý AI chuyên nghiệp và thân thiện.", # Tùy chọn: Mô tả vai trò của Agent
    
    # --- Chỉ dẫn (Instructions) ---
    instructions=[
        "Luôn trả lời bằng tiếng Việt.",
        "Sử dụng định dạng markdown cho các danh sách hoặc khối mã."
    ], # Tùy chọn: Danh sách các chỉ dẫn cố định cho Agent
    
    # --- Công cụ (Tools) ---
    # tools=[DuckDuckGoTools(search_knowledge=True)], # Tùy chọn: Danh sách các tool Agent có thể dùng
    
    # --- Bộ nhớ (Memory) ---
    # memory=SqliteMemory(db_file="/path/to/your/memory.db"), # Tùy chọn: Cấu hình bộ nhớ
    # add_history_to_messages=True, # Quan trọng nếu dùng memory
    
    # --- Cấu hình Phản hồi ---
    markdown=True, # Tùy chọn: Tự động định dạng markdown cho console output
    # response_model=MyPydanticModel, # Tùy chọn: Yêu cầu LLM trả về cấu trúc Pydantic
    
    # --- Cấu hình khác ---
    # user_id="default_user", # Tùy chọn: ID người dùng mặc định
    # session_id="default_session", # Tùy chọn: ID phiên mặc định
    # debug_mode=True # Tùy chọn: Bật chế độ debug để xem thêm log
)

# Sau khi khởi tạo, bạn có thể dùng my_agent.run(...) hoặc my_agent.print_response(...)
my_agent.print_response("Giới thiệu ngắn về bản thân bạn.")
```

**Các tham số quan trọng khi khởi tạo `Agent`:**

*   **`model` (bắt buộc):** Xác định mô hình ngôn ngữ lớn (LLM) mà Agent sẽ sử dụng. Agno hỗ trợ nhiều nhà cung cấp mô hình (OpenAI, Anthropic, Google Gemini, Ollama, v.v.). Bạn cần import lớp mô hình tương ứng (ví dụ: `agno.models.openai.OpenAIChat`) và khởi tạo nó, thường với ID của mô hình cụ thể (ví dụ: `"gpt-4o"`).
*   **`description` (`str`, tùy chọn):** Một mô tả ngắn gọn về vai trò hoặc tính cách của Agent. Thông tin này thường được thêm vào system prompt gửi đến LLM.
*   **`instructions` (`list[str]`, tùy chọn):** Một danh sách các chỉ dẫn cố định mà Agent nên tuân theo. Chúng cũng thường được thêm vào system prompt.
*   **`tools` (`list`, tùy chọn):** Danh sách các công cụ (tools) mà Agent được phép sử dụng. Mỗi phần tử trong danh sách là một instance của một lớp Tool (ví dụ: `DuckDuckGoTools()`).
*   **`memory` (tùy chọn):** Một instance của một lớp Memory (ví dụ: `SqliteMemory`) để lưu trữ lịch sử trò chuyện và trạng thái phiên.
*   **`add_history_to_messages` (`bool`, mặc định `False` trừ khi `memory` được cung cấp):** Nếu sử dụng `memory`, bạn thường cần đặt tham số này thành `True` để lịch sử trò chuyện được tự động thêm vào prompt gửi đến LLM.
*   **`markdown` (`bool`, mặc định `False`):** Nếu `True`, các phương thức như `print_response` sẽ cố gắng hiển thị định dạng markdown trên console.
*   **`response_model` (Pydantic Model, tùy chọn):** Cho phép bạn yêu cầu LLM trả về kết quả dưới dạng một cấu trúc dữ liệu Pydantic cụ thể, thay vì chỉ là văn bản thuần túy.
*   **`debug_mode` (`bool`, mặc định `False`):** Bật chế độ này sẽ in ra nhiều thông tin gỡ lỗi hơn trong quá trình Agent hoạt động.

## 2. Khái niệm về Công cụ (Tools)

Tools là các chức năng hoặc khả năng bổ sung mà bạn có thể trang bị cho Agent, cho phép nó tương tác với thế giới bên ngoài hoặc thực hiện các tác vụ cụ thể mà LLM không thể tự làm được (ví dụ: tìm kiếm web, truy vấn cơ sở dữ liệu, thực thi mã, gọi API khác).

*   **Cách hoạt động:** Khi Agent (thông qua LLM) quyết định cần sử dụng một tool, nó sẽ tạo ra một yêu cầu gọi tool (Function Calling/Tool Calling). Framework Agno sẽ bắt yêu cầu này, thực thi hàm Python tương ứng với tool đó, và gửi kết quả trả về từ hàm cho LLM để tiếp tục xử lý.
*   **Các Tool có sẵn:** Agno cung cấp nhiều tool tích hợp sẵn trong module `agno.tools` (ví dụ: `DuckDuckGoTools` để tìm kiếm web, `YFinanceTools` để lấy thông tin tài chính, `FileTools` để đọc/ghi file, `ReasoningTools` để hỗ trợ suy luận từng bước).
*   **Tạo Tool tùy chỉnh:** Bạn cũng có thể dễ dàng tạo các tool của riêng mình bằng cách định nghĩa các hàm Python thông thường và sử dụng decorator `@tool` hoặc truyền trực tiếp hàm vào danh sách `tools` của Agent.

**Ví dụ sử dụng Tool có sẵn:**

```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.duckduckgo import DuckDuckGoTools # Import tool

# Khởi tạo Agent với DuckDuckGoTools
search_agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    description="Bạn là trợ lý tìm kiếm thông tin.",
    tools=[DuckDuckGoTools(search_knowledge=True)], # Thêm tool vào danh sách
    markdown=True
)

# Yêu cầu Agent tìm kiếm thông tin
search_agent.print_response("Thủ đô của Pháp là gì?") 
# Agent sẽ nhận ra cần tìm kiếm, gọi DuckDuckGoTools, nhận kết quả và trả lời.
```

## 3. Quản lý API Keys và Cấu hình

Khi sử dụng các mô hình LLM hoặc tools yêu cầu xác thực (như các mô hình của OpenAI, Anthropic), bạn cần cung cấp API key.

*   **Cách phổ biến nhất:** Đặt API key vào biến môi trường (environment variables). Agno (và các thư viện client tương ứng như `openai`, `anthropic`) thường tự động tìm các biến môi trường chuẩn như `OPENAI_API_KEY`, `ANTHROPIC_API_KEY`.
    ```bash
    export OPENAI_API_KEY=\'sk-...\' 
    export ANTHROPIC_API_KEY=\'sk-ant-...\' 
    python your_agent_script.py
    ```
*   **Truyền trực tiếp (ít khuyến khích hơn cho key):** Một số lớp Model hoặc Tool có thể cho phép bạn truyền API key trực tiếp vào hàm khởi tạo (ví dụ: `OpenAIChat(api_key=\'sk-...\')`). Tuy nhiên, cách này kém an toàn hơn và không linh hoạt bằng việc sử dụng biến môi trường.

## 4. Khái niệm về Agent Teams (Nâng cao)

Đối với các tác vụ phức tạp hơn, Agno hỗ trợ kiến trúc **Agent Teams**, cho phép nhiều Agent làm việc cùng nhau.

*   **Mục đích:** Phân chia công việc, chuyên môn hóa vai trò của từng Agent, và phối hợp để giải quyết vấn đề lớn.
*   **Các chế độ hoạt động:** Teams có thể hoạt động ở các chế độ khác nhau như `route` (định tuyến yêu cầu đến Agent phù hợp), `collaborate` (cùng nhau đóng góp vào một kết quả chung), `coordinate` (một Agent điều phối công việc cho các Agent khác).
*   **Mức độ:** Đây là một khái niệm nâng cao hơn. Người mới bắt đầu nên tập trung vào việc xây dựng và hiểu các Agent đơn lẻ trước khi khám phá Agent Teams.

Nắm vững các khái niệm cơ bản này, đặc biệt là cách khởi tạo `Agent` và ý tưởng về `Tools`, sẽ cung cấp nền tảng vững chắc để bạn tiếp tục khám phá và xây dựng các ứng dụng mạnh mẽ hơn với Agno.
