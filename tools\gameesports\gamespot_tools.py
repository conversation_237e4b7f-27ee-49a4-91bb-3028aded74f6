from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime, timedelta

class GameSpotTool(Toolkit):
    """
    Công cụ tìm kiếm GameSpot để tìm kiếm đánh giá game, tin tức, cập nhật esports và video.
    
    Các từ khóa tìm kiếm gợi ý:
    - Đánh giá game (game reviews, game ratings)
    - Tin tức game mới nhất (latest game news, game updates)
    - Thông tin esports (esports news, tournament coverage)
    - Video gameplay và phỏng vấn (gameplay videos, interviews)
    - <PERSON><PERSON><PERSON> hành game sắp tới (upcoming game releases)
    - <PERSON> s<PERSON>h phần cứng (hardware reviews, PC components)
    """
    
    def __init__(self):
        super().__init__(
            name="Công cụ tìm kiếm GameSpot",
            tools=[self.search_gamespot]
        )
        self.base_url = "https://www.gamespot.com/api"
        self.search_types = ["all", "reviews", "news", "videos", "articles", "features"]
        self.platforms = ["pc", "playstation-5", "xbox-series-x", "nintendo-switch", "mobile", "all"]
        self.genres = ["action", "rpg", "fps", "strategy", "sports", "fighting", "adventure", "all"]
        self.time_ranges = ["day", "week", "month", "year", "all"]

    def search_gamespot(self, query: str, search_type: str = "all", platform: str = "all", 
                       genre: str = "all", time_range: str = "all", limit: int = 5) -> str:
        """
        Tìm kiếm thông tin game trên GameSpot.
        
        Args:
            query: Từ khóa tìm kiếm (tên game, thể loại, v.v.)
            search_type: Loại nội dung (all, reviews, news, videos, articles, features)
            platform: Nền tảng (pc, playstation-5, xbox-series-x, nintendo-switch, mobile, all)
            genre: Thể loại game (action, rpg, fps, strategy, sports, fighting, adventure, all)
            time_range: Khoảng thời gian (day, week, month, year, all)
            limit: Số lượng kết quả trả về (tối đa 10)
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm GameSpot với từ khóa: {query}, loại: {search_type}")
        
        # Xác thực tham số
        if search_type not in self.search_types:
            search_type = "all"
        if platform not in self.platforms:
            platform = "all"
        if genre not in self.genres:
            genre = "all"
        if time_range not in self.time_ranges:
            time_range = "all"
            
        limit = max(1, min(limit, 10))  # Giới hạn trong khoảng 1-10
        
        try:
            # Giả lập kết quả tìm kiếm
            results = []
            current_date = datetime.now()
            
            # Tạo ngày đăng tải dựa trên time_range
            def get_publish_date(days_ago: int) -> str:
                return (current_date - timedelta(days=days_ago)).strftime("%Y-%m-%d")
                
            if search_type in ["all", "reviews"]:
                results.extend([
                    {
                        "title": f"{query} Review",
                        "type": "review",
                        "platform": platform if platform != "all" else "PC",
                        "score": 8 + (i * 0.5),
                        "author": f"GameSpot Staff",
                        "publish_date": get_publish_date(i),
                        "summary": f"GameSpot's in-depth review of {query} for {platform if platform != 'all' else 'multiple platforms'}.",
                        "url": f"https://www.gamespot.com/reviews/{query.lower().replace(' ', '-')}-review/1900-641{i}",
                        "image_url": f"https://www.gamespot.com/a/uploads/screen_medium/1/1234/12345678/{i}00x200.jpg"
                    } for i in range(1, min(limit, 3) + 1)
                ])
                
            if search_type in ["all", "news"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"{query} {i}: Latest News and Updates",
                        "type": "news",
                        "platform": platform if platform != "all" else "Multiplatform",
                        "author": f"GameSpot News",
                        "publish_date": get_publish_date(i),
                        "summary": f"The latest news and updates about {query} for {platform if platform != 'all' else 'all platforms'}.",
                        "url": f"https://www.gamespot.com/articles/{query.lower().replace(' ', '-')}-news-{i}/1100-649{i}",
                        "image_url": f"https://www.gamespot.com/a/uploads/screen_medium/1/1234/12345679/news_{i}00x200.jpg"
                    } for i in range(1, min(limit - len(results), 2) + 1)
                ])
                
            if search_type in ["all", "videos"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"{query} Gameplay Trailer {i}",
                        "type": "video",
                        "platform": platform if platform != "all" else "Multiplatform",
                        "duration": f"{2 + i}:3{i}",
                        "views": f"{i * 2}K",
                        "publish_date": get_publish_date(i),
                        "summary": f"Watch the latest gameplay trailer for {query}.",
                        "url": f"https://www.gamespot.com/videos/{query.lower().replace(' ', '-')}-trailer-{i}/2300-{i}234567/",
                        "thumbnail_url": f"https://www.gamespot.com/a/uploads/screen_medium/1/1234/12345680/video_thumb_{i}00x200.jpg"
                    } for i in range(1, min(limit - len(results), 2) + 1)
                ])
                
            if search_type in ["all", "features"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"Why {query} Is The Best Game of The Year {current_date.year - i}",
                        "type": "feature",
                        "author": "GameSpot Editors",
                        "publish_date": get_publish_date(i * 30),
                        "summary": f"An in-depth analysis of why {query} stands out as one of the best games of the year.",
                        "url": f"https://www.gamespot.com/features/why-{query.lower().replace(' ', '-')}-is-the-best-game-{current_date.year - i}-{i}",
                        "image_url": f"https://www.gamespot.com/a/uploads/screen_medium/1/1234/12345681/feature_{i}00x200.jpg"
                    } for i in range(1, min(limit - len(results), 2) + 1)
                ])
            
            result = {
                "status": "success",
                "source": "GameSpot",
                "query": query,
                "search_type": search_type,
                "platform": platform,
                "genre": genre,
                "time_range": time_range,
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm GameSpot: {str(e)}")
            result = {
                "status": "error",
                "source": "GameSpot",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm {query} trên GameSpot",
                        "url": f"https://www.gamespot.com/search/?q={query}",
                        "summary": f"Tìm kiếm thông tin về {query} trên GameSpot"
                    },
                    {
                        "title": f"Tin tức {query} mới nhất",
                        "url": f"https://www.gamespot.com/search/?q={query}&i=content&type=1",
                        "summary": f"Đọc các tin tức mới nhất về {query} trên GameSpot"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
