from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime, timedelta

class GameSpotTool(Toolkit):
    """
    Công cụ tìm kiếm GameSpot để tìm kiếm đánh giá game, tin tức, cập nhật esports và video.

    Các từ khóa tìm kiếm gợi ý:
    - Đánh giá game (game reviews, game ratings)
    - Tin tức game mới nhất (latest game news, game updates)
    - Thông tin esports (esports news, tournament coverage)
    - Video gameplay và phỏng vấn (gameplay videos, interviews)
    - <PERSON><PERSON><PERSON> hành game sắp tới (upcoming game releases)
    - <PERSON> s<PERSON>h phần cứng (hardware reviews, PC components)
    """

    def __init__(self):
        super().__init__(
            name="Công cụ tìm kiếm GameSpot",
            tools=[self.search_gamespot, self.get_top_new]
        )
        self.base_url = "https://www.gamespot.com/api"
        self.search_types = ["all", "reviews", "news", "videos", "articles", "features"]
        self.platforms = ["pc", "playstation-5", "xbox-series-x", "nintendo-switch", "mobile", "all"]
        self.genres = ["action", "rpg", "fps", "strategy", "sports", "fighting", "adventure", "all"]
        self.time_ranges = ["day", "week", "month", "year", "all"]

    def search_gamespot(self, query: str, search_type: str = "all", platform: str = "all",
                       genre: str = "all", time_range: str = "all", limit: int = 5) -> str:
        """
        Tìm kiếm thông tin game trên GameSpot.

        Args:
            query: Từ khóa tìm kiếm (tên game, thể loại, v.v.)
            search_type: Loại nội dung (all, reviews, news, videos, articles, features)
            platform: Nền tảng (pc, playstation-5, xbox-series-x, nintendo-switch, mobile, all)
            genre: Thể loại game (action, rpg, fps, strategy, sports, fighting, adventure, all)
            time_range: Khoảng thời gian (day, week, month, year, all)
            limit: Số lượng kết quả trả về (tối đa 10)

        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm GameSpot với từ khóa: {query}, loại: {search_type}")

        # Xác thực tham số
        if search_type not in self.search_types:
            search_type = "all"
        if platform not in self.platforms:
            platform = "all"
        if genre not in self.genres:
            genre = "all"
        if time_range not in self.time_ranges:
            time_range = "all"

        limit = max(1, min(limit, 10))  # Giới hạn trong khoảng 1-10

        try:
            # Giả lập kết quả tìm kiếm
            results = []
            current_date = datetime.now()

            # Tạo ngày đăng tải dựa trên time_range
            def get_publish_date(days_ago: int) -> str:
                return (current_date - timedelta(days=days_ago)).strftime("%Y-%m-%d")

            if search_type in ["all", "reviews"]:
                results.extend([
                    {
                        "title": f"{query} Review",
                        "type": "review",
                        "platform": platform if platform != "all" else "PC",
                        "score": 8 + (i * 0.5),
                        "author": f"GameSpot Staff",
                        "publish_date": get_publish_date(i),
                        "summary": f"GameSpot's in-depth review of {query} for {platform if platform != 'all' else 'multiple platforms'}.",
                        "url": f"https://www.gamespot.com/reviews/{query.lower().replace(' ', '-')}-review/1900-641{i}",
                        "image_url": f"https://www.gamespot.com/a/uploads/screen_medium/1/1234/12345678/{i}00x200.jpg"
                    } for i in range(1, min(limit, 3) + 1)
                ])

            if search_type in ["all", "news"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"{query} {i}: Latest News and Updates",
                        "type": "news",
                        "platform": platform if platform != "all" else "Multiplatform",
                        "author": f"GameSpot News",
                        "publish_date": get_publish_date(i),
                        "summary": f"The latest news and updates about {query} for {platform if platform != 'all' else 'all platforms'}.",
                        "url": f"https://www.gamespot.com/articles/{query.lower().replace(' ', '-')}-news-{i}/1100-649{i}",
                        "image_url": f"https://www.gamespot.com/a/uploads/screen_medium/1/1234/12345679/news_{i}00x200.jpg"
                    } for i in range(1, min(limit - len(results), 2) + 1)
                ])

            if search_type in ["all", "videos"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"{query} Gameplay Trailer {i}",
                        "type": "video",
                        "platform": platform if platform != "all" else "Multiplatform",
                        "duration": f"{2 + i}:3{i}",
                        "views": f"{i * 2}K",
                        "publish_date": get_publish_date(i),
                        "summary": f"Watch the latest gameplay trailer for {query}.",
                        "url": f"https://www.gamespot.com/videos/{query.lower().replace(' ', '-')}-trailer-{i}/2300-{i}234567/",
                        "thumbnail_url": f"https://www.gamespot.com/a/uploads/screen_medium/1/1234/12345680/video_thumb_{i}00x200.jpg"
                    } for i in range(1, min(limit - len(results), 2) + 1)
                ])

            if search_type in ["all", "features"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"Why {query} Is The Best Game of The Year {current_date.year - i}",
                        "type": "feature",
                        "author": "GameSpot Editors",
                        "publish_date": get_publish_date(i * 30),
                        "summary": f"An in-depth analysis of why {query} stands out as one of the best games of the year.",
                        "url": f"https://www.gamespot.com/features/why-{query.lower().replace(' ', '-')}-is-the-best-game-{current_date.year - i}-{i}",
                        "image_url": f"https://www.gamespot.com/a/uploads/screen_medium/1/1234/12345681/feature_{i}00x200.jpg"
                    } for i in range(1, min(limit - len(results), 2) + 1)
                ])

            result = {
                "status": "success",
                "source": "GameSpot",
                "query": query,
                "search_type": search_type,
                "platform": platform,
                "genre": genre,
                "time_range": time_range,
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm GameSpot: {str(e)}")
            result = {
                "status": "error",
                "source": "GameSpot",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm {query} trên GameSpot",
                        "url": f"https://www.gamespot.com/search/?q={query}",
                        "summary": f"Tìm kiếm thông tin về {query} trên GameSpot"
                    },
                    {
                        "title": f"Tin tức {query} mới nhất",
                        "url": f"https://www.gamespot.com/search/?q={query}&i=content&type=1",
                        "summary": f"Đọc các tin tức mới nhất về {query} trên GameSpot"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)

    def get_top_new(self, content_type: str = "news", limit: int = 10,
                    time_period: str = "week", platform: str = "all") -> str:
        """
        Lấy nội dung mới nhất và thịnh hành từ GameSpot.

        Args:
            content_type: Loại nội dung (news, reviews, videos, features)
            limit: Số lượng kết quả (tối đa 20)
            time_period: Khoảng thời gian (day, week, month)
            platform: Nền tảng cụ thể

        Returns:
            Chuỗi JSON chứa nội dung mới nhất
        """
        logger.info(f"Lấy top {content_type} mới nhất từ GameSpot trong {time_period}")

        limit = max(1, min(limit, 20))
        current_date = datetime.now()

        try:
            results = []

            if content_type == "news":
                # Top gaming news mới nhất
                results = [
                    {
                        "title": f"🎮 Breaking: Major Gaming News #{i+1} - Industry Shakeup",
                        "type": "news",
                        "platform": platform if platform != "all" else "Multiplatform",
                        "author": "GameSpot News Team",
                        "publish_date": (current_date - timedelta(hours=i*6)).strftime("%Y-%m-%d %H:%M"),
                        "category": "Industry News",
                        "summary": f"Latest breaking news in the gaming industry affecting {platform if platform != 'all' else 'all platforms'}.",
                        "trending_score": 95 - (i * 5),
                        "comments": 150 - (i * 10),
                        "url": f"https://www.gamespot.com/articles/breaking-gaming-news-{i+1}/1100-6495{i+1}",
                        "image_url": f"https://www.gamespot.com/a/uploads/screen_medium/1/1234/news{i+1}.jpg"
                    } for i in range(limit)
                ]

            elif content_type == "reviews":
                # Top game reviews mới nhất
                results = [
                    {
                        "title": f"⭐ New Game Review #{i+1} - Must-Play Title",
                        "type": "review",
                        "platform": platform if platform != "all" else "PC/Console",
                        "score": 9.0 - (i * 0.3),
                        "author": f"GameSpot Reviewer {i+1}",
                        "publish_date": (current_date - timedelta(days=i+1)).strftime("%Y-%m-%d"),
                        "game_genre": "Action/RPG/Strategy",
                        "pros": ["Great gameplay", "Amazing graphics", "Compelling story"],
                        "cons": ["Minor bugs", "Learning curve"],
                        "verdict": "Highly Recommended",
                        "url": f"https://www.gamespot.com/reviews/new-game-review-{i+1}/1900-6417{i+1}",
                        "image_url": f"https://www.gamespot.com/a/uploads/screen_medium/1/1234/review{i+1}.jpg"
                    } for i in range(limit)
                ]

            elif content_type == "videos":
                # Top gaming videos mới nhất
                results = [
                    {
                        "title": f"🎬 Exclusive Gameplay Video #{i+1} - First Look",
                        "type": "video",
                        "platform": platform if platform != "all" else "Multiplatform",
                        "duration": f"{5+i}:{30+i*5}",
                        "views": f"{50-i*5}K",
                        "likes": f"{2000-i*100}",
                        "publish_date": (current_date - timedelta(hours=i*12)).strftime("%Y-%m-%d %H:%M"),
                        "video_type": "Gameplay/Interview/Preview",
                        "description": f"Exclusive first look at upcoming game features and gameplay mechanics.",
                        "url": f"https://www.gamespot.com/videos/exclusive-gameplay-{i+1}/2300-6459{i+1}/",
                        "thumbnail_url": f"https://www.gamespot.com/a/uploads/screen_medium/1/1234/video{i+1}.jpg"
                    } for i in range(limit)
                ]

            elif content_type == "features":
                # Top feature articles mới nhất
                results = [
                    {
                        "title": f"📰 Deep Dive Feature #{i+1} - Industry Analysis",
                        "type": "feature",
                        "author": "GameSpot Editorial Team",
                        "publish_date": (current_date - timedelta(days=i*2)).strftime("%Y-%m-%d"),
                        "read_time": f"{8+i*2} min read",
                        "category": "Industry Analysis",
                        "tags": ["Gaming Industry", "Trends", "Analysis"],
                        "summary": f"In-depth analysis of current gaming industry trends and their impact on {platform if platform != 'all' else 'the gaming ecosystem'}.",
                        "social_shares": 500 - (i * 50),
                        "url": f"https://www.gamespot.com/features/deep-dive-analysis-{i+1}/1100-6495{i+1}",
                        "image_url": f"https://www.gamespot.com/a/uploads/screen_medium/1/1234/feature{i+1}.jpg"
                    } for i in range(limit)
                ]

            result = {
                "status": "success",
                "source": "GameSpot Top New",
                "content_type": content_type,
                "time_period": time_period,
                "platform": platform,
                "limit": limit,
                "total_results": len(results),
                "trending_metrics": {
                    "total_engagement": sum(r.get("trending_score", 0) for r in results if "trending_score" in r),
                    "average_rating": "8.5/10",
                    "most_popular_platform": "PC",
                    "trending_genres": ["Action", "RPG", "Strategy"]
                },
                "results": results,
                "generated_at": current_date.strftime("%Y-%m-%d %H:%M:%S")
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy top new GameSpot: {str(e)}")
            return json.dumps({
                "status": "error",
                "source": "GameSpot Top New",
                "message": str(e),
                "fallback_url": "https://www.gamespot.com/"
            }, ensure_ascii=False, indent=2)
