from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class WikidataDeityTool(Toolkit):
    """
    Wikidata Deity Tool for searching deity entities and metadata via SPARQL.
    """

    def __init__(self):
        super().__init__(
            name="Wikidata Deity Tools",
            tools=[
                self.search_wikidata_deity,
                self.get_deity_details,
                self.get_deity_relationships,
                self.search_by_mythology,
                self.compare_deities
            ]
        )

    async def search_wikidata_deity(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Search Wikidata for deity entities using SPARQL.

        Parameters:
        - query: Deity name or mythology (e.g., 'Thor', 'Japanese mythology')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with deity metadata, mythology, description, and Wikidata/Wikipedia URLs
        """
        logger.info(f"Searching Wikidata for deity: {query}")

        try:
            # SPARQL query for deity entities (Q22989102 is "deity" class)
            sparql_url = "https://query.wikidata.org/sparql"
            sparql_query = f"""
            SELECT ?item ?itemLabel ?description ?instanceOfLabel ?mythologyLabel ?cultureLabel ?wikipediaEN
            WHERE {{
              ?item wdt:P31/wdt:P279* wd:Q22989102 .
              ?item rdfs:label ?itemLabel .
              FILTER(LANG(?itemLabel) = "en")
              FILTER(CONTAINS(LCASE(?itemLabel), LCASE("{query}")))
              OPTIONAL {{ ?item schema:description ?description . FILTER(LANG(?description) = "en") }}
              OPTIONAL {{ ?item wdt:P31 ?instanceOf . ?instanceOf rdfs:label ?instanceOfLabel . FILTER(LANG(?instanceOfLabel) = "en") }}
              OPTIONAL {{ ?item wdt:P1400 ?mythology . ?mythology rdfs:label ?mythologyLabel . FILTER(LANG(?mythologyLabel) = "en") }}
              OPTIONAL {{ ?item wdt:P2596 ?culture . ?culture rdfs:label ?cultureLabel . FILTER(LANG(?cultureLabel) = "en") }}
              OPTIONAL {{
                ?wikipediaEN schema:about ?item ;
                  schema:isPartOf <https://en.wikipedia.org/> .
              }}
            }}
            LIMIT {limit}
            """

            headers = {
                "Accept": "application/sparql-results+json",
                "User-Agent": "MythologyDeitySearchBot/1.0"
            }
            params = {
                "query": sparql_query,
                "format": "json"
            }
            response = requests.get(sparql_url, params=params, headers=headers, timeout=20)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikidata",
                    "message": f"SPARQL query failed with status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            bindings = data.get("results", {}).get("bindings", [])
            results = []
            for b in bindings:
                item_url = b.get("item", {}).get("value")
                label = b.get("itemLabel", {}).get("value")
                description = b.get("description", {}).get("value") if "description" in b else None
                instance_of = b.get("instanceOfLabel", {}).get("value") if "instanceOfLabel" in b else None
                mythology = b.get("mythologyLabel", {}).get("value") if "mythologyLabel" in b else None
                culture = b.get("cultureLabel", {}).get("value") if "cultureLabel" in b else None
                wikipedia_url = b.get("wikipediaEN", {}).get("value") if "wikipediaEN" in b else None

                results.append({
                    "wikidata_url": item_url,
                    "name": label,
                    "description": description,
                    "instance_of": instance_of,
                    "mythology": mythology,
                    "culture": culture,
                    "wikipedia_url": wikipedia_url
                })

            return {
                "status": "success",
                "source": "Wikidata",
                "query": query,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching Wikidata Deity: {str(e)}")
            return {
                "status": "error",
                "source": "Wikidata",
                "message": str(e),
                "query": query
            }

    async def get_deity_details(self, wikidata_id: str) -> Dict[str, Any]:
        """
        Lấy thông tin chi tiết về một vị thần dựa trên Wikidata ID.

        Parameters:
        - wikidata_id: ID của vị thần trong Wikidata (ví dụ: "Q5685" cho thần Zeus)

        Returns:
        - JSON với thông tin chi tiết về vị thần
        """
        logger.info(f"Lấy thông tin chi tiết cho vị thần với ID: {wikidata_id}")

        try:
            # Kiểm tra định dạng ID
            if not wikidata_id.startswith("Q") or not wikidata_id[1:].isdigit():
                return {
                    "status": "error",
                    "source": "Wikidata",
                    "message": "ID không hợp lệ. ID phải bắt đầu bằng 'Q' theo sau là các chữ số.",
                    "wikidata_id": wikidata_id
                }

            # Sử dụng Wikidata API để lấy thông tin chi tiết
            api_url = f"https://www.wikidata.org/w/api.php"
            params = {
                "action": "wbgetentities",
                "ids": wikidata_id,
                "format": "json",
                "languages": "en",
                "props": "labels|descriptions|claims|sitelinks"
            }
            
            response = requests.get(api_url, params=params, timeout=20)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikidata",
                    "message": f"Không thể lấy dữ liệu. Mã lỗi: {response.status_code}",
                    "wikidata_id": wikidata_id
                }
            
            data = response.json()
            entity = data.get("entities", {}).get(wikidata_id)
            
            if not entity:
                return {
                    "status": "error",
                    "source": "Wikidata",
                    "message": "Không tìm thấy thông tin cho ID này",
                    "wikidata_id": wikidata_id
                }
            
            # Trích xuất thông tin cơ bản
            label = entity.get("labels", {}).get("en", {}).get("value", "Không có tên")
            description = entity.get("descriptions", {}).get("en", {}).get("value", "Không có mô tả")
            
            # Lấy các thuộc tính quan trọng
            claims = entity.get("claims", {})
            
            # Hàm trợ giúp để lấy giá trị claim
            def get_claim_value(prop_id, data_type="string"):
                if prop_id not in claims:
                    return None
                
                main_snaks = claims[prop_id]
                if not main_snaks:
                    return None
                
                main_snak = main_snaks[0].get("mainsnak", {})
                if not main_snak:
                    return None
                
                datavalue = main_snak.get("datavalue")
                if not datavalue:
                    return None
                
                value = datavalue.get("value")
                if not value:
                    return None
                
                if data_type == "entity":
                    return {
                        "id": value.get("id"),
                        "label": value.get("labels", {}).get("en", {}).get("value") if isinstance(value, dict) else value
                    }
                elif data_type == "time":
                    return value.get("time") if isinstance(value, dict) else value
                elif data_type == "quantity":
                    return value.get("amount") if isinstance(value, dict) else value
                else:
                    return value
            
            # Trích xuất các thuộc tính quan trọng
            instance_of = [get_claim_value("P31", "entity")]  # Thể loại
            gender = get_claim_value("P21", "entity")  # Giới tính
            father = get_claim_value("P22", "entity")  # Cha
            mother = get_claim_value("P25", "entity")  # Mẹ
            spouse = get_claim_value("P26", "entity")  # Vợ/chồng
            child = get_claim_value("P40", "entity")  # Con cái
            position_held = get_claim_value("P39", "entity")  # Chức vụ
            country = get_claim_value("P17", "entity")  # Quốc gia
            religion = get_claim_value("P140", "entity")  # Tôn giáo
            
            # Lấy liên kết Wikipedia
            sitelinks = entity.get("sitelinks", {})
            wikipedia_url = None
            if "enwiki" in sitelinks:
                title = sitelinks["enwiki"].get("title", "").replace(" ", "_")
                wikipedia_url = f"https://en.wikipedia.org/wiki/{title}"
            
            # Tạo URL Wikidata
            wikidata_url = f"https://www.wikidata.org/entity/{wikidata_id}"
            
            return {
                "status": "success",
                "source": "Wikidata",
                "wikidata_id": wikidata_id,
                "name": label,
                "description": description,
                "instance_of": instance_of,
                "gender": gender,
                "family": {
                    "father": father,
                    "mother": mother,
                    "spouse": spouse,
                    "children": child
                },
                "attributes": {
                    "position_held": position_held,
                    "country": country,
                    "religion": religion
                },
                "urls": {
                    "wikipedia": wikipedia_url,
                    "wikidata": wikidata_url
                }
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy thông tin chi tiết vị thần: {str(e)}")
            return {
                "status": "error",
                "source": "Wikidata",
                "message": str(e),
                "wikidata_id": wikidata_id
            }

    async def get_deity_relationships(self, wikidata_id: str, depth: int = 1) -> Dict[str, Any]:
        """
        Lấy thông tin về các mối quan hệ của một vị thần.

        Parameters:
        - wikidata_id: ID của vị thần trong Wikidata
        - depth: Độ sâu của mối quan hệ (1-2, mặc định: 1)

        Returns:
        - JSON với thông tin về các mối quan hệ
        """
        logger.info(f"Lấy mối quan hệ cho vị thần {wikidata_id} với độ sâu {depth}")

        try:
            # Kiểm tra độ sâu hợp lệ
            depth = max(1, min(2, depth))  # Giới hạn độ sâu từ 1-2
            
            # Lấy thông tin cơ bản của vị thần
            deity_info = await self.get_deity_details(wikidata_id)
            if deity_info.get("status") != "success":
                return deity_info
            
            # Tạo cấu trúc kết quả
            result = {
                "deity": {
                    "id": wikidata_id,
                    "name": deity_info.get("name"),
                    "description": deity_info.get("description"),
                    "url": deity_info.get("urls", {}).get("wikidata")
                },
                "relationships": {
                    "family": {},
                    "associated_with": []
                }
            }
            
            # Lấy thông tin gia đình
            family = deity_info.get("family", {})
            if family:
                result["relationships"]["family"] = {
                    "parents": [
                        {"type": "father", **family.get("father")} if family.get("father") else None,
                        {"type": "mother", **family.get("mother")} if family.get("mother") else None
                    ],
                    "spouse": [{"type": "spouse", **family.get("spouse")}] if family.get("spouse") else [],
                    "children": [{"type": "child", **child} for child in family.get("children", []) if child]
                }
            
            # Ở đây có thể thêm logic để lấy thêm các mối quan hệ khác từ Wikidata
            # bằng cách sử dụng SPARQL hoặc API khác
            
            # Nếu depth > 1, lấy thêm thông tin về các mối quan hệ bậc 2
            if depth > 1:
                # Thêm logic để lấy thông tin về các vị thần liên quan
                # và mối quan hệ của họ
                pass
            
            return {
                "status": "success",
                "source": "Wikidata",
                "deity_id": wikidata_id,
                "depth": depth,
                "data": result
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy thông tin mối quan hệ: {str(e)}")
            return {
                "status": "error",
                "source": "Wikidata",
                "message": str(e),
                "wikidata_id": wikidata_id
            }

    async def search_by_mythology(self, mythology_name: str, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm các vị thần theo nền thần thoại.

        Parameters:
        - mythology_name: Tên nền thần thoại (ví dụ: 'Greek', 'Norse', 'Hindu')
        - limit: Số lượng kết quả tối đa (mặc định: 5)

        Returns:
        - JSON với danh sách các vị thần thuộc nền thần thoại đó
        """
        logger.info(f"Tìm kiếm các vị thần thuộc thần thoại: {mythology_name}")

        try:
            # Sử dụng SPARQL để tìm các vị thần thuộc nền thần thoại cụ thể
            sparql_url = "https://query.wikidata.org/sparql"
            sparql_query = f"""
            SELECT DISTINCT ?item ?itemLabel ?description ?wikipediaEN
            WHERE {{
              ?item wdt:P31/wdt:P279* wd:Q22989102 .  # Là vị thần
              ?item rdfs:label ?itemLabel .
              FILTER(LANG(?itemLabel) = "en")
              
              # Tìm theo tên thần thoại trong các thuộc tính liên quan
              {{
                ?item wdt:P1400 ?mythology .  # Thuộc tính thần thoại
                ?mythology rdfs:label ?mythologyLabel .
                FILTER(LANG(?mythologyLabel) = "en")
                FILTER(CONTAINS(LCASE(?mythologyLabel), LCASE("{mythology_name}")))
              }} UNION {{
                ?item wdt:P495 ?country .  # Thuộc tính quốc gia
                ?country rdfs:label ?countryLabel .
                FILTER(LANG(?countryLabel) = "en")
                FILTER(CONTAINS(LCASE(?countryLabel), LCASE("{mythology_name}")))
              }} UNION {{
                ?item wdt:P361 ?part_of .  # Thuộc tính là một phần của
                ?part_of rdfs:label ?partOfLabel .
                FILTER(LANG(?partOfLabel) = "en")
                FILTER(CONTAINS(LCASE(?partOfLabel), LCASE("{mythology_name}")))
              }}
              
              OPTIONAL {{ ?item schema:description ?description . FILTER(LANG(?description) = "en") }}
              OPTIONAL {{
                ?wikipediaEN schema:about ?item ;
                  schema:isPartOf <https://en.wikipedia.org/> .
              }}
            }}
            LIMIT {limit}
            """

            headers = {
                "Accept": "application/sparql-results+json",
                "User-Agent": "MythologyDeitySearchBot/1.0"
            }
            params = {
                "query": sparql_query,
                "format": "json"
            }
            
            response = requests.get(sparql_url, params=params, headers=headers, timeout=30)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikidata",
                    "message": f"Lỗi truy vấn SPARQL: {response.status_code}",
                    "mythology_name": mythology_name
                }
            
            data = response.json()
            bindings = data.get("results", {}).get("bindings", [])
            
            deities = []
            for b in bindings:
                item_url = b.get("item", {}).get("value")
                label = b.get("itemLabel", {}).get("value")
                description = b.get("description", {}).get("value") if "description" in b else None
                wikipedia_url = b.get("wikipediaEN", {}).get("value") if "wikipediaEN" in b else None
                
                deities.append({
                    "wikidata_url": item_url,
                    "name": label,
                    "description": description,
                    "wikipedia_url": wikipedia_url
                })
            
            return {
                "status": "success",
                "source": "Wikidata",
                "mythology": mythology_name,
                "deities_count": len(deities),
                "deities": deities,
                "search_url": f"https://www.wikidata.org/w/index.php?search={mythology_name}+deity"
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm thần thoại: {str(e)}")
            return {
                "status": "error",
                "source": "Wikidata",
                "message": str(e),
                "mythology_name": mythology_name
            }

    async def compare_deities(self, deity_id1: str, deity_id2: str) -> Dict[str, Any]:
        """
        So sánh hai vị thần dựa trên thông tin từ Wikidata.

        Parameters:
        - deity_id1: ID Wikidata của vị thần thứ nhất
        - deity_id2: ID Wikidata của vị thần thứ hai

        Returns:
        - JSON với thông tin so sánh chi tiết
        """
        logger.info(f"So sánh hai vị thần: {deity_id1} và {deity_id2}")

        try:
            # Lấy thông tin chi tiết của cả hai vị thần
            deity1 = await self.get_deity_details(deity_id1)
            deity2 = await self.get_deity_details(deity_id2)
            
            if deity1.get("status") != "success" or deity2.get("status") != "success":
                return {
                    "status": "error",
                    "source": "Wikidata",
                    "message": "Không thể lấy thông tin một hoặc cả hai vị thần",
                    "deity1_status": deity1.get("status"),
                    "deity2_status": deity2.get("status")
                }
            
            # Lấy thông tin cơ bản
            deity1_info = {
                "id": deity_id1,
                "name": deity1.get("name"),
                "description": deity1.get("description"),
                "instance_of": deity1.get("instance_of", []),
                "gender": deity1.get("gender", {}).get("label") if deity1.get("gender") else None,
                "religion": deity1.get("attributes", {}).get("religion", {}).get("label") if deity1.get("attributes", {}).get("religion") else None
            }
            
            deity2_info = {
                "id": deity_id2,
                "name": deity2.get("name"),
                "description": deity2.get("description"),
                "instance_of": deity2.get("instance_of", []),
                "gender": deity2.get("gender", {}).get("label") if deity2.get("gender") else None,
                "religion": deity2.get("attributes", {}).get("religion", {}).get("label") if deity2.get("attributes", {}).get("religion") else None
            }
            
            # Tìm điểm tương đồng và khác biệt
            similarities = []
            differences = []
            
            # So sánh giới tính
            if deity1_info["gender"] and deity2_info["gender"]:
                if deity1_info["gender"] == deity2_info["gender"]:
                    similarities.append(f"Cả hai đều là {deity1_info['gender']}")
                else:
                    differences.append(f"Giới tính: {deity1_info['gender']} vs {deity2_info['gender']}")
            
            # So sánh tôn giáo/thần thoại
            if deity1_info["religion"] and deity2_info["religion"]:
                if deity1_info["religion"] == deity2_info["religion"]:
                    similarities.append(f"Cùng thuộc tôn giáo/thần thoại: {deity1_info['religion']}")
                else:
                    differences.append(f"Tôn giáo/Thần thoại: {deity1_info['religion']} vs {deity2_info['religion']}")
            
            # So sánh thể loại (instance_of)
            common_types = set(d1.get("label") for d1 in deity1_info["instance_of"] if d1) & \
                          set(d2.get("label") for d2 in deity2_info["instance_of"] if d2)
            
            if common_types:
                similarities.append(f"Cùng thuộc các loại: {', '.join(common_types)}")
            
            return {
                "status": "success",
                "source": "Wikidata",
                "deity1": deity1_info,
                "deity2": deity2_info,
                "similarities": similarities,
                "differences": differences,
                "urls": {
                    "deity1": f"https://www.wikidata.org/entity/{deity_id1}",
                    "deity2": f"https://www.wikidata.org/entity/{deity_id2}",
                    "compare": f"https://www.wikidata.org/wiki/Special:Diff/{deity_id1}/{deity_id2}"
                },
                "suggested_comparisons": [
                    f"So sánh {deity1_info['name']} với các vị thần khác trong cùng thần thoại",
                    f"So sánh {deity2_info['name']} với các vị thần tương tự",
                    f"So sánh các vị thần {deity1_info.get('religion', '')} với {deity2_info.get('religion', '')}"
                ]
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi so sánh các vị thần: {str(e)}")
            return {
                "status": "error",
                "source": "Wikidata",
                "message": str(e),
                "deity1_id": deity_id1,
                "deity2_id": deity_id2
            }
