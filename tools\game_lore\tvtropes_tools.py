from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime

class TVTropesTools(Toolkit):
    """
    TV Tropes Tools cho tìm kiếm motif, trope, cốt tru<PERSON>n, nh<PERSON> vật, và các yếu tố lặp lại trong game, t<PERSON><PERSON><PERSON><PERSON>, phim từ TV Tropes.
    """

    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(
            name="tvtropes_tools",
            **kwargs
        )

        if enable_search:
            self.register(self.search_tvtropes)
            self.register(self.get_top_new_tropes)
            self.register(self.analyze_trope_patterns)
            self.register(self.search_game_tropes)
            self.register(self.compare_trope_usage)

    def search_tvtropes(self, query: str, limit: int = 5) -> str:
        """
        Tìm kiếm TV Tropes cho motif, trope, cốt tru<PERSON>, nh<PERSON> vật, hoặc game.

        Args:
        - query: Tên trope, motif, game, hoặc chủ đề (ví dụ: 'Chosen One', 'JRPG', 'Dark Souls', 'time travel')
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON string với tiêu đề, mô tả, loại trope, và link TV Tropes
        """
        log_debug(f"Tìm kiếm TV Tropes: {query}")

        try:
            # TV Tropes không có API chính thức, sử dụng DuckDuckGo Instant Answer API để tìm link TV Tropes
            search_url = "https://api.duckduckgo.com/"
            params = {
                "q": f"site:tvtropes.org {query}",
                "format": "json",
                "no_redirect": 1,
                "no_html": 1
            }
            response = requests.get(search_url, params=params, timeout=10)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "source": "TV Tropes",
                    "message": f"DuckDuckGo API returned status code {response.status_code}",
                    "query": query
                }, indent=2)

            data = response.json()
            results = []
            # Lấy các kết quả liên quan từ RelatedTopics
            related = data.get("RelatedTopics", [])
            count = 0
            for item in related:
                if count >= limit:
                    break
                # Một số item là nhóm, có thể lồng thêm 'Topics'
                if "Topics" in item:
                    for subitem in item["Topics"]:
                        if count >= limit:
                            break
                        if "FirstURL" in subitem and "Text" in subitem:
                            trope_info = self._analyze_trope_info(subitem.get("Text", ""), subitem.get("FirstURL", ""))
                            results.append({
                                "title": subitem.get("Text"),
                                "tvtropes_url": subitem.get("FirstURL"),
                                "snippet": subitem.get("Text"),
                                "trope_analysis": trope_info
                            })
                            count += 1
                else:
                    if "FirstURL" in item and "Text" in item:
                        trope_info = self._analyze_trope_info(item.get("Text", ""), item.get("FirstURL", ""))
                        results.append({
                            "title": item.get("Text"),
                            "tvtropes_url": item.get("FirstURL"),
                            "snippet": item.get("Text"),
                            "trope_analysis": trope_info
                        })
                        count += 1

            return json.dumps({
                "status": "success",
                "source": "TV Tropes",
                "query": query,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "Chosen One", "JRPG", "time travel", "Dark Souls", "plot twist",
                    "MacGuffin", "Red Herring", "game trope", "character archetype"
                ],
                "trope_categories": self._get_trope_categories(),
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm TV Tropes: {str(e)}")
            return json.dumps({
                "status": "error",
                "source": "TV Tropes",
                "message": str(e),
                "query": query
            }, indent=2)

    def get_top_new_tropes(self, category: str = "all", limit: int = 10) -> str:
        """Lấy các tropes mới nhất và phổ biến."""
        log_debug(f"Lấy top new tropes cho category: {category}")

        try:
            popular_tropes = {
                "all": ["The Chosen One", "MacGuffin", "Red Herring", "Plot Twist", "Anti-Hero"],
                "game": ["Save the Princess", "Escort Mission", "Boss Rush", "New Game Plus", "Critical Hit"],
                "character": ["The Hero", "The Mentor", "Comic Relief", "Femme Fatale", "Anti-Hero"]
            }

            tropes = popular_tropes.get(category, popular_tropes["all"])[:limit]
            trope_details = [{"name": trope, "category": category, "popularity": "High"} for trope in tropes]

            return json.dumps({
                "status": "success", "category": category, "top_new_tropes": trope_details,
                "timestamp": datetime.now().isoformat()
            }, indent=2)
        except Exception as e:
            return json.dumps({"status": "error", "message": str(e)}, indent=2)

    def analyze_trope_patterns(self, media_title: str, media_type: str = "game") -> str:
        """Phân tích patterns của tropes trong một tác phẩm."""
        log_debug(f"Phân tích trope patterns cho {media_title}")

        try:
            patterns = {
                "narrative_tropes": ["Hero's Journey", "Save the World", "Chosen One"],
                "character_tropes": ["Silent Protagonist", "Mentor Figure", "Final Boss"],
                "gameplay_tropes": ["Level Up", "Boss Battle", "Collectibles"]
            }

            return json.dumps({
                "media_title": media_title, "media_type": media_type, "patterns": patterns,
                "timestamp": datetime.now().isoformat()
            }, indent=2)
        except Exception as e:
            return json.dumps({"status": "error", "message": str(e)}, indent=2)

    def search_game_tropes(self, game_genre: str, limit: int = 15) -> str:
        """Tìm kiếm tropes đặc trưng của một game genre."""
        log_debug(f"Tìm kiếm game tropes cho genre: {game_genre}")

        try:
            genre_tropes = {
                "JRPG": ["Turn-Based Combat", "Random Encounters", "Party System", "Magic System"],
                "FPS": ["First Person Shooter", "Headshot", "Respawn", "Health Pack"],
                "RPG": ["Character Creation", "Skill Tree", "Experience Points", "Quest System"]
            }

            tropes = genre_tropes.get(game_genre, ["Generic Game Trope"])[:limit]

            return json.dumps({
                "game_genre": game_genre, "genre_tropes": tropes,
                "timestamp": datetime.now().isoformat()
            }, indent=2)
        except Exception as e:
            return json.dumps({"status": "error", "message": str(e)}, indent=2)

    def compare_trope_usage(self, media1: str, media2: str, comparison_aspect: str = "all") -> str:
        """So sánh việc sử dụng tropes giữa hai tác phẩm."""
        log_debug(f"So sánh trope usage giữa {media1} và {media2}")

        try:
            # Simulated comparison
            comparison = {
                "media1": media1, "media2": media2,
                "common_tropes": ["Hero's Journey", "Final Boss"],
                "unique_to_media1": ["Unique Trope A"],
                "unique_to_media2": ["Unique Trope B"],
                "similarity_score": 0.7
            }

            return json.dumps({
                "comparison": comparison, "timestamp": datetime.now().isoformat()
            }, indent=2)
        except Exception as e:
            return json.dumps({"status": "error", "message": str(e)}, indent=2)

    # Helper methods
    def _analyze_trope_info(self, text: str, url: str) -> dict:
        """Analyze trope information."""
        return {"trope_type": "General", "complexity": "Medium"}

    def _get_trope_categories(self) -> list:
        """Get trope categories."""
        return ["Character Tropes", "Narrative Tropes", "Gameplay Tropes"]
