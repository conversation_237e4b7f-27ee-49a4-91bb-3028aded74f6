#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script cho Mysteries Tools
"""

import sys
import os
import json
import random
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_cia_foia_tools():
    """Test CIA FOIA Tools"""
    print("🔓 Testing CIA FOIA Tools...")
    try:
        from tools.mysteries.cia_foia_tools import CIAFOIAMysteryTools

        cia_tool = CIAFOIAMysteryTools()

        print("  - Testing CIA FOIA search...")
        result = cia_tool.search_documents("UFO", 3)
        data = json.loads(result)
        assert data["status"] == "success"
        print("    ✅ CIA FOIA search works")

        print("  - Testing CIA FOIA get_top_new...")
        result = cia_tool.get_top_new("ufo", 5, "month", "aerial")
        data = json.loads(result)
        assert data["status"] == "success"
        print("    ✅ CIA FOIA get_top_new works")

        return True

    except Exception as e:
        print(f"    ❌ CIA FOIA Tools failed: {str(e)}")
        return False

def test_mysteries_search_toolkit():
    """Test Mysteries Search Toolkit"""
    print("🔍 Testing Mysteries Search Toolkit...")
    try:
        from tools.mysteries.mysteries_search_toolkit import MysteriesSearchToolkit

        toolkit = MysteriesSearchToolkit()

        print("  - Testing mystery phenomena search...")
        result = toolkit.search_mystery_phenomena("Bigfoot", "all", "Pacific Northwest", "modern")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Mystery phenomena search works")

        print("  - Testing UFO encounters search...")
        result = toolkit.search_ufo_encounters("sighting", "Nevada", "high", "modern")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ UFO encounters search works")

        print("  - Testing comprehensive mystery search...")
        result = toolkit.comprehensive_mystery_search("Roswell incident", "all", "medium", "1940s")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive mystery search works")

        return True

    except Exception as e:
        print(f"    ❌ Mysteries Search Toolkit failed: {str(e)}")
        return False

def test_other_mystery_tools():
    """Test other mystery tools"""
    print("📚 Testing Other Mystery Tools...")
    try:
        # Test Wikipedia Mystery Tools only
        from tools.mysteries.wikipedia_mystery_tools import WikipediaMysteryTools

        wiki_tool = WikipediaMysteryTools()

        print("  - Testing Wikipedia Mystery Tools...")
        result = wiki_tool.search_mysteries("UFO", 3)
        data = json.loads(result)
        assert data["status"] == "success"
        print("    ✅ Wikipedia Mystery Tools work")

        return True

    except Exception as e:
        print(f"    ❌ Other Mystery Tools failed: {str(e)}")
        return False

def test_random_mystery_functionality():
    """Test random mystery functionality"""
    print("\n🎲 Testing Random Mystery Functionality...")

    try:
        # Random CIA FOIA test
        from tools.mysteries.cia_foia_tools import CIAFOIAMysteryTools
        cia_tool = CIAFOIAMysteryTools()

        content_types = ["declassified", "ufo", "psychic", "mind_control"]
        content_type = random.choice(content_types)
        result = cia_tool.get_top_new(content_type, random.randint(3, 8), "month", "")
        data = json.loads(result)
        assert data["status"] == "success"
        print(f"  🎯 Random CIA FOIA {content_type} test passed")

        # Random mystery search test
        from tools.mysteries.mysteries_search_toolkit import MysteriesSearchToolkit
        toolkit = MysteriesSearchToolkit()

        phenomena = ["UFO", "Bigfoot", "Ghost", "Telepathy"]
        phenomenon = random.choice(phenomena)
        result = toolkit.search_mystery_phenomena(phenomenon, "all", "", "modern")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random mystery {phenomenon} search test passed")

        # Random conspiracy theory test
        theories = ["Area 51", "MKUltra", "Roswell", "Philadelphia Experiment"]
        theory = random.choice(theories)
        result = toolkit.search_conspiracy_theories(theory, "documented", "all", "global")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random conspiracy {theory} test passed")

        return True

    except Exception as e:
        print(f"    ❌ Random Mystery Functionality failed: {str(e)}")
        return False

def test_mystery_search_variations():
    """Test various mystery search variations"""
    print("\n🔮 Testing Mystery Search Variations...")

    try:
        from tools.mysteries.mysteries_search_toolkit import MysteriesSearchToolkit
        toolkit = MysteriesSearchToolkit()

        # Test paranormal investigations
        print("  - Testing paranormal investigations...")
        result = toolkit.search_paranormal_investigations("ghost", "scientific", "England", "academic")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Paranormal investigations search works")

        # Test UFO encounters with different parameters
        print("  - Testing UFO encounters variations...")
        result = toolkit.search_ufo_encounters("close_encounter", "New Mexico", "medium", "1950s")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ UFO encounters variations work")

        return True

    except Exception as e:
        print(f"    ❌ Mystery Search Variations failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 MYSTERIES TOOLS TEST SUITE")
    print("=" * 50)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing Mysteries channel tools...")
    print()

    test_results = []

    # Test all mystery tools
    test_functions = [
        ("CIA FOIA Tools", test_cia_foia_tools),
        ("Mysteries Search Toolkit", test_mysteries_search_toolkit),
        ("Other Mystery Tools", test_other_mystery_tools),
        ("Random Mystery Functionality", test_random_mystery_functionality),
        ("Mystery Search Variations", test_mystery_search_variations)
    ]

    for test_name, test_func in test_functions:
        print(f"\n{'='*15} {test_name} {'='*15}")
        result = test_func()
        test_results.append((test_name, result))
        print()

    # Summary
    print("\n" + "="*50)
    print("📋 MYSTERIES TOOLS TEST SUMMARY")
    print("="*50)

    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")

    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 All mysteries tools are working correctly!")
        print("✨ Mysteries channel fully functional!")
    elif passed >= total * 0.8:
        print("✅ Excellent performance - most functionality working!")
    elif passed >= total * 0.6:
        print("✅ Good performance - majority working!")
    else:
        print("⚠️  Some issues detected. Please check the error messages above.")

    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
