from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
from bs4 import BeautifulSoup
import json

class NaturalHistoryMuseumTool(Toolkit):
    """
    <PERSON><PERSON> công cụ để tìm kiếm và truy xuất dữ liệu mẫu vật hóa thạch từ Bảo tàng <PERSON> sử Tự nhiên London.
    """

    def __init__(self):
        super().__init__(
            name="Natural History Museum Tools",
            tools=[
                self.search_nhm,
                self.get_specimen_details,
                self.search_fossils_by_taxon,
                self.get_collection_records,
                self.search_by_geography
            ]
        )

    async def search_nhm(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Search the Natural History Museum's collection for fossil specimens.

        Parameters:
        - query: Search query using specimen ID, taxon, or fossil type (e.g., 'NHMUK PV R7996', 'Archaeopteryx fossil type:bone')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with search results including specimen metadata, taxonomy, and NHM URLs
        """
        logger.info(f"Searching Natural History Museum for: {query}")

        try:
            # NHM Open Data API endpoint (specimen search)
            # Documentation: https://data.nhm.ac.uk/api/3/action/package_search
            api_url = "https://data.nhm.ac.uk/api/3/action/package_search"
            params = {
                "q": query,
                "rows": limit
            }
            response = requests.get(api_url, params=params)

            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Natural History Museum",
                    "message": f"NHM API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for item in data.get("result", {}).get("results", []):
                nhm_id = item.get("id")
                title = item.get("title")
                notes = item.get("notes")
                url = item.get("url") or f"https://data.nhm.ac.uk/dataset/{nhm_id}"
                tags = [tag.get("display_name") for tag in item.get("tags", [])]
                resources = item.get("resources", [])

                # Optionally, extract specimen metadata from resources
                specimen_metadata = []
                for res in resources:
                    specimen_metadata.append({
                        "name": res.get("name"),
                        "format": res.get("format"),
                        "url": res.get("url"),
                        "description": res.get("description")
                    })

                results.append({
                    "nhm_id": nhm_id,
                    "title": title,
                    "description": notes,
                    "tags": tags,
                    "nhm_url": url,
                    "resources": specimen_metadata
                })

            return {
                "status": "success",
                "source": "Natural History Museum",
                "query": query,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching Natural History Museum: {str(e)}")
            return {
                "status": "error",
                "source": "Natural History Museum",
                "message": str(e),
                "query": query
            }

    async def get_specimen_details(self, specimen_id: str) -> Dict[str, Any]:
        """
        Lấy thông tin chi tiết về một mẫu vật cụ thể từ Bảo tàng Lịch sử Tự nhiên.

        Parameters:
        - specimen_id: ID của mẫu vật (ví dụ: 'NHMUK PV R 3915')

        Returns:
        - JSON với thông tin chi tiết về mẫu vật
        """
        logger.info(f"Lấy thông tin mẫu vật: {specimen_id}")

        try:
            # Sử dụng API dữ liệu mở của Bảo tàng Lịch sử Tự nhiên
            base_url = "https://data.nhm.ac.uk/api/3/action/package_show"
            params = {"id": specimen_id}
            
            response = requests.get(base_url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Natural History Museum",
                    "message": f"API trả về mã lỗi {response.status_code}",
                    "specimen_id": specimen_id
                }
            
            data = response.json()
            
            if not data.get("success"):
                return {
                    "status": "error",
                    "source": "Natural History Museum",
                    "message": "Không tìm thấy thông tin mẫu vật",
                    "specimen_id": specimen_id
                }
            
            result = data.get("result", {})
            
            # Trích xuất thông tin cơ bản
            specimen_info = {
                "title": result.get("title"),
                "description": result.get("notes"),
                "collection": result.get("collection"),
                "license": result.get("license_title"),
                "url": f"https://data.nhm.ac.uk/dataset/{specimen_id}"
            }
            
            # Trích xuất tài nguyên (hình ảnh, dữ liệu)
            resources = []
            for resource in result.get("resources", []):
                resources.append({
                    "format": resource.get("format"),
                    "url": resource.get("url"),
                    "description": resource.get("description")
                })
            
            return {
                "status": "success",
                "source": "Natural History Museum",
                "specimen_id": specimen_id,
                "info": specimen_info,
                "resources": resources,
                "metadata_created": result.get("metadata_created"),
                "metadata_modified": result.get("metadata_modified")
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy thông tin mẫu vật: {str(e)}")
            return {
                "status": "error",
                "source": "Natural History Museum",
                "message": str(e),
                "specimen_id": specimen_id
            }

    async def search_fossils_by_taxon(self, taxon_name: str, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm mẫu vật hóa thạch theo tên taxon.

        Parameters:
        - taxon_name: Tên taxon (ví dụ: 'Tyrannosaurus', 'Ammonoidea')
        - limit: Số kết quả tối đa (mặc định: 5)

        Returns:
        - JSON với danh sách các mẫu vật phù hợp
        """
        logger.info(f"Tìm kiếm mẫu vật theo taxon: {taxon_name}")

        try:
            base_url = "https://data.nhm.ac.uk/api/3/action/package_search"
            params = {
                "q": f'title:{taxon_name} OR notes:{taxon_name} OR tags:{taxon_name}'
            }
            
            response = requests.get(base_url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Natural History Museum",
                    "message": f"API trả về mã lỗi {response.status_code}",
                    "taxon_name": taxon_name
                }
            
            data = response.json()
            results = []
            
            for result in data.get("result", {}).get("results", [])[:limit]:
                results.append({
                    "id": result.get("id"),
                    "title": result.get("title"),
                    "description": result.get("notes"),
                    "url": f"https://data.nhm.ac.uk/dataset/{result.get('id')}",
                    "resources_count": len(result.get("resources", []))
                })
            
            return {
                "status": "success",
                "source": "Natural History Museum",
                "taxon_name": taxon_name,
                "results_count": len(results),
                "results": results
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm mẫu vật theo taxon: {str(e)}")
            return {
                "status": "error",
                "source": "Natural History Museum",
                "message": str(e),
                "taxon_name": taxon_name
            }

    async def get_collection_records(self, collection_name: str, limit: int = 5) -> Dict[str, Any]:
        """
        Lấy thông tin về các bản ghi trong một bộ sưu tập cụ thể.

        Parameters:
        - collection_name: Tên bộ sưu tập (ví dụ: 'Fossil Mammals', 'Dinosaurs')
        - limit: Số kết quả tối đa (mặc định: 5)

        Returns:
        - JSON với thông tin về các bản ghi trong bộ sưu tập
        """
        logger.info(f"Lấy thông tin bộ sưu tập: {collection_name}")

        try:
            base_url = "https://data.nhm.ac.uk/api/3/action/package_search"
            params = {
                "q": f'groups:{collection_name}',
                "rows": limit
            }
            
            response = requests.get(base_url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Natural History Museum",
                    "message": f"API trả về mã lỗi {response.status_code}",
                    "collection_name": collection_name
                }
            
            data = response.json()
            records = []
            
            for result in data.get("result", {}).get("results", []):
                records.append({
                    "id": result.get("id"),
                    "title": result.get("title"),
                    "description": result.get("notes"),
                    "url": f"https://data.nhm.ac.uk/dataset/{result.get('id')}",
                    "resources_count": len(result.get("resources", [])),
                    "metadata_created": result.get("metadata_created")
                })
            
            return {
                "status": "success",
                "source": "Natural History Museum",
                "collection_name": collection_name,
                "records_count": len(records),
                "records": records
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy thông tin bộ sưu tập: {str(e)}")
            return {
                "status": "error",
                "source": "Natural History Museum",
                "message": str(e),
                "collection_name": collection_name
            }

    async def search_by_geography(self, location: str, radius_km: int = 10, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm mẫu vật theo vị trí địa lý.

        Parameters:
        - location: Tên địa điểm (ví dụ: 'London', 'Dorset')
        - radius_km: Bán kính tìm kiếm tính bằng km (mặc định: 10)
        - limit: Số kết quả tối đa (mặc định: 5)

        Returns:
        - JSON với danh sách các mẫu vật tìm thấy trong khu vực
        """
        logger.info(f"Tìm kiếm mẫu vật tại: {location} (bán kính {radius_km}km)")

        try:
            # Đầu tiên lấy tọa độ địa lý của địa điểm
            geocode_url = "https://nominatim.openstreetmap.org/search"
            params = {
                "q": location,
                "format": "json",
                "limit": 1
            }
            
            geocode_response = requests.get(geocode_url, params=params)
            
            if geocode_response.status_code != 200 or not geocode_response.json():
                return {
                    "status": "error",
                    "source": "Natural History Museum",
                    "message": "Không thể xác định tọa độ địa lý",
                    "location": location
                }
            
            location_data = geocode_response.json()[0]
            lat, lon = location_data["lat"], location_data["lon"]
            
            # Tìm kiếm mẫu vật trong bán kính chỉ định
            base_url = "https://data.nhm.ac.uk/api/3/action/package_search"
            params = {
                "q": f'*:*',  # Tìm kiếm tất cả
                "ext_bbox": f"{float(lon)-0.1},{float(lat)-0.1},{float(lon)+0.1},{float(lat)+0.1}",
                "rows": limit
            }
            
            response = requests.get(base_url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Natural History Museum",
                    "message": f"API trả về mã lỗi {response.status_code}",
                    "location": location
                }
            
            data = response.json()
            specimens = []
            
            for result in data.get("result", {}).get("results", []):
                specimens.append({
                    "id": result.get("id"),
                    "title": result.get("title"),
                    "description": result.get("notes"),
                    "url": f"https://data.nhm.ac.uk/dataset/{result.get('id')}",
                    "resources_count": len(result.get("resources", []))
                })
            
            return {
                "status": "success",
                "source": "Natural History Museum",
                "location": location,
                "coordinates": {"latitude": lat, "longitude": lon},
                "radius_km": radius_km,
                "specimens_count": len(specimens),
                "specimens": specimens
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm theo địa lý: {str(e)}")
            return {
                "status": "error",
                "source": "Natural History Museum",
                "message": str(e),
                "location": location
            }
