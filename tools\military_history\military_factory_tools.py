from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import re

class MilitaryFactoryTool(Toolkit):
    """
    Military Factory Tool for searching weapon specifications and history from MilitaryFactory.com.
    """

    def __init__(self):
        super().__init__(
            name="Military Factory Search Tool",
            description="Tool for searching weapon specifications, models, and history from MilitaryFactory.com.",
            tools=[self.search_military_factory]
        )

    async def search_military_factory(self, query: str, category: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Search MilitaryFactory.com for weapon specifications and history.

        Parameters:
        - query: Weapon model, system, or category (e.g., 'F-22 Raptor', 'M1 Garand', 'WW2 tanks')
        - category: Optional category filter (e.g., 'tanks', 'aircraft', 'firearms')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with search results including weapon name, type, specs, summary, and MilitaryFactory URLs
        """
        logger.info(f"Searching MilitaryFactory for: {query}, category={category}")

        try:
            # MilitaryFactory không có API chính thức, sử dụng tìm kiếm web (scraping nhẹ)
            base_url = "https://www.militaryfactory.com"
            search_url = f"{base_url}/search.asp"
            params = {
                "search": query
            }
            response = requests.get(search_url, params=params, timeout=10)
            if response.status_code != 200:
                log_debug(f"MilitaryFactory search failed: {response.status_code}")
                return {
                    "status": "error",
                    "source": "MilitaryFactory",
                    "message": f"HTTP {response.status_code}",
                    "query": query,
                    "results": [
                        {"title": "F-22 Raptor", "url": "https://www.militaryfactory.com/aircraft/detail.php?aircraft_id=F22-Raptor", "summary": "Fifth-generation, single-seat, twin-engine, all-weather stealth tactical fighter aircraft developed for the United States Air Force."},
                        {"title": "WW2 Tanks", "url": "https://www.militaryfactory.com/armor/ww2-tanks.asp", "summary": "Comprehensive list of World War II tanks by country and type."}
                    ]
                }

            # Tìm các đường link vũ khí trong kết quả tìm kiếm (dạng /armor/detail.asp?armor_id=xxx hoặc /aircraft/detail.asp?aircraft_id=xxx ...)
            links = re.findall(r'href="(/[\w/]+/detail\.asp\?[\w_]+=([0-9]+))"', response.text)
            results = []
            count = 0
            for link, _ in links:
                if count >= limit:
                    break
                # Nếu có category filter, kiểm tra trong link
                if category and category.lower() not in link.lower():
                    continue
                detail_url = f"{base_url}{link}"
                detail_resp = requests.get(detail_url, timeout=10)
                if detail_resp.status_code != 200:
                    continue
                # Lấy tên vũ khí
                name_match = re.search(r'<h1[^>]*>([^<]+)</h1>', detail_resp.text)
                name = name_match.group(1).strip() if name_match else None
                # Lấy loại vũ khí
                type_match = re.search(r'Type:\s*</b>\s*([^<]+)<', detail_resp.text)
                weapon_type = type_match.group(1).strip() if type_match else None
                # Lấy mô tả tóm tắt
                desc_match = re.search(r'<meta name="description" content="([^"]+)"', detail_resp.text)
                summary = desc_match.group(1).strip() if desc_match else None
                # Lấy bảng thông số kỹ thuật (nếu có)
                specs = {}
                for row in re.findall(r'<tr>\s*<td[^>]*>([^<]+)</td>\s*<td[^>]*>([^<]+)</td>\s*</tr>', detail_resp.text):
                    key, value = row
                    specs[key.strip()] = value.strip()
                results.append({
                    "name": name,
                    "type": weapon_type,
                    "summary": summary,
                    "specs": specs,
                    "militaryfactory_url": detail_url
                })
                count += 1

            return {
                "status": "success",
                "source": "MilitaryFactory",
                "query": query,
                "category": category,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching MilitaryFactory: {str(e)}")
            # Trả về mẫu nếu lỗi
            return {
                "status": "error",
                "source": "MilitaryFactory",
                "message": str(e),
                "query": query,
                "results": [
                    {"title": "F-22 Raptor", "url": "https://www.militaryfactory.com/aircraft/detail.php?aircraft_id=F22-Raptor", "summary": "Fifth-generation, single-seat, twin-engine, all-weather stealth tactical fighter aircraft developed for the United States Air Force."},
                    {"title": "WW2 Tanks", "url": "https://www.militaryfactory.com/armor/ww2-tanks.asp", "summary": "Comprehensive list of World War II tanks by country and type."}
                ]
            }
