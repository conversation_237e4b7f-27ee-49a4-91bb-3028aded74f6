from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json
import re
import aiohttp
import asyncio
from urllib.parse import urljoin, quote, unquote

from agno.tools import Toolkit
from agno.utils.log import logger

# Các hằng số
WIKIPEDIA_API = "https://{lang}.wikipedia.org/w/api.php"
WIKIPEDIA_PAGE = "https://{lang}.wikipedia.org/wiki/{title}"
CACHE_EXPIRY_DAYS = 7

@dataclass
class WikipediaArticle:
    """Lớp đại diện cho một bài viết Wikipedia"""
    title: str
    pageid: int
    url: str
    summary: str
    extract: str
    thumbnail: Optional[Dict[str, str]] = None
    categories: List[str] = None
    links: List[Dict[str, str]] = None
    references: List[Dict[str, str]] = None
    last_modified: str = ""
    word_count: int = 0

class WikipediaPhilosophyTool(Toolkit):
    """
    Công cụ tìm kiếm thông tin triết học từ Wikipedia
    """

    def __init__(self):
        super().__init__(
            name="Công cụ Wikipedia Triết học",
            description="""
            Công cụ tìm kiếm và phân tích thông tin triết học từ Wikipedia.
            Hỗ trợ tìm kiếm bài viết, triết gia, trường phái và khái niệm triết học.
            """,
            tools=[
                self.search_wikipedia,
                self.get_article,
                self.get_philosopher_info,
                self.get_philosophy_schools,
                self.get_related_articles
            ]
        )
        self.session = None
        self.cache = {}
        self._load_cache()

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def _load_cache(self) -> None:
        """Tải dữ liệu cache từ file"""
        try:
            with open(".wikipedia_cache.json", 'r', encoding='utf-8') as f:
                self.cache = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            self.cache = {}

    def _save_cache(self) -> None:
        """Lưu dữ liệu cache vào file"""
        with open(".wikipedia_cache.json", 'w', encoding='utf-8') as f:
            json.dump(self.cache, f, ensure_ascii=False, indent=2)

    def _get_cache(self, key: str) -> Any:
        """Lấy dữ liệu từ cache"""
        cached = self.cache.get(key)
        if cached and datetime.now().timestamp() < cached.get('expires', 0):
            return cached['data']
        return None

    def _set_cache(self, key: str, data: Any, ttl: int = 604800) -> None:
        """Lưu dữ liệu vào cache (mặc định 7 ngày)"""
        self.cache[key] = {
            'data': data,
            'expires': datetime.now().timestamp() + ttl
        }
        self._save_cache()

    async def _make_request(self, url: str, params: Dict = None, use_cache: bool = True) -> Dict:
        """Gửi yêu cầu HTTP đến Wikipedia API"""
        cache_key = f"req_{url}?{urlencode(params or {})}"
        
        # Kiểm tra cache
        if use_cache:
            cached = self._get_cache(cache_key)
            if cached is not None:
                return cached
        
        # Gửi yêu cầu mới
        try:
            async with self.session.get(url, params=params) as response:
                response.raise_for_status()
                data = await response.json()
                
                # Lưu vào cache
                self._set_cache(cache_key, data)
                return data
                
        except Exception as e:
            logger.error(f"Lỗi khi gửi yêu cầu đến {url}: {str(e)}")
            raise

    async def search_wikipedia(
        self, 
        query: str, 
        language: str = "en",
        limit: int = 5,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Tìm kiếm trên Wikipedia
        
        Parameters:
        - query: Từ khóa tìm kiếm
        - language: Mã ngôn ngữ (mặc định: 'en')
        - limit: Số lượng kết quả tối đa
        - use_cache: Sử dụng cache hay không
        
        Returns:
        - Dict chứa kết quả tìm kiếm
        """
        logger.info(f"Tìm kiếm Wikipedia ({language}): {query}")
        
        try:
            # Tạo URL API
            api_url = WIKIPEDIA_API.format(lang=language)
            
            # Tham số tìm kiếm
            params = {
                "action": "query",
                "format": "json",
                "list": "search",
                "srsearch": query,
                "srlimit": limit,
                "srprop": "snippet|titlesnippet",
                "utf8": ""
            }
            
            # Gửi yêu cầu tìm kiếm
            data = await self._make_request(api_url, params, use_cache)
            
            # Xử lý kết quả
            results = []
            if "query" in data and "search" in data["query"]:
                for item in data["query"]["search"]:
                    results.append({
                        "title": item.get("title"),
                        "pageid": item.get("pageid"),
                        "snippet": item.get("snippet"),
                        "url": WIKIPEDIA_PAGE.format(
                            lang=language,
                            title=quote(item.get("title").replace(" ", "_"))
                        )
                    })
            
            return {
                "status": "success",
                "query": query,
                "language": language,
                "results_count": len(results),
                "results": results,
                "suggested_searches": self._get_search_suggestions()
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi tìm kiếm Wikipedia: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "query": query,
                "language": language
            }
    
    async def get_article(
        self, 
        title: str,
        language: str = "en",
        include_links: bool = True,
        include_references: bool = True,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Lấy nội dung chi tiết một bài viết Wikipedia
        
        Parameters:
        - title: Tiêu đề bài viết
        - language: Mã ngôn ngữ (mặc định: 'en')
        - include_links: Có bao gồm các liên kết trong bài không
        - include_references: Có bao gồm tài liệu tham khảo không
        - use_cache: Sử dụng cache hay không
        """
        logger.info(f"Lấy bài viết '{title}' từ Wikipedia ({language})")
        
        try:
            # Tạo URL API
            api_url = WIKIPEDIA_API.format(lang=language)
            
            # Tham số yêu cầu
            params = {
                "action": "query",
                "format": "json",
                "prop": "extracts|pageprops|pageimages|info",
                "titles": title,
                "exintro": "",
                "explaintext": "",
                "inprop": "url|talkid|subjectid|url|readable|preload|displaytitle",
                "piprop": "thumbnail",
                "pithumbsize": 300,
                "redirects": 1
            }
            
            # Thêm tham số tùy chọn
            if include_links:
                params["pllimit"] = "max"
                params["prop"] += "|links"
                
            if include_references:
                params["prop"] += "|extlinks"
            
            # Gửi yêu cầu
            data = await self._make_request(api_url, params, use_cache)
            
            # Xử lý kết quả
            pages = data.get("query", {}).get("pages", {})
            if not pages:
                return {
                    "status": "not_found",
                    "message": f"Không tìm thấy bài viết: {title}",
                    "title": title,
                    "language": language
                }
            
            # Lấy thông tin trang đầu tiên
            page_id, page = next(iter(pages.items()))
            
            # Tạo đối tượng bài viết
            article = {
                "title": page.get("title"),
                "pageid": int(page_id) if page_id.isdigit() else None,
                "url": page.get("fullurl"),
                "extract": page.get("extract", ""),
                "thumbnail": {
                    "source": page.get("thumbnail", {}).get("source"),
                    "width": page.get("thumbnail", {}).get("width"),
                    "height": page.get("thumbnail", {}).get("height")
                } if "thumbnail" in page else None,
                "categories": [
                    cat["title"].replace("Category:", "") 
                    for cat in page.get("categories", [])
                ],
                "last_modified": page.get("touched", ""),
                "word_count": len(page.get("extract", "").split())
            }
            
            # Lấy các liên kết nếu được yêu cầu
            if include_links and "links" in page:
                article["links"] = [
                    {"title": link["title"], "ns": link["ns"]}
                    for link in page["links"]
                ]
            
            # Lấy tài liệu tham khảo nếu được yêu cầu
            if include_references and "extlinks" in page:
                article["references"] = [
                    {"url": ref["*"], "domain": ref["*"].split("//")[-1].split("/")[0]}
                    for ref in page["extlinks"]
                ]
            
            return {
                "status": "success",
                "article": article,
                "language": language
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy bài viết: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "title": title,
                "language": language
            }
    
    async def get_philosopher_info(
        self, 
        philosopher_name: str,
        language: str = "en",
        detailed: bool = False
    ) -> Dict[str, Any]:
        """
        Lấy thông tin về một triết gia từ Wikipedia
        
        Parameters:
        - philosopher_name: Tên triết gia
        - language: Mã ngôn ngữ (mặc định: 'en')
        - detailed: Có lấy thông tin chi tiết không
        """
        try:
            # Tìm kiếm bài viết về triết gia
            search_result = await self.search_wikipedia(
                f"{philosopher_name} philosopher",
                language=language,
                limit=1
            )
            
            if search_result["results_count"] == 0:
                return {
                    "status": "not_found",
                    "message": f"Không tìm thấy thông tin về triết gia: {philosopher_name}",
                    "suggestions": self._get_philosopher_suggestions()
                }
            
            # Lấy thông tin chi tiết nếu cần
            result = search_result["results"][0]
            if detailed:
                article = await self.get_article(result["title"], language=language)
                if article["status"] == "success":
                    result.update(article["article"])
            
            return {
                "status": "success",
                "philosopher": philosopher_name,
                "data": result,
                "related_philosophers": self._get_related_philosophers(philosopher_name)
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy thông tin triết gia: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "philosopher": philosopher_name,
                "language": language
            }
    
    async def get_philosophy_schools(
        self, 
        language: str = "en",
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        Lấy danh sách các trường phái triết học
        
        Parameters:
        - language: Mã ngôn ngữ (mặc định: 'en')
        - limit: Số lượng kết quả tối đa
        """
        try:
            # Danh sách các trường phái triết học chính
            schools = [
                "Analytic philosophy", "Continental philosophy", "Phenomenology",
                "Existentialism", "Stoicism", "Epicureanism", "Skepticism",
                "Idealism", "Materialism", "Rationalism", "Empiricism",
                "Pragmatism", "Post-structuralism", "Postmodernism",
                "Feminist philosophy", "Marxist philosophy", "Utilitarianism",
                "Deontological ethics", "Virtue ethics", "Confucianism",
                "Taoism", "Buddhist philosophy", "Vedanta", "Nyaya", "Yogacara"
            ]
            
            # Lấy thông tin chi tiết nếu cần
            detailed_schools = []
            for school in schools[:limit]:
                try:
                    article = await self.get_article(school, language=language, include_links=False)
                    if article["status"] == "success":
                        detailed_schools.append({
                            "name": school,
                            "summary": article["article"].get("extract", ""),
                            "url": article["article"].get("url")
                        })
                except Exception as e:
                    logger.warning(f"Không thể lấy thông tin trường phái {school}: {str(e)}")
            
            return {
                "status": "success",
                "language": language,
                "schools_count": len(detailed_schools),
                "schools": detailed_schools,
                "available_categories": [
                    "Western", "Eastern", "Ancient", "Modern", "Contemporary",
                    "Ethics", "Metaphysics", "Epistemology", "Logic"
                ]
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy danh sách trường phái: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "language": language
            }
    
    async def get_related_articles(
        self, 
        title: str,
        language: str = "en",
        limit: int = 5,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Lấy các bài viết liên quan từ Wikipedia
        
        Parameters:
        - title: Tiêu đề bài viết gốc
        - language: Mã ngôn ngữ (mặc định: 'en')
        - limit: Số lượng bài viết liên quan tối đa
        - use_cache: Sử dụng cache hay không
        """
        try:
            # Lấy các liên kết từ bài viết
            article = await self.get_article(
                title,
                language=language,
                include_links=True,
                use_cache=use_cache
            )
            
            if article["status"] != "success":
                return {
                    "status": "error",
                    "message": "Không thể lấy thông tin bài viết",
                    "title": title,
                    "language": language
                }
            
            # Lọc các liên kết có vẻ liên quan đến triết học
            related_links = []
            for link in article["article"].get("links", []):
                if self._is_philosophy_related(link["title"]):
                    related_links.append({
                        "title": link["title"],
                        "url": WIKIPEDIA_PAGE.format(
                            lang=language,
                            title=quote(link["title"].replace(" ", "_"))
                        )
                    })
                    
                    if len(related_links) >= limit:
                        break
            
            return {
                "status": "success",
                "original_title": title,
                "language": language,
                "related_articles": related_links,
                "related_count": len(related_links)
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy bài viết liên quan: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "title": title,
                "language": language
            }
    
    def _is_philosophy_related(self, title: str) -> bool:
        """Kiểm tra xem tiêu đề có liên quan đến triết học không"""
        if not title:
            return False
            
        # Loại bỏ các trang không phải bài viết chính
        if any(prefix in title for prefix in ["Template:", "Category:", "File:", "Help:", "Portal:"]):
            return False
            
        # Các từ khóa liên quan đến triết học
        keywords = [
            "philosophy", "philosopher", "ethics", "metaphysics", "epistemology",
            "logic", "aesthetics", "political", "theory", "thinker", "school of thought",
            "existential", "phenomenology", "analytic", "continental", "stoicism",
            "utilitarian", "kant", "hegel", "nietzsche", "plato", "aristotle",
            "confucius", "laozi", "buddhism", "vedanta", "rationalism", "empiricism"
        ]
        
        title_lower = title.lower()
        return any(keyword in title_lower for keyword in keywords)
    
    def _get_search_suggestions(self) -> List[Dict[str, str]]:
        """Trả về danh sách gợi ý tìm kiếm"""
        return [
            {"query": "Existentialism", "description": "Chủ nghĩa hiện sinh"},
            {"query": "Immanuel Kant", "description": "Triết gia Đức"},
            {"query": "Ethics", "description": "Đạo đức học"},
            {"query": "Metaphysics", "description": "Siêu hình học"},
            {"query": "Philosophy of mind", "description": "Triết học tinh thần"},
            {"query": "Eastern philosophy", "description": "Triết học phương Đông"},
            {"query": "Political philosophy", "description": "Triết học chính trị"}
        ]
    
    def _get_philosopher_suggestions(self) -> List[str]:
        """Trả về danh sách gợi ý triết gia"""
        return [
            "Plato", "Aristotle", "Immanuel Kant", "Friedrich Nietzsche",
            "Jean-Paul Sartre", "Simone de Beauvoir", "Ludwig Wittgenstein",
            "Martin Heidegger", "Confucius", "Laozi", "Karl Marx", "John Locke",
            "David Hume", "René Descartes", "Baruch Spinoza", "Gottfried Leibniz",
            "Georg Wilhelm Friedrich Hegel", "Arthur Schopenhauer", "Søren Kierkegaard",
            "Michel Foucault", "Jacques Derrida", "Judith Butler", "Martha Nussbaum"
        ]
    
    def _get_related_philosophers(self, philosopher: str) -> List[str]:
        """Trả về danh sách triết gia liên quan"""
        related_map = {
            "Plato": ["Aristotle", "Socrates", "Plotinus", "Augustine"],
            "Aristotle": ["Plato", "Thomas Aquinas", "John Locke", "Immanuel Kant"],
            "Immanuel Kant": ["David Hume", "G.W.F. Hegel", "Arthur Schopenhauer"],
            "Friedrich Nietzsche": ["Arthur Schopenhauer", "Martin Heidegger", "Michel Foucault"],
            "Jean-Paul Sartre": ["Simone de Beauvoir", "Albert Camus", "Maurice Merleau-Ponty"],
            "Confucius": ["Mencius", "Xunzi", "Zhu Xi"],
            "Laozi": ["Zhuangzi", "Liezi", "Lao Tzu"],
            "Karl Marx": ["Friedrich Engels", "Vladimir Lenin", "Antonio Gramsci"],
            "Ludwig Wittgenstein": ["Bertrand Russell", "G.E. Moore", "Saul Kripke"]
        }
        return related_map.get(philosopher, [])