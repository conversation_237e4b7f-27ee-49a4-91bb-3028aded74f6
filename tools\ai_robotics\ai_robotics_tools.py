from typing import Dict, Any, List, Optional
from agno.tools import <PERSON>lk<PERSON>
from agno.utils.log import log_debug, logger
import requests
import json
import os

class AIRoboticsToolkit(Toolkit):
    """
    AI Robotics Toolkit providing tools to search specialized AI, Robotics, and Machine Learning resources.
    """
    
    def __init__(self):
        super().__init__(
            name="AI Robotics Toolkit",
            description="Tools for searching AI, robotics, and machine learning resources like Papers With Code, Hugging Face, arXiv, OpenML, and Wikipedia.",
            tools=[
                self.search_papers_with_code,
                self.search_huggingface_hub,
                self.search_arxiv,
                self.search_openml,
                self.search_wikipedia_ai,
            ]
        )
        # Set up authentication tokens
        self.huggingface_token = os.environ.get("HUGGINGFACE_TOKEN", "*************************************")
        self.github_token = os.environ.get("GITHUB_TOKEN", "*********************************************************************************************")

    async def search_papers_with_code(self, query: str) -> Dict[str, Any]:
        """
        Search Papers With Code for AI research papers and their implementations.
        
        Parameters:
        - query: Search query in format '<task>/<subtask>' or '<model_architecture>:<application>' 
                (e.g., "vision/segmentation", "transformer:time-series")
        
        Returns:
        - JSON with search results including paper titles, links, code repositories, and descriptions
        """
        logger.info(f"Searching Papers With Code for: {query}")
        
        try:
            # Papers With Code API endpoint
            url = f"https://paperswithcode.com/api/v1/papers/?search={query}"
            response = requests.get(url)
            
            if response.status_code == 200:
                data = response.json()
                results = []
                
                for paper in data.get("results", [])[:10]:  # Limit to top 10 results
                    paper_data = {
                        "title": paper.get("title", ""),
                        "abstract": paper.get("abstract", ""),
                        "url": paper.get("url", ""),
                        "published": paper.get("published", ""),
                        "code_repositories": [repo.get("url", "") for repo in paper.get("repositories", [])],
                        "tasks": [task.get("name", "") for task in paper.get("tasks", [])]
                    }
                    results.append(paper_data)
                
                return {
                    "status": "success",
                    "source": "Papers With Code",
                    "query": query,
                    "results_count": len(results),
                    "results": results
                }
            else:
                return {
                    "status": "error",
                    "source": "Papers With Code",
                    "message": f"API returned status code {response.status_code}",
                    "query": query
                }
                
        except Exception as e:
            log_debug(f"Error searching Papers With Code: {str(e)}")
            return {
                "status": "error",
                "source": "Papers With Code",
                "message": str(e),
                "query": query
            }

    async def search_huggingface_hub(self, query: str, model_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Search Hugging Face Hub for models and datasets.
        
        Parameters:
        - query: Search query in format '<model_type>-<variant>' or '<task>::<dataset>' 
                (e.g., "bert-base-uncased", "translation::wmt16")
        - model_type: Optional filter by model type (like "text-classification", "image-segmentation")
        
        Returns:
        - JSON with search results including model/dataset names, descriptions, and download links
        """
        logger.info(f"Searching Hugging Face Hub for: {query}")
        
        try:
            # Hugging Face Hub API endpoint
            url = "https://huggingface.co/api/models"
            if "::" in query:
                # This is a task::dataset query
                task, dataset = query.split("::", 1)
                params = {"search": dataset, "task": task}
            else:
                params = {"search": query}
                
            if model_type:
                params["filter"] = model_type
                
            headers = {"Authorization": f"Bearer {self.huggingface_token}"}
            response = requests.get(url, params=params, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                results = []
                
                for model in data[:10]:  # Limit to top 10 results
                    model_data = {
                        "id": model.get("id", ""),
                        "name": model.get("modelId", ""),
                        "tags": model.get("tags", []),
                        "downloads": model.get("downloads", 0),
                        "likes": model.get("likes", 0),
                        "pipeline_tag": model.get("pipeline_tag", ""),
                        "url": f"https://huggingface.co/{model.get('id', '')}"
                    }
                    results.append(model_data)
                
                return {
                    "status": "success",
                    "source": "Hugging Face Hub",
                    "query": query,
                    "results_count": len(results),
                    "results": results
                }
            else:
                return {
                    "status": "error",
                    "source": "Hugging Face Hub",
                    "message": f"API returned status code {response.status_code}",
                    "query": query
                }
                
        except Exception as e:
            log_debug(f"Error searching Hugging Face Hub: {str(e)}")
            return {
                "status": "error",
                "source": "Hugging Face Hub",
                "message": str(e),
                "query": query
            }

    async def search_arxiv(self, query: str, max_results: int = 10) -> Dict[str, Any]:
        """
        Search arXiv for AI-related research papers.
        
        Parameters:
        - query: Search query in format '<cs.XX>+<cs.YY>:<keyword>' 
                (e.g., "cs.AI+cs.CV:neural rendering", "cs.LG+cs.NE:reinforcement learning")
        - max_results: Maximum number of results to return (default: 10)
        
        Returns:
        - JSON with search results including paper titles, authors, abstracts, and links
        """
        logger.info(f"Searching arXiv for: {query}")
        
        try:
            # Parse the query for categories
            categories = []
            search_term = query
            
            if ":" in query:
                cat_part, search_term = query.split(":", 1)
                if "+" in cat_part:
                    categories = cat_part.split("+")
                else:
                    categories = [cat_part]
            
            # Construct the arXiv API query
            cat_query = " OR ".join([f"cat:{cat}" for cat in categories]) if categories else ""
            if cat_query and search_term:
                arxiv_query = f"({cat_query}) AND all:{search_term}"
            elif cat_query:
                arxiv_query = cat_query
            else:
                arxiv_query = f"all:{search_term}"
            
            base_url = "http://export.arxiv.org/api/query"
            params = {
                "search_query": arxiv_query,
                "start": 0,
                "max_results": max_results,
                "sortBy": "relevance",
                "sortOrder": "descending",
            }
            
            response = requests.get(base_url, params=params)
            
            if response.status_code == 200:
                # arXiv returns XML, so we need to parse it
                import xml.etree.ElementTree as ET
                from datetime import datetime
                
                root = ET.fromstring(response.content)
                
                # Define namespaces used in arXiv XML
                ns = {
                    "atom": "http://www.w3.org/2005/Atom",
                    "arxiv": "http://arxiv.org/schemas/atom"
                }
                
                results = []
                entries = root.findall(".//atom:entry", ns)
                
                for entry in entries:
                    # Extract paper details
                    title = entry.find("atom:title", ns).text.strip().replace("\n", " ").replace("  ", " ")
                    summary = entry.find("atom:summary", ns).text.strip().replace("\n", " ").replace("  ", " ")
                    published = entry.find("atom:published", ns).text
                    
                    # Format the published date
                    pub_date = datetime.strptime(published, "%Y-%m-%dT%H:%M:%SZ")
                    formatted_date = pub_date.strftime("%d %b %Y")
                    
                    # Get the arXiv ID and construct URLs
                    arxiv_id = entry.find("atom:id", ns).text.split("/")[-1]
                    pdf_url = f"https://arxiv.org/pdf/{arxiv_id}.pdf"
                    abstract_url = f"https://arxiv.org/abs/{arxiv_id}"
                    
                    # Get authors
                    authors = []
                    author_elements = entry.findall(".//atom:author/atom:name", ns)
                    for author in author_elements:
                        authors.append(author.text)
                    
                    # Get categories
                    categories = []
                    category_elements = entry.findall(".//arxiv:primary_category", ns)
                    for category in category_elements:
                        categories.append(category.attrib["term"])
                    
                    paper_data = {
                        "title": title,
                        "authors": authors,
                        "summary": summary,
                        "published": formatted_date,
                        "arxiv_id": arxiv_id,
                        "pdf_url": pdf_url,
                        "abstract_url": abstract_url,
                        "categories": categories
                    }
                    results.append(paper_data)
                
                return {
                    "status": "success",
                    "source": "arXiv",
                    "query": query,
                    "query_translated": arxiv_query,
                    "results_count": len(results),
                    "results": results
                }
            else:
                return {
                    "status": "error",
                    "source": "arXiv",
                    "message": f"API returned status code {response.status_code}",
                    "query": query
                }
                
        except Exception as e:
            log_debug(f"Error searching arXiv: {str(e)}")
            return {
                "status": "error",
                "source": "arXiv",
                "message": str(e),
                "query": query
            }

    async def search_openml(self, query: str) -> Dict[str, Any]:
        """
        Search OpenML for machine learning datasets and tasks.
        
        Parameters:
        - query: Search query in format 'data/<category>/<specific>' or 'task/<task_id>' 
                (e.g., "data/image/mnist", "task/classification")
        
        Returns:
        - JSON with search results including dataset/task names, descriptions, and statistics
        """
        logger.info(f"Searching OpenML for: {query}")
        
        try:
            # Parse the query
            query_type = "data"
            search_term = query
            
            if query.startswith("data/") or query.startswith("task/"):
                query_parts = query.split("/")
                query_type = query_parts[0]
                search_term = "/".join(query_parts[1:]) if len(query_parts) > 1 else ""
            
            # OpenML API endpoint
            base_url = "https://www.openml.org/api/v1"
            
            if query_type == "data":
                url = f"{base_url}/data/list/data_name/{search_term}/limit/10/status/active"
            elif query_type == "task":
                url = f"{base_url}/task/list/type/{search_term}/limit/10"
            else:
                url = f"{base_url}/data/list/data_name/{search_term}/limit/10/status/active"
            
            response = requests.get(url)
            
            if response.status_code == 200:
                data = response.json()
                results = []
                
                if query_type == "data":
                    for dataset in data.get("data", {}).get("dataset", []):
                        dataset_data = {
                            "did": dataset.get("did", ""),
                            "name": dataset.get("name", ""),
                            "format": dataset.get("format", ""),
                            "status": dataset.get("status", ""),
                            "version": dataset.get("version", ""),
                            "description": dataset.get("description", ""),
                            "url": f"https://www.openml.org/d/{dataset.get('did', '')}"
                        }
                        results.append(dataset_data)
                elif query_type == "task":
                    for task in data.get("tasks", {}).get("task", []):
                        task_data = {
                            "task_id": task.get("task_id", ""),
                            "name": task.get("name", ""),
                            "task_type": task.get("task_type", ""),
                            "dataset_id": task.get("source_data", {}).get("data_set_id", ""),
                            "estimation_procedure": task.get("estimation_procedure", {}).get("name", ""),
                            "url": f"https://www.openml.org/t/{task.get('task_id', '')}"
                        }
                        results.append(task_data)
                
                return {
                    "status": "success",
                    "source": "OpenML",
                    "query": query,
                    "query_type": query_type,
                    "results_count": len(results),
                    "results": results
                }
            else:
                return {
                    "status": "error",
                    "source": "OpenML",
                    "message": f"API returned status code {response.status_code}",
                    "query": query
                }
                
        except Exception as e:
            log_debug(f"Error searching OpenML: {str(e)}")
            return {
                "status": "error",
                "source": "OpenML",
                "message": str(e),
                "query": query
            }

    async def search_wikipedia_ai(self, query: str) -> Dict[str, Any]:
        """
        Search Wikipedia for AI, robotics, and machine learning concepts and history.
        
        Parameters:
        - query: Search query in format '<algorithm_type> algorithm' or '<technique> in <field>' 
                (e.g., "backpropagation algorithm", "transformer in NLP", "convolutional neural networks")
        
        Returns:
        - JSON with search results including article summaries and links
        """
        logger.info(f"Searching Wikipedia for AI topic: {query}")
        
        try:
            # Wikipedia API endpoint for searching
            search_url = "https://en.wikipedia.org/w/api.php"
            search_params = {
                "action": "query",
                "format": "json",
                "list": "search",
                "srsearch": query + " (artificial intelligence OR machine learning OR robotics)",
                "srlimit": 5
            }
            
            search_response = requests.get(search_url, params=search_params)
            
            if search_response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": f"Search API returned status code {search_response.status_code}",
                    "query": query
                }
            
            search_data = search_response.json()
            search_results = search_data.get("query", {}).get("search", [])
            
            results = []
            
            # Get detailed information for each search result
            for item in search_results:
                page_id = str(item.get("pageid"))
                
                # Get the page extract
                extract_params = {
                    "action": "query",
                    "format": "json",
                    "prop": "extracts|info|pageimages",
                    "exintro": 1,
                    "explaintext": 1,
                    "inprop": "url",
                    "pageids": page_id,
                    "piprop": "thumbnail",
                    "pithumbsize": 300
                }
                
                extract_response = requests.get(search_url, params=extract_params)
                
                if extract_response.status_code == 200:
                    extract_data = extract_response.json()
                    page_data = extract_data.get("query", {}).get("pages", {}).get(page_id, {})
                    
                    # Extract relevant information
                    title = page_data.get("title", "")
                    extract = page_data.get("extract", "")
                    url = page_data.get("canonicalurl", "")
                    
                    # Get thumbnail if available
                    thumbnail = page_data.get("thumbnail", {}).get("source", "") if "thumbnail" in page_data else ""
                    
                    article_data = {
                        "title": title,
                        "extract": extract,
                        "url": url,
                        "thumbnail": thumbnail
                    }
                    
                    results.append(article_data)
            
            return {
                "status": "success",
                "source": "Wikipedia",
                "query": query,
                "results_count": len(results),
                "results": results
            }
                
        except Exception as e:
            log_debug(f"Error searching Wikipedia for AI topics: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "query": query
            }