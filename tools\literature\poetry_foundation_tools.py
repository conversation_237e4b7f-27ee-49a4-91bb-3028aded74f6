from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import re

class PoetryFoundationTool(Toolkit):
    """
    Poetry Foundation Tool cho tìm kiếm thơ, tá<PERSON> giả, chủ đề từ Poetry Foundation.
    """

    def __init__(self):
        super().__init__(
            name="Poetry Foundation Search Tool",
            tools=[self.search_poetry_foundation]
        )

    async def search_poetry_foundation(self, query: str, type_: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm Poetry Foundation cho thơ, tác giả, chủ đề, thể loại.

        Parameters:
        - query: <PERSON><PERSON> kh<PERSON>a thơ, tác gi<PERSON>, chủ đề, thể loại (ví dụ: '<PERSON> Dickinson', 'love', 'haiku', 'nature')
        - type_: 'poem', 'author', 'article', 'audio' (nếu muốn lọc)
        - limit: <PERSON><PERSON> lượng kết qu<PERSON> tối đa (default: 5)

        Returns:
        - <PERSON><PERSON><PERSON> với ti<PERSON> đ<PERSON>, t<PERSON><PERSON>, t<PERSON><PERSON><PERSON>, url Poetry Foundation
        """
        logger.info(f"Tìm kiếm Poetry Foundation: query={query}, type={type_}, limit={limit}")

        try:
            base_url = "https://www.poetryfoundation.org"
            search_url = f"{base_url}/search"
            params = {
                "q": query
            }
            if type_:
                params["refinement"] = type_

            headers = {
                "User-Agent": "Mozilla/5.0 (compatible; PoetryFoundationBot/1.0)"
            }
            response = requests.get(search_url, params=params, headers=headers, timeout=10)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Poetry Foundation",
                    "message": f"Poetry Foundation search returned status code {response.status_code}",
                    "query": query
                }

            # Đơn giản: lấy các link kết quả đầu tiên (dùng regex)
            results = []
            # Mỗi kết quả nằm trong <div class="c-feature">...</div>
            for match in re.finditer(r'<div class="c-feature".*?>(.*?)</div>', response.text, re.DOTALL):
                if len(results) >= limit:
                    break
                block = match.group(1)
                # Lấy link và tiêu đề
                link_match = re.search(r'<a href="([^"]+)"[^>]*>(.*?)</a>', block)
                url = base_url + link_match.group(1) if link_match else None
                title = re.sub(r'<.*?>', '', link_match.group(2)).strip() if link_match else None
                # Lấy tác giả (nếu có)
                author_match = re.search(r'<span class="c-feature-sub">(.*?)</span>', block)
                author = re.sub(r'<.*?>', '', author_match.group(1)).strip() if author_match else None
                # Lấy trích đoạn (nếu có)
                excerpt_match = re.search(r'<div class="c-feature-excerpt">(.*?)</div>', block, re.DOTALL)
                excerpt = re.sub(r'<.*?>', '', excerpt_match.group(1)).strip() if excerpt_match else None
                results.append({
                    "title": title,
                    "author": author,
                    "excerpt": excerpt,
                    "poetryfoundation_url": url
                })

            return {
                "status": "success",
                "source": "Poetry Foundation",
                "query": query,
                "type": type_,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "Emily Dickinson",
                    "love poem",
                    "nature",
                    "haiku",
                    "Shakespeare sonnet",
                    "Vietnamese poetry",
                    "poet <name>",
                    "theme <topic>"
                ],
                "official_data_url": "https://www.poetryfoundation.org/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Poetry Foundation: {str(e)}")
            return {
                "status": "error",
                "source": "Poetry Foundation",
                "message": str(e),
                "query": query
            }
