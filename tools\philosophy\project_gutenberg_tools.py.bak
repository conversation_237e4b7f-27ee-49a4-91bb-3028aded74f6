from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class ProjectGutenbergPhilosophyTool(Toolkit):
    """
    Project Gutenberg Philosophy Tool cho tìm kiếm tác phẩm triết học kinh điển từ Project Gutenberg.
    """

    def __init__(self):
        super().__init__(
            name="Project Gutenberg Philosophy Search Tool",
            description="Tool cho tìm kiếm tác phẩm triết học kinh điển, tá<PERSON> gi<PERSON>, và bản dịch từ Project Gutenberg.",
            tools=[self.search_gutenberg_philosophy]
        )

    async def search_gutenberg_philosophy(self, query: str, author: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm Project Gutenberg cho tác phẩm triết học kinh điển.

        Parameters:
        - query: <PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON>, chủ đề, hoặc từ kh<PERSON><PERSON> triế<PERSON> họ<PERSON> (ví dụ: 'Thus Spoke Z<PERSON>thustra', '<PERSON>', 'ethics')
        - author: <PERSON><PERSON><PERSON><PERSON><PERSON> (ví dụ: '<PERSON><PERSON>z<PERSON>', '<PERSON>', 'Kant')
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với tiêu đề, tác giả, năm, mô tả, và link Project Gutenberg
        """
        logger.info(f"Tìm kiếm Project Gutenberg Philosophy: query={query}, author={author}")

        try:
            # Sử dụng API của Gutendex (Project Gutenberg open API)
            api_url = "https://gutendex.com/books"
            params = {
                "search": query,
                "languages": "en",
                "topic": "philosophy"
            }
            if author:
                params["author"] = author

            response = requests.get(api_url, params=params)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Project Gutenberg",
                    "message": f"Gutenberg API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for book in data.get("results", [])[:limit]:
                download_url = None
                for fmt, url in book.get("formats", {}).items():
                    if "text/html" in fmt and "utf-8" in fmt:
                        download_url = url
                        break
                    if "text/plain" in fmt and not download_url:
                        download_url = url
                results.append({
                    "title": book.get("title"),
                    "authors": [a.get("name") for a in book.get("authors", [])],
                    "subjects": book.get("subjects"),
                    "bookshelves": book.get("bookshelves"),
                    "languages": book.get("languages"),
                    "copyright": book.get("copyright"),
                    "download_count": book.get("download_count"),
                    "gutenberg_id": book.get("id"),
                    "gutenberg_url": f"https://www.gutenberg.org/ebooks/{book.get('id')}",
                    "download_url": download_url
                })

            return {
                "status": "success",
                "source": "Project Gutenberg",
                "query": query,
                "author": author,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "Plato",
                    "Nietzsche",
                    "Kant",
                    "ethics",
                    "existentialism",
                    "Thus Spoke Zarathustra",
                    "Meditations Marcus Aurelius",
                    "philosophy classic"
                ],
                "official_data_url": "https://www.gutenberg.org/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Project Gutenberg Philosophy: {str(e)}")
            return {
                "status": "error",
                "source": "Project Gutenberg",
                "message": str(e),
                "query": query
            }
