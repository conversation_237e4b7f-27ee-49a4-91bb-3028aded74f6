#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script cho tất cả get_top_new functions trong Music Art Tools
<PERSON> tra tất cả 5 tools đã đư<PERSON><PERSON> cường hóa với get_top_new functionality
"""

import sys
import os
import json
import random
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_spotify_get_top_new():
    """Test Spotify get_top_new functionality"""
    print("🎵 Testing Spotify get_top_new...")
    try:
        from tools.music_art.spotify_tools import SpotifyTool
        
        spotify = SpotifyTool()
        
        # Test different content types
        content_types = ["tracks", "albums", "artists", "playlists", "podcasts"]
        
        for content_type in content_types[:3]:  # Test first 3
            print(f"  - Testing {content_type}...")
            result = spotify.get_top_new(content_type, 5, "week", "pop")
            data = json.loads(result)
            assert data["status"] == "success"
            assert data["content_type"] == content_type
            assert len(data["results"]) == 5
            print(f"    ✅ {content_type} works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Spotify get_top_new failed: {str(e)}")
        return False

def test_pitchfork_get_top_new():
    """Test Pitchfork get_top_new functionality"""
    print("🎤 Testing Pitchfork get_top_new...")
    try:
        from tools.music_art.pitchfork_tools import PitchforkTool
        
        pitchfork = PitchforkTool()
        
        # Test different content types
        content_types = ["reviews", "features", "news", "best_new_music", "interviews"]
        
        for content_type in content_types[:3]:  # Test first 3
            print(f"  - Testing {content_type}...")
            result = pitchfork.get_top_new(content_type, 5, "week", "indie")
            data = json.loads(result)
            assert data["status"] == "success"
            assert data["content_type"] == content_type
            assert len(data["results"]) == 5
            print(f"    ✅ {content_type} works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Pitchfork get_top_new failed: {str(e)}")
        return False

def test_bandcamp_get_top_new():
    """Test Bandcamp get_top_new functionality"""
    print("🎼 Testing Bandcamp get_top_new...")
    try:
        from tools.music_art.bandcamp_tools import BandcampTool
        
        bandcamp = BandcampTool()
        
        # Test different content types
        content_types = ["music", "albums", "artists", "labels", "discoveries"]
        
        for content_type in content_types[:3]:  # Test first 3
            print(f"  - Testing {content_type}...")
            result = bandcamp.get_top_new(content_type, 5, "week", "indie")
            data = json.loads(result)
            assert data["status"] == "success"
            assert data["content_type"] == content_type
            assert len(data["results"]) == 5
            print(f"    ✅ {content_type} works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Bandcamp get_top_new failed: {str(e)}")
        return False

def test_artsy_get_top_new():
    """Test Artsy get_top_new functionality"""
    print("🎨 Testing Artsy get_top_new...")
    try:
        from tools.music_art.artsy_tools import ArtsyTool
        
        artsy = ArtsyTool()
        
        # Test different content types
        content_types = ["artworks", "artists", "exhibitions", "galleries", "fairs"]
        
        for content_type in content_types[:3]:  # Test first 3
            print(f"  - Testing {content_type}...")
            result = artsy.get_top_new(content_type, 5, "week", "contemporary")
            data = json.loads(result)
            assert data["status"] == "success"
            assert data["content_type"] == content_type
            assert len(data["results"]) == 5
            print(f"    ✅ {content_type} works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Artsy get_top_new failed: {str(e)}")
        return False

def test_deviantart_get_top_new():
    """Test DeviantArt get_top_new functionality"""
    print("🖼️ Testing DeviantArt get_top_new...")
    try:
        from tools.music_art.deviantart_tools import DeviantArtTool
        
        deviantart = DeviantArtTool()
        
        # Test different content types
        content_types = ["deviations", "artists", "groups", "journals", "collections"]
        
        for content_type in content_types[:3]:  # Test first 3
            print(f"  - Testing {content_type}...")
            result = deviantart.get_top_new(content_type, 5, "week", "digital")
            data = json.loads(result)
            assert data["status"] == "success"
            assert data["content_type"] == content_type
            assert len(data["results"]) == 5
            print(f"    ✅ {content_type} works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ DeviantArt get_top_new failed: {str(e)}")
        return False

def test_random_get_top_new_combinations():
    """Test random combinations of get_top_new functions"""
    print("\n🎲 Testing Random get_top_new Combinations...")
    
    try:
        from tools.music_art.spotify_tools import SpotifyTool
        from tools.music_art.bandcamp_tools import BandcampTool
        from tools.music_art.artsy_tools import ArtsyTool
        
        # Random Spotify test
        spotify = SpotifyTool()
        spotify_types = ["tracks", "albums", "artists"]
        spotify_type = random.choice(spotify_types)
        result = spotify.get_top_new(spotify_type, random.randint(3, 8), "week", "rock")
        data = json.loads(result)
        assert data["status"] == "success"
        print(f"  🎯 Random Spotify {spotify_type} test passed")
        
        # Random Bandcamp test
        bandcamp = BandcampTool()
        bandcamp_types = ["music", "albums", "artists"]
        bandcamp_type = random.choice(bandcamp_types)
        result = bandcamp.get_top_new(bandcamp_type, random.randint(3, 8), "month", "electronic")
        data = json.loads(result)
        assert data["status"] == "success"
        print(f"  🎯 Random Bandcamp {bandcamp_type} test passed")
        
        # Random Artsy test
        artsy = ArtsyTool()
        artsy_types = ["artworks", "artists", "exhibitions"]
        artsy_type = random.choice(artsy_types)
        result = artsy.get_top_new(artsy_type, random.randint(3, 8), "week", "painting")
        data = json.loads(result)
        assert data["status"] == "success"
        print(f"  🎯 Random Artsy {artsy_type} test passed")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Random combinations failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 GET_TOP_NEW FUNCTIONS TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing all get_top_new functions in Music Art Tools...")
    print()
    
    test_results = []
    
    # Test all get_top_new functions
    test_functions = [
        ("Spotify get_top_new", test_spotify_get_top_new),
        ("Pitchfork get_top_new", test_pitchfork_get_top_new),
        ("Bandcamp get_top_new", test_bandcamp_get_top_new),
        ("Artsy get_top_new", test_artsy_get_top_new),
        ("DeviantArt get_top_new", test_deviantart_get_top_new)
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()
    
    # Run random combination tests
    random_result = test_random_get_top_new_combinations()
    test_results.append(("Random Combinations", random_result))
    
    # Summary
    print("\n" + "="*60)
    print("📋 GET_TOP_NEW TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} get_top_new tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All get_top_new functions are working correctly!")
        print("✨ Music Art Tools fully enhanced with get_top_new functionality!")
    else:
        print("⚠️  Some get_top_new tests failed. Please check the error messages above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
