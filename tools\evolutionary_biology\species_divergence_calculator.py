# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math
from datetime import datetime

class SpeciesDivergenceCalculator(Toolkit):
    """
    Species Divergence Calculator cho tính toán species divergence, population genetics và speciation events.
    """

    def __init__(self, enable_calculations: bool = True, **kwargs):
        super().__init__(
            name="species_divergence_calculator",
            **kwargs
        )
        
        # Known divergence times (million years ago)
        self.known_divergences = {
            # Primates
            "human_chimp": 6.0,
            "human_gorilla": 8.0,
            "human_orangutan": 14.0,
            "human_macaque": 25.0,
            
            # Mammals
            "human_mouse": 95.0,
            "human_dog": 95.0,
            "human_cow": 95.0,
            "mouse_rat": 12.0,
            
            # Vertebrates
            "mammals_birds": 310.0,
            "mammals_reptiles": 310.0,
            "vertebrates_fish": 400.0,
            
            # Major groups
            "vertebrates_invertebrates": 550.0,
            "animals_plants": 1000.0,
            "eukaryotes_prokaryotes": 2000.0
        }
        
        # Population genetics parameters
        self.population_parameters = {
            "effective_population_size": {
                "human": 10000,
                "chimp": 20000,
                "mouse": 500000,
                "drosophila": 1000000,
                "bacteria": 1000000000
            },
            "generation_time": {
                "human": 25,
                "chimp": 20,
                "mouse": 0.25,
                "drosophila": 0.05,
                "bacteria": 0.01
            }
        }
        
        # Speciation mechanisms
        self.speciation_types = {
            "allopatric": {"frequency": 0.6, "time_scale": "slow"},
            "sympatric": {"frequency": 0.2, "time_scale": "fast"},
            "parapatric": {"frequency": 0.15, "time_scale": "medium"},
            "peripatric": {"frequency": 0.05, "time_scale": "fast"}
        }
        
        if enable_calculations:
            self.register(self.calculate_species_divergence)
            self.register(self.estimate_population_genetics)
            self.register(self.analyze_speciation_events)
            self.register(self.predict_evolutionary_trends)

    def calculate_species_divergence(self, species1: str, species2: str, 
                                   genetic_distance: float = None, 
                                   molecular_data: str = "nuclear_dna") -> str:
        """
        Tính toán species divergence time và evolutionary distance.
        
        Args:
            species1: Loài thứ nhất
            species2: Loài thứ hai
            genetic_distance: Khoảng cách di truyền (nếu biết)
            molecular_data: Loại dữ liệu phân tử
            
        Returns:
            Chuỗi JSON chứa tính toán species divergence
        """
        log_debug(f"Calculating species divergence between {species1} and {species2}")
        
        try:
            # Check for known divergence
            pair_key = f"{species1.lower()}_{species2.lower()}"
            reverse_key = f"{species2.lower()}_{species1.lower()}"
            
            known_time = None
            if pair_key in self.known_divergences:
                known_time = self.known_divergences[pair_key]
            elif reverse_key in self.known_divergences:
                known_time = self.known_divergences[reverse_key]
            
            # Estimate genetic distance if not provided
            if genetic_distance is None:
                if known_time:
                    # Estimate based on known time
                    molecular_rates = {
                        "nuclear_dna": 2.3e-9,
                        "mitochondrial_dna": 2.0e-8,
                        "protein": 1.0e-9
                    }
                    rate = molecular_rates.get(molecular_data, 2.3e-9)
                    genetic_distance = known_time * rate * 1e6 * 2  # 2 for both lineages
                else:
                    # Default estimate based on taxonomic distance
                    genetic_distance = 0.1  # 10% divergence
            
            # Calculate divergence time if not known
            if not known_time:
                molecular_rates = {
                    "nuclear_dna": 2.3e-9,
                    "mitochondrial_dna": 2.0e-8,
                    "protein": 1.0e-9
                }
                rate = molecular_rates.get(molecular_data, 2.3e-9)
                estimated_time = genetic_distance / (2 * rate * 1e6)
            else:
                estimated_time = known_time
            
            # Calculate evolutionary statistics
            substitutions_per_site = genetic_distance
            synonymous_substitutions = substitutions_per_site * 0.7  # ~70% synonymous
            nonsynonymous_substitutions = substitutions_per_site * 0.3  # ~30% nonsynonymous
            
            # Selection analysis
            dn_ds_ratio = nonsynonymous_substitutions / max(synonymous_substitutions, 0.001)
            
            if dn_ds_ratio > 1:
                selection_type = "Positive selection"
            elif dn_ds_ratio < 0.3:
                selection_type = "Strong purifying selection"
            else:
                selection_type = "Neutral evolution"
            
            # Confidence intervals
            lower_bound = estimated_time * 0.7
            upper_bound = estimated_time * 1.3
            
            # Evolutionary context
            evolutionary_context = self._get_evolutionary_context(estimated_time)
            
            result = {
                "species_pair": {
                    "species1": species1,
                    "species2": species2,
                    "comparison_type": "pairwise_divergence"
                },
                "divergence_analysis": {
                    "estimated_time_mya": round(estimated_time, 2),
                    "confidence_interval": f"{lower_bound:.1f}-{upper_bound:.1f} MYA",
                    "data_source": "known_divergence" if known_time else "molecular_clock",
                    "molecular_data_type": molecular_data
                },
                "genetic_distance_analysis": {
                    "total_genetic_distance": round(genetic_distance, 4),
                    "substitutions_per_site": round(substitutions_per_site, 4),
                    "synonymous_substitutions": round(synonymous_substitutions, 4),
                    "nonsynonymous_substitutions": round(nonsynonymous_substitutions, 4),
                    "dn_ds_ratio": round(dn_ds_ratio, 3)
                },
                "selection_analysis": {
                    "selection_type": selection_type,
                    "selection_strength": "Strong" if abs(dn_ds_ratio - 1) > 0.5 else "Moderate",
                    "evolutionary_constraint": "High" if dn_ds_ratio < 0.3 else "Low"
                },
                "evolutionary_context": evolutionary_context,
                "geological_period": self._get_geological_period(estimated_time),
                "speciation_factors": self._get_speciation_factors(estimated_time),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error calculating species divergence: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate species divergence: {str(e)}"
            }, indent=4)

    def estimate_population_genetics(self, species: str, population_size: int = None, 
                                   mutation_rate: float = None) -> str:
        """
        Ước tính population genetics parameters.
        
        Args:
            species: Tên loài
            population_size: Kích thước quần thể hiệu quả
            mutation_rate: Tỷ lệ mutation
            
        Returns:
            Chuỗi JSON chứa ước tính population genetics
        """
        log_debug(f"Estimating population genetics for {species}")
        
        try:
            # Get or estimate population parameters
            species_key = species.lower()
            
            if population_size is None:
                population_size = self.population_parameters["effective_population_size"].get(species_key, 100000)
            
            if mutation_rate is None:
                mutation_rates = {
                    "human": 1e-8,
                    "chimp": 1e-8,
                    "mouse": 5e-10,
                    "drosophila": 3e-9,
                    "bacteria": 1e-10
                }
                mutation_rate = mutation_rates.get(species_key, 1e-9)
            
            # Calculate population genetics statistics
            theta = 4 * population_size * mutation_rate  # Expected heterozygosity
            coalescence_time = 2 * population_size  # Expected coalescence time in generations
            
            # Genetic drift effects
            drift_strength = 1 / (2 * population_size)
            fixation_probability_neutral = 1 / (2 * population_size)
            
            # Selection effects
            selection_coefficient = 0.01  # Assume 1% selection advantage
            fixation_probability_beneficial = (1 - math.exp(-2 * selection_coefficient)) / (1 - math.exp(-4 * population_size * selection_coefficient))
            
            # Time to fixation
            time_to_fixation_neutral = 4 * population_size  # generations
            time_to_fixation_beneficial = (2 / selection_coefficient) * math.log(2 * population_size)
            
            # Demographic history
            demographic_events = self._simulate_demographic_history(species, population_size)
            
            # Genetic diversity
            genetic_diversity = {
                "nucleotide_diversity": theta / 4,  # π
                "watterson_theta": theta,
                "tajima_d": 0.0,  # Neutral expectation
                "expected_heterozygosity": theta / (theta + 1)
            }
            
            result = {
                "species": species,
                "population_parameters": {
                    "effective_population_size": population_size,
                    "mutation_rate": mutation_rate,
                    "theta": round(theta, 6),
                    "coalescence_time_generations": coalescence_time
                },
                "genetic_drift": {
                    "drift_strength": drift_strength,
                    "fixation_probability_neutral": fixation_probability_neutral,
                    "variance_in_allele_frequency": drift_strength / 2
                },
                "selection_dynamics": {
                    "selection_coefficient": selection_coefficient,
                    "fixation_probability_beneficial": round(fixation_probability_beneficial, 4),
                    "time_to_fixation_neutral": time_to_fixation_neutral,
                    "time_to_fixation_beneficial": round(time_to_fixation_beneficial, 1)
                },
                "genetic_diversity": genetic_diversity,
                "demographic_history": demographic_events,
                "evolutionary_implications": [
                    "Rate of adaptive evolution",
                    "Accumulation of deleterious mutations",
                    "Response to environmental change",
                    "Speciation potential"
                ],
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error estimating population genetics: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to estimate population genetics: {str(e)}"
            }, indent=4)

    def analyze_speciation_events(self, ancestral_species: str, descendant_species: List[str], 
                                speciation_type: str = "allopatric") -> str:
        """
        Phân tích speciation events và mechanisms.
        
        Args:
            ancestral_species: Loài tổ tiên
            descendant_species: Danh sách loài con cháu
            speciation_type: Loại speciation
            
        Returns:
            Chuỗi JSON chứa phân tích speciation events
        """
        log_debug(f"Analyzing speciation events from {ancestral_species}")
        
        try:
            # Speciation analysis
            num_species = len(descendant_species)
            speciation_info = self.speciation_types.get(speciation_type, self.speciation_types["allopatric"])
            
            # Estimate speciation timeline
            if speciation_info["time_scale"] == "fast":
                speciation_time = 0.5  # 0.5 MYA
            elif speciation_info["time_scale"] == "medium":
                speciation_time = 2.0  # 2 MYA
            else:
                speciation_time = 5.0  # 5 MYA
            
            # Diversification rate
            diversification_rate = math.log(num_species) / speciation_time if speciation_time > 0 else 0
            
            # Speciation mechanisms
            mechanisms = {
                "allopatric": {
                    "description": "Geographic isolation",
                    "factors": ["Mountain formation", "Sea level changes", "Continental drift"],
                    "genetic_basis": "Accumulation of incompatibilities"
                },
                "sympatric": {
                    "description": "Reproductive isolation without geographic separation",
                    "factors": ["Polyploidy", "Sexual selection", "Ecological specialization"],
                    "genetic_basis": "Chromosomal rearrangements"
                },
                "parapatric": {
                    "description": "Partial geographic overlap",
                    "factors": ["Environmental gradients", "Limited gene flow"],
                    "genetic_basis": "Local adaptation"
                },
                "peripatric": {
                    "description": "Small isolated populations",
                    "factors": ["Founder effects", "Genetic drift"],
                    "genetic_basis": "Random fixation"
                }
            }
            
            mechanism_details = mechanisms.get(speciation_type, mechanisms["allopatric"])
            
            # Reproductive isolation
            isolation_mechanisms = [
                "Prezygotic isolation",
                "Postzygotic isolation",
                "Behavioral isolation",
                "Temporal isolation",
                "Mechanical isolation"
            ]
            
            # Adaptive radiation analysis
            adaptive_radiation = {
                "is_adaptive_radiation": num_species > 5,
                "ecological_opportunity": "Available niches" if num_species > 5 else "Limited niches",
                "key_innovations": "Morphological/physiological adaptations",
                "diversification_pattern": "Rapid" if diversification_rate > 1 else "Gradual"
            }
            
            result = {
                "speciation_analysis": {
                    "ancestral_species": ancestral_species,
                    "descendant_species": descendant_species,
                    "number_of_species": num_species,
                    "speciation_type": speciation_type,
                    "estimated_time_mya": speciation_time
                },
                "diversification_metrics": {
                    "diversification_rate": round(diversification_rate, 3),
                    "speciation_frequency": speciation_info["frequency"],
                    "time_scale": speciation_info["time_scale"]
                },
                "speciation_mechanism": mechanism_details,
                "reproductive_isolation": {
                    "isolation_mechanisms": isolation_mechanisms,
                    "strength": "Complete" if speciation_time > 1 else "Partial",
                    "reversibility": "Irreversible" if speciation_time > 2 else "Potentially reversible"
                },
                "adaptive_radiation": adaptive_radiation,
                "evolutionary_factors": [
                    "Geographic barriers",
                    "Ecological opportunities",
                    "Genetic incompatibilities",
                    "Sexual selection"
                ],
                "conservation_implications": self._get_conservation_implications(num_species, speciation_type),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error analyzing speciation events: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze speciation events: {str(e)}"
            }, indent=4)

    def predict_evolutionary_trends(self, species: str, timeframe_years: int = 1000000, 
                                  environmental_change: str = "climate_change") -> str:
        """
        Dự đoán evolutionary trends trong tương lai.
        
        Args:
            species: Tên loài
            timeframe_years: Khung thời gian dự đoán (years)
            environmental_change: Loại thay đổi môi trường
            
        Returns:
            Chuỗi JSON chứa dự đoán evolutionary trends
        """
        log_debug(f"Predicting evolutionary trends for {species} over {timeframe_years} years")
        
        try:
            # Convert timeframe to generations
            generation_time = self.population_parameters["generation_time"].get(species.lower(), 25)
            generations = int(timeframe_years / generation_time)
            
            # Environmental pressure analysis
            environmental_pressures = {
                "climate_change": {
                    "selection_strength": 0.05,
                    "affected_traits": ["Temperature tolerance", "Metabolic rate", "Body size"],
                    "adaptation_potential": "High"
                },
                "habitat_loss": {
                    "selection_strength": 0.1,
                    "affected_traits": ["Dispersal ability", "Habitat flexibility", "Population size"],
                    "adaptation_potential": "Medium"
                },
                "pollution": {
                    "selection_strength": 0.08,
                    "affected_traits": ["Detoxification", "Immune response", "Reproductive success"],
                    "adaptation_potential": "Medium"
                },
                "disease": {
                    "selection_strength": 0.15,
                    "affected_traits": ["Immune system", "Resistance genes", "Behavioral avoidance"],
                    "adaptation_potential": "High"
                }
            }
            
            pressure_info = environmental_pressures.get(environmental_change, environmental_pressures["climate_change"])
            
            # Evolutionary response prediction
            selection_strength = pressure_info["selection_strength"]
            response_rate = selection_strength * 0.5  # Heritability factor
            
            # Genetic changes
            allele_frequency_change = selection_strength * generations * 0.001
            new_mutations = generations * 1e-8 * 3e9  # Assuming human-like genome
            
            # Phenotypic evolution
            trait_evolution = {
                "magnitude": f"{response_rate * 100:.1f}% change per generation",
                "direction": "Adaptive response to environmental pressure",
                "constraints": "Genetic correlations and trade-offs"
            }
            
            # Population dynamics
            population_effects = {
                "size_change": "Decline" if selection_strength > 0.1 else "Stable",
                "genetic_diversity": "Reduced" if selection_strength > 0.08 else "Maintained",
                "extinction_risk": "Elevated" if selection_strength > 0.12 else "Low"
            }
            
            # Speciation potential
            speciation_probability = min(0.9, generations / 1000000)  # Higher with more generations
            
            result = {
                "prediction_parameters": {
                    "species": species,
                    "timeframe_years": timeframe_years,
                    "generations": generations,
                    "environmental_change": environmental_change,
                    "generation_time": generation_time
                },
                "environmental_pressure": pressure_info,
                "evolutionary_response": {
                    "selection_strength": selection_strength,
                    "response_rate": round(response_rate, 4),
                    "allele_frequency_change": round(allele_frequency_change, 4),
                    "new_mutations_expected": int(new_mutations)
                },
                "phenotypic_evolution": trait_evolution,
                "population_dynamics": population_effects,
                "speciation_potential": {
                    "probability": round(speciation_probability, 3),
                    "likelihood": "High" if speciation_probability > 0.5 else "Low",
                    "timeframe": "Within prediction period" if speciation_probability > 0.3 else "Beyond prediction period"
                },
                "uncertainty_factors": [
                    "Environmental unpredictability",
                    "Genetic constraints",
                    "Population bottlenecks",
                    "Species interactions"
                ],
                "conservation_recommendations": self._get_conservation_recommendations(species, pressure_info),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error predicting evolutionary trends: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to predict evolutionary trends: {str(e)}"
            }, indent=4)

    # Helper methods
    def _get_evolutionary_context(self, time_mya: float) -> str:
        """Get evolutionary context for divergence time."""
        if time_mya < 1:
            return "Recent divergence - subspecies or population level"
        elif time_mya < 10:
            return "Species-level divergence - recent speciation"
        elif time_mya < 50:
            return "Genus-level divergence - adaptive radiation"
        elif time_mya < 200:
            return "Family-level divergence - major evolutionary transitions"
        else:
            return "Deep evolutionary divergence - ancient lineages"

    def _get_geological_period(self, time_mya: float) -> str:
        """Get geological period for given time."""
        if time_mya < 2.6:
            return "Quaternary"
        elif time_mya < 23:
            return "Neogene"
        elif time_mya < 66:
            return "Paleogene"
        elif time_mya < 145:
            return "Cretaceous"
        elif time_mya < 252:
            return "Mesozoic"
        else:
            return "Paleozoic or earlier"

    def _get_speciation_factors(self, time_mya: float) -> List[str]:
        """Get relevant speciation factors for time period."""
        factors = ["Genetic drift", "Natural selection"]
        if time_mya > 50:
            factors.extend(["Continental drift", "Climate change"])
        if time_mya > 5:
            factors.append("Geographic isolation")
        if time_mya < 10:
            factors.extend(["Sexual selection", "Ecological specialization"])
        return factors

    def _simulate_demographic_history(self, species: str, current_size: int) -> List[Dict]:
        """Simulate demographic history events."""
        return [
            {
                "event": "Population expansion",
                "time_ago": "10,000 years",
                "size_change": "10x increase",
                "cause": "Post-glacial expansion"
            },
            {
                "event": "Bottleneck",
                "time_ago": "70,000 years",
                "size_change": "90% reduction",
                "cause": "Environmental catastrophe"
            }
        ]

    def _get_conservation_implications(self, num_species: int, speciation_type: str) -> List[str]:
        """Get conservation implications."""
        implications = ["Monitor genetic diversity", "Protect habitat connectivity"]
        if num_species > 10:
            implications.append("Prioritize hotspot conservation")
        if speciation_type == "allopatric":
            implications.append("Maintain geographic barriers")
        return implications

    def _get_conservation_recommendations(self, species: str, pressure_info: Dict) -> List[str]:
        """Get conservation recommendations."""
        recommendations = ["Monitor population trends", "Assess genetic diversity"]
        if pressure_info["adaptation_potential"] == "Low":
            recommendations.append("Consider assisted migration")
        recommendations.append("Reduce environmental stressors")
        return recommendations
