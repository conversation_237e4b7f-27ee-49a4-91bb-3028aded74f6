# -*- coding: utf-8 -*-
from typing import List, Dict, Any
import json
from agno.tools import Toolkit
from agno.utils.log import logger

class ArtHistorySearchToolkit(Toolkit):
    """A custom Toolkit for generating search keywords for art history databases.

    This toolkit provides functions to generate search keywords for Europeana,
    Google Arts & Culture, Wikipedia, Metropolitan Museum, and Rijksmuseum,
    tailored for art history research.
    """

    # == Detailed Instructions for the Agent ==
    instruction = [
        "Bạn là một trợ lý nghiên cứu lịch sử nghệ thuật, chuyên cung cấp từ khóa tìm kiếm tối ưu cho các cơ sở dữ liệu nghệ thuật.",
        "<PERSON>hi sử dụng các công cụ trong ArtHistorySearchToolkit, tuân thủ các định dạng từ khóa được chỉ định như sau:",
        "- Europeana: Sử dụng định dạng 'artist period' hoặc 'artwork type' (ví dụ: 'Van Gogh Post-Impressionism', 'Renaissance painting').",
        "- Google Arts & Culture: Sử dụng định dạng 'artwork OR artist OR movement' (ví dụ: 'Starry Night OR Van Gogh OR Post-Impressionism').",
        "- Wikipedia Art: Sử dụng định dạng 'art topic' hoặc 'artist biography' (ví dụ: 'Renaissance painting', 'Leonardo da Vinci').",
        "- Metropolitan Museum: Sử dụng định dạng 'culture medium period' (ví dụ: 'European painting 19th century', 'American sculpture').",
        "- Rijksmuseum: Sử dụng định dạng 'Dutch artist period' (ví dụ: 'Rembrandt Golden Age', 'Vermeer 17th century').",
        "Ngoài ra, toolkit cũng hỗ trợ tạo từ khóa cho việc tìm kiếm nội dung mới nhất và phổ biến:",
        "- Europeana Recent: Tạo từ khóa cho artworks và collections mới theo theme.",
        "- Google Arts Trending: Tạo từ khóa cho exhibitions và artists trending.",
        "- Wikipedia Art Recent: Tạo từ khóa cho bài viết nghệ thuật mới được tạo hoặc cập nhật.",
        "- Museum Recent: Tạo từ khóa cho acquisitions và highlighted works mới theo department.",
        "- Art Discovery: Tạo từ khóa cho khám phá nghệ thuật theo theme và period.",
        "Kiểm tra tính hợp lệ của tham số đầu vào và trả về từ khóa phù hợp với từng cơ sở dữ liệu.",
        "Trả về kết quả dưới dạng JSON với trạng thái ('status'), danh sách từ khóa ('keywords'), và thông báo ('message').",
        "Nếu có lỗi, trả về trạng thái 'error' với mô tả lỗi chi tiết."
    ]

    # == Detailed Few-Shot Examples ==
    few_shot_examples = [
        {
            "user": "Tìm thông tin về Van Gogh và Post-Impressionism.",
            "tool_calls": [
                {
                    "name": "generate_europeana_keywords",
                    "arguments": {"artist": "Van Gogh", "period": "Post-Impressionism", "type": "painting"}
                },
                {
                    "name": "generate_google_arts_keywords",
                    "arguments": {"query": "Van Gogh", "type": "artist"}
                },
                {
                    "name": "generate_wikipedia_art_keywords",
                    "arguments": {"concept": "Van Gogh", "category": "artist"}
                }
            ]
        },
        {
            "user": "Tìm recent exhibitions và trending artists.",
            "tool_calls": [
                {
                    "name": "generate_google_arts_trending_keywords",
                    "arguments": {"type": "exhibitions", "period": "month"}
                },
                {
                    "name": "generate_europeana_recent_keywords",
                    "arguments": {"theme": "contemporary art", "days_back": 30}
                },
                {
                    "name": "generate_museum_recent_keywords",
                    "arguments": {"museum": "met", "department": "European Paintings"}
                }
            ]
        },
        {
            "user": "Tìm nghiên cứu về Dutch Golden Age art.",
            "tool_calls": [
                {
                    "name": "generate_rijksmuseum_keywords",
                    "arguments": {"artist": "Rembrandt", "type": "painting", "period": "Golden Age"}
                },
                {
                    "name": "generate_wikipedia_art_keywords",
                    "arguments": {"concept": "Dutch Golden Age painting", "category": "movement"}
                },
                {
                    "name": "generate_art_discovery_keywords",
                    "arguments": {"theme": "Dutch art", "period": "17th century"}
                }
            ]
        }
    ]

    def __init__(self):
        """Initializes the ArtHistorySearchToolkit."""
        super().__init__(
            name="art_history_search_toolkit",
            tools=[
                self.generate_europeana_keywords,
                self.generate_google_arts_keywords,
                self.generate_wikipedia_art_keywords,
                self.generate_met_museum_keywords,
                self.generate_rijksmuseum_keywords,
                self.generate_europeana_recent_keywords,
                self.generate_google_arts_trending_keywords,
                self.generate_wikipedia_art_recent_keywords,
                self.generate_museum_recent_keywords,
                self.generate_art_discovery_keywords
            ],
            instructions=self.instruction
        )
        self.few_shot_examples = self.few_shot_examples
        logger.info("ArtHistorySearchToolkit initialized.")

    def generate_europeana_keywords(self, artist: str, period: str = None, type: str = None) -> str:
        """Generates search keywords for Europeana.

        Args:
            artist: The artist name (e.g., 'Van Gogh', 'Picasso').
            period: Optional art period (e.g., 'Post-Impressionism', 'Cubism').
            type: Optional artwork type (e.g., 'painting', 'sculpture').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating Europeana keywords for artist: '{artist}', period: '{period}', type: '{type}'")
        try:
            if not artist.strip():
                raise ValueError("Artist cannot be empty.")

            # Tạo từ khóa cho Europeana
            keywords = [artist]
            if period:
                keywords.append(f"{artist} {period}")
            if type:
                keywords.append(f"{artist} {type}")
                if period:
                    keywords.append(f"{period} {type}")

            # Thêm từ khóa mở rộng
            keywords.extend([f"{artist} art", f"{artist} works"])
            if period:
                keywords.append(f"{period} movement")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Europeana keywords for artist '{artist}', period '{period or 'all'}', type '{type or 'all'}'.",
                "search_type": "european_artworks",
                "parameters": {
                    "artist": artist,
                    "period": period,
                    "type": type
                }
            }
            logger.debug(f"Europeana keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating Europeana keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate Europeana keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_google_arts_keywords(self, query: str, type: str = None) -> str:
        """Generates search keywords for Google Arts & Culture."""
        logger.info(f"Generating Google Arts keywords for query: '{query}', type: '{type}'")
        try:
            if not query.strip():
                raise ValueError("Query cannot be empty.")

            keywords = [query]
            if type == "artist":
                keywords.extend([f"{query} artist", f"{query} biography", f"{query} works"])
            elif type == "artwork":
                keywords.extend([f"{query} painting", f"{query} artwork", f"{query} analysis"])
            elif type == "movement":
                keywords.extend([f"{query} movement", f"{query} style", f"{query} period"])
            else:
                keywords.extend([f"{query} art", f"{query} museum", f"{query} exhibition"])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Google Arts keywords for query '{query}' and type '{type or 'general'}'.",
                "search_type": "arts_culture",
                "parameters": {"query": query, "type": type}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Google Arts keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_art_keywords(self, concept: str, category: str = None) -> str:
        """Generates search keywords for Wikipedia art topics."""
        logger.info(f"Generating Wikipedia art keywords for concept: '{concept}', category: '{category}'")
        try:
            if not concept.strip():
                raise ValueError("Concept cannot be empty.")

            keywords = [concept]
            if category == "artist":
                keywords.extend([f"{concept} biography", f"{concept} works", f"{concept} style"])
            elif category == "movement":
                keywords.extend([f"{concept} movement", f"{concept} artists", f"{concept} characteristics"])
            elif category == "artwork":
                keywords.extend([f"{concept} analysis", f"{concept} history", f"{concept} interpretation"])
            else:
                keywords.extend([f"{concept} art", f"{concept} history", f"{concept} culture"])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia art keywords for concept '{concept}' and category '{category or 'general'}'.",
                "search_type": "encyclopedia_articles",
                "parameters": {"concept": concept, "category": category}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia art keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_met_museum_keywords(self, culture: str, medium: str = None, period: str = None) -> str:
        """Generates search keywords for Metropolitan Museum."""
        logger.info(f"Generating Met Museum keywords for culture: '{culture}', medium: '{medium}', period: '{period}'")
        try:
            if not culture.strip():
                raise ValueError("Culture cannot be empty.")

            keywords = [culture]
            if medium:
                keywords.append(f"{culture} {medium}")
            if period:
                keywords.append(f"{culture} {period}")
                if medium:
                    keywords.append(f"{culture} {medium} {period}")

            keywords.extend([f"{culture} art", f"{culture} collection"])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Met Museum keywords for culture '{culture}', medium '{medium or 'all'}', period '{period or 'all'}'.",
                "search_type": "museum_collection",
                "parameters": {"culture": culture, "medium": medium, "period": period}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Met Museum keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_rijksmuseum_keywords(self, artist: str = None, type: str = None, period: str = None) -> str:
        """Generates search keywords for Rijksmuseum."""
        logger.info(f"Generating Rijksmuseum keywords for artist: '{artist}', type: '{type}', period: '{period}'")
        try:
            keywords = []
            if artist:
                keywords.append(artist)
                if period:
                    keywords.append(f"{artist} {period}")
                if type:
                    keywords.append(f"{artist} {type}")

            if period:
                keywords.append(f"Dutch {period}")
            if type:
                keywords.append(f"Dutch {type}")

            keywords.extend(["Dutch art", "Golden Age", "Netherlands"])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Rijksmuseum keywords for artist '{artist or 'all'}', type '{type or 'all'}', period '{period or 'all'}'.",
                "search_type": "dutch_art",
                "parameters": {"artist": artist, "type": type, "period": period}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Rijksmuseum keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    # == Recent/Trending Keywords Functions ==

    def generate_europeana_recent_keywords(self, theme: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent artworks on Europeana."""
        logger.info(f"Generating Europeana recent keywords for theme: '{theme}', days_back: {days_back}")
        try:
            keywords = ["recent artworks", "new collections", "latest additions"]
            if theme:
                keywords.extend([f"recent {theme}", f"{theme} new", f"latest {theme}"])
            keywords.extend(["contemporary art", "modern collections", f"last {days_back} days"])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Europeana recent keywords for theme '{theme or 'all'}' within last {days_back} days.",
                "search_type": "recent_artworks",
                "parameters": {"theme": theme, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Europeana recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_google_arts_trending_keywords(self, type: str = "exhibitions", period: str = "month") -> str:
        """Generates keywords for trending content on Google Arts & Culture."""
        logger.info(f"Generating Google Arts trending keywords for type: '{type}', period: '{period}'")
        try:
            keywords = [f"trending {type}", f"popular {type}", f"featured {type}"]
            if type == "exhibitions":
                keywords.extend(["current exhibitions", "new exhibitions", "virtual tours"])
            elif type == "artists":
                keywords.extend(["popular artists", "featured artists", "artist spotlights"])
            elif type == "artworks":
                keywords.extend(["featured artworks", "popular paintings", "masterpieces"])

            keywords.append(f"last {period}")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Google Arts trending keywords for {type} within last {period}.",
                "search_type": "trending_content",
                "parameters": {"type": type, "period": period}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Google Arts trending keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_art_recent_keywords(self, days_back: int = 30, language: str = "en") -> str:
        """Generates keywords for recent art articles on Wikipedia."""
        logger.info(f"Generating Wikipedia art recent keywords for days_back: {days_back}, language: '{language}'")
        try:
            keywords = [
                "recent art articles", "new artist biographies", "latest art movements",
                "recent art history", "new museum articles", "latest exhibition articles",
                f"last {days_back} days art", "recent art discoveries"
            ]

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia art recent keywords within last {days_back} days for language '{language}'.",
                "search_type": "recent_articles",
                "parameters": {"days_back": days_back, "language": language}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia art recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_museum_recent_keywords(self, museum: str, department: str = None) -> str:
        """Generates keywords for recent museum acquisitions and highlights."""
        logger.info(f"Generating museum recent keywords for museum: '{museum}', department: '{department}'")
        try:
            keywords = ["recent acquisitions", "new additions", "latest highlights"]
            if department:
                keywords.extend([f"recent {department}", f"{department} acquisitions"])

            if museum.lower() == "met":
                keywords.extend(["Met Museum new", "Metropolitan acquisitions"])
            elif museum.lower() == "rijks":
                keywords.extend(["Rijksmuseum new", "Dutch art acquisitions"])

            keywords.extend([f"new {museum} collection", f"recent {museum} artworks"])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated museum recent keywords for {museum} museum, department '{department or 'all'}'.",
                "search_type": "recent_acquisitions",
                "parameters": {"museum": museum, "department": department}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate museum recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_art_discovery_keywords(self, theme: str, period: str = None) -> str:
        """Generates keywords for art discovery and exploration."""
        logger.info(f"Generating art discovery keywords for theme: '{theme}', period: '{period}'")
        try:
            keywords = [theme, f"{theme} art", f"{theme} artists"]
            if period:
                keywords.extend([f"{theme} {period}", f"{period} {theme}", f"{period} art"])

            keywords.extend([
                f"{theme} masterpieces", f"{theme} collections", f"{theme} movements",
                f"explore {theme}", f"discover {theme}", f"{theme} history"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated art discovery keywords for theme '{theme}' and period '{period or 'all'}'.",
                "search_type": "art_discovery",
                "parameters": {"theme": theme, "period": period}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate art discovery keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)