from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime

class WikiaApiTools(Toolkit):
    """
    Wikia API Tools cho tìm kiếm nội dung structured từ các fandom/game wikis sử dụng Wikia API.
    """

    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(
            name="wikia_api_tools",
            **kwargs
        )

        if enable_search:
            self.register(self.search_wikia_api)
            self.register(self.get_top_new_pages)
            self.register(self.get_page_content)
            self.register(self.search_categories)
            self.register(self.get_wiki_info)

    def search_wikia_api(self, query: str, wiki: str, limit: int = 5) -> str:
        """
        Tìm kiếm nội dung structured từ Wikia API.

        Args:
        - query: Từ khóa tìm kiếm (ví dụ: 'Dark Souls boss', 'StarCraft Zerg', 'Elden Ring Ranni')
        - wiki: Tên wiki (subdomain, ví dụ: 'elderscrolls', 'starcraft', 'darksouls', 'halo', 'marvel')
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON string với tiêu đề, snippet, url, thumbnail, và các trường structured khác nếu có
        """
        log_debug(f"Tìm kiếm Wikia API: query={query}, wiki={wiki}, limit={limit}")

        try:
            # API endpoint cho MediaWiki (Wikia/Fandom)
            api_url = f"https://{wiki}.fandom.com/api.php"
            params = {
                "action": "query",
                "list": "search",
                "srsearch": query,
                "format": "json",
                "srlimit": limit
            }
            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "source": "Wikia API",
                    "message": f"Wikia API trả về mã lỗi {response.status_code}",
                    "query": query,
                    "wiki": wiki
                }, indent=2)

            data = response.json()
            search_results = data.get("query", {}).get("search", [])
            results = []
            for item in search_results:
                title = item.get("title")
                snippet = item.get("snippet", "").replace("<span class=\"searchmatch\">", "").replace("</span>", "")
                pageid = item.get("pageid")
                url = f"https://{wiki}.fandom.com/wiki/{title.replace(' ', '_')}" if title else None

                # Lấy thumbnail và thông tin bổ sung
                additional_info = self._get_page_additional_info(wiki, title)

                results.append({
                    "title": title,
                    "snippet": snippet,
                    "pageid": pageid,
                    "url": url,
                    "thumbnail": additional_info.get("thumbnail"),
                    "page_info": additional_info
                })

            return json.dumps({
                "status": "success",
                "source": "Wikia API",
                "query": query,
                "wiki": wiki,
                "results_count": len(results),
                "results": results,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Wikia API: {str(e)}")
            return json.dumps({
                "status": "error",
                "source": "Wikia API",
                "message": str(e),
                "query": query,
                "wiki": wiki
            }, indent=2)

    def get_top_new_pages(self, wiki: str, limit: int = 10, namespace: int = 0) -> str:
        """
        Lấy các trang mới nhất từ wiki.

        Args:
        - wiki: Tên wiki
        - limit: Số lượng trang
        - namespace: Namespace (0 = main, 14 = category)

        Returns:
        - JSON string với danh sách trang mới nhất
        """
        log_debug(f"Lấy top new pages từ {wiki}")

        try:
            api_url = f"https://{wiki}.fandom.com/api.php"
            params = {
                "action": "query",
                "list": "recentchanges",
                "format": "json",
                "rclimit": limit,
                "rctype": "new",
                "rcnamespace": namespace
            }

            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to get new pages: {response.status_code}"
                }, indent=2)

            data = response.json()
            changes = data.get("query", {}).get("recentchanges", [])

            pages = []
            for change in changes:
                title = change.get("title")
                timestamp = change.get("timestamp")
                user = change.get("user")

                pages.append({
                    "title": title,
                    "url": f"https://{wiki}.fandom.com/wiki/{title.replace(' ', '_')}",
                    "created_date": timestamp,
                    "created_by": user,
                    "page_id": change.get("pageid")
                })

            return json.dumps({
                "status": "success",
                "wiki": wiki,
                "namespace": namespace,
                "results_count": len(pages),
                "top_new_pages": pages,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy top new pages: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def get_page_content(self, wiki: str, page_title: str, section: str = None) -> str:
        """
        Lấy nội dung của một trang cụ thể.

        Args:
        - wiki: Tên wiki
        - page_title: Tiêu đề trang
        - section: Section cụ thể (tùy chọn)

        Returns:
        - JSON string với nội dung trang
        """
        log_debug(f"Lấy nội dung trang {page_title} từ {wiki}")

        try:
            api_url = f"https://{wiki}.fandom.com/api.php"
            params = {
                "action": "query",
                "prop": "extracts|info|pageimages",
                "titles": page_title,
                "format": "json",
                "exintro": True if not section else False,
                "explaintext": True,
                "pithumbsize": 300
            }

            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to get page content: {response.status_code}"
                }, indent=2)

            data = response.json()
            pages = data.get("query", {}).get("pages", {})

            for page_id, page_data in pages.items():
                if page_id == "-1":
                    return json.dumps({
                        "status": "error",
                        "message": "Page not found"
                    }, indent=2)

                content = {
                    "title": page_data.get("title"),
                    "page_id": page_id,
                    "url": f"https://{wiki}.fandom.com/wiki/{page_title.replace(' ', '_')}",
                    "extract": page_data.get("extract", "")[:1000] + "..." if page_data.get("extract") else "",
                    "length": page_data.get("length", 0),
                    "last_modified": page_data.get("touched"),
                    "thumbnail": page_data.get("thumbnail", {}).get("source") if page_data.get("thumbnail") else None
                }

                return json.dumps({
                    "status": "success",
                    "wiki": wiki,
                    "page_content": content,
                    "timestamp": datetime.now().isoformat()
                }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy nội dung trang: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_categories(self, wiki: str, category: str = None, limit: int = 20) -> str:
        """
        Tìm kiếm categories hoặc lấy members của category.

        Args:
        - wiki: Tên wiki
        - category: Tên category (để trống để lấy all categories)
        - limit: Số lượng kết quả

        Returns:
        - JSON string với categories hoặc category members
        """
        log_debug(f"Tìm kiếm categories trong {wiki}")

        try:
            api_url = f"https://{wiki}.fandom.com/api.php"

            if category:
                # Lấy members của category
                params = {
                    "action": "query",
                    "list": "categorymembers",
                    "cmtitle": f"Category:{category}",
                    "format": "json",
                    "cmlimit": limit
                }
            else:
                # Lấy all categories
                params = {
                    "action": "query",
                    "list": "allcategories",
                    "format": "json",
                    "aclimit": limit
                }

            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to search categories: {response.status_code}"
                }, indent=2)

            data = response.json()

            if category:
                members = data.get("query", {}).get("categorymembers", [])
                results = [{"title": member.get("title"), "page_id": member.get("pageid")} for member in members]
                result_type = "category_members"
            else:
                categories = data.get("query", {}).get("allcategories", [])
                results = [{"category": cat.get("*"), "size": cat.get("size", 0)} for cat in categories]
                result_type = "all_categories"

            return json.dumps({
                "status": "success",
                "wiki": wiki,
                "category": category,
                "result_type": result_type,
                "results_count": len(results),
                "results": results,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm categories: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def get_wiki_info(self, wiki: str) -> str:
        """
        Lấy thông tin tổng quan về wiki.

        Args:
        - wiki: Tên wiki

        Returns:
        - JSON string với thông tin wiki
        """
        log_debug(f"Lấy thông tin wiki: {wiki}")

        try:
            api_url = f"https://{wiki}.fandom.com/api.php"
            params = {
                "action": "query",
                "meta": "siteinfo",
                "siprop": "general|statistics",
                "format": "json"
            }

            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to get wiki info: {response.status_code}"
                }, indent=2)

            data = response.json()
            general = data.get("query", {}).get("general", {})
            stats = data.get("query", {}).get("statistics", {})

            wiki_info = {
                "wiki_name": general.get("sitename"),
                "wiki_url": general.get("server"),
                "language": general.get("lang"),
                "main_page": general.get("mainpage"),
                "statistics": {
                    "total_pages": stats.get("pages", 0),
                    "total_articles": stats.get("articles", 0),
                    "total_edits": stats.get("edits", 0),
                    "total_users": stats.get("users", 0),
                    "active_users": stats.get("activeusers", 0)
                }
            }

            return json.dumps({
                "status": "success",
                "wiki": wiki,
                "wiki_info": wiki_info,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy thông tin wiki: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    # Helper methods
    def _get_page_additional_info(self, wiki: str, title: str) -> dict:
        """Get additional page information including thumbnail."""
        try:
            api_url = f"https://{wiki}.fandom.com/api.php"
            params = {
                "action": "query",
                "prop": "pageimages|info",
                "titles": title,
                "format": "json",
                "pithumbsize": 200
            }

            response = requests.get(api_url, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                pages = data.get("query", {}).get("pages", {})

                for page_data in pages.values():
                    return {
                        "thumbnail": page_data.get("thumbnail", {}).get("source"),
                        "length": page_data.get("length", 0),
                        "last_modified": page_data.get("touched", "")
                    }
        except:
            pass

        return {"thumbnail": None, "length": 0, "last_modified": ""}
