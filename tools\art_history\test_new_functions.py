#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho các hàm get_recent/popular mới đư<PERSON>c thêm vào art history tools.
"""

import sys
import os
import json

# Thêm thư mục gốc vào Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_europeana():
    """Test Europeana tools."""
    print("=== Testing Europeana Art Tools ===")
    try:
        from tools.art_history.europeana_art_tools import EuropeanaArtTools
        
        tool = EuropeanaArtTools()
        
        # Test regular search
        print("--- Regular Search ---")
        result1 = tool.search_europeana_art("<PERSON> Gogh", None, None, 3)
        print("Search result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test recent artworks
        print("\n--- Recent Artworks ---")
        result2 = tool.get_recent_artworks(3, 30, "IMAGE")
        print("Recent artworks result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # Test trending collections
        print("\n--- Trending Collections ---")
        result3 = tool.get_trending_collections(3, "art")
        print("Trending collections result:", result3[:200] + "..." if len(result3) > 200 else result3)
        
    except Exception as e:
        print(f"Error testing Europeana: {e}")
    print()

def test_metropolitan_museum():
    """Test Metropolitan Museum tools."""
    print("=== Testing Metropolitan Museum Tools ===")
    try:
        from tools.art_history.metropolitan_museum_tools import MetropolitanMuseumTools
        
        tool = MetropolitanMuseumTools()
        
        # Test regular search
        print("--- Regular Search ---")
        result1 = tool.search_met_art("European", None, None, 3)
        print("Search result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test recent acquisitions
        print("\n--- Recent Acquisitions ---")
        result2 = tool.get_recent_acquisitions(3, 90, "European Paintings")
        print("Recent acquisitions result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # Test highlighted artworks
        print("\n--- Highlighted Artworks ---")
        result3 = tool.get_highlighted_artworks(3, "American Wing")
        print("Highlighted artworks result:", result3[:200] + "..." if len(result3) > 200 else result3)
        
    except Exception as e:
        print(f"Error testing Metropolitan Museum: {e}")
    print()

def test_rijksmuseum():
    """Test Rijksmuseum tools."""
    print("=== Testing Rijksmuseum Tools ===")
    try:
        from tools.art_history.rijksmuseum_tools import RijksmuseumTools
        
        tool = RijksmuseumTools()
        
        # Test regular search
        print("--- Regular Search ---")
        result1 = tool.search_rijksmuseum("Rembrandt", None, None, 3)
        print("Search result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test recent additions
        print("\n--- Recent Additions ---")
        result2 = tool.get_recent_additions(3, 90, "painting")
        print("Recent additions result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # Test popular Dutch art
        print("\n--- Popular Dutch Art ---")
        result3 = tool.get_popular_dutch_art(3, "Golden Age")
        print("Popular Dutch art result:", result3[:200] + "..." if len(result3) > 200 else result3)
        
    except Exception as e:
        print(f"Error testing Rijksmuseum: {e}")
    print()

def test_search_toolkit():
    """Test Art History Search Toolkit."""
    print("=== Testing Art History Search Toolkit ===")
    try:
        from tools.art_history.art_history_search_toolkit import ArtHistorySearchToolkit
        
        toolkit = ArtHistorySearchToolkit()
        
        # Test regular keyword generation
        print("--- Europeana Keywords ---")
        result1 = toolkit.generate_europeana_keywords("Van Gogh", "Post-Impressionism", "painting")
        print(result1)
        
        print("\n--- Google Arts Keywords ---")
        result2 = toolkit.generate_google_arts_keywords("Starry Night", "artwork")
        print(result2)
        
        print("\n--- Wikipedia Art Keywords ---")
        result3 = toolkit.generate_wikipedia_art_keywords("Renaissance painting", "movement")
        print(result3)
        
        print("\n--- Met Museum Keywords ---")
        result4 = toolkit.generate_met_museum_keywords("European", "painting", "19th century")
        print(result4)
        
        print("\n--- Rijksmuseum Keywords ---")
        result5 = toolkit.generate_rijksmuseum_keywords("Rembrandt", "painting", "Golden Age")
        print(result5)
        
        # Test recent/trending keyword generation
        print("\n--- Europeana Recent Keywords ---")
        result6 = toolkit.generate_europeana_recent_keywords("contemporary art", 30)
        print(result6)
        
        print("\n--- Google Arts Trending Keywords ---")
        result7 = toolkit.generate_google_arts_trending_keywords("exhibitions", "month")
        print(result7)
        
        print("\n--- Wikipedia Art Recent Keywords ---")
        result8 = toolkit.generate_wikipedia_art_recent_keywords(30, "en")
        print(result8)
        
        print("\n--- Museum Recent Keywords ---")
        result9 = toolkit.generate_museum_recent_keywords("met", "European Paintings")
        print(result9)
        
        print("\n--- Art Discovery Keywords ---")
        result10 = toolkit.generate_art_discovery_keywords("Dutch art", "17th century")
        print(result10)
        
    except Exception as e:
        print(f"Error testing Search Toolkit: {e}")
    print()

def test_other_tools():
    """Test other art history tools briefly."""
    print("=== Testing Other Art History Tools ===")
    
    # Test Google Arts & Culture (if available)
    try:
        print("--- Google Arts & Culture Tools ---")
        from tools.art_history.google_arts_culture_tools import GoogleArtsCultureTool
        tool = GoogleArtsCultureTool()
        print("Google Arts & Culture tools loaded successfully")
    except Exception as e:
        print(f"Google Arts & Culture tools error: {e}")
    
    # Test Wikipedia Art (if available)
    try:
        print("--- Wikipedia Art Tools ---")
        from tools.art_history.wikipedia_art_tools import WikipediaArtTool
        tool = WikipediaArtTool()
        print("Wikipedia Art tools loaded successfully")
    except Exception as e:
        print(f"Wikipedia Art tools error: {e}")
    
    print()

def main():
    """Chạy tất cả các test."""
    print("Testing New Art History Tools Functions")
    print("=" * 50)
    print()
    
    # Test các tool đã cải tiến và mới
    test_europeana()
    test_metropolitan_museum()
    test_rijksmuseum()
    test_search_toolkit()
    test_other_tools()
    
    print("=" * 50)
    print("Testing completed!")

if __name__ == "__main__":
    main()
