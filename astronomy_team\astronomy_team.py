from typing import Dict, Any, Optional
from agno.team import Team
from agno.models.ollama import Ollama
from agno.workflow import Workflow
from agno.run.response import RunResponse
from astronomy_team.agents.analysis_agent import AnalysisAgent
from astronomy_team.agents.nasa_ads_agent import NasaAdsAgent
from astronomy_team.agents.quality_control_agent import QualityControlAgent
from astronomy_team.agents.writer_agent import WriterAgent
from astronomy_team.config import MODEL_CONFIG, MEMORY_CONFIG
import logging
import json

# Cấu hình logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AstronomyTeam(Team):
    """Đội ngũ chuyên gia thiên văn học."""
    
    def __init__(self, **kwargs):
        # Khởi tạo các thành viên trong team
        self.analysis_agent = AnalysisAgent(
            instructions=[
                "Phân tích câu hỏi và xác định các từ khóa tìm kiếm khoa học.",
                "Tr<PERSON> về danh sách từ khóa và phân tích ngắn gọn."
            ]
        )
        self.nasa_ads_agent = NasaAdsAgent(
            instructions=[
                "Tìm kiếm các bài báo khoa học liên quan đến từ khóa được cung cấp từ Analysis Agent.",
                "Trả về danh sách các bài báo phù hợp."
            ]
        )
        self.quality_control_agent = QualityControlAgent(
            instructions=[
                "Đánh giá chất lượng các bài báo đã tìm được dựa trên độ phù hợp, độ tin cậy, độ cập nhật và độ đầy đủ.",
                "Đưa ra điểm số và đề xuất cải thiện nếu cần."
            ]
        )
        self.writer_agent = WriterAgent(
            instructions=[
                "Tổng hợp thông tin từ các bước trước và viết câu trả lời rõ ràng, trích dẫn nguồn, định dạng markdown.",
                "Nếu thông tin không đủ, hãy nêu rõ điều đó."
            ]
        )
        
        # Hướng dẫn tổng thể cho team
        team_instructions = [
            "1. Phân tích câu hỏi để xác định từ khóa (Analysis Agent).",
            "2. Tìm kiếm tài liệu khoa học dựa trên từ khóa (NASA ADS Agent).",
            "3. Đánh giá chất lượng kết quả tìm kiếm (Quality Control Agent).",
            "4. Tổng hợp và viết câu trả lời cuối cùng (Writer Agent).",
            "Luôn đảm bảo nguồn tham khảo rõ ràng và câu trả lời dễ hiểu cho người không chuyên."
        ]
        
        super().__init__(
            members=[
                self.analysis_agent,
                self.nasa_ads_agent,
                self.quality_control_agent,
                self.writer_agent
            ],
            name="Astronomy Expert Team",
            description="""
            Một đội ngũ chuyên gia thiên văn học với khả năng phân tích câu hỏi, 
            tìm kiếm tài liệu khoa học, đánh giá chất lượng và tổng hợp thông tin 
            để đưa ra câu trả lời chính xác và đáng tin cậy.
            """,
            mode="coordinate",  # Sử dụng chế độ điều phối
            instructions=team_instructions,
            model=Ollama(
                id=MODEL_CONFIG["base_model"],
            ),
            **kwargs
        )
        
        logger.info("Khởi tạo Astronomy Team thành công")

    async def arun(self, query: str, **kwargs) -> RunResponse:
        """Xử lý câu hỏi và trả về câu trả lời bằng cách để LLM điều phối các agent thành viên."""
        try:
            logger.info(f"Bắt đầu xử lý câu hỏi: {query}")
            # Giao cho Team (LLM) điều phối các bước, truyền query vào
            response = await super().arun(query, **kwargs)
            # Lưu lịch sử tương tác nếu cần
            self._save_interaction(query, response, getattr(response, 'metadata', {}))
            logger.info("Hoàn thành xử lý câu hỏi")
            return response
        except Exception as e:
            error_msg = f"Lỗi khi xử lý câu hỏi: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return RunResponse(
                content="Xin lỗi, đã xảy ra lỗi khi xử lý yêu cầu của bạn. Vui lòng thử lại sau.",
            )
    
    def _save_interaction(self, query: str, response: Dict[str, Any], search_results: Dict[str, Any]) -> None:
        """Lưu lại lịch sử tương tác."""
        try:
            # Tạo bản ghi tương tác
            interaction = {
                "timestamp": self._get_current_timestamp(),
                "query": query,
                "response": response.get("answer", ""),
                "sources": search_results.get("papers", []),
                "status": response.get("status", "unknown")
            }
            
            # Lưu vào session state
            if not hasattr(self, 'interaction_history_'):
                self.interaction_history_ = []
                
            self.interaction_history_.append(interaction)
            
            # Giới hạn lịch sử
            max_history = MEMORY_CONFIG.get("max_history_length", 10)
            if len(self.interaction_history_) > max_history:
                self.interaction_history_ = self.interaction_history_[-max_history:]
                
            logger.debug(f"Đã lưu tương tác vào lịch sử. Tổng số: {len(self.interaction_history_)}")
            
        except Exception as e:
            logger.error(f"Lỗi khi lưu lịch sử tương tác: {str(e)}")
    
    def _get_current_timestamp(self) -> str:
        """Lấy thời gian hiện tại dưới dạng chuỗi."""
        from datetime import datetime
        return datetime.now().isoformat()


class AstronomyWorkflow(Workflow):
    """Quy trình làm việc của đội ngũ thiên văn."""
    
    def __init__(self, **kwargs):
        super().__init__(
            name="Astronomy Research Workflow",
            description="""
            Quy trình làm việc để xử lý các câu hỏi về thiên văn học,
            từ phân tích câu hỏi, tìm kiếm tài liệu, kiểm định chất lượng
            đến tổng hợp thông tin và đưa ra câu trả lời.
            """,
            **kwargs
        )
        self.team = AstronomyTeam()
        self.analysis_agent = self.team.analysis_agent
        self.nasa_ads_agent = self.team.nasa_ads_agent
        self.quality_control_agent = self.team.quality_control_agent
        self.writer_agent = self.team.writer_agent
        logger.info("Khởi tạo Astronomy Workflow thành công")

    async def arun(self, query: str, **kwargs) -> RunResponse:
        """Thực thi workflow từng bước rõ ràng."""
        logger.info(f"Bắt đầu workflow với câu hỏi: {query}")
        try:
            # Bước 1: Phân tích câu hỏi
            logger.info("Đang phân tích câu hỏi...")
            analysis = await self.analysis_agent.run(query)
            # Bước 1b: Trích xuất từ khóa bằng Team (LLM)
            logger.info("Đang trích xuất từ khóa...")
            keywords_response = await self.team.run(analysis["keyword_prompt"])
            # Giả sử LLM trả về chuỗi từ khóa, phân tách bằng dấu phẩy
            keywords = [k.strip() for k in str(keywords_response.content).split(",") if k.strip()]
            # Bước 2: Tìm kiếm tài liệu
            logger.info("Đang tìm kiếm tài liệu...")
            search_results = await self.nasa_ads_agent.run({"keywords": keywords})
            # Bước 3: Kiểm định chất lượng
            logger.info("Đang đánh giá chất lượng kết quả...")
            evaluation = await self.quality_control_agent.run({
                "query": query,
                "search_results": search_results,
                "analysis": analysis
            })
            # Bước 4: Tổng hợp và viết câu trả lời
            logger.info("Đang tổng hợp câu trả lời...")
            response = await self.writer_agent.run({
                "query": query,
                "analysis": analysis,
                "search_results": search_results,
                "evaluation": evaluation.get("evaluation")
            })
            # Lưu lịch sử tương tác
            self._log_workflow_result(query, response)
            logger.info("Hoàn thành xử lý câu hỏi")
            return RunResponse(
                content=response.get("answer", "Không thể tạo câu trả lời."),
                metadata={
                    "status": response.get("status", "unknown"),
                    "sources": response.get("sources", []),
                    "evaluation": evaluation.get("evaluation")
                }
            )
        except Exception as e:
            error_msg = f"Lỗi trong quá trình xử lý workflow: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return RunResponse(
                content="Đã xảy ra lỗi trong quá trình xử lý yêu cầu của bạn.",
            )
    
    def _log_workflow_result(self, query: str, response: RunResponse) -> None:
        """Ghi log kết quả xử lý workflow."""
        try:
            log_entry = {
                "timestamp": self._get_current_timestamp(),
                "query": query,
                "response_status": response.metadata.get("status", "unknown"),
                "sources_count": len(response.metadata.get("sources", [])),
                "error": response.metadata.get("error")
            }
            
            logger.info(f"Kết quả workflow: {json.dumps(log_entry, ensure_ascii=False)}")
            
        except Exception as e:
            logger.error(f"Lỗi khi ghi log workflow: {str(e)}")
    
    def _get_current_timestamp(self) -> str:
        """Lấy thời gian hiện tại dưới dạng chuỗi."""
        from datetime import datetime
        return datetime.now().isoformat()


# Hàm tiện ích để chạy thử
def run_example():
    """Chạy một ví dụ minh họa."""
    import asyncio
    
    async def main():
        # Khởi tạo workflow
        workflow = AstronomyWorkflow()
        
        # Câu hỏi mẫu
        query = "Hãy giải thích về hố đen và các đặc điểm của nó"
        
        # Thực thi workflow
        print(f"Đang xử lý câu hỏi: {query}")
        response = await workflow.arun(query)
        
        # In kết quả
        print("\n=== KẾT QUẢ ===")
        print(response.content)
        
        if response.metadata.get("sources"):
            print("\n=== NGUỒN THAM KHẢO ===")
            for i, source in enumerate(response.metadata["sources"], 1):
                print(f"{i}. {source}")
    
    # Chạy ví dụ
    asyncio.run(main())


if __name__ == "__main__":
    run_example()
