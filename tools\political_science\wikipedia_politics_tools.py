from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import logger
import requests
import json
from datetime import datetime, timedelta
from urllib.parse import quote_plus

class WikipediaPoliticsTool(Toolkit):
    """
    Công cụ tìm kiếm thông tin chính trị, quan hệ quốc tế, <PERSON><PERSON><PERSON> đạo, sự kiện từ Wikipedia.
    """

    def __init__(self):
        super().__init__(
            name="Công cụ tìm kiếm Wikipedia về chính trị",
            tools=[
                self.search_wikipedia_politics,
                self.get_article_categories,
                self.get_article_links
            ]
        )
        self.base_url = "https://{lang}.wikipedia.org/api/rest_v1"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

    async def search_wikipedia_politics(self, query: str, language: str = "en") -> Dict[str, Any]:
        """
        <PERSON><PERSON><PERSON> kiếm thông tin chính trị, <PERSON><PERSON><PERSON> đ<PERSON>, sự kiện trên Wikipedia.

        Parameters:
        - query: <PERSON><PERSON> đ<PERSON> chính trị, lãnh đạo, sự kiện (ví dụ: 'United Nations', 'Joe Biden')
        - language: Mã ngôn ngữ Wikipedia (mặc định: 'en')

        Returns:
        - Dict chứa tóm tắt, URL, ảnh đại diện và chủ đề liên quan
        """
        logger.info(f"Tìm kiếm Wikipedia ({language}): {query}")

        try:
            # Lấy thông tin tóm tắt
            url = f"{self.base_url.format(lang=language)}/page/summary/{quote_plus(query)}"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 404:
                # Thử tìm kiếm gần đúng nếu không tìm thấy chính xác
                return await self._search_alternative(query, language)
                
            response.raise_for_status()
            data = response.json()
            
            # Lấy thông tin cơ bản
            result = {
                "status": "success",
                "source": "Wikipedia",
                "query": query,
                "title": data.get("title"),
                "summary": data.get("extract"),
                "description": data.get("description"),
                "page_url": data.get("content_urls", {}).get("desktop", {}).get("page"),
                "thumbnail": data.get("thumbnail", {}).get("source")
            }
            
            # Thêm các chủ đề liên quan
            related = await self._get_related_topics(query, language)
            if related:
                result["related_topics"] = related
                
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Lỗi kết nối Wikipedia: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": f"Lỗi kết nối: {str(e)}",
                "query": query
            }

    async def _search_alternative(self, query: str, language: str) -> Dict[str, Any]:
        """Tìm kiếm các bài viết liên quan khi không tìm thấy kết quả chính xác"""
        try:
            search_url = f"https://{language}.wikipedia.org/w/api.php"
            params = {
                "action": "query",
                "list": "search",
                "srsearch": query,
                "format": "json",
                "srlimit": 3
            }
            response = requests.get(search_url, params=params, timeout=10)
            response.raise_for_status()
            
            results = []
            data = response.json()
            for item in data.get("query", {}).get("search", [])[:3]:
                results.append({
                    "title": item.get("title"),
                    "snippet": item.get("snippet"),
                    "url": f"https://{language}.wikipedia.org/wiki/{quote_plus(item['title'])}"
                })
            
            return {
                "status": "not_found",
                "message": "Không tìm thấy bài viết chính xác",
                "suggestions": results,
                "query": query
            }
            
        except Exception as e:
            logger.error(f"Lỗi khi tìm kiếm thay thế: {str(e)}")
            return {
                "status": "error",
                "message": "Không tìm thấy kết quả phù hợp",
                "query": query
            }
    
    async def _get_related_topics(self, query: str, language: str, limit: int = 5) -> List[str]:
        """Lấy danh sách các chủ đề liên quan"""
        try:
            search_url = f"https://{language}.wikipedia.org/w/api.php"
            params = {
                "action": "query",
                "prop": "links",
                "titles": query,
                "pllimit": limit,
                "format": "json"
            }
            response = requests.get(search_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            pages = next(iter(data.get("query", {}).get("pages", {}).values()), {})
            return [link["title"] for link in pages.get("links", []) if not link["ns"]][:limit]
            
        except Exception as e:
            logger.warning(f"Không thể lấy chủ đề liên quan: {str(e)}")
            return []
    
    async def get_article_categories(self, title: str, language: str = "en") -> Dict[str, Any]:
        """
        Lấy danh sách các thể loại của bài viết
        
        Parameters:
        - title: Tiêu đề bài viết
        - language: Mã ngôn ngữ (mặc định: 'en')
        """
        try:
            url = f"{self.base_url.format(lang=language)}/page/categories/{quote_plus(title)}"
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return {
                "status": "success",
                "title": title,
                "categories": [c["title"].replace("Category:", "") for c in data.get("categories", [])]
            }
            
        except Exception as e:
            logger.error(f"Lỗi khi lấy thể loại: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "title": title
            }
    
    async def get_article_links(self, title: str, language: str = "en", limit: int = 10) -> Dict[str, Any]:
        """
        Lấy danh sách các liên kết từ bài viết
        
        Parameters:
        - title: Tiêu đề bài viết
        - language: Mã ngôn ngữ (mặc định: 'en')
        - limit: Số lượng liên kết tối đa
        """
        try:
            url = f"{self.base_url.format(lang=language)}/page/links/{quote_plus(title)}"
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            links = [{"title": l["title"], "url": l["content_urls"]["desktop"]["page"]} 
                    for l in data.get("links", [])[:limit]]
            
            return {
                "status": "success",
                "title": title,
                "links": links
            }
            
        except Exception as e:
            logger.error(f"Lỗi khi lấy liên kết: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "title": title
            }
