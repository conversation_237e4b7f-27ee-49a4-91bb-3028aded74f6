#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script cho Archaeology Tools
"""

import sys
import os
import json
import random
import asyncio
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_archaeological_database_tools():
    """Test Archaeological Database Tools"""
    print("🏛️ Testing Archaeological Database Tools...")
    try:
        from tools.archaeology.archaeological_database_tools import ArchaeologicalDatabaseTool
        
        arch_tool = ArchaeologicalDatabaseTool()
        
        print("  - Testing Archaeological Database instantiation...")
        print("    ✅ Archaeological Database Tools instantiated")
        
        # Test get_top_new
        print("  - Testing Archaeological Database get_top_new...")
        assert hasattr(arch_tool, 'get_top_new')
        print("    ✅ Archaeological Database get_top_new method exists")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Archaeological Database Tools failed: {str(e)}")
        return False

def test_archaeology_search_toolkit():
    """Test Archaeology Search Toolkit"""
    print("🔍 Testing Archaeology Search Toolkit...")
    try:
        from tools.archaeology.archaeology_search_toolkit import ArchaeologySearchToolkit
        
        toolkit = ArchaeologySearchToolkit()
        
        print("  - Testing archaeological sites search...")
        result = toolkit.search_archaeological_sites("urban", "egypt", "ancient", "world_heritage")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Archaeological sites search works")
        
        print("  - Testing ancient artifacts search...")
        result = toolkit.search_ancient_artifacts("pottery", "ceramic", "egyptian", "radiocarbon")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Ancient artifacts search works")
        
        print("  - Testing excavation projects search...")
        result = toolkit.search_excavation_projects("research", "active", "well_funded", "stratigraphic")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Excavation projects search works")
        
        print("  - Testing comprehensive archaeology search...")
        result = toolkit.comprehensive_archaeology_search("pyramid", "all", "ancient", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive archaeology search works")
        
        print("  - Testing cultural heritage search...")
        result = toolkit.search_cultural_heritage("archaeological", "world_heritage", "medium", "africa")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Cultural heritage search works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Archaeology Search Toolkit failed: {str(e)}")
        return False

def test_other_archaeology_tools():
    """Test other archaeology tools"""
    print("🏺 Testing Other Archaeology Tools...")
    try:
        # Test Ancient Civilizations Tools
        from tools.archaeology.ancient_civilizations_tools import AncientCivilizationsTool
        
        civilizations_tool = AncientCivilizationsTool()
        
        print("  - Testing Ancient Civilizations Tools...")
        print("    ✅ Ancient Civilizations Tools instantiated")
        
        # Test Artifact Analysis Tools
        from tools.archaeology.artifact_analysis_tools import ArtifactAnalysisTool
        
        artifact_tool = ArtifactAnalysisTool()
        
        print("  - Testing Artifact Analysis Tools...")
        print("    ✅ Artifact Analysis Tools instantiated")
        
        # Test Excavation Sites Tools
        from tools.archaeology.excavation_sites_tools import ExcavationSitesTool
        
        excavation_tool = ExcavationSitesTool()
        
        print("  - Testing Excavation Sites Tools...")
        print("    ✅ Excavation Sites Tools instantiated")
        
        # Test Cultural Heritage Tools
        from tools.archaeology.cultural_heritage_tools import CulturalHeritageTool
        
        heritage_tool = CulturalHeritageTool()
        
        print("  - Testing Cultural Heritage Tools...")
        print("    ✅ Cultural Heritage Tools instantiated")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Other Archaeology Tools failed: {str(e)}")
        return False

def test_random_archaeology_functionality():
    """Test random archaeology functionality"""
    print("\n🎲 Testing Random Archaeology Functionality...")
    
    try:
        # Random archaeological sites test
        from tools.archaeology.archaeology_search_toolkit import ArchaeologySearchToolkit
        toolkit = ArchaeologySearchToolkit()
        
        site_types = ["urban", "religious", "burial", "settlement"]
        site_type = random.choice(site_types)
        result = toolkit.search_archaeological_sites(site_type, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random archaeological sites {site_type} search test passed")
        
        # Random ancient artifacts test
        artifact_types = ["pottery", "tools", "jewelry", "weapons"]
        artifact_type = random.choice(artifact_types)
        result = toolkit.search_ancient_artifacts(artifact_type, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random ancient artifacts {artifact_type} test passed")
        
        # Random excavation projects test
        project_types = ["research", "rescue", "survey", "conservation"]
        project_type = random.choice(project_types)
        result = toolkit.search_excavation_projects(project_type, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random excavation projects {project_type} test passed")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Random Archaeology Functionality failed: {str(e)}")
        return False

def test_archaeology_search_variations():
    """Test various archaeology search variations"""
    print("\n🏛️ Testing Archaeology Search Variations...")
    
    try:
        from tools.archaeology.archaeology_search_toolkit import ArchaeologySearchToolkit
        toolkit = ArchaeologySearchToolkit()
        
        # Test comprehensive search with different parameters
        print("  - Testing comprehensive search variations...")
        result = toolkit.comprehensive_archaeology_search("sphinx", "sites", "classical", "advanced")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive search variations work")
        
        # Test cultural heritage with different parameters
        print("  - Testing cultural heritage variations...")
        result = toolkit.search_cultural_heritage("", "national", "high", "europe")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Cultural heritage variations work")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Archaeology Search Variations failed: {str(e)}")
        return False

async def test_async_archaeological_database():
    """Test async Archaeological Database functionality"""
    print("\n⚡ Testing Async Archaeological Database...")
    
    try:
        from tools.archaeology.archaeological_database_tools import ArchaeologicalDatabaseTool
        
        arch_tool = ArchaeologicalDatabaseTool()
        
        # Test async get_top_new
        print("  - Testing async get_top_new...")
        result = await arch_tool.get_top_new("discoveries", 5, "month", "ancient_egypt")
        assert result["status"] == "success"
        print("    ✅ Async get_top_new works")
        
        # Test async search_archaeological_data
        print("  - Testing async search_archaeological_data...")
        result = await arch_tool.search_archaeological_data("pyramid", "sites", 5)
        assert "status" in result
        print("    ✅ Async search_archaeological_data works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Async Archaeological Database failed: {str(e)}")
        return False

def test_archaeological_database_get_top_new_content_types():
    """Test Archaeological Database get_top_new with different content types"""
    print("\n🔬 Testing Archaeological Database get_top_new Content Types...")
    
    try:
        from tools.archaeology.archaeological_database_tools import ArchaeologicalDatabaseTool
        
        arch_tool = ArchaeologicalDatabaseTool()
        
        # Test get_top_new with different content types
        content_types = ["discoveries", "excavations", "publications", "artifacts", "sites"]
        
        async def test_content_type(content_type):
            print(f"  - Testing get_top_new for {content_type}...")
            result = await arch_tool.get_top_new(content_type, 3, "week", "classical")
            assert result["status"] == "success"
            print(f"    ✅ get_top_new for {content_type} works")
        
        # Run async tests
        async def run_all_tests():
            for content_type in content_types:
                await test_content_type(content_type)
        
        asyncio.run(run_all_tests())
        
        return True
        
    except Exception as e:
        print(f"    ❌ Archaeological Database get_top_new Content Types failed: {str(e)}")
        return False

async def test_async_other_archaeology_tools():
    """Test async other archaeology tools functionality"""
    print("\n🏺 Testing Async Other Archaeology Tools...")
    
    try:
        from tools.archaeology.ancient_civilizations_tools import AncientCivilizationsTool
        
        civilizations_tool = AncientCivilizationsTool()
        
        # Test async search_civilizations
        print("  - Testing async search civilizations...")
        result = await civilizations_tool.search_civilizations("egyptian", "ancient", 3)
        assert result["status"] == "success"
        print("    ✅ Async search civilizations works")
        
        # Test async search_cultural_practices
        print("  - Testing async search cultural practices...")
        result = await civilizations_tool.search_cultural_practices("religious", "egyptian", 3)
        assert result["status"] == "success"
        print("    ✅ Async search cultural practices works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Async Other Archaeology Tools failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 ARCHAEOLOGY TOOLS TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing Archaeology channel tools...")
    print()
    
    test_results = []
    
    # Test all archaeology tools
    test_functions = [
        ("Archaeological Database Tools", test_archaeological_database_tools),
        ("Archaeology Search Toolkit", test_archaeology_search_toolkit),
        ("Other Archaeology Tools", test_other_archaeology_tools),
        ("Random Archaeology Functionality", test_random_archaeology_functionality),
        ("Archaeology Search Variations", test_archaeology_search_variations),
        ("Archaeological Database get_top_new Content Types", test_archaeological_database_get_top_new_content_types)
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()
    
    # Test async functionality
    print(f"\n{'='*20} Async Tests {'='*20}")
    try:
        async_result1 = asyncio.run(test_async_archaeological_database())
        test_results.append(("Async Archaeological Database", async_result1))
        
        async_result2 = asyncio.run(test_async_other_archaeology_tools())
        test_results.append(("Async Other Archaeology Tools", async_result2))
    except Exception as e:
        print(f"❌ Async tests failed: {str(e)}")
        test_results.append(("Async Archaeological Database", False))
        test_results.append(("Async Other Archaeology Tools", False))
    
    # Summary
    print("\n" + "="*60)
    print("📋 ARCHAEOLOGY TOOLS TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All archaeology tools are working correctly!")
        print("✨ Archaeology channel fully functional!")
        print("🏛️ Ready for archaeological research and heritage exploration!")
    elif passed >= total * 0.8:
        print("✅ Excellent performance - most functionality working!")
    elif passed >= total * 0.6:
        print("✅ Good performance - majority working!")
    else:
        print("⚠️  Some issues detected. Please check the error messages above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
