from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
from bs4 import BeautifulSoup
import re

class WikipediaPaleoTool(Toolkit):
    """
    Wikipedia Paleo Tool for searching and retrieving information about extinct species 
    and paleontological eras from Wikipedia.
    """

    def __init__(self):
        super().__init__(
            name="Wikipedia Paleontology Tools",
            tools=[
                self.search_wikipedia_paleo,
                self.get_extinct_species_info,
                self.get_geological_period,
                self.get_extinction_events,
                self.search_paleo_articles
            ]
        )

    async def search_wikipedia_paleo(self, query: str, language: str = "en") -> Dict[str, Any]:
        """
        Search Wikipedia for extinct species, paleontological periods, or extinction events.

        Parameters:
        - query: Extinct species, period, or event (e.g., 'Stegosaurus stenops', 'Permian extinction event')
        - language: Wikipedia language code (default: 'en')

        Returns:
        - JSON with summary, page URL, and related topics
        """
        logger.info(f"Searching Wikipedia ({language}) for: {query}")

        try:
            # Wikipedia API endpoint
            api_url = f"https://{language}.wikipedia.org/api/rest_v1/page/summary/{query.replace(' ', '_')}"
            response = requests.get(api_url)

            if response.status_code == 404:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": "No article found for query",
                    "query": query
                }
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": f"Wikipedia API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            summary = data.get("extract")
            page_url = data.get("content_urls", {}).get("desktop", {}).get("page")
            title = data.get("title")
            thumbnail = data.get("thumbnail", {}).get("source")

            # Optionally, get related topics (using search API)
            related = []
            try:
                search_url = f"https://{language}.wikipedia.org/w/api.php"
                search_params = {
                    "action": "query",
                    "list": "search",
                    "srsearch": query,
                    "format": "json",
                    "srlimit": 5
                }
                search_resp = requests.get(search_url, params=search_params)
                if search_resp.status_code == 200:
                    search_data = search_resp.json()
                    for item in search_data.get("query", {}).get("search", []):
                        if item.get("title") != title:
                            related.append(item.get("title"))
            except Exception as rel_err:
                log_debug(f"Error fetching related Wikipedia topics: {str(rel_err)}")

            return {
                "status": "success",
                "source": "Wikipedia",
                "query": query,
                "title": title,
                "summary": summary,
                "page_url": page_url,
                "thumbnail": thumbnail,
                "related_topics": related
            }

        except Exception as e:
            log_debug(f"Error searching Wikipedia: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "query": query
            }

    async def get_extinct_species_info(self, species_name: str, language: str = "en") -> Dict[str, Any]:
        """
        Lấy thông tin chi tiết về một loài đã tuyệt chủng từ Wikipedia.

        Parameters:
        - species_name: Tên loài (ví dụ: 'Tyrannosaurus rex', 'Mammuthus primigenius')
        - language: Mã ngôn ngữ Wikipedia (mặc định: 'en')

        Returns:
        - JSON với thông tin chi tiết về loài đã tuyệt chủng
        """
        logger.info(f"Lấy thông tin loài đã tuyệt chủng: {species_name}")

        try:
            # Lấy thông tin cơ bản
            api_url = f"https://{language}.wikipedia.org/api/rest_v1/page/summary/{species_name.replace(' ', '_')}"
            response = requests.get(api_url)
            
            if response.status_code == 404:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": "Không tìm thấy bài viết cho loài này",
                    "species_name": species_name
                }
                
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": f"Lỗi API Wikipedia: {response.status_code}",
                    "species_name": species_name
                }
            
            data = response.json()
            
            # Lấy thông tin bảng phân loại
            taxo_url = f"https://{language}.wikipedia.org/w/api.php"
            params = {
                "action": "parse",
                "page": species_name,
                "prop": "text",
                "format": "json"
            }
            taxo_response = requests.get(taxo_url, params=params)
            
            taxonomy = {}
            if taxo_response.status_code == 200:
                soup = BeautifulSoup(taxo_response.json()["parse"]["text"]["*"], 'html.parser')
                taxo_table = soup.find('table', class_='biota')
                if taxo_table:
                    for row in taxo_table.find_all('tr'):
                        cells = row.find_all(['th', 'td'])
                        if len(cells) == 2:
                            key = cells[0].get_text(strip=True).lower()
                            value = cells[1].get_text(strip=True)
                            taxonomy[key] = value
            
            return {
                "status": "success",
                "source": "Wikipedia",
                "species_name": species_name,
                "title": data.get("title"),
                "extract": data.get("extract"),
                "description": data.get("description"),
                "taxonomy": taxonomy,
                "thumbnail": data.get("thumbnail", {}).get("source") if data.get("thumbnail") else None,
                "url": data.get("content_urls", {}).get("desktop", {}).get("page")
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy thông tin loài: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "species_name": species_name
            }

    async def get_geological_period(self, period_name: str, language: str = "en") -> Dict[str, Any]:
        """
        Lấy thông tin về một thời kỳ địa chất từ Wikipedia.

        Parameters:
        - period_name: Tên thời kỳ (ví dụ: 'Cretaceous', 'Jurassic')
        - language: Mã ngôn ngữ Wikipedia (mặc định: 'en')

        Returns:
        - JSON với thông tin chi tiết về thời kỳ địa chất
        """
        logger.info(f"Lấy thông tin thời kỳ địa chất: {period_name}")

        try:
            # Lấy thông tin cơ bản
            api_url = f"https://{language}.wikipedia.org/api/rest_v1/page/summary/{period_name.replace(' ', '_')}"
            response = requests.get(api_url)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": f"Lỗi API Wikipedia: {response.status_code}",
                    "period_name": period_name
                }
            
            data = response.json()
            
            # Lấy thông tin bảng thời gian
            timeline = {}
            timeline_url = f"https://{language}.wikipedia.org/w/api.php"
            params = {
                "action": "parse",
                "page": period_name,
                "prop": "text",
                "format": "json"
            }
            timeline_response = requests.get(timeline_url, params=params)
            
            if timeline_response.status_code == 200:
                soup = BeautifulSoup(timeline_response.json()["parse"]["text"]["*"], 'html.parser')
                # Tìm bảng thông tin bên phải (infobox)
                infobox = soup.find('table', class_='infobox')
                if infobox:
                    for row in infobox.find_all('tr'):
                        header = row.find('th')
                        data_cell = row.find('td')
                        if header and data_cell:
                            key = header.get_text(strip=True).lower()
                            value = data_cell.get_text('\n', strip=True)
                            timeline[key] = value
            
            return {
                "status": "success",
                "source": "Wikipedia",
                "period_name": period_name,
                "title": data.get("title"),
                "extract": data.get("extract"),
                "timeline": timeline,
                "thumbnail": data.get("thumbnail", {}).get("source") if data.get("thumbnail") else None,
                "url": data.get("content_urls", {}).get("desktop", {}).get("page")
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy thông tin thời kỳ địa chất: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "period_name": period_name
            }

    async def get_extinction_events(self, limit: int = 5, language: str = "en") -> Dict[str, Any]:
        """
        Lấy danh sách các sự kiện tuyệt chủng hàng loạt từ Wikipedia.

        Parameters:
        - limit: Số lượng sự kiện tối đa (mặc định: 5)
        - language: Mã ngôn ngữ Wikipedia (mặc định: 'en')

        Returns:
        - JSON với danh sách các sự kiện tuyệt chủng
        """
        logger.info(f"Lấy danh sách sự kiện tuyệt chủng, giới hạn: {limit}")

        try:
            # Trang chính về các sự kiện tuyệt chủng hàng loạt
            page_title = "Extinction_event"
            api_url = f"https://{language}.wikipedia.org/w/api.php"
            params = {
                "action": "parse",
                "page": page_title,
                "prop": "text",
                "format": "json"
            }
            
            response = requests.get(api_url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": f"Lỗi API Wikipedia: {response.status_code}"
                }
            
            soup = BeautifulSoup(response.json()["parse"]["text"]["*"], 'html.parser')
            
            # Tìm bảng liệt kê các sự kiện tuyệt chủng
            events = []
            tables = soup.find_all('table', class_='wikitable')
            
            for table in tables:
                if 'Major extinction events' in str(table):
                    rows = table.find_all('tr')[1:limit+1]  # Bỏ qua hàng tiêu đề
                    for row in rows:
                        cols = row.find_all(['th', 'td'])
                        if len(cols) >= 5:  # Đảm bảo đủ cột thông tin
                            event = {
                                'period': cols[0].get_text(strip=True),
                                'age_ma': cols[1].get_text(strip=True),
                                'extinction': cols[2].get_text(strip=True),
                                'affected_organisms': cols[3].get_text(strip=True),
                                'possible_cause': cols[4].get_text(strip=True)
                            }
                            events.append(event)
                    break
            
            return {
                "status": "success",
                "source": "Wikipedia",
                "events_count": len(events),
                "events": events,
                "url": f"https://{language}.wikipedia.org/wiki/Extinction_event"
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy danh sách sự kiện tuyệt chủng: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e)
            }

    async def search_paleo_articles(self, query: str, language: str = "en", limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm các bài viết liên quan đến cổ sinh vật học trên Wikipedia.

        Parameters:
        - query: Từ khóa tìm kiếm
        - language: Mã ngôn ngữ Wikipedia (mặc định: 'en')
        - limit: Số kết quả tối đa (mặc định: 5)

        Returns:
        - JSON với danh sách các bài viết liên quan
        """
        logger.info(f"Tìm kiếm bài viết cổ sinh vật học: {query}")

        try:
            api_url = f"https://{language}.wikipedia.org/w/api.php"
            params = {
                "action": "query",
                "list": "search",
                "srsearch": f"{query} paleontology OR fossil OR prehistoric",
                "format": "json",
                "srlimit": limit
            }
            
            response = requests.get(api_url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": f"Lỗi API Wikipedia: {response.status_code}",
                    "query": query
                }
            
            data = response.json()
            results = []
            
            if 'query' in data and 'search' in data['query']:
                for item in data['query']['search']:
                    results.append({
                        'title': item.get('title'),
                        'snippet': item.get('snippet'),
                        'url': f"https://{language}.wikipedia.org/wiki/{item.get('title').replace(' ', '_')}"
                    })
            
            return {
                "status": "success",
                "source": "Wikipedia",
                "query": query,
                "results_count": len(results),
                "results": results
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm bài viết cổ sinh vật học: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "query": query
            }
