from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
import time
from urllib.parse import urlencode, quote_plus

class InternetArchiveMysteryTools(Toolkit):
    """
    Công cụ tìm kiếm tài li<PERSON>, sách và báo cáo về các bí ẩn từ Internet Archive.
    
    Công cụ này cung cấp khả năng tìm kiếm trong kho lưu trữ khổng lồ của Internet Archive
    để tìm các tài liệu liên quan đến các hiện tư<PERSON> b<PERSON>, chưa giải thích đ<PERSON>.
    
    Các từ khóa tìm kiếm phổ biến:
    - "paranormal"
    - "ufo sightings"
    - "ghost stories"
    - "cryptozoology"
    - "unexplained phenomena"
    - "conspiracy theories"
    """
    
    def __init__(self):
        super().__init__(
            name="internet_archive_tools",
            description="Công cụ tìm kiếm tài liệu về các hiện tượng bí ẩn từ Internet Archive.",
            tools=[
                self.search_documents,
                self.get_popular_items,
                self.get_item_metadata
            ]
        )
        self.base_url = "https://archive.org/advancedsearch.php"
        self.details_url = "https://archive.org/metadata/"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'MysteryResearchTool/1.0',
            'Accept': 'application/json'
        })

    def search_documents(self, query: str, limit: int = 5, media_type: str = "texts") -> str:
        """
        Tìm kiếm tài liệu từ Internet Archive dựa trên từ khóa.
        
        Args:
            query (str): Từ khóa tìm kiếm (có thể dùng cú pháp nâng cao của Internet Archive)
            limit (int, optional): Số lượng kết quả trả về. Mặc định là 5.
            media_type (str, optional): Loại phương tiện (texts, audio, movies, etc). Mặc định là "texts".
            
        Returns:
            str: Kết quả tìm kiếm dưới dạng JSON
            
        Example:
            >>> tool = InternetArchiveMysteryTools()
            >>> result = tool.search_documents("mothman", limit=3)
            >>> print(json.loads(result)['results'][0]['title'])
            'The Mothman Prophecies'
            
        Keyword guidance:
            - Tìm theo tác giả: "creator:John Keel"
            - Kết hợp nhiều điều kiện: "ghost AND vietnam"
            - Tìm trong tiêu đề: "title:UFO"
            - Theo năm: "year:1975"
        """
        logger.info(f"Searching Internet Archive for: {query}")
        
        try:
            # Xây dựng tham số tìm kiếm
            params = {
                'q': f"{query} AND mediatype:({media_type})",
                'fl[]': [
                    'identifier', 'title', 'creator', 'year', 'description',
                    'publisher', 'language', 'downloads', 'publicdate'
                ],
                'rows': min(limit, 50),  # Giới hạn tối đa 50 kết quả
                'page': 1,
                'output': 'json',
                'sort[]': ['downloads desc'],  # Sắp xếp theo lượt tải về
                'fields': 'identifier,title,creator,year,description,publisher,language,downloads,publicdate',
                'callback': 'callback',
                'save': 'yes',
                'count': min(limit, 50)
            }
            
            # Thêm thời gian chờ giữa các yêu cầu
            time.sleep(1)
            
            # Gửi yêu cầu tìm kiếm
            response = self.session.get(
                self.base_url,
                params=params,
                timeout=15
            )
            response.raise_for_status()
            
            # Xử lý kết quả JSONP trả về
            content = response.text
            if content.startswith('callback(') and content.endswith(')'):
                content = content[9:-1]  # Bỏ đi 'callback(' ở đầu và ')' ở cuối
            
            data = json.loads(content)
            results = []
            
            for doc in data.get('response', {}).get('docs', [])[:limit]:
                item_id = doc.get('identifier')
                if not item_id:
                    continue
                    
                results.append({
                    'id': item_id,
                    'title': doc.get('title', 'Không có tiêu đề'),
                    'creator': doc.get('creator', ['Không rõ'])[0] if isinstance(doc.get('creator'), list) else doc.get('creator', 'Không rõ'),
                    'year': doc.get('year', 'N/A'),
                    'description': doc.get('description', 'Không có mô tả'),
                    'publisher': doc.get('publisher', ['Không rõ'])[0] if isinstance(doc.get('publisher'), list) else doc.get('publisher', 'Không rõ'),
                    'language': doc.get('language', ['en'])[0] if isinstance(doc.get('language'), list) else doc.get('language', 'en'),
                    'downloads': doc.get('downloads', 0),
                    'publication_date': doc.get('publicdate', ''),
                    'url': f"https://archive.org/details/{item_id}",
                    'direct_download': f"https://archive.org/download/{item_id}"
                })
            
            return json.dumps({
                'status': 'success',
                'query': query,
                'total_results': data.get('response', {}).get('numFound', 0),
                'results': results,
                'timestamp': datetime.now().isoformat()
            }, indent=2, ensure_ascii=False)
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Lỗi kết nối đến Internet Archive: {str(e)}"
            logger.error(error_msg)
            return json.dumps({
                'status': 'error',
                'message': error_msg,
                'query': query,
                'results': []
            }, indent=2, ensure_ascii=False)
            
        except Exception as e:
            error_msg = f"Lỗi khi xử lý kết quả tìm kiếm: {str(e)}"
            logger.error(error_msg)
            return json.dumps({
                'status': 'error',
                'message': error_msg,
                'query': query,
                'results': []
            }, indent=2, ensure_ascii=False)
    
    def get_popular_items(self, days: int = 30, limit: int = 5) -> str:
        """
        Lấy danh sách các tài liệu phổ biến trong một khoảng thời gian.
        
        Args:
            days (int, optional): Số ngày gần đây. Mặc định là 30.
            limit (int, optional): Số lượng kết quả trả về. Mặc định là 5.
            
        Returns:
            str: Danh sách tài liệu phổ biến dưới dạng JSON
        """
        try:
            # Tìm kiếm các tài liệu được tải về nhiều nhất trong khoảng thời gian
            params = {
                'q': f'publicdate:[{days}day-ago TO NOW]',
                'fl[]': ['identifier', 'title', 'downloads', 'publicdate'],
                'rows': min(limit, 20),
                'sort[]': ['downloads desc'],
                'output': 'json',
                'fields': 'identifier,title,downloads,publicdate'
            }
            
            response = self.session.get(
                self.base_url,
                params=params,
                timeout=15
            )
            response.raise_for_status()
            
            content = response.text
            if content.startswith('callback(') and content.endswith(')'):
                content = content[9:-1]
                
            data = json.loads(content)
            
            results = []
            for doc in data.get('response', {}).get('docs', [])[:limit]:
                item_id = doc.get('identifier')
                if not item_id:
                    continue
                    
                results.append({
                    'id': item_id,
                    'title': doc.get('title', 'Không có tiêu đề'),
                    'downloads': doc.get('downloads', 0),
                    'publication_date': doc.get('publicdate', ''),
                    'url': f"https://archive.org/details/{item_id}"
                })
            
            return json.dumps({
                'status': 'success',
                'days': days,
                'results': results,
                'timestamp': datetime.now().isoformat()
            }, indent=2, ensure_ascii=False)
            
        except Exception as e:
            error_msg = f"Không thể lấy danh sách tài liệu phổ biến: {str(e)}"
            logger.error(error_msg)
            return json.dumps({
                'status': 'error',
                'message': error_msg,
                'results': []
            }, indent=2, ensure_ascii=False)
    
    def get_item_metadata(self, item_id: str) -> str:
        """
        Lấy thông tin chi tiết về một tài liệu cụ thể từ Internet Archive.
        
        Args:
            item_id (str): Định danh của tài liệu trên Internet Archive
            
        Returns:
            str: Thông tin chi tiết về tài liệu dưới dạng JSON
        """
        try:
            response = self.session.get(
                f"{self.details_url}{item_id}",
                timeout=10
            )
            response.raise_for_status()
            
            data = response.json()
            
            if 'error' in data:
                return json.dumps({
                    'status': 'error',
                    'message': data['error'],
                    'item_id': item_id
                }, indent=2, ensure_ascii=False)
            
            # Trích xuất thông tin hữu ích
            metadata = data.get('metadata', {})
            files = data.get('files', [])
            
            # Tìm các file có thể đọc trực tuyến hoặc tải về
            viewable_files = []
            for file_info in files:
                if file_info.get('format') in ['JPEG', 'PNG', 'GIF']:
                    continue  # Bỏ qua ảnh thumbnail
                    
                viewable_files.append({
                    'name': file_info.get('name', ''),
                    'format': file_info.get('format', ''),
                    'size': file_info.get('size', 0),
                    'url': f"https://archive.org/download/{item_id}/{file_info.get('name', '')}"
                })
            
            result = {
                'status': 'success',
                'item_id': item_id,
                'title': metadata.get('title', 'Không có tiêu đề'),
                'creator': metadata.get('creator', 'Không rõ'),
                'description': metadata.get('description', 'Không có mô tả'),
                'year': metadata.get('year', 'N/A'),
                'publisher': metadata.get('publisher', 'Không rõ'),
                'language': metadata.get('language', 'en'),
                'publication_date': metadata.get('publicdate', ''),
                'url': f"https://archive.org/details/{item_id}",
                'files': viewable_files,
                'timestamp': datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2, ensure_ascii=False)
            
        except Exception as e:
            error_msg = f"Không thể lấy thông tin tài liệu: {str(e)}"
            logger.error(error_msg)
            return json.dumps({
                'status': 'error',
                'message': error_msg,
                'item_id': item_id
            }, indent=2, ensure_ascii=False)
