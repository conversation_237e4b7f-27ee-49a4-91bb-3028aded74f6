"""
Reranker cho các chunk văn bản.
"""

import logging
import numpy as np
import hashlib
import json
import time
from functools import lru_cache
from typing import List, Dict, Any, Optional, Union, Callable, Tuple

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Cache cho các kết quả reranking
_cache = {}
_cache_hits = 0
_cache_misses = 0
_cache_lifetime = 3600  # Thời gian sống của cache (giây)

def generate_cache_key(chunks: List[Dict[str, Any]], query: str, strategy: str, **kwargs) -> str:
    """
    Tạo khóa cache dựa trên chunks, query và strategy.

    Args:
        chunks: Danh sách các chunk
        query: Truy vấn
        strategy: Chiến lược reranking
        **kwargs: <PERSON><PERSON><PERSON> tham số khác

    Returns:
        Khóa cache
    """
    # Tạo chuỗi đại diện cho chunks (chỉ sử dụng text và id)
    chunks_repr = []
    for chunk in chunks:
        chunk_id = chunk.get("id", "")
        chunk_text = chunk.get("text", "")[:100]  # Chỉ lấy 100 ký tự đầu tiên để giảm kích thước khóa
        chunks_repr.append(f"{chunk_id}:{chunk_text}")

    # Tạo chuỗi đại diện cho kwargs
    kwargs_repr = json.dumps(kwargs, sort_keys=True)

    # Tạo chuỗi đại diện cho toàn bộ input
    input_repr = f"{','.join(chunks_repr)}|{query}|{strategy}|{kwargs_repr}"

    # Tạo hash từ chuỗi đại diện
    return hashlib.md5(input_repr.encode()).hexdigest()

def get_from_cache(key: str) -> Optional[List[Dict[str, Any]]]:
    """
    Lấy kết quả từ cache.

    Args:
        key: Khóa cache

    Returns:
        Kết quả từ cache hoặc None nếu không tìm thấy
    """
    global _cache_hits, _cache_misses

    if key not in _cache:
        _cache_misses += 1
        return None

    # Kiểm tra thời gian sống
    cache_entry = _cache[key]
    if time.time() - cache_entry["timestamp"] > _cache_lifetime:
        # Cache đã hết hạn
        _cache.pop(key)
        _cache_misses += 1
        return None

    _cache_hits += 1
    return cache_entry["result"]

def add_to_cache(key: str, result: List[Dict[str, Any]]) -> None:
    """
    Thêm kết quả vào cache.

    Args:
        key: Khóa cache
        result: Kết quả cần lưu
    """
    _cache[key] = {
        "result": result,
        "timestamp": time.time()
    }

    # Giới hạn kích thước cache
    if len(_cache) > 1000:
        # Xóa các entry cũ nhất
        oldest_key = min(_cache.keys(), key=lambda k: _cache[k]["timestamp"])
        _cache.pop(oldest_key)

def get_cache_stats() -> Dict[str, Any]:
    """
    Lấy thống kê về cache.

    Returns:
        Thống kê về cache
    """
    return {
        "size": len(_cache),
        "hits": _cache_hits,
        "misses": _cache_misses,
        "hit_ratio": _cache_hits / (_cache_hits + _cache_misses) if (_cache_hits + _cache_misses) > 0 else 0
    }

def clear_cache() -> None:
    """
    Xóa toàn bộ cache.
    """
    global _cache, _cache_hits, _cache_misses
    _cache = {}
    _cache_hits = 0
    _cache_misses = 0

def get_reranker_strategy(strategy_name: str) -> Callable:
    """
    Lấy chiến lược reranking dựa trên tên.

    Args:
        strategy_name: Tên chiến lược reranking

    Returns:
        Hàm reranking tương ứng
    """
    strategies = {
        "relevance": relevance_reranker,
        "mmr": mmr_reranker,
        "hybrid": hybrid_reranker
    }

    if strategy_name not in strategies:
        logger.warning(f"Unknown reranking strategy: {strategy_name}. Using relevance reranker.")
        return relevance_reranker

    return strategies[strategy_name]

def rerank_chunks(chunks: List[Dict[str, Any]], query: str, strategy: str = "relevance", top_k: int = 5, use_cache: bool = True, **kwargs) -> List[Dict[str, Any]]:
    """
    Sắp xếp lại các chunk dựa trên độ liên quan với truy vấn.

    Args:
        chunks: Danh sách các chunk cần sắp xếp lại
        query: Truy vấn
        strategy: Chiến lược reranking (relevance, mmr, hybrid)
        top_k: Số lượng chunk trả về
        use_cache: Sử dụng cache hay không
        **kwargs: Các tham số khác cho chiến lược reranking

    Returns:
        Danh sách các chunk đã sắp xếp lại
    """
    # Nếu không có chunk, trả về danh sách rỗng
    if not chunks:
        return []

    # Nếu sử dụng cache
    if use_cache:
        # Tạo khóa cache
        cache_key = generate_cache_key(chunks, query, strategy, top_k=top_k, **kwargs)

        # Kiểm tra cache
        cached_result = get_from_cache(cache_key)
        if cached_result is not None:
            logger.debug(f"Cache hit for {strategy} reranking")
            return cached_result

    # Lấy hàm reranker
    reranker_func = get_reranker_strategy(strategy)

    # Thực hiện reranking
    result = reranker_func(chunks, query, top_k, **kwargs)

    # Lưu vào cache nếu cần
    if use_cache:
        add_to_cache(cache_key, result)

    return result

def calculate_relevance_scores(chunks: List[Dict[str, Any]], query: str) -> List[float]:
    """
    Tính toán điểm liên quan giữa các chunk và truy vấn.

    Args:
        chunks: Danh sách các chunk
        query: Truy vấn

    Returns:
        Danh sách điểm liên quan
    """
    try:
        # Sử dụng thư viện sentence-transformers nếu có
        from sentence_transformers import SentenceTransformer, util

        # Tải model
        model = SentenceTransformer('all-MiniLM-L6-v2')

        # Mã hóa truy vấn
        query_embedding = model.encode(query, convert_to_tensor=True)

        # Mã hóa các chunk
        chunk_texts = [chunk["text"] for chunk in chunks]
        chunk_embeddings = model.encode(chunk_texts, convert_to_tensor=True)

        # Tính toán độ tương đồng cosine
        similarities = util.cos_sim(query_embedding, chunk_embeddings)[0].tolist()

        return similarities
    except ImportError:
        logger.warning("sentence-transformers not installed. Using simple keyword matching.")

        # Sử dụng phương pháp đơn giản: đếm số từ khóa
        query_terms = set(query.lower().split())
        scores = []

        for chunk in chunks:
            text = chunk["text"].lower()
            score = sum(1 for term in query_terms if term in text) / len(query_terms) if query_terms else 0
            scores.append(score)

        return scores

def relevance_reranker(chunks: List[Dict[str, Any]], query: str, top_k: int = 5, **kwargs) -> List[Dict[str, Any]]:
    """
    Sắp xếp lại các chunk dựa trên độ liên quan với truy vấn.

    Args:
        chunks: Danh sách các chunk cần sắp xếp lại
        query: Truy vấn
        top_k: Số lượng chunk trả về
        **kwargs: Các tham số khác

    Returns:
        Danh sách các chunk đã sắp xếp lại
    """
    if not chunks:
        return []

    # Tính toán điểm liên quan
    relevance_scores = calculate_relevance_scores(chunks, query)

    # Gán điểm cho các chunk
    for i, chunk in enumerate(chunks):
        chunk["relevance_score"] = relevance_scores[i]

    # Sắp xếp các chunk theo điểm liên quan
    sorted_chunks = sorted(chunks, key=lambda x: x["relevance_score"], reverse=True)

    # Trả về top_k chunk
    return sorted_chunks[:top_k]

def mmr_reranker(chunks: List[Dict[str, Any]], query: str, top_k: int = 5, lambda_param: float = 0.5, **kwargs) -> List[Dict[str, Any]]:
    """
    Sắp xếp lại các chunk sử dụng Maximal Marginal Relevance (MMR).

    Args:
        chunks: Danh sách các chunk cần sắp xếp lại
        query: Truy vấn
        top_k: Số lượng chunk trả về
        lambda_param: Tham số lambda cho MMR (0-1)
        **kwargs: Các tham số khác

    Returns:
        Danh sách các chunk đã sắp xếp lại
    """
    if not chunks:
        return []

    # Tính toán điểm liên quan
    relevance_scores = calculate_relevance_scores(chunks, query)

    # Gán điểm cho các chunk
    for i, chunk in enumerate(chunks):
        chunk["relevance_score"] = relevance_scores[i]

    # Tính toán ma trận tương đồng giữa các chunk
    try:
        from sentence_transformers import SentenceTransformer, util

        # Tải model
        model = SentenceTransformer('all-MiniLM-L6-v2')

        # Mã hóa các chunk
        chunk_texts = [chunk["text"] for chunk in chunks]
        chunk_embeddings = model.encode(chunk_texts, convert_to_tensor=True)

        # Tính toán ma trận tương đồng
        similarity_matrix = util.cos_sim(chunk_embeddings, chunk_embeddings).numpy()
    except ImportError:
        logger.warning("sentence-transformers not installed. Using simple similarity matrix.")

        # Tạo ma trận tương đồng đơn giản
        n = len(chunks)
        similarity_matrix = np.zeros((n, n))

        for i in range(n):
            for j in range(n):
                if i == j:
                    similarity_matrix[i][j] = 1.0
                else:
                    # Tính tương đồng đơn giản dựa trên từ chung
                    words_i = set(chunks[i]["text"].lower().split())
                    words_j = set(chunks[j]["text"].lower().split())
                    common_words = words_i.intersection(words_j)
                    similarity_matrix[i][j] = len(common_words) / max(len(words_i), len(words_j)) if max(len(words_i), len(words_j)) > 0 else 0    # Thực hiện MMR
    remaining_chunks = chunks.copy()
    selected_chunks = []
    selected_indices = []

    for _ in range(min(top_k, len(chunks))):
        if not remaining_chunks:
            break

        # Tính điểm MMR cho các chunk còn lại
        mmr_scores = []

        for i, chunk in enumerate(remaining_chunks):
            if not selected_indices:
                # Nếu chưa có chunk nào được chọn, sử dụng điểm liên quan
                mmr_scores.append(chunk["relevance_score"])
            else:
                # Tính điểm MMR
                relevance = chunk["relevance_score"]

                # Tìm chỉ số gốc của chunk hiện tại
                original_index = chunks.index(chunk)

                # Tính độ đa dạng (1 - max similarity với các chunk đã chọn)
                max_similarity = max(similarity_matrix[original_index][idx] for idx in selected_indices)
                diversity = 1 - max_similarity

                # Tính điểm MMR
                mmr = lambda_param * relevance + (1 - lambda_param) * diversity
                mmr_scores.append(mmr)

        # Chọn chunk có điểm MMR cao nhất
        best_index = np.argmax(mmr_scores)
        selected_chunk = remaining_chunks[best_index]
        selected_chunks.append(selected_chunk)

        # Lưu chỉ số gốc của chunk đã chọn
        original_index = chunks.index(selected_chunk)
        selected_indices.append(original_index)

        # Loại bỏ chunk đã chọn
        remaining_chunks.pop(best_index)

    return selected_chunks

def hybrid_reranker(chunks: List[Dict[str, Any]], query: str, top_k: int = 5, relevance_weight: float = 0.7, **kwargs) -> List[Dict[str, Any]]:
    """
    Sắp xếp lại các chunk sử dụng kết hợp nhiều chiến lược.

    Args:
        chunks: Danh sách các chunk cần sắp xếp lại
        query: Truy vấn
        top_k: Số lượng chunk trả về
        relevance_weight: Trọng số cho điểm liên quan (0-1)
        **kwargs: Các tham số khác

    Returns:
        Danh sách các chunk đã sắp xếp lại
    """
    if not chunks:
        return []

    # Lấy top_k*2 chunk dựa trên độ liên quan
    relevance_chunks = relevance_reranker(chunks, query, top_k=min(top_k*2, len(chunks)))

    # Sắp xếp lại sử dụng MMR
    mmr_chunks = mmr_reranker(relevance_chunks, query, top_k=top_k, lambda_param=relevance_weight)

    return mmr_chunks