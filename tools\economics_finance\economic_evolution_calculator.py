# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math
from datetime import datetime

class EconomicEvolutionCalculator(Toolkit):
    """
    Economic Evolution Calculator cho tính toán economic system evolution, market development và financial innovation.
    """

    def __init__(self, enable_calculations: bool = True, **kwargs):
        super().__init__(
            name="economic_evolution_calculator",
            **kwargs
        )
        
        # Economic system evolution stages
        self.economic_stages = {
            "subsistence": {"complexity": 1, "trade_ratio": 0.1, "specialization": 0.2},
            "agricultural": {"complexity": 3, "trade_ratio": 0.3, "specialization": 0.4},
            "mercantile": {"complexity": 6, "trade_ratio": 0.6, "specialization": 0.6},
            "industrial": {"complexity": 15, "trade_ratio": 0.8, "specialization": 0.8},
            "service": {"complexity": 25, "trade_ratio": 0.9, "specialization": 0.9},
            "digital": {"complexity": 40, "trade_ratio": 0.95, "specialization": 0.95}
        }
        
        # Market evolution rates (per decade)
        self.evolution_rates = {
            "financial_innovation": 0.3,
            "market_integration": 0.2,
            "institutional_development": 0.15,
            "technological_adoption": 0.4,
            "regulatory_evolution": 0.1,
            "globalization": 0.25
        }
        
        # Economic complexity indicators
        self.complexity_indicators = {
            "product_diversity": 0.3,
            "export_sophistication": 0.25,
            "institutional_quality": 0.2,
            "financial_depth": 0.15,
            "innovation_capacity": 0.1
        }
        
        if enable_calculations:
            self.register(self.calculate_economic_system_divergence)
            self.register(self.estimate_market_evolution)
            self.register(self.analyze_financial_innovation)
            self.register(self.predict_economic_trends)

    def calculate_economic_system_divergence(self, system1: str, system2: str,
                                           divergence_years: int = None,
                                           evolution_aspect: str = "institutional") -> str:
        """
        Tính toán economic system divergence và institutional evolution.
        
        Args:
            system1: Hệ thống kinh tế thứ nhất
            system2: Hệ thống kinh tế thứ hai
            divergence_years: Thời gian divergence (years)
            evolution_aspect: Khía cạnh evolution
            
        Returns:
            Chuỗi JSON chứa tính toán economic system divergence
        """
        log_debug(f"Calculating economic system divergence between {system1} and {system2}")
        
        try:
            if divergence_years is None:
                divergence_years = 50  # Default 50 years
            
            # Get system parameters
            system1_params = self.economic_stages.get(system1, self.economic_stages["industrial"])
            system2_params = self.economic_stages.get(system2, self.economic_stages["service"])
            
            # Calculate divergence metrics
            evolution_rate = self.evolution_rates.get(f"{evolution_aspect}_evolution", 0.2)
            divergence_index = 1 - math.exp(-evolution_rate * (divergence_years / 10))
            
            # Economic complexity analysis
            complexity_gap = abs(system2_params["complexity"] - system1_params["complexity"])
            institutional_distance = complexity_gap * 0.1
            
            # Trade and specialization evolution
            trade_evolution = {
                "trade_integration": abs(system2_params["trade_ratio"] - system1_params["trade_ratio"]),
                "specialization_gap": abs(system2_params["specialization"] - system1_params["specialization"]),
                "comparative_advantage": "Developed" if complexity_gap > 10 else "Emerging"
            }
            
            # Institutional convergence/divergence
            institutional_analysis = {
                "convergence_pressure": max(0, 1 - (divergence_years * 0.01)),
                "path_dependency": divergence_years * 0.02,
                "institutional_lock_in": "High" if divergence_years > 100 else "Moderate",
                "reform_potential": "Limited" if institutional_distance > 2 else "Significant"
            }
            
            # Economic performance indicators
            performance_metrics = {
                "productivity_gap": round(complexity_gap * 0.05, 3),
                "income_divergence": round(complexity_gap * 0.1, 3),
                "innovation_differential": round(complexity_gap * 0.03, 3),
                "competitiveness_gap": round(institutional_distance, 3)
            }
            
            # Globalization effects
            globalization_impact = {
                "market_integration": min(1.0, divergence_years * 0.01),
                "technology_transfer": "High" if complexity_gap < 15 else "Limited",
                "capital_flows": "Significant" if institutional_distance < 1.5 else "Restricted",
                "knowledge_spillovers": "Active" if divergence_years < 30 else "Minimal"
            }
            
            result = {
                "system_comparison": {
                    "system1": system1,
                    "system2": system2,
                    "divergence_years": divergence_years,
                    "evolution_aspect": evolution_aspect
                },
                "divergence_analysis": {
                    "divergence_index": round(divergence_index, 3),
                    "complexity_gap": complexity_gap,
                    "institutional_distance": round(institutional_distance, 3),
                    "evolution_rate": evolution_rate
                },
                "trade_specialization": trade_evolution,
                "institutional_analysis": institutional_analysis,
                "performance_metrics": performance_metrics,
                "globalization_impact": globalization_impact,
                "convergence_potential": self._assess_convergence_potential(complexity_gap, divergence_years),
                "policy_implications": self._generate_policy_implications(system1, system2, complexity_gap),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error calculating economic system divergence: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate economic system divergence: {str(e)}"
            }, indent=4)

    def estimate_market_evolution(self, market_type: str, evolution_period_years: int = 30,
                                development_stage: str = "emerging") -> str:
        """
        Ước tính market evolution và financial development.
        """
        log_debug(f"Estimating market evolution for {market_type} over {evolution_period_years} years")
        
        try:
            # Market development parameters
            stage_params = {
                "emerging": {"liquidity": 0.3, "efficiency": 0.4, "regulation": 0.5},
                "developing": {"liquidity": 0.6, "efficiency": 0.6, "regulation": 0.7},
                "developed": {"liquidity": 0.9, "efficiency": 0.8, "regulation": 0.9},
                "mature": {"liquidity": 0.95, "efficiency": 0.9, "regulation": 0.95}
            }
            
            current_params = stage_params.get(development_stage, stage_params["emerging"])
            
            # Calculate evolution trajectory
            development_rate = 0.05  # 5% improvement per year
            final_development = min(1.0, current_params["efficiency"] + (development_rate * evolution_period_years))
            
            # Market evolution phases
            evolution_phases = []
            phases_count = max(3, evolution_period_years // 10)
            
            for phase in range(phases_count):
                phase_years = (phase + 1) * (evolution_period_years / phases_count)
                phase_development = min(1.0, current_params["efficiency"] + (development_rate * phase_years))
                
                evolution_phases.append({
                    "phase": phase + 1,
                    "years": int(phase_years),
                    "development_level": round(phase_development, 2),
                    "key_developments": self._generate_market_developments(market_type, phase),
                    "infrastructure_needs": self._identify_infrastructure_needs(phase_development)
                })
            
            # Financial innovation timeline
            innovation_timeline = {
                "digital_payments": "Implemented" if evolution_period_years > 5 else "Developing",
                "algorithmic_trading": "Advanced" if evolution_period_years > 15 else "Basic",
                "blockchain_integration": "Mature" if evolution_period_years > 20 else "Experimental",
                "ai_risk_management": "Sophisticated" if evolution_period_years > 25 else "Emerging"
            }
            
            # Regulatory evolution
            regulatory_development = {
                "framework_maturity": min(1.0, current_params["regulation"] + (0.03 * evolution_period_years)),
                "international_harmonization": "High" if evolution_period_years > 20 else "Moderate",
                "consumer_protection": "Comprehensive" if final_development > 0.8 else "Basic",
                "systemic_risk_management": "Advanced" if evolution_period_years > 25 else "Developing"
            }
            
            result = {
                "market_evolution": {
                    "market_type": market_type,
                    "evolution_period_years": evolution_period_years,
                    "starting_stage": development_stage,
                    "final_development_level": round(final_development, 2)
                },
                "development_trajectory": {
                    "development_rate": development_rate,
                    "efficiency_improvement": round(final_development - current_params["efficiency"], 2),
                    "liquidity_enhancement": round(min(1.0, current_params["liquidity"] + 0.02 * evolution_period_years), 2)
                },
                "evolution_phases": evolution_phases,
                "innovation_timeline": innovation_timeline,
                "regulatory_development": regulatory_development,
                "market_structure_changes": self._analyze_market_structure_changes(market_type, evolution_period_years),
                "competitive_dynamics": self._assess_competitive_dynamics(final_development),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error estimating market evolution: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to estimate market evolution: {str(e)}"
            }, indent=4)

    def analyze_financial_innovation(self, innovation_type: str, adoption_years: int = 15,
                                   market_context: str = "developed") -> str:
        """
        Phân tích financial innovation diffusion và market impact.
        """
        log_debug(f"Analyzing financial innovation: {innovation_type}")
        
        try:
            # Innovation adoption curve
            adoption_phases = {
                "introduction": {"duration_ratio": 0.2, "adopters": 0.025, "market_share": 0.01},
                "early_growth": {"duration_ratio": 0.3, "adopters": 0.135, "market_share": 0.05},
                "rapid_growth": {"duration_ratio": 0.3, "adopters": 0.34, "market_share": 0.25},
                "maturity": {"duration_ratio": 0.2, "adopters": 0.5, "market_share": 0.7}
            }
            
            # Calculate adoption timeline
            adoption_timeline = []
            for phase_name, phase_data in adoption_phases.items():
                phase_duration = int(adoption_years * phase_data["duration_ratio"])
                adoption_timeline.append({
                    "phase": phase_name.replace("_", " ").title(),
                    "duration_years": phase_duration,
                    "adopter_percentage": round(phase_data["adopters"] * 100, 1),
                    "market_share": round(phase_data["market_share"] * 100, 1),
                    "key_characteristics": self._get_adoption_characteristics(phase_name)
                })
            
            # Innovation impact analysis
            impact_analysis = {
                "efficiency_gains": round(adoption_years * 0.02, 2),
                "cost_reduction": round(adoption_years * 0.015, 2),
                "market_expansion": round(adoption_years * 0.03, 2),
                "risk_profile_change": "Improved" if adoption_years > 10 else "Mixed"
            }
            
            # Regulatory response
            regulatory_response = {
                "initial_reaction": "Cautious observation",
                "adaptation_timeline": f"{max(2, adoption_years // 3)} years",
                "regulatory_framework": "Comprehensive" if adoption_years > 12 else "Basic",
                "international_coordination": "High" if market_context == "developed" else "Limited"
            }
            
            # Market disruption assessment
            disruption_assessment = {
                "incumbent_response": "Adaptive integration" if adoption_years > 8 else "Resistance",
                "new_entrant_advantage": "Significant" if adoption_years < 10 else "Moderate",
                "market_concentration": "Decreased" if adoption_years > 12 else "Increased",
                "consumer_benefits": "Substantial" if adoption_years > 10 else "Emerging"
            }
            
            result = {
                "innovation_analysis": {
                    "innovation_type": innovation_type,
                    "adoption_period_years": adoption_years,
                    "market_context": market_context,
                    "innovation_maturity": "Mature" if adoption_years > 12 else "Developing"
                },
                "adoption_timeline": adoption_timeline,
                "impact_analysis": impact_analysis,
                "regulatory_response": regulatory_response,
                "disruption_assessment": disruption_assessment,
                "success_factors": self._identify_success_factors(innovation_type, market_context),
                "future_evolution": self._predict_innovation_evolution(innovation_type, adoption_years),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error analyzing financial innovation: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze financial innovation: {str(e)}"
            }, indent=4)

    def predict_economic_trends(self, economic_indicators: List[str], 
                              prediction_years: int = 20,
                              scenario: str = "baseline") -> str:
        """
        Dự đoán economic trends và structural changes.
        """
        log_debug(f"Predicting economic trends over {prediction_years} years")
        
        try:
            # Scenario parameters
            scenario_params = {
                "optimistic": {"growth_rate": 0.04, "innovation_rate": 0.06, "stability": 0.8},
                "baseline": {"growth_rate": 0.025, "innovation_rate": 0.04, "stability": 0.7},
                "pessimistic": {"growth_rate": 0.015, "innovation_rate": 0.02, "stability": 0.5}
            }
            
            params = scenario_params.get(scenario, scenario_params["baseline"])
            
            # Trend predictions by category
            trend_predictions = {
                "digitalization": {
                    "probability": 0.95,
                    "timeline": f"{prediction_years // 4} years to mainstream",
                    "impact_areas": ["Banking", "Payments", "Investment", "Insurance"],
                    "disruption_level": "High"
                },
                "sustainability_finance": {
                    "probability": 0.85,
                    "timeline": f"{prediction_years // 3} years to integration",
                    "impact_areas": ["ESG investing", "Green bonds", "Carbon markets"],
                    "disruption_level": "Moderate"
                },
                "decentralized_finance": {
                    "probability": 0.7,
                    "timeline": f"{prediction_years // 2} years to adoption",
                    "impact_areas": ["Lending", "Trading", "Asset management"],
                    "disruption_level": "High"
                },
                "ai_automation": {
                    "probability": 0.9,
                    "timeline": f"{prediction_years // 5} years to implementation",
                    "impact_areas": ["Risk management", "Trading", "Customer service"],
                    "disruption_level": "Very High"
                }
            }
            
            # Economic structure evolution
            structural_changes = {
                "service_economy_dominance": round(0.7 + (prediction_years * 0.01), 2),
                "digital_economy_share": round(0.3 + (prediction_years * 0.02), 2),
                "gig_economy_penetration": round(0.2 + (prediction_years * 0.015), 2),
                "automation_impact": f"{min(50, prediction_years * 2)}% of jobs affected"
            }
            
            # Policy evolution
            policy_evolution = {
                "monetary_policy": "Digital currency integration",
                "fiscal_policy": "Automated tax systems",
                "regulatory_approach": "Adaptive and real-time",
                "international_coordination": "Enhanced through technology"
            }
            
            result = {
                "prediction_parameters": {
                    "economic_indicators": economic_indicators,
                    "prediction_years": prediction_years,
                    "scenario": scenario,
                    "growth_assumption": params["growth_rate"]
                },
                "trend_predictions": trend_predictions,
                "structural_changes": structural_changes,
                "policy_evolution": policy_evolution,
                "risk_factors": [
                    "Technological disruption pace",
                    "Regulatory adaptation speed",
                    "Geopolitical stability",
                    "Climate change impacts"
                ],
                "opportunity_areas": self._identify_opportunity_areas(prediction_years),
                "strategic_implications": self._generate_strategic_implications(scenario, prediction_years),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error predicting economic trends: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to predict economic trends: {str(e)}"
            }, indent=4)

    # Helper methods
    def _assess_convergence_potential(self, complexity_gap: float, years: int) -> Dict[str, str]:
        """Assess economic convergence potential."""
        if complexity_gap < 5 and years < 30:
            return {"potential": "High", "timeline": "1-2 decades"}
        elif complexity_gap < 15 and years < 50:
            return {"potential": "Moderate", "timeline": "3-5 decades"}
        else:
            return {"potential": "Low", "timeline": "Multiple generations"}

    def _generate_policy_implications(self, system1: str, system2: str, gap: float) -> List[str]:
        """Generate policy implications for economic systems."""
        implications = ["Institutional capacity building", "Trade facilitation"]
        if gap > 10:
            implications.extend(["Technology transfer programs", "Education system reform"])
        return implications

    def _generate_market_developments(self, market_type: str, phase: int) -> List[str]:
        """Generate market developments for evolution phase."""
        developments = ["Infrastructure improvement", "Regulatory framework"]
        if phase > 1:
            developments.extend(["Technology adoption", "International integration"])
        return developments

    def _identify_infrastructure_needs(self, development_level: float) -> List[str]:
        """Identify infrastructure needs based on development level."""
        needs = ["Basic trading systems", "Settlement infrastructure"]
        if development_level > 0.6:
            needs.extend(["Advanced analytics", "Risk management systems"])
        return needs

    def _analyze_market_structure_changes(self, market_type: str, years: int) -> Dict[str, str]:
        """Analyze market structure changes."""
        return {
            "concentration": "Decreasing" if years > 15 else "Stable",
            "competition": "Intensifying" if years > 10 else "Moderate",
            "barriers_to_entry": "Lowering" if years > 20 else "Stable"
        }

    def _assess_competitive_dynamics(self, development: float) -> Dict[str, str]:
        """Assess competitive dynamics."""
        return {
            "competition_intensity": "High" if development > 0.8 else "Moderate",
            "innovation_pressure": "Strong" if development > 0.7 else "Moderate",
            "market_efficiency": "High" if development > 0.85 else "Developing"
        }

    def _get_adoption_characteristics(self, phase: str) -> List[str]:
        """Get characteristics for adoption phase."""
        characteristics_map = {
            "introduction": ["High uncertainty", "Limited adoption", "Experimental use"],
            "early_growth": ["Proof of concept", "Early adopters", "Initial scaling"],
            "rapid_growth": ["Mainstream adoption", "Network effects", "Competitive response"],
            "maturity": ["Market saturation", "Optimization focus", "Regulatory clarity"]
        }
        return characteristics_map.get(phase, ["General characteristics"])

    def _identify_success_factors(self, innovation: str, context: str) -> List[str]:
        """Identify success factors for financial innovation."""
        return [
            "Regulatory support",
            "Market readiness",
            "Technology infrastructure",
            "Consumer acceptance",
            "Industry collaboration"
        ]

    def _predict_innovation_evolution(self, innovation: str, years: int) -> Dict[str, str]:
        """Predict future evolution of financial innovation."""
        return {
            "next_generation": "Enhanced capabilities" if years > 10 else "Incremental improvements",
            "market_position": "Dominant" if years > 15 else "Competitive",
            "regulatory_status": "Fully integrated" if years > 12 else "Evolving framework"
        }

    def _identify_opportunity_areas(self, years: int) -> List[str]:
        """Identify economic opportunity areas."""
        opportunities = ["Digital transformation", "Sustainable finance"]
        if years > 15:
            opportunities.extend(["Space economy", "Quantum computing applications"])
        return opportunities

    def _generate_strategic_implications(self, scenario: str, years: int) -> List[str]:
        """Generate strategic implications for economic predictions."""
        implications = ["Invest in digital capabilities", "Develop adaptive strategies"]
        if scenario == "optimistic" and years > 15:
            implications.extend(["Expand into emerging markets", "Lead innovation initiatives"])
        return implications
