from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import re

class WikidataSpeciesTool(Toolkit):
    """
    Wikidata/Wikispecies Tool for searching taxonomic metadata.
    """
    
    def __init__(self):
        super().__init__(
            name="Wikidata Species Search Tool",
            tools=[self.search_wikidata_species]
        )

    async def search_wikidata_species(self, query: str) -> Dict[str, Any]:
        """
        Search Wikidata and Wikispecies for taxonomic information.
        
        Parameters:
        - query: Scientific name to search (e.g., "Panthera leo", "Quercus alba")
        
        Returns:
        - JSON with search results including taxonomic information, relationships, and images
        """
        logger.info(f"Searching Wikidata/Wikispecies for: {query}")
        
        try:
            # First, search Wikidata for the entity
            # We'll use the Wikidata search API to find the item
            search_url = "https://www.wikidata.org/w/api.php"
            search_params = {
                "action": "wbsearchentities",
                "format": "json",
                "language": "en",
                "search": query,
                "type": "item",
                "limit": 5
            }
            
            search_response = requests.get(search_url, params=search_params)
            
            if search_response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikidata/Wikispecies",
                    "message": f"Search API returned status code {search_response.status_code}",
                    "query": query
                }
            
            search_data = search_response.json()
            search_results = search_data.get("search", [])
            
            if not search_results:
                return {
                    "status": "error",
                    "source": "Wikidata/Wikispecies",
                    "message": "No results found",
                    "query": query
                }
            
            # Find the best match (prioritize items that look like species)
            entity_id = None
            for result in search_results:
                # Check for a scientific name pattern (Genus species)
                if re.match(r'^[A-Z][a-z]+ [a-z]+$', result.get("label", "")):
                    entity_id = result.get("id")
                    break
            
            # If no species-like match found, use the first result
            if not entity_id and search_results:
                entity_id = search_results[0].get("id")
            
            if not entity_id:
                return {
                    "status": "error",
                    "source": "Wikidata/Wikispecies",
                    "message": "Could not find a valid entity ID",
                    "query": query
                }
            
            # Now query data for this entity using SPARQL
            sparql_url = "https://query.wikidata.org/sparql"
            
            # SPARQL query to get taxonomic information
            # This query gets taxonomic ranks, parent taxa, common names, images, and conservation status
            sparql_query = f'''
            SELECT ?item ?itemLabel ?rank ?rankLabel ?parent ?parentLabel ?image ?iucn ?iucnLabel ?commonName 
                   ?kingdom ?kingdomLabel ?phylum ?phylumLabel ?class ?classLabel
                   ?order ?orderLabel ?family ?familyLabel ?genus ?genusLabel
                   ?wikispeciesURL ?wikipediaURL
            WHERE {{
              BIND(wd:{entity_id} AS ?item)
              OPTIONAL {{ ?item wdt:P171 ?parent. }}  # parent taxon
              OPTIONAL {{ ?item wdt:P105 ?rank. }}    # taxonomic rank
              OPTIONAL {{ ?item wdt:P18 ?image. }}    # image
              OPTIONAL {{ ?item wdt:P141 ?iucn. }}    # conservation status
              
              # Get taxonomy
              OPTIONAL {{ ?item wdt:P105 ?taxonRank. }}
              OPTIONAL {{ ?item wdt:P171*/wdt:P105 wd:Q36732. ?item wdt:P171* ?kingdom. ?kingdom wdt:P105 wd:Q36732. }}  # kingdom
              OPTIONAL {{ ?item wdt:P171*/wdt:P105 wd:Q37517. ?item wdt:P171* ?phylum. ?phylum wdt:P105 wd:Q37517. }}    # phylum
              OPTIONAL {{ ?item wdt:P171*/wdt:P105 wd:Q37525. ?item wdt:P171* ?class. ?class wdt:P105 wd:Q37525. }}       # class
              OPTIONAL {{ ?item wdt:P171*/wdt:P105 wd:Q36602. ?item wdt:P171* ?order. ?order wdt:P105 wd:Q36602. }}       # order
              OPTIONAL {{ ?item wdt:P171*/wdt:P105 wd:Q35409. ?item wdt:P171* ?family. ?family wdt:P105 wd:Q35409. }}     # family
              OPTIONAL {{ ?item wdt:P171*/wdt:P105 wd:Q34740. ?item wdt:P171* ?genus. ?genus wdt:P105 wd:Q34740. }}       # genus
              
              # Get common names (English)
              OPTIONAL {{ ?item rdfs:label ?commonName. FILTER(LANG(?commonName) = "en") }}
              
              # Get URLs for Wikispecies and Wikipedia
              OPTIONAL {{ ?wikispeciesURL schema:about ?item; schema:isPartOf <https://species.wikimedia.org/>. }}
              OPTIONAL {{ ?wikipediaURL schema:about ?item; schema:isPartOf <https://en.wikipedia.org/>. }}
              
              SERVICE wikibase:label {{ bd:serviceParam wikibase:language "en". }}
            }}
            '''
            
            sparql_params = {
                "query": sparql_query,
                "format": "json"
            }
            
            sparql_headers = {
                "Accept": "application/sparql-results+json",
                "User-Agent": "TaxonomySearchBot/1.0"
            }
            
            sparql_response = requests.get(sparql_url, params=sparql_params, headers=sparql_headers)
            
            if sparql_response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikidata/Wikispecies",
                    "message": f"SPARQL query failed with status code {sparql_response.status_code}",
                    "query": query
                }
            
            sparql_data = sparql_response.json()
            results = sparql_data.get("results", {}).get("bindings", [])
            
            if not results:
                return {
                    "status": "error",
                    "source": "Wikidata/Wikispecies",
                    "message": "No detailed results found",
                    "query": query
                }
            
            # Process the results
            # Extract the first result for basic information (should all be the same item)
            result = results[0]
            
            # Basic info
            item_label = result.get("itemLabel", {}).get("value") if "itemLabel" in result else None
            rank = result.get("rankLabel", {}).get("value") if "rankLabel" in result else None
            
            # Images
            image_url = None
            if "image" in result:
                image_value = result["image"]["value"]
                # Convert Wikimedia Commons file path to URL
                if "File:" in image_value:
                    file_name = image_value.split("File:")[1].replace(" ", "_")
                    # Calculate MD5 hash prefix for the Commons URL structure
                    import hashlib
                    md5_hash = hashlib.md5(file_name.encode('utf-8')).hexdigest()
                    image_url = f"https://upload.wikimedia.org/wikipedia/commons/{md5_hash[0]}/{md5_hash[0:2]}/{file_name}"
            
            # Conservation status
            conservation_status = result.get("iucnLabel", {}).get("value") if "iucnLabel" in result else None
            
            # Common name
            common_name = result.get("commonName", {}).get("value") if "commonName" in result else None
            
            # Taxonomy
            taxonomy = {}
            for level in ["kingdom", "phylum", "class", "order", "family", "genus"]:
                level_label = f"{level}Label"
                if level_label in result and result[level_label].get("value"):
                    taxonomy[level] = result[level_label]["value"]
            
            # URLs
            wikispecies_url = result.get("wikispeciesURL", {}).get("value") if "wikispeciesURL" in result else None
            wikipedia_url = result.get("wikipediaURL", {}).get("value") if "wikipediaURL" in result else None
            
            # Parent taxon
            parent_taxon = result.get("parentLabel", {}).get("value") if "parentLabel" in result else None
            
            # Combine all data
            species_data = {
                "scientific_name": item_label,
                "common_name": common_name,
                "taxonomic_rank": rank,
                "taxonomy": taxonomy,
                "parent_taxon": parent_taxon,
                "conservation_status": conservation_status,
                "image_url": image_url,
                "wikidata_id": entity_id,
                "wikispecies_url": wikispecies_url,
                "wikipedia_url": wikipedia_url
            }
            
            return {
                "status": "success",
                "source": "Wikidata/Wikispecies",
                "query": query,
                "results": species_data
            }
        
        except Exception as e:
            log_debug(f"Error searching Wikidata/Wikispecies: {str(e)}")
            return {
                "status": "error",
                "source": "Wikidata/Wikispecies",
                "message": str(e),
                "query": query
            }