import requests
import json
import time
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
from bs4 import BeautifulSoup
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class CNETTools(Toolkit):
    """
    Công cụ tìm kiếm và truy xuất thông tin đánh giá sản phẩm công nghệ từ CNET.
    
    Cung cấp quyền truy cập vào các bài đ<PERSON>h giá, tin tức công nghệ và hướng dẫn từ CNET.
    
    Keyword gợi ý: "điện thoại mới nhất", "đánh giá laptop", "so sánh TV 4K",
    "hướng dẫn công nghệ", "tin tức AI", "đánh giá sản phẩm Apple/Samsung"
    """
    
    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(name="cnet_tools", **kwargs)
        self.base_url = "https://www.cnet.com"
        self.search_url = f"{self.base_url}/search/"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        if enable_search:
            self.register(self.search_articles)
            self.register(self.get_latest_reviews)
    
    def search_articles(self, query: str, max_results: int = 5) -> str:
        """
        Tìm kiếm bài viết trên CNET.
        
        Args:
            query (str): Từ khóa tìm kiếm (ví dụ: "đánh giá iPhone 15", "tin tức AI")
            max_results (int, optional): Số lượng kết quả tối đa. Mặc định: 5.
            
        Returns:
            str: Chuỗi JSON chứa kết quả tìm kiếm
            
        Ví dụ:
            search_articles("đánh giá Samsung Galaxy S24", 3)
        """
        log_debug(f"Tìm kiếm trên CNET: {query}")
        
        try:
            params = {
                "query": query,
                "sort": "mostRelevant"
            }
            
            response = requests.get(
                self.search_url,
                params=params,
                headers=self.headers,
                timeout=15
            )
            response.raise_for_status()
            
            # Phân tích kết quả HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # Lấy các kết quả tìm kiếm
            search_results = soup.select('.item')
            
            for result in search_results[:max_results]:
                title_elem = result.select_one('h3 a')
                if not title_elem:
                    continue
                    
                title = title_elem.get_text(strip=True)
                url = title_elem.get('href', '')
                if url and not url.startswith('http'):
                    url = f"{self.base_url}{url}"
                
                # Lấy mô tả nếu có
                desc_elem = result.select_one('.c-heading-6')
                description = desc_elem.get_text(strip=True) if desc_elem else ""
                
                # Lấy ngày đăng
                date_elem = result.select_one('time')
                date = date_elem.get('datetime') if date_elem else ""
                
                results.append({
                    "title": title,
                    "url": url,
                    "summary": description,
                    "date": date,
                    "source": "CNET"
                })
            
            # Nếu không có kết quả, trả về kết quả mặc định
            if not results:
                return self._get_default_results(query)
            
            return json.dumps({
                "status": "success",
                "source": "CNET",
                "query": query,
                "results": results,
                "result_count": len(results),
                "search_url": response.url
            }, indent=2, ensure_ascii=False)
            
        except requests.RequestException as e:
            logger.error(f"Lỗi khi truy vấn CNET: {e}")
            return self._get_error_response(query, str(e))
    
    def get_latest_reviews(self, category: str = "all", limit: int = 5) -> str:
        """
        Lấy các bài đánh giá mới nhất từ CNET theo danh mục.
        
        Args:
            category (str): Danh mục cần lấy (all, phones, laptops, tvs, etc.)
            limit (int): Số lượng kết quả tối đa
            
        Returns:
            str: Chuỗi JSON chứa danh sách bài đánh giá
        """
        log_debug(f"Lấy bài đánh giá mới nhất từ CNET - Danh mục: {category}")
        
        try:
            category_map = {
                "all": "reviews",
                "phones": "mobile/reviews",
                "laptops": "computing/laptops/reviews",
                "tvs": "home-theatre/tvs/reviews",
                "headphones": "mobile/headphones/reviews"
            }
            
            category_path = category_map.get(category.lower(), "reviews")
            url = f"{self.base_url}/{category_path}/"
            
            response = requests.get(url, headers=self.headers, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            articles = soup.select('.item')
            for article in articles[:limit]:
                title_elem = article.select_one('h3 a')
                if not title_elem:
                    continue
                    
                title = title_elem.get_text(strip=True)
                url = title_elem.get('href', '')
                if url and not url.startswith('http'):
                    url = f"{self.base_url}{url}"
                
                desc_elem = article.select_one('p')
                description = desc_elem.get_text(strip=True) if desc_elem else ""
                
                results.append({
                    "title": title,
                    "url": url,
                    "summary": description,
                    "category": category,
                    "source": "CNET"
                })
            
            return json.dumps({
                "status": "success",
                "source": "CNET",
                "category": category,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"Lỗi khi lấy bài đánh giá từ CNET: {e}")
            return json.dumps({
                "status": "error",
                "source": "CNET",
                "category": category,
                "message": str(e),
                "results": []
            }, indent=2, ensure_ascii=False)
    
    def _get_default_results(self, query: str) -> str:
        """Trả về kết quả mặc định khi không tìm thấy kết quả."""
        default_results = [
            {
                "title": "Đánh giá iPhone 15 Pro Max 2024",
                "url": "https://www.cnet.com/reviews/iphone-15-pro-max-review/",
                "summary": "Đánh giá chi tiết iPhone 15 Pro Max với camera cải tiến và hiệu năng mạnh mẽ.",
                "source": "CNET"
            },
            {
                "title": "Top 5 Laptop tốt nhất 2024",
                "url": "https://www.cnet.com/best-laptops/",
                "summary": "Danh sách những chiếc laptop đáng mua nhất năm 2024 cho mọi nhu cầu.",
                "source": "CNET"
            },
            {
                "title": "Hướng dẫn cài đặt VPN toàn tập",
                "url": "https://www.cnet.com/how-to/vpn-setup-guide/",
                "summary": "Cách cài đặt và sử dụng VPN cho người mới bắt đầu.",
                "source": "CNET"
            }
        ]
        
        return json.dumps({
            "status": "success",
            "source": "CNET",
            "query": query,
            "message": "Không tìm thấy kết quả phù hợp. Dưới đây là một số gợi ý.",
            "results": default_results,
            "result_count": len(default_results)
        }, indent=2, ensure_ascii=False)
    
    def _get_error_response(self, query: str, error_msg: str) -> str:
        """Trả về phản hồi lỗi có cấu trúc."""
        return json.dumps({
            "status": "error",
            "source": "CNET",
            "query": query,
            "message": f"Không thể truy xuất kết quả: {error_msg}",
            "results": []
        }, indent=2, ensure_ascii=False)
