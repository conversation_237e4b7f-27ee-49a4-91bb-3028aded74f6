from typing import Dict, Any, Optional, List, Union, Tuple
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger, log_warning
import requests
import re
import json
import asyncio
from datetime import datetime, timedelta
from urllib.parse import quote_plus, urlparse, parse_qs
from dataclasses import dataclass
from enum import Enum

# Các hằng số
UN_BASE_URL = "https://digitallibrary.un.org"
UN_SEARCH_URL = f"{UN_BASE_URL}/search"
UN_API_URL = "https://repository.un.org/api"
CACHE_EXPIRY_DAYS = 7  # Thời gian hết hạn cache

class UNDocumentType(str, Enum):
    """Các loại tài liệu của <PERSON>ên <PERSON>ợp <PERSON>uố<PERSON>"""
    RESOLUTION = "resolution"
    REPORT = "report"
    TREATY = "treaty"
    MEETING_RECORD = "meeting record"
    PUBLICATION = "publication"
    SPEECH = "speech"
    LETTER = "letter"
    DECISION = "decision"
    STATEMENT = "statement"

class UNBody(str, Enum):
    """<PERSON><PERSON><PERSON> c<PERSON> quan ch<PERSON>h của <PERSON>"""
    GA = "General Assembly"
    SC = "Security Council"
    ECOSOC = "Economic and Social Council"
    ICJ = "International Court of Justice"
    SECRETARIAT = "Secretariat"
    TRUSTEESHIP = "Trusteeship Council"

@dataclass
class UNLibraryCache:
    data: Dict[str, Any]
    last_updated: datetime
    expiry_days: int = 7
    
    def is_expired(self) -> bool:
        return datetime.now() > (self.last_updated + timedelta(days=self.expiry_days))

class UNLibraryTool(Toolkit):
    """
    UN Library Tool cho tìm kiếm tài liệu, nghị quyết, dữ liệu từ UN Digital Library.
    Hỗ trợ tìm kiếm và truy xuất tài liệu từ các cơ quan Liên Hợp Quốc.
    """

    def __init__(self):
        super().__init__(
            name="UN Digital Library Search Tool",
            tools=[
                self.search_un_library,
                self.get_document_types,
                self.get_un_bodies,
                self.get_document_metadata,
                self.search_resolutions
            ]
        )
        self.base_url = UN_BASE_URL
        self.search_url = UN_SEARCH_URL
        self.api_url = UN_API_URL
        self.cache: Dict[str, UNLibraryCache] = {}
        self.timeout = 20
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "application/json"
        }
        self._load_cache()

    def _load_cache(self) -> None:
        """Tải dữ liệu cache từ file nếu có"""
        try:
            with open(".un_library_cache.json", 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                for key, data in cache_data.items():
                    self.cache[key] = UNLibraryCache(
                        data=data['data'],
                        last_updated=datetime.fromisoformat(data['last_updated']),
                        expiry_days=data.get('expiry_days', 7)
                    )
        except (FileNotFoundError, json.JSONDecodeError):
            self.cache = {}

    def _save_cache(self) -> None:
        """Lưu dữ liệu cache vào file"""
        cache_data = {}
        for key, cache in self.cache.items():
            cache_data[key] = {
                'data': cache.data,
                'last_updated': cache.last_updated.isoformat(),
                'expiry_days': cache.expiry_days
            }
        with open(".un_library_cache.json", 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)

    def _get_cache(self, key: str) -> Optional[Dict[str, Any]]:
        """Lấy dữ liệu từ cache nếu còn hiệu lực"""
        if key in self.cache and not self.cache[key].is_expired():
            return self.cache[key].data
        return None

    def _set_cache(self, key: str, data: Any, expiry_days: int = 7) -> None:
        """Lưu dữ liệu vào cache"""
        self.cache[key] = UNLibraryCache(
            data=data,
            last_updated=datetime.now(),
            expiry_days=expiry_days
        )
        self._save_cache()

    async def _fetch_with_retry(self, url: str, params: Optional[Dict] = None, max_retries: int = 3) -> Optional[requests.Response]:
        """Gửi yêu cầu HTTP với cơ chế thử lại"""
        for attempt in range(max_retries):
            try:
                response = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: requests.get(url, params=params, headers=self.headers, timeout=self.timeout)
                )
                response.raise_for_status()
                return response
            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    log_warning(f"Request failed after {max_retries} attempts: {str(e)}")
                    return None
                await asyncio.sleep(1 * (attempt + 1))
        return None

    async def get_document_types(self) -> List[Dict[str, str]]:
        """
        Lấy danh sách các loại tài liệu có sẵn trong UN Digital Library
        
        Returns:
        - Danh sách các loại tài liệu với tên và mô tả
        """
        return [{"id": t.value, "name": t.name.replace("_", " ").title()} for t in UNDocumentType]

    async def get_un_bodies(self) -> List[Dict[str, str]]:
        """
        Lấy danh sách các cơ quan chính của Liên Hợp Quốc
        
        Returns:
        - Danh sách các cơ quan với tên viết tắt và tên đầy đủ
        """
        return [{"code": b.name, "name": b.value} for b in UNBody]

    async def get_document_metadata(self, document_id: str) -> Dict[str, Any]:
        """
        Lấy siêu dữ liệu chi tiết của một tài liệu
        
        Parameters:
        - document_id: ID của tài liệu (có thể là số hoặc mã định danh)
        
        Returns:
        - Dict chứa siêu dữ liệu chi tiết của tài liệu
        """
        cache_key = f"document_{document_id}"
        cached_data = self._get_cache(cache_key)
        if cached_data:
            return {"status": "success", "source": "UN Digital Library (cached)", "data": cached_data}

        try:
            # Sử dụng API của UN Digital Library để lấy thông tin chi tiết
            api_url = f"{self.api_url}/record/{document_id}"
            response = await self._fetch_with_retry(api_url)
            
            if not response or response.status_code != 200:
                return {
                    "status": "error",
                    "source": "UN Digital Library",
                    "message": f"Failed to fetch document metadata (HTTP {response.status_code if response else 'timeout'})",
                    "document_id": document_id
                }
            
            metadata = response.json()
            
            # Lưu vào cache
            self._set_cache(cache_key, metadata)
            
            return {
                "status": "success",
                "source": "UN Digital Library",
                "data": metadata
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy siêu dữ liệu tài liệu: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "source": "UN Digital Library",
                "message": error_msg,
                "document_id": document_id
            }

            # Xử lý kết quả tìm kiếm
            results = []
            
            # Tạo cache key dựa trên tham số tìm kiếm
            cache_key = f"search_{query}_{doc_type or ''}_{year or ''}_{limit}"
            cached_results = self._get_cache(cache_key)
            
            if cached_results:
                return {
                    "status": "success",
                    "source": "UN Digital Library (cached)",
                    "query": query,
                    "doc_type": doc_type,
                    "year": year,
                    "results_count": len(cached_results),
                    "results": cached_results,
                    "keyword_guide": self._get_search_guides(),
                    "official_data_url": self.base_url
                }
            
            # Sử dụng BeautifulSoup nếu có, nếu không dùng regex
            try:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                result_items = soup.select('div.result-item')
                
                for item in result_items[:limit]:
                    # Lấy tiêu đề và URL
                    title_elem = item.select_one('h3 a')
                    title = title_elem.get_text(strip=True) if title_elem else None
                    url = f"{self.base_url}{title_elem['href']}" if title_elem and title_elem.get('href') else None
                    
                    # Lấy mô tả
                    desc_elem = item.select_one('div.result-description')
                    description = desc_elem.get_text(strip=True) if desc_elem else None
                    
                    # Lấy loại tài liệu
                    type_elem = item.select_one('span.result-doctype')
                    doc_type_val = type_elem.get_text(strip=True) if type_elem else None
                    
                    # Lấy năm
                    year_elem = item.select_one('span.result-year')
                    year_val = year_elem.get_text(strip=True) if year_elem else None
                    
                    # Lấy thêm thông tin nếu có
                    metadata = {}
                    metadata_elems = item.select('div.result-meta span')
                    for meta in metadata_elems:
                        text = meta.get_text(strip=True)
                        if ':' in text:
                            key, value = text.split(':', 1)
                            metadata[key.strip().lower()] = value.strip()
                    
                    # Thêm vào kết quả
                    result = {
                        "title": title,
                        "description": description,
                        "doc_type": doc_type_val,
                        "year": year_val,
                        "un_library_url": url,
                        "metadata": metadata if metadata else None
                    }
                    
                    # Lấy thêm thông tin chi tiết nếu cần
                    if url and 'record' in url:
                        doc_id = url.split('/')[-1]
                        doc_meta = await self.get_document_metadata(doc_id)
                        if doc_meta.get('status') == 'success':
                            result['full_metadata'] = doc_meta['data']
                    
                    results.append(result)
            except ImportError:
                # Fallback sử dụng regex nếu không cài đặt BeautifulSoup
                logger.warning("BeautifulSoup not installed, using regex fallback")
                for match in re.finditer(r'<div class="result-item".*?>(.*?)</div>\s*</div>', response.text, re.DOTALL):
                    if len(results) >= limit:
                        break
                    block = match.group(1)
                    # Lấy link và tiêu đề
                    link_match = re.search(r'<a href="(/record/\d+)"[^>]*>(.*?)</a>', block)
                    url = f"{self.base_url}{link_match.group(1)}" if link_match else None
                    title = re.sub(r'<.*?>', '', link_match.group(2)).strip() if link_match else None
                    # Lấy mô tả (nếu có)
                    desc_match = re.search(r'<div class="result-description">(.*?)</div>', block, re.DOTALL)
                    description = re.sub(r'<.*?>', '', desc_match.group(1)).strip() if desc_match else None
                    # Lấy loại tài liệu
                    type_match = re.search(r'<span class="result-doctype">(.*?)</span>', block)
                    doc_type_val = re.sub(r'<.*?>', '', type_match.group(1)).strip() if type_match else None
                    # Lấy năm
                    year_match = re.search(r'<span class="result-year">(.*?)</span>', block)
                    year_val = re.sub(r'<.*?>', '', year_match.group(1)).strip() if year_match else None
                    
                    # Lấy thêm metadata nếu có
                    metadata = {}
                    meta_matches = re.finditer(r'<span class="result-meta-data">(.*?)</span>', block)
                    for meta in meta_matches:
                        meta_text = re.sub(r'<.*?>', '', meta.group(1)).strip()
                        if ':' in meta_text:
                            key, value = meta_text.split(':', 1)
                            metadata[key.strip().lower()] = value.strip()
                    
                    results.append({
                        "title": title,
                        "description": description,
                        "doc_type": doc_type_val,
                        "year": year_val,
                        "un_library_url": url,
                        "metadata": metadata if metadata else None
                    })
            
            # Lưu kết quả vào cache
            if results:
                self._set_cache(cache_key, results, expiry_days=1)  # Cache 1 ngày cho kết quả tìm kiếm

            # Tạo response
            response_data = {
                "status": "success",
                "source": "UN Digital Library",
                "query": query,
                "doc_type": doc_type,
                "year": year,
                "results_count": len(results),
                "results": results,
                "search_metadata": {
                    "query_type": "general_search",
                    "results_returned": len(results),
                    "has_more": len(results) >= limit,
                    "suggested_queries": self._get_search_suggestions(query)
                },
                "keyword_guide": self._get_search_guides(),
                "official_data_url": self.base_url
            }
            
            # Lưu kết quả tìm kiếm gần đây
            self._save_recent_search(query, doc_type, year, len(results))
            
            return response_data

        except requests.exceptions.RequestException as e:
            error_msg = f"Lỗi kết nối khi tìm kiếm UN Digital Library: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "source": "UN Digital Library",
                "message": error_msg,
                "query": query,
                "suggested_actions": [
                    "Kiểm tra kết nối mạng",
                    "Thử lại sau ít phút",
                    "Kiểm tra lại từ khóa tìm kiếm"
                ]
            }
            
        except Exception as e:
            error_msg = f"Lỗi không xác định khi tìm kiếm UN Digital Library: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "source": "UN Digital Library",
                "message": error_msg,
                "query": query,
                "suggested_actions": [
                    "Thử lại với từ khóa khác",
                    "Kiểm tra lại cú pháp tìm kiếm",
                    "Liên hệ quản trị viên nếu lỗi tiếp tục xảy ra"
                ]
            }
    
    async def search_resolutions(self, 
                              query: str = "", 
                              body: Optional[Union[str, UNBody]] = None,
                              year: Optional[Union[str, int]] = None,
                              resolution_number: Optional[str] = None,
                              limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm các nghị quyết của Liên Hợp Quốc
        
        Parameters:
        - query: Từ khóa tìm kiếm (tùy chọn)
        - body: Cơ quan ban hành (ví dụ: 'SC' cho Hội đồng Bảo an, 'GA' cho Đại hội đồng)
        - year: Năm ban hành (ví dụ: 2020 hoặc '2020-2022')
        - resolution_number: Số hiệu nghị quyết (ví dụ: '2178' hoặc 'S/RES/2178')
        - limit: Số lượng kết quả tối đa (mặc định: 5)
        
        Returns:
        - Dict chứa kết quả tìm kiếm các nghị quyết
        """
        try:
            # Xây dựng query tìm kiếm
            search_terms = ["resolution"]
            
            if query:
                search_terms.append(f'"{query}"')
                
            if isinstance(body, UNBody):
                search_terms.append(f'"{body.value}"')
            elif body:
                search_terms.append(f'"{body}"')
                
            if resolution_number:
                # Chuẩn hóa số hiệu nghị quyết
                if '/' in resolution_number:
                    # Định dạng đầy đủ như S/RES/2178
                    search_terms.append(f'"{resolution_number}"')
                else:
                    # Chỉ số nghị quyết
                    search_terms.append(f'"S/RES/{resolution_number}"')
            
            search_query = ' AND '.join(search_terms)
            
            # Tìm kiếm với loại tài liệu là resolution
            return await self.search_un_library(
                query=search_query,
                doc_type=UNDocumentType.RESOLUTION,
                year=str(year) if year else None,
                limit=limit
            )
            
        except Exception as e:
            error_msg = f"Lỗi khi tìm kiếm nghị quyết: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "source": "UN Digital Library",
                "message": error_msg,
                "query": query,
                "body": str(body) if body else None,
                "year": str(year) if year else None,
                "resolution_number": resolution_number
            }
    
    def _get_search_guides(self) -> List[Dict[str, str]]:
        """Trả về danh sách hướng dẫn tìm kiếm"""
        return [
            {"query": "<từ khóa>", "description": "Tìm kiếm chung trong toàn bộ thư viện"},
            {"query": "resolution <số>", "description": "Tìm nghị quyết theo số"},
            {"query": "report <chủ đề>", "description": "Tìm báo cáo theo chủ đề"},
            {"query": "treaty <tên hiệp ước>", "description": "Tìm hiệp ước quốc tế"},
            {"query": "list_countries()", "description": "Xem danh sách quốc gia thành viên"},
            {"query": "get_document_types()", "description": "Xem các loại tài liệu có sẵn"},
            {"query": "get_un_bodies()", "description": "Xem các cơ quan của Liên Hợp Quốc"}
        ]
    
    def _get_search_suggestions(self, query: str) -> List[str]:
        """Gợi ý các từ khóa tìm kiếm liên quan"""
        query = query.lower().strip()
        suggestions = []
        
        # Gợi ý theo loại tài liệu
        doc_types = [t.value for t in UNDocumentType]
        for doc_type in doc_types:
            if doc_type in query:
                suggestions.extend([
                    f"{query} {t}" for t in ["latest", "recent", "important"] 
                    if t not in query
                ])
        
        # Gợi ý theo cơ quan LHQ
        for body in UNBody:
            if body.name.lower() in query or body.value.lower() in query:
                suggestions.extend([
                    f"{query} resolution",
                    f"{query} report",
                    f"{query} meeting record"
                ])
        
        # Gợi ý chung
        if not suggestions:
            suggestions = [
                f"{query} resolution",
                f"{query} report",
                f"{query} {datetime.now().year-1}-{datetime.now().year}",
                f"{query} UN"
            ]
        
        return list(dict.fromkeys(suggestions))[:5]  # Loại bỏ trùng lặp và giới hạn số lượng
    
    def _save_recent_search(self, query: str, doc_type: Optional[str] = None, 
                           year: Optional[str] = None, result_count: int = 0) -> None:
        """Lưu lịch sử tìm kiếm gần đây"""
        try:
            recent_searches = self._get_cache("recent_searches") or []
            recent_searches.insert(0, {
                "query": query,
                "doc_type": doc_type,
                "year": year,
                "timestamp": datetime.now().isoformat(),
                "result_count": result_count
            })
            # Giới hạn lưu tối đa 10 lịch sử
            self._set_cache("recent_searches", recent_searches[:10], expiry_days=30)
        except Exception as e:
            logger.warning(f"Không thể lưu lịch sử tìm kiếm: {str(e)}")
