from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json

class DeviantArtTool(Toolkit):
    """
    Công cụ tìm kiếm DeviantArt giúp khám phá nghệ thuật kỹ thuật số, nghệ sĩ và bộ sưu tập.
    
    Các từ khóa tìm kiếm gợi ý:
    - Thể loại nghệ thuật (fantasy, anime, digital painting, v.v.)
    - <PERSON><PERSON><PERSON> nghệ sĩ (Loish, Sakimichan, WLOP, v.v.)
    - Ch<PERSON> đề nghệ thuật (portrait, landscape, character design, v.v.)
    - <PERSON><PERSON> cách nghệ thuật (semi-realistic, chibi, cyberpunk, v.v.)
    - <PERSON><PERSON><PERSON> cụ sáng tác (Photoshop, Procreate, Blender, v.v.)
    """
    def __init__(self):
        super().__init__(
            name="<PERSON><PERSON>ng cụ tìm kiếm DeviantArt",
            tools=[self.search_deviantart]
        )
        self.base_url = "https://www.deviantart.com"
        self.search_types = ["all", "popular", "undiscovered", "deviations", "users", "groups"]
        self.time_ranges = ["alltime", "8hr", "24hr", "3days", "1week", "1month"]

    def search_deviantart(self, query: str, search_type: str = "popular", time_range: str = "alltime", 
                         limit: int = 5, mature_content: bool = False) -> str:
        """
        Tìm kiếm trên DeviantArt theo từ khóa và bộ lọc.
        
        Args:
            query: Từ khóa tìm kiếm (tên tác phẩm, nghệ sĩ, chủ đề...)
            search_type: Loại tìm kiếm (all, popular, undiscovered, deviations, users, groups)
            time_range: Khoảng thời gian (alltime, 8hr, 24hr, 3days, 1week, 1month)
            limit: Số lượng kết quả trả về (tối đa 24)
            mature_content: Có bao gồm nội dung người lớn hay không
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm DeviantArt với từ khóa: {query}, loại: {search_type}")
        
        if search_type not in self.search_types:
            search_type = "popular"
        if time_range not in self.time_ranges:
            time_range = "alltime"
            
        limit = max(1, min(limit, 24))  # Giới hạn trong khoảng 1-24
        
        try:
            # Giả lập kết quả tìm kiếm
            results = []
            
            if search_type in ["all", "popular"]:
                results.extend([
                    {
                        "title": f"{query} #{i+1}",
                        "artist": f"{query}Artist{i+1}",
                        "type": "deviation",
                        "category": "Digital Art",
                        "tags": [query.lower(), "digital art", "art"],
                        "favorites": 1000 + i*100,
                        "comments": 50 + i*5,
                        "views": 10000 + i*1000,
                        "publish_date": f"2025-{i+1:02d}-01T12:00:00Z",
                        "mature": i % 3 == 0,  # 1/3 tác phẩm là nội dung người lớn
                        "url": f"https://www.deviantart.com/{query.lower()}artist{i+1}/art/{query.lower().replace(' ', '-')}-{900000000 + i}",
                        "image_url": f"https://images-wixmp-ed30a86b8c4ca887773594c2.wixmp.com/f/12345678-1234-5678-90ab-1234567890ab/def12345-6789-4321-abcd-ef0123456789.jpg?token=abc123"
                    } for i in range(min(limit, 6))
                ])
                
            if search_type in ["all", "users"] and len(results) < limit:
                results.extend([
                    {
                        "username": f"{query}Artist{i+1}",
                        "type": "user",
                        "title": f"{query} Digital Artist",
                        "specialty": "Digital Artist",
                        "country": "Vietnam" if i % 2 == 0 else "International",
                        "member_since": f"202{i%5}",
                        "pageviews": 100000 + i*10000,
                        "watchers": 1000 + i*100,
                        "deviations": 50 + i*5,
                        "is_watching": i % 3 == 0,
                        "url": f"https://www.deviantart.com/{query.lower()}artist{i+1}",
                        "avatar_url": f"https://a.deviantart.net/avatars-big/{i%26}/d/e/v/i/a/n/t/a/r/t/{i%10}/avatar.png"
                    } for i in range(min(limit - len(results), 3))
                ])
                
            if search_type in ["all", "groups"] and len(results) < limit:
                results.extend([
                    {
                        "name": f"{query} Art Group {i+1}",
                        "type": "group",
                        "category": "Digital Art",
                        "members": 1000 + i*100,
                        "founded": f"202{i%5}",
                        "description": f"A community of {query} artists and enthusiasts.",
                        "is_member": i % 4 == 0,
                        "is_watching": i % 3 == 0,
                        "url": f"https://www.deviantart.com/{query.lower().replace(' ', '')}group{i+1}",
                        "avatar_url": f"https://a.deviantart.net/avatars-big/g/r/o/u/p/{i%10}/group_avatar.png"
                    } for i in range(min(limit - len(results), 2))
                ])
                
            if search_type == "undiscovered" and not results:
                results = [
                    {
                        "title": f"Undiscovered: {query} Art",
                        "type": "undiscovered",
                        "description": f"Emerging artists creating amazing {query} artworks.",
                        "url": f"https://www.deviantart.com/tag/{query.lower().replace(' ', '')}",
                        "image_url": "https://images-wixmp-ed30a86b8c4ca887773594c2.wixmp.com/f/12345678-1234-5678-90ab-1234567890ab/undiscovered.jpg?token=xyz789"
                    }
                ]
            
            # Lọc nội dung người lớn nếu cần
            if not mature_content:
                results = [r for r in results if not r.get('mature', False)]
            
            result = {
                "status": "success",
                "source": "DeviantArt",
                "query": query,
                "search_type": search_type,
                "time_range": time_range,
                "mature_content": mature_content,
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm DeviantArt: {str(e)}")
            result = {
                "status": "error",
                "source": "DeviantArt",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm {query} trên DeviantArt",
                        "url": f"https://www.deviantart.com/search?q={query}",
                        "summary": f"Tìm kiếm tác phẩm nghệ thuật liên quan đến {query} trên DeviantArt"
                    },
                    {
                        "title": f"Nghệ sĩ {query}",
                        "url": f"https://www.deviantart.com/search/users?q={query}",
                        "summary": f"Tìm kiếm nghệ sĩ {query} trên DeviantArt"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
