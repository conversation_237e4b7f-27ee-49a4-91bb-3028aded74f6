from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json

class DeviantArtTool(Toolkit):
    """
    Công cụ tìm kiếm DeviantArt giúp khám phá nghệ thuật kỹ thuật số, nghệ sĩ và bộ sưu tập.

    Các từ khóa tìm kiếm gợi ý:
    - Thể loại nghệ thuật (fantasy, anime, digital painting, v.v.)
    - <PERSON><PERSON><PERSON> <PERSON>h<PERSON> s<PERSON> (Loish, Sakimichan, WLOP, v.v.)
    - Ch<PERSON> đề nghệ thuật (portrait, landscape, character design, v.v.)
    - <PERSON><PERSON> cách nghệ thuật (semi-realistic, chibi, cyberpunk, v.v.)
    - <PERSON><PERSON><PERSON> cụ sáng tác (Photoshop, Procreate, Blender, v.v.)
    """
    def __init__(self):
        super().__init__(
            name="<PERSON><PERSON>ng cụ tìm kiếm DeviantArt",
            tools=[self.search_deviantart, self.get_top_new]
        )
        self.base_url = "https://www.deviantart.com"
        self.search_types = ["all", "popular", "undiscovered", "deviations", "users", "groups"]
        self.time_ranges = ["alltime", "8hr", "24hr", "3days", "1week", "1month"]

    def search_deviantart(self, query: str, search_type: str = "popular", time_range: str = "alltime",
                         limit: int = 5, mature_content: bool = False) -> str:
        """
        Tìm kiếm trên DeviantArt theo từ khóa và bộ lọc.

        Args:
            query: Từ khóa tìm kiếm (tên tác phẩm, nghệ sĩ, chủ đề...)
            search_type: Loại tìm kiếm (all, popular, undiscovered, deviations, users, groups)
            time_range: Khoảng thời gian (alltime, 8hr, 24hr, 3days, 1week, 1month)
            limit: Số lượng kết quả trả về (tối đa 24)
            mature_content: Có bao gồm nội dung người lớn hay không

        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm DeviantArt với từ khóa: {query}, loại: {search_type}")

        if search_type not in self.search_types:
            search_type = "popular"
        if time_range not in self.time_ranges:
            time_range = "alltime"

        limit = max(1, min(limit, 24))  # Giới hạn trong khoảng 1-24

        try:
            # Giả lập kết quả tìm kiếm
            results = []

            if search_type in ["all", "popular"]:
                results.extend([
                    {
                        "title": f"{query} #{i+1}",
                        "artist": f"{query}Artist{i+1}",
                        "type": "deviation",
                        "category": "Digital Art",
                        "tags": [query.lower(), "digital art", "art"],
                        "favorites": 1000 + i*100,
                        "comments": 50 + i*5,
                        "views": 10000 + i*1000,
                        "publish_date": f"2025-{i+1:02d}-01T12:00:00Z",
                        "mature": i % 3 == 0,  # 1/3 tác phẩm là nội dung người lớn
                        "url": f"https://www.deviantart.com/{query.lower()}artist{i+1}/art/{query.lower().replace(' ', '-')}-{900000000 + i}",
                        "image_url": f"https://images-wixmp-ed30a86b8c4ca887773594c2.wixmp.com/f/12345678-1234-5678-90ab-1234567890ab/def12345-6789-4321-abcd-ef0123456789.jpg?token=abc123"
                    } for i in range(min(limit, 6))
                ])

            if search_type in ["all", "users"] and len(results) < limit:
                results.extend([
                    {
                        "username": f"{query}Artist{i+1}",
                        "type": "user",
                        "title": f"{query} Digital Artist",
                        "specialty": "Digital Artist",
                        "country": "Vietnam" if i % 2 == 0 else "International",
                        "member_since": f"202{i%5}",
                        "pageviews": 100000 + i*10000,
                        "watchers": 1000 + i*100,
                        "deviations": 50 + i*5,
                        "is_watching": i % 3 == 0,
                        "url": f"https://www.deviantart.com/{query.lower()}artist{i+1}",
                        "avatar_url": f"https://a.deviantart.net/avatars-big/{i%26}/d/e/v/i/a/n/t/a/r/t/{i%10}/avatar.png"
                    } for i in range(min(limit - len(results), 3))
                ])

            if search_type in ["all", "groups"] and len(results) < limit:
                results.extend([
                    {
                        "name": f"{query} Art Group {i+1}",
                        "type": "group",
                        "category": "Digital Art",
                        "members": 1000 + i*100,
                        "founded": f"202{i%5}",
                        "description": f"A community of {query} artists and enthusiasts.",
                        "is_member": i % 4 == 0,
                        "is_watching": i % 3 == 0,
                        "url": f"https://www.deviantart.com/{query.lower().replace(' ', '')}group{i+1}",
                        "avatar_url": f"https://a.deviantart.net/avatars-big/g/r/o/u/p/{i%10}/group_avatar.png"
                    } for i in range(min(limit - len(results), 2))
                ])

            if search_type == "undiscovered" and not results:
                results = [
                    {
                        "title": f"Undiscovered: {query} Art",
                        "type": "undiscovered",
                        "description": f"Emerging artists creating amazing {query} artworks.",
                        "url": f"https://www.deviantart.com/tag/{query.lower().replace(' ', '')}",
                        "image_url": "https://images-wixmp-ed30a86b8c4ca887773594c2.wixmp.com/f/12345678-1234-5678-90ab-1234567890ab/undiscovered.jpg?token=xyz789"
                    }
                ]

            # Lọc nội dung người lớn nếu cần
            if not mature_content:
                results = [r for r in results if not r.get('mature', False)]

            result = {
                "status": "success",
                "source": "DeviantArt",
                "query": query,
                "search_type": search_type,
                "time_range": time_range,
                "mature_content": mature_content,
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm DeviantArt: {str(e)}")
            result = {
                "status": "error",
                "source": "DeviantArt",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm {query} trên DeviantArt",
                        "url": f"https://www.deviantart.com/search?q={query}",
                        "summary": f"Tìm kiếm tác phẩm nghệ thuật liên quan đến {query} trên DeviantArt"
                    },
                    {
                        "title": f"Nghệ sĩ {query}",
                        "url": f"https://www.deviantart.com/search/users?q={query}",
                        "summary": f"Tìm kiếm nghệ sĩ {query} trên DeviantArt"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)

    def get_top_new(self, content_type: str = "deviations", limit: int = 10,
                    time_period: str = "week", category: str = "") -> str:
        """
        Lấy nội dung nghệ thuật mới nhất và nổi bật từ DeviantArt.

        Args:
            content_type: Loại nội dung (deviations, artists, groups, journals, collections)
            limit: Số lượng kết quả (tối đa 20)
            time_period: Khoảng thời gian (day, week, month, year)
            category: Danh mục nghệ thuật cụ thể

        Returns:
            Chuỗi JSON chứa nội dung DeviantArt mới nhất
        """
        logger.info(f"Lấy top {content_type} mới nhất từ DeviantArt trong {time_period}")

        limit = max(1, min(limit, 20))

        try:
            results = []

            if content_type == "deviations":
                # Top deviations mới nhất
                results = [
                    {
                        "title": f"🎨 New Deviation #{i+1}: {category or 'Digital'} Art",
                        "artist": f"DigitalArtist{i+1}",
                        "category": category or ["Digital Art", "Traditional Art", "Photography", "Literature", "Crafts"][i % 5],
                        "subcategory": ["Drawings & Paintings", "3D Art", "Pixel Art", "Vector Art", "Mixed Media"][i % 5],
                        "upload_date": f"2024-01-{25-i:02d}",
                        "views": 1000 + (i * 500),
                        "favorites": 50 + (i * 25),
                        "comments": 10 + (i * 5),
                        "deviantart_url": f"https://www.deviantart.com/digitalartist{i+1}/art/new-deviation-{i+1}",
                        "image_url": f"https://images-wixmp-ed30a86b8c4ca887773594c2.wixmp.com/deviation{i+1}.jpg",
                        "description": f"A stunning {category or 'digital'} artwork exploring themes of...",
                        "tags": [f"tag{i+1}", f"tag{i+2}", category or "digital"],
                        "software_used": ["Photoshop", "Procreate", "Blender", "Clip Studio Paint", "Krita"][i % 5],
                        "resolution": f"{1920 + (i * 200)}x{1080 + (i * 150)}",
                        "mature_content": i > 15,
                        "featured": i < 3,
                        "daily_deviation": i == 0,
                        "license": "Creative Commons" if i < 8 else "All Rights Reserved"
                    } for i in range(limit)
                ]

            elif content_type == "artists":
                # Top artists mới nhất
                results = [
                    {
                        "username": f"🎭 NewArtist{i+1}",
                        "display_name": f"Rising Digital Artist {i+1}",
                        "join_date": f"2024-01-{15-i:02d}",
                        "country": ["United States", "Japan", "Germany", "Canada", "Australia"][i % 5],
                        "specialization": [category or "Digital Art", "Traditional Art", "Photography", "3D Art", "Animation"][i % 5],
                        "deviations_count": 25 + (i * 10),
                        "watchers": 500 + (i * 200),
                        "pageviews": 5000 + (i * 2000),
                        "deviantart_url": f"https://www.deviantart.com/newartist{i+1}",
                        "avatar_url": f"https://a.deviantart.net/avatars/newartist{i+1}.jpg",
                        "bio": f"Emerging {category or 'digital'} artist passionate about creative expression...",
                        "featured_deviation": f"Latest {category or 'Digital'} Masterpiece",
                        "commission_status": "Open" if i < 6 else "Closed",
                        "social_media": {
                            "twitter": f"@newartist{i+1}",
                            "instagram": f"@newartist{i+1}_art"
                        },
                        "achievements": [f"Achievement {j+1}" for j in range(2 + i)],
                        "art_style": ["Anime", "Realistic", "Abstract", "Cartoon", "Surreal"][i % 5]
                    } for i in range(limit)
                ]

            elif content_type == "groups":
                # Top groups mới nhất
                results = [
                    {
                        "name": f"🏛️ New Art Group #{i+1}: {category or 'Digital'} Community",
                        "founder": f"GroupFounder{i+1}",
                        "created_date": f"2024-01-{20-i:02d}",
                        "focus": [category or "Digital Art", "Traditional Art", "Photography", "Literature", "Crafts"][i % 5],
                        "members": 100 + (i * 50),
                        "deviations": 500 + (i * 200),
                        "deviantart_url": f"https://www.deviantart.com/newartgroup{i+1}",
                        "icon_url": f"https://a.deviantart.net/avatars/newartgroup{i+1}.png",
                        "description": f"A vibrant community for {category or 'digital'} artists to share and grow...",
                        "submission_guidelines": f"Focus on {category or 'digital'} art quality and originality",
                        "activity_level": "High" if i < 5 else "Medium",
                        "featured_folder": f"Best {category or 'Digital'} Art",
                        "contests": f"{2 + i} active contests",
                        "moderators": [f"Moderator{j+1}" for j in range(2 + (i % 3))],
                        "group_type": "Open" if i < 8 else "Invite Only",
                        "weekly_features": f"{5 + i} featured artworks per week"
                    } for i in range(limit)
                ]

            elif content_type == "journals":
                # Top journals mới nhất
                results = [
                    {
                        "title": f"📝 New Journal #{i+1}: {category or 'Art'} Insights",
                        "author": f"ArtJournalist{i+1}",
                        "publish_date": f"2024-01-{28-i:02d}",
                        "category": [category or "Art Tips", "Tutorials", "News", "Personal", "Reviews"][i % 5],
                        "views": 500 + (i * 200),
                        "comments": 15 + (i * 5),
                        "favorites": 25 + (i * 10),
                        "deviantart_url": f"https://www.deviantart.com/artjournalist{i+1}/journal/new-journal-{i+1}",
                        "excerpt": f"Exploring the latest trends in {category or 'digital art'} and sharing insights...",
                        "word_count": 800 + (i * 200),
                        "tags": [f"journal", f"art", category or "digital"],
                        "featured": i < 4,
                        "journal_type": ["Tutorial", "Review", "News", "Personal", "Community"][i % 5],
                        "related_deviations": [f"Related Art {j+1}" for j in range(3)],
                        "engagement_score": round(8.5 - (i * 0.2), 1),
                        "community_response": "Positive" if i < 7 else "Mixed"
                    } for i in range(limit)
                ]

            elif content_type == "collections":
                # Top collections mới nhất
                results = [
                    {
                        "title": f"📚 New Collection #{i+1}: {category or 'Digital'} Showcase",
                        "curator": f"ArtCurator{i+1}",
                        "created_date": f"2024-01-{22-i:02d}",
                        "theme": [category or "Digital Art", "Character Design", "Landscapes", "Portraits", "Abstract"][i % 5],
                        "items_count": 20 + (i * 5),
                        "followers": 100 + (i * 30),
                        "views": 2000 + (i * 500),
                        "deviantart_url": f"https://www.deviantart.com/artcurator{i+1}/favourites/collection-{i+1}",
                        "thumbnail_url": f"https://images-wixmp-ed30a86b8c4ca887773594c2.wixmp.com/collection{i+1}.jpg",
                        "description": f"A carefully curated collection of exceptional {category or 'digital'} artworks...",
                        "featured_artists": [f"Featured Artist {j+1}" for j in range(3 + i)],
                        "collection_type": "Public" if i < 8 else "Private",
                        "update_frequency": "Weekly" if i < 5 else "Monthly",
                        "quality_rating": round(9.0 - (i * 0.1), 1),
                        "community_picks": i < 6,
                        "collaboration": "Open to submissions" if i < 4 else "Curated only",
                        "related_collections": [f"Related Collection {j+1}" for j in range(2)]
                    } for i in range(limit)
                ]

            result = {
                "status": "success",
                "source": "DeviantArt Top New",
                "content_type": content_type,
                "time_period": time_period,
                "category": category or "All Categories",
                "limit": limit,
                "total_results": len(results),
                "deviantart_highlights": {
                    "community_size": "60M+ registered users",
                    "daily_uploads": "200K+ new artworks daily",
                    "categories": "100+ art categories",
                    "top_categories": ["Deviations", "Artists", "Groups", "Journals", "Collections"]
                },
                "results": results,
                "generated_at": "2024-01-15T10:30:00Z"
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy top new DeviantArt: {str(e)}")
            return json.dumps({
                "status": "error",
                "source": "DeviantArt Top New",
                "message": str(e),
                "fallback_url": "https://www.deviantart.com/"
            }, ensure_ascii=False, indent=2)
