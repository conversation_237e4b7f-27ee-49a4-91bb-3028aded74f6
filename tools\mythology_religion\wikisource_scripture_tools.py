from typing import Dict, Any, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class WikisourceScriptureTool(Toolkit):
    """
    Công cụ tìm kiếm và đọc văn bản tôn gi<PERSON><PERSON>, thần thoại từ Wikisource.
    Hỗ trợ tìm kiếm theo tên văn bản, tá<PERSON> gi<PERSON>, chủ đề tôn giáo.
    """

    def __init__(self):
        super().__init__(
            name="Wikisource Scripture Tools",
            tools=[
                self.search_wikisource_scripture,
                self.get_text_content,
                self.search_by_author,
                self.browse_by_religion,
                self.get_popular_texts
            ]
        )

    async def search_wikisource_scripture(self, query: str, language: str = "en", limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm văn bản tôn giáo, thần thoại trên Wikisource.

        Parameters:
        - query: <PERSON><PERSON> khóa tìm kiếm (tê<PERSON> v<PERSON><PERSON> b<PERSON>, t<PERSON><PERSON>, chủ đề...)
        - language: Mã ngôn ngữ (mặc định: 'en')
        - limit: Số lượng kết quả tối đa (mặc định: 5)

        Returns:
        - Dict chứa kết quả tìm kiếm với các thông tin: tiêu đề, tác giả, mô tả và URL
        """
        logger.info(f"Đang tìm kiếm trên Wikisource ({language}): {query}")

        try:
            # Gọi API tìm kiếm của MediaWiki
            api_url = f"https://{language}.wikisource.org/w/api.php"
            params = {
                "action": "query",
                "list": "search",
                "srsearch": query,
                "format": "json",
                "srlimit": limit
            }
            
            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikisource",
                    "message": f"Lỗi kết nối. Mã lỗi: {response.status_code}",
                    "query": query
                }
                
            data = response.json()
            search_results = data.get("query", {}).get("search", [])[:limit]
            results = []
            
            for item in search_results:
                page_id = item.get("pageid")
                title = item.get("title")
                snippet = item.get("snippet")
                
                # Lấy thêm thông tin chi tiết
                info_url = f"https://{language}.wikisource.org/w/api.php"
                info_params = {
                    "action": "query",
                    "prop": "extracts|pageprops",
                    "exintro": True,
                    "explaintext": True,
                    "pageids": page_id,
                    "format": "json"
                }
                
                try:
                    info_resp = requests.get(info_url, params=info_params, timeout=10)
                    if info_resp.status_code == 200:
                        info_data = info_resp.json()
                        page_info = info_data.get("query", {}).get("pages", {}).get(str(page_id), {})
                        extract = page_info.get("extract", "")
                        
                        # Lấy tác giả nếu có
                        author = page_info.get("pageprops", {}).get("author", "Không rõ tác giả")
                        
                        results.append({
                            "title": title,
                            "author": author,
                            "snippet": snippet,
                            "extract": extract[:300] + "..." if len(extract) > 300 else extract,
                            "page_url": f"https://{language}.wikisource.org/wiki/{title.replace(' ', '_')}",
                            "page_id": page_id
                        })
                except Exception as e:
                    log_debug(f"Lỗi khi lấy thông tin chi tiết: {str(e)}")
                    results.append({
                        "title": title,
                        "snippet": snippet,
                        "page_url": f"https://{language}.wikisource.org/wiki/{title.replace(' ', '_')}",
                        "page_id": page_id
                    })
            
            return {
                "status": "success",
                "source": "Wikisource",
                "query": query,
                "language": language,
                "results_count": len(results),
                "results": results,
                "search_url": f"https://{language}.wikisource.org/w/index.php?search={query.replace(' ', '+')}",
                "keyword_guide": [
                    "Kinh thánh",
                    "Kinh Phật",
                    "Kinh Vệ Đà",
                    "Kinh Koran",
                    "Talmud",
                    "Kinh điển Đạo giáo",
                    "tác giả:<tên tác giả>"
                ]
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Wikisource: {str(e)}")
            return {
                "status": "error",
                "source": "Wikisource",
                "message": str(e),
                "query": query
            }
            
    async def get_text_content(self, title: str, language: str = "en") -> Dict[str, Any]:
        """
        Lấy nội dung đầy đủ của một văn bản từ Wikisource.

        Parameters:
        - title: Tiêu đề chính xác của văn bản
        - language: Mã ngôn ngữ Wikisource (mặc định: 'en')

        Returns:
        - Dict chứa nội dung đầy đủ và siêu dữ liệu
        """
        logger.info(f"Đang lấy nội dung từ Wikisource: {title}")

        try:
            # Lấy thông tin cơ bản
            api_url = f"https://{language}.wikisource.org/w/api.php"
            params = {
                "action": "parse",
                "page": title,
                "prop": "text|sections|displaytitle",
                "format": "json"
            }
            
            response = requests.get(api_url, params=params, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikisource",
                    "message": f"Không tìm thấy văn bản. Mã lỗi: {response.status_code}",
                    "title": title
                }
                
            data = response.json()
            parse_data = data.get("parse", {})
            
            # Lấy nội dung HTML
            html_content = parse_data.get("text", {}).get("*", "")
            
            # Lấy mục lục
            sections = [{"level": s.get("level"), "line": s.get("line")} 
                      for s in parse_data.get("sections", [])]
            
            # Lấy thông tin tác giả và siêu dữ liệu
            metadata_params = {
                "action": "query",
                "prop": "pageprops|revisions",
                "titles": title,
                "rvprop": "timestamp|user",
                "format": "json"
            }
            
            metadata_resp = requests.get(api_url, params=metadata_params, timeout=10)
            metadata = {}
            if metadata_resp.status_code == 200:
                pages = metadata_resp.json().get("query", {}).get("pages", {})
                if pages:
                    page_data = next(iter(pages.values()))
                    metadata = {
                        "author": page_data.get("pageprops", {}).get("author", "Không rõ"),
                        "last_modified": page_data.get("revisions", [{}])[0].get("timestamp", ""),
                        "last_editor": page_data.get("revisions", [{}])[0].get("user", "")
                    }
            
            return {
                "status": "success",
                "source": "Wikisource",
                "title": parse_data.get("title"),
                "displaytitle": parse_data.get("displaytitle", ""),
                "metadata": metadata,
                "sections": sections,
                "content_length": len(html_content),
                "page_url": f"https://{language}.wikisource.org/wiki/{title.replace(' ', '_')}",
                "download_url": f"https://{language}.wikisource.org/w/index.php?title=Special:DownloadAsPdf&page={title.replace(' ', '_')}"
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy nội dung: {str(e)}")
            return {
                "status": "error",
                "source": "Wikisource",
                "message": str(e),
                "title": title
            }
    
    async def search_by_author(self, author: str, language: str = "en", limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm văn bản theo tác giả.

        Parameters:
        - author: Tên tác giả cần tìm
        - language: Mã ngôn ngữ (mặc định: 'en')
        - limit: Số lượng kết quả tối đa (mặc định: 5)

        Returns:
        - Dict chứa danh sách các tác phẩm của tác giả
        """
        return await self.search_wikisource_scripture(f"author:{author}", language, limit)
    
    async def browse_by_religion(self, religion: str, language: str = "en", limit: int = 5) -> Dict[str, Any]:
        """
        Duyệt văn bản theo tôn giáo hoặc truyền thống.

        Parameters:
        - religion: Tên tôn giáo (ví dụ: 'Buddhism', 'Christianity', 'Hinduism')
        - language: Mã ngôn ngữ (mặc định: 'en')
        - limit: Số lượng kết quả tối đa (mặc định: 5)

        Returns:
        - Dict chứa danh sách văn bản theo tôn giáo
        """
        return await self.search_wikisource_scripture(f"{religion} scripture", language, limit)
    
    async def get_popular_texts(self, language: str = "en", limit: int = 5) -> Dict[str, Any]:
        """
        Lấy danh sách các văn bản phổ biến.

        Parameters:
        - language: Mã ngôn ngữ (mặc định: 'en')
        - limit: Số lượng kết quả tối đa (mặc định: 5)

        Returns:
        - Dict chứa danh sách văn bản phổ biến
        """
        popular_searches = [
            "Bible", "Q1845",  # Kinh Thánh
            "Q42941",  # Kinh Koran
            "Bhagavad Gita",  # Ấn Độ giáo
            "Tao Te Ching",  # Đạo Đức Kinh
            "Analects"  # Luận Ngữ
        ]
        
        results = []
        for query in popular_searches[:limit]:
            try:
                result = await self.search_wikisource_scripture(query, language, 1)
                if result.get("status") == "success" and result.get("results"):
                    results.append(result["results"][0])
            except Exception as e:
                log_debug(f"Lỗi khi lấy văn bản phổ biến {query}: {str(e)}")
        
        return {
            "status": "success",
            "source": "Wikisource",
            "results_count": len(results),
            "results": results
        }
