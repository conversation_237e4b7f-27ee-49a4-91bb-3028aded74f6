from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class WorldBankDataTool(Toolkit):
    """
    World Bank Data Tool cho tìm kiếm dữ liệu kinh tế, tà<PERSON>, xã hội từ World Bank Open Data.
    """

    def __init__(self):
        super().__init__(
            name="World Bank Open Data Search Tool",
            tools=[self.search_world_bank_data]
        )

    async def search_world_bank_data(
        self,
        indicator: str,
        country: Optional[str] = None,
        date: Optional[str] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        Tìm kiếm dữ liệu World Bank Open Data cho chỉ số kinh tế, tài ch<PERSON>h, xã hội.

        Parameters:
        - indicator: Mã chỉ số hoặc từ khóa (ví dụ: 'NY.GDP.MKTP.CD', 'SP.POP.TOTL', 'GDP', 'population')
        - country: Mã quốc gia (ISO2/ISO3) hoặc 'all' (ví dụ: 'VN', 'USA', 'all')
        - date: Kho<PERSON>ng năm (ví dụ: '2010:2022', '2020', None)
        - limit: Số lượng kết quả tối đa (default: 10)

        Returns:
        - JSON với dữ liệu chỉ số, quốc gia, năm, giá trị, link World Bank
        """
        logger.info(f"Tìm kiếm World Bank Data: indicator={indicator}, country={country}, date={date}, limit={limit}")

        try:
            # Bước 1: Nếu indicator là từ khóa, tìm mã chỉ số phù hợp
            indicator_code = indicator
            if not indicator.isupper() or "." not in indicator:
                # Tìm kiếm mã chỉ số qua API
                search_url = "http://api.worldbank.org/v2/indicator"
                params = {
                    "format": "json",
                    "per_page": 20,
                    "q": indicator
                }
                resp = requests.get(search_url, params=params, timeout=10)
                if resp.status_code == 200:
                    data = resp.json()
                    if isinstance(data, list) and len(data) > 1 and data[1]:
                        indicator_code = data[1][0].get("id", indicator)
            # Bước 2: Lấy dữ liệu chỉ số
            country_code = country or "all"
            data_url = f"http://api.worldbank.org/v2/country/{country_code}/indicator/{indicator_code}"
            params = {
                "format": "json",
                "per_page": limit
            }
            if date:
                params["date"] = date
            resp = requests.get(data_url, params=params, timeout=10)
            if resp.status_code != 200:
                return {
                    "status": "error",
                    "source": "World Bank Open Data",
                    "message": f"API trả về mã lỗi {resp.status_code}",
                    "indicator": indicator,
                    "country": country,
                    "date": date
                }
            data = resp.json()
            results = []
            if isinstance(data, list) and len(data) > 1:
                for item in data[1]:
                    results.append({
                        "indicator": item.get("indicator", {}).get("id"),
                        "indicator_name": item.get("indicator", {}).get("value"),
                        "country": item.get("country", {}).get("id"),
                        "country_name": item.get("country", {}).get("value"),
                        "date": item.get("date"),
                        "value": item.get("value"),
                        "unit": item.get("unit"),
                        "obs_status": item.get("obs_status"),
                        "decimal": item.get("decimal"),
                        "wb_url": f"https://data.worldbank.org/indicator/{indicator_code}"
                    })
            return {
                "status": "success",
                "source": "World Bank Open Data",
                "indicator": indicator,
                "indicator_code": indicator_code,
                "country": country,
                "date": date,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "GDP",
                    "population",
                    "inflation",
                    "unemployment",
                    "poverty",
                    "NY.GDP.MKTP.CD",
                    "SP.POP.TOTL",
                    "SL.UEM.TOTL.ZS",
                    "FP.CPI.TOTL"
                ],
                "official_data_url": "https://data.worldbank.org/"
            }
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm World Bank Data: {str(e)}")
            return {
                "status": "error",
                "source": "World Bank Open Data",
                "message": str(e),
                "indicator": indicator,
                "country": country,
                "date": date
            }
