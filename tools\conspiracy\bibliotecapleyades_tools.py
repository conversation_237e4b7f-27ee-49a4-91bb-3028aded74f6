import requests
import json
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
from bs4 import BeautifulSoup
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class BibliotecaPleyadesTools(Toolkit):
    """
    Công cụ tìm kiếm và truy xuất thông tin từ thư viện Biblioteca Pleyades.
    
    Thư viện chứa các tài liệu về thuyết âm mưu, người ngoài hành tinh cổ đại,
    ch<PERSON>h phủ bí mật và các hiện tượng huyền bí.
    
    Keyword gợi ý: "ancient aliens", "secret government", "extraterrestrial",
    "conspiracy theories", "ufo sightings", "ancient civilizations"
    """
    
    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(name="biblioteca_pleyades_tools", **kwargs)
        self.base_url = "https://www.bibliotecapleyades.net"
        self.search_url = f"{self.base_url}/search/search.php"
        if enable_search:
            self.register(self.search_articles)
    
    def search_articles(self, query: str, max_results: int = 5) -> str:
        """
        Tìm kiếm bài viết trên Biblioteca Pleyades.
        
        Args:
            query (str): Từ khóa tìm kiếm (ví dụ: "ancient aliens", "secret government")
            max_results (int, optional): Số lượng kết quả tối đa. Mặc định: 5.
            
        Returns:
            str: Chuỗi JSON chứa kết quả tìm kiếm
            
        Ví dụ:
            search_articles("ancient aliens", 3)
        """
        log_debug(f"Tìm kiếm trên Biblioteca Pleyades: {query}")
        
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        try:
            # Gửi yêu cầu tìm kiếm
            response = requests.get(
                self.search_url,
                params={"q": query},
                headers=headers,
                timeout=15
            )
            response.raise_for_status()
            
            # Phân tích kết quả HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # Lấy các kết quả tìm kiếm
            search_results = soup.select('.search-results .result')
            
            for result in search_results[:max_results]:
                title_elem = result.select_one('h3 a')
                if not title_elem:
                    continue
                    
                title = title_elem.get_text(strip=True)
                url = title_elem['href']
                if not url.startswith('http'):
                    url = f"{self.base_url}/{url.lstrip('/')}"
                
                # Lấy mô tả nếu có
                desc_elem = result.select_one('.description')
                description = desc_elem.get_text(strip=True) if desc_elem else ""
                
                results.append({
                    "title": title,
                    "url": url,
                    "summary": description,
                    "source": "Biblioteca Pleyades"
                })
            
            # Nếu không có kết quả, trả về kết quả mặc định
            if not results:
                return self._get_default_results(query)
            
            return json.dumps({
                "status": "success",
                "source": "Biblioteca Pleyades",
                "query": query,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)
            
        except requests.RequestException as e:
            logger.error(f"Lỗi khi truy vấn Biblioteca Pleyades: {e}")
            return self._get_error_response(query, str(e))
    
    def _get_default_results(self, query: str) -> str:
        """Trả về kết quả mặc định khi không tìm thấy kết quả."""
        default_results = [
            {
                "title": "Ancient Aliens",
                "url": "https://www.bibliotecapleyades.net/ancient/esp_ancient_aliens.htm",
                "summary": "Các bài viết và tài liệu về thuyết người ngoài hành tinh cổ đại.",
                "source": "Biblioteca Pleyades"
            },
            {
                "title": "Secret Government",
                "url": "https://www.bibliotecapleyades.net/sociopolitica/esp_sociopol_secretgov.htm",
                "summary": "Các lý thuyết và tài liệu về các hoạt động bí mật của chính phủ.",
                "source": "Biblioteca Pleyades"
            },
            {
                "title": "Extraterrestrial Life",
                "url": "https://www.bibliotecapleyades.net/vida_alien/esp_vida_alien.htm",
                "summary": "Nghiên cứu và tài liệu về sự sống ngoài Trái Đất.",
                "source": "Biblioteca Pleyades"
            }
        ]
        
        return json.dumps({
            "status": "success",
            "source": "Biblioteca Pleyades",
            "query": query,
            "message": "Không tìm thấy kết quả phù hợp. Dưới đây là một số tài nguyên mặc định.",
            "results": default_results,
            "result_count": len(default_results)
        }, indent=2, ensure_ascii=False)
    
    def _get_error_response(self, query: str, error_msg: str) -> str:
        """Trả về phản hồi lỗi có cấu trúc."""
        return json.dumps({
            "status": "error",
            "source": "Biblioteca Pleyades",
            "query": query,
            "message": f"Không thể truy xuất kết quả: {error_msg}",
            "results": []
        }, indent=2, ensure_ascii=False)
