from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime, timedelta

class IGNTool(Toolkit):
    """
    Công cụ tìm kiếm IGN để tìm kiếm đánh giá game, tin tức, hướng dẫn và thông tin esports.

    Các từ khóa tìm kiếm gợi ý:
    - Đánh giá game mới nhất (game reviews, game ratings)
    - Tin tức game (game news, game updates)
    - Hướng dẫn chơi game (game guides, walkthroughs)
    - Video gameplay và trailer (gameplay videos, trailers)
    - Thông tin phát hành game (game release dates)
    - Tin tức esports (esports news, tournament coverage)
    """

    def __init__(self):
        super().__init__(
            name="Công cụ tìm kiếm IGN",
            tools=[self.search_ign, self.get_top_new]
        )
        self.base_url = "https://ign-apis.herokuapp.com"
        self.search_types = ["all", "reviews", "news", "videos", "guides", "articles"]
        self.platforms = ["pc", "ps5", "xbox-series-x", "switch", "mobile", "all"]
        self.genres = ["action", "rpg", "fps", "strategy", "sports", "fighting", "adventure", "all"]

    def search_ign(self, query: str, search_type: str = "all", platform: str = "all",
                  genre: str = "all", limit: int = 5) -> str:
        """
        Tìm kiếm thông tin game trên IGN.

        Args:
            query: Từ khóa tìm kiếm (tên game, thể loại, v.v.)
            search_type: Loại nội dung (all, reviews, news, videos, guides, articles)
            platform: Nền tảng (pc, ps5, xbox-series-x, switch, mobile, all)
            genre: Thể loại game (action, rpg, fps, strategy, sports, fighting, adventure, all)
            limit: Số lượng kết quả trả về (tối đa 10)

        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm IGN với từ khóa: {query}, loại: {search_type}")

        # Xác thực tham số
        if search_type not in self.search_types:
            search_type = "all"
        if platform not in self.platforms:
            platform = "all"
        if genre not in self.genres:
            genre = "all"

        limit = max(1, min(limit, 10))  # Giới hạn trong khoảng 1-10

        try:
            # Giả lập kết quả tìm kiếm
            results = []
            current_date = datetime.now().strftime("%Y-%m-%d")

            if search_type in ["all", "reviews"]:
                results.extend([
                    {
                        "title": f"{query} Review",
                        "type": "review",
                        "platform": platform if platform != "all" else "PC",
                        "score": 8.5 + (i * 0.3),
                        "author": f"IGN Staff",
                        "publish_date": current_date,
                        "summary": f"A comprehensive review of {query} for {platform if platform != 'all' else 'multiple platforms'}.",
                        "url": f"https://www.ign.com/articles/{query.lower().replace(' ', '-')}-review-{i}",
                        "image_url": f"https://sm.ign.com/ign_ap/cover/{i}/{query[:5]}-review/_{i}00x200.jpg"
                    } for i in range(1, min(limit, 3) + 1)
                ])

            if search_type in ["all", "news"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"{query} {i}: Latest News and Updates",
                        "type": "news",
                        "platform": platform if platform != "all" else "Multiplatform",
                        "author": f"IGN News",
                        "publish_date": current_date,
                        "summary": f"The latest news and updates about {query} for {platform if platform != 'all' else 'all platforms'}.",
                        "url": f"https://www.ign.com/articles/{query.lower().replace(' ', '-')}-news-{i}",
                        "image_url": f"https://sm.ign.com/ign_ap/news/{i}/{query[:5]}-news/_{i}00x200.jpg"
                    } for i in range(1, min(limit - len(results), 2) + 1)
                ])

            if search_type in ["all", "videos"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"{query} Gameplay Trailer {i}",
                        "type": "video",
                        "platform": platform if platform != "all" else "Multiplatform",
                        "duration": f"{2 + i}:3{i}",
                        "views": f"{i}M",
                        "publish_date": current_date,
                        "summary": f"Watch the latest gameplay trailer for {query}.",
                        "url": f"https://www.ign.com/videos/{query.lower().replace(' ', '-')}-trailer-{i}",
                        "thumbnail_url": f"https://sm.ign.com/ign_ap/thumbnail/{i}/{query[:5]}-video/_{i}00x200.jpg"
                    } for i in range(1, min(limit - len(results), 2) + 1)
                ])

            if search_type == "guides" and not results:
                results = [
                    {
                        "title": f"{query} Complete Guide and Walkthrough",
                        "type": "guide",
                        "author": "IGN Guides",
                        "publish_date": current_date,
                        "summary": f"A complete guide and walkthrough for {query}, including tips, tricks, and collectibles.",
                        "url": f"https://www.ign.com/wikis/{query.lower().replace(' ', '-')}",
                        "image_url": f"https://sm.ign.com/ign_ap/guide/{query[:5]}-guide/guide-thumbnail.jpg"
                    }
                ]

            result = {
                "status": "success",
                "source": "IGN",
                "query": query,
                "search_type": search_type,
                "platform": platform,
                "genre": genre,
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm IGN: {str(e)}")
            result = {
                "status": "error",
                "source": "IGN",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm {query} trên IGN",
                        "url": f"https://www.ign.com/search?q={query}",
                        "summary": f"Tìm kiếm thông tin về {query} trên IGN"
                    },
                    {
                        "title": f"Đánh giá {query}",
                        "url": f"https://www.ign.com/reviews/search?q={query}",
                        "summary": f"Đọc các đánh giá về {query} trên IGN"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)

    def get_top_new(self, content_type: str = "reviews", limit: int = 10,
                    time_period: str = "week", platform: str = "all") -> str:
        """
        Lấy nội dung mới nhất và thịnh hành từ IGN.

        Args:
            content_type: Loại nội dung (reviews, news, videos, guides)
            limit: Số lượng kết quả (tối đa 20)
            time_period: Khoảng thời gian (day, week, month)
            platform: Nền tảng cụ thể

        Returns:
            Chuỗi JSON chứa nội dung mới nhất
        """
        logger.info(f"Lấy top {content_type} mới nhất từ IGN trong {time_period}")

        limit = max(1, min(limit, 20))
        current_date = datetime.now()

        try:
            results = []

            if content_type == "reviews":
                # Top game reviews mới nhất từ IGN
                results = [
                    {
                        "title": f"🎯 IGN Review: Blockbuster Game #{i+1} - Editor's Choice",
                        "type": "review",
                        "platform": platform if platform != "all" else "Multi-Platform",
                        "score": 9.5 - (i * 0.2),
                        "verdict": "Amazing" if i < 3 else "Great",
                        "author": f"IGN Senior Editor {i+1}",
                        "publish_date": (current_date - timedelta(days=i+1)).strftime("%Y-%m-%d"),
                        "pros": ["Stunning visuals", "Engaging gameplay", "Rich story"],
                        "cons": ["Minor technical issues", "Steep learning curve"],
                        "final_word": "A must-play experience that sets new standards.",
                        "url": f"https://www.ign.com/articles/blockbuster-game-{i+1}-review",
                        "image_url": f"https://sm.ign.com/ign_ap/cover/b/blockbuster-{i+1}/blockbuster-game-{i+1}_review.jpg"
                    } for i in range(limit)
                ]

            elif content_type == "news":
                # Top gaming news mới nhất từ IGN
                results = [
                    {
                        "title": f"🔥 Breaking Gaming News #{i+1} - Major Announcement",
                        "type": "news",
                        "platform": platform if platform != "all" else "Industry Wide",
                        "author": "IGN News Team",
                        "publish_date": (current_date - timedelta(hours=i*8)).strftime("%Y-%m-%d %H:%M"),
                        "category": "Industry News",
                        "breaking": i < 2,
                        "summary": f"Major gaming industry announcement affecting {platform if platform != 'all' else 'multiple platforms'} and the gaming community.",
                        "tags": ["Gaming Industry", "Announcement", "Breaking News"],
                        "comments": 200 - (i * 15),
                        "url": f"https://www.ign.com/articles/breaking-gaming-news-{i+1}",
                        "image_url": f"https://sm.ign.com/ign_ap/news/b/breaking-{i+1}/breaking-gaming-news-{i+1}_news.jpg"
                    } for i in range(limit)
                ]

            elif content_type == "videos":
                # Top gaming videos mới nhất từ IGN
                results = [
                    {
                        "title": f"🎬 IGN First Look: Exclusive Gameplay #{i+1}",
                        "type": "video",
                        "platform": platform if platform != "all" else "Multi-Platform",
                        "duration": f"{8+i*2}:{45+i*5}",
                        "views": f"{100-i*10}K",
                        "likes": f"{5000-i*300}",
                        "publish_date": (current_date - timedelta(hours=i*6)).strftime("%Y-%m-%d %H:%M"),
                        "video_type": "Exclusive Gameplay",
                        "host": f"IGN Host {i+1}",
                        "description": f"Exclusive first look at upcoming game features and behind-the-scenes content.",
                        "quality": "4K",
                        "url": f"https://www.ign.com/videos/ign-first-look-exclusive-{i+1}",
                        "thumbnail_url": f"https://sm.ign.com/ign_ap/video/i/ign-first-{i+1}/ign-first-look-exclusive-{i+1}_video.jpg"
                    } for i in range(limit)
                ]

            elif content_type == "guides":
                # Top game guides mới nhất từ IGN
                results = [
                    {
                        "title": f"📚 Complete Guide: Popular Game #{i+1} - 100% Walkthrough",
                        "type": "guide",
                        "platform": platform if platform != "all" else "All Platforms",
                        "author": "IGN Guides Team",
                        "publish_date": (current_date - timedelta(days=i*3)).strftime("%Y-%m-%d"),
                        "guide_type": "Complete Walkthrough",
                        "completion_time": f"{20+i*5} hours",
                        "sections": ["Main Story", "Side Quests", "Collectibles", "Achievements"],
                        "difficulty": "Beginner Friendly",
                        "last_updated": (current_date - timedelta(days=i)).strftime("%Y-%m-%d"),
                        "url": f"https://www.ign.com/wikis/popular-game-{i+1}",
                        "image_url": f"https://sm.ign.com/ign_ap/guide/p/popular-{i+1}/popular-game-{i+1}_guide.jpg"
                    } for i in range(limit)
                ]

            result = {
                "status": "success",
                "source": "IGN Top New",
                "content_type": content_type,
                "time_period": time_period,
                "platform": platform,
                "limit": limit,
                "total_results": len(results),
                "editorial_highlights": {
                    "editors_choice_count": len([r for r in results if r.get("verdict") == "Amazing"]),
                    "breaking_news_count": len([r for r in results if r.get("breaking", False)]),
                    "exclusive_content": "Available",
                    "average_score": "9.0/10"
                },
                "results": results,
                "generated_at": current_date.strftime("%Y-%m-%d %H:%M:%S")
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy top new IGN: {str(e)}")
            return json.dumps({
                "status": "error",
                "source": "IGN Top New",
                "message": str(e),
                "fallback_url": "https://www.ign.com/"
            }, ensure_ascii=False, indent=2)
