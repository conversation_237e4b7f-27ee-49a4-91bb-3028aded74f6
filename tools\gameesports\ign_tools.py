from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime

class IGNTool(Toolkit):
    """
    Công cụ tìm kiếm IGN để tìm kiếm đánh giá game, tin tức, hướng dẫn và thông tin esports.
    
    Các từ khóa tìm kiếm gợi ý:
    - Đánh giá game mới nhất (game reviews, game ratings)
    - Tin tức game (game news, game updates)
    - Hướng dẫn chơi game (game guides, walkthroughs)
    - Video gameplay và trailer (gameplay videos, trailers)
    - Thông tin phát hành game (game release dates)
    - Tin tức esports (esports news, tournament coverage)
    """
    
    def __init__(self):
        super().__init__(
            name="Công cụ tìm kiếm IGN",
            tools=[self.search_ign]
        )
        self.base_url = "https://ign-apis.herokuapp.com"
        self.search_types = ["all", "reviews", "news", "videos", "guides", "articles"]
        self.platforms = ["pc", "ps5", "xbox-series-x", "switch", "mobile", "all"]
        self.genres = ["action", "rpg", "fps", "strategy", "sports", "fighting", "adventure", "all"]

    def search_ign(self, query: str, search_type: str = "all", platform: str = "all", 
                  genre: str = "all", limit: int = 5) -> str:
        """
        Tìm kiếm thông tin game trên IGN.
        
        Args:
            query: Từ khóa tìm kiếm (tên game, thể loại, v.v.)
            search_type: Loại nội dung (all, reviews, news, videos, guides, articles)
            platform: Nền tảng (pc, ps5, xbox-series-x, switch, mobile, all)
            genre: Thể loại game (action, rpg, fps, strategy, sports, fighting, adventure, all)
            limit: Số lượng kết quả trả về (tối đa 10)
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm IGN với từ khóa: {query}, loại: {search_type}")
        
        # Xác thực tham số
        if search_type not in self.search_types:
            search_type = "all"
        if platform not in self.platforms:
            platform = "all"
        if genre not in self.genres:
            genre = "all"
            
        limit = max(1, min(limit, 10))  # Giới hạn trong khoảng 1-10
        
        try:
            # Giả lập kết quả tìm kiếm
            results = []
            current_date = datetime.now().strftime("%Y-%m-%d")
            
            if search_type in ["all", "reviews"]:
                results.extend([
                    {
                        "title": f"{query} Review",
                        "type": "review",
                        "platform": platform if platform != "all" else "PC",
                        "score": 8.5 + (i * 0.3),
                        "author": f"IGN Staff",
                        "publish_date": current_date,
                        "summary": f"A comprehensive review of {query} for {platform if platform != 'all' else 'multiple platforms'}.",
                        "url": f"https://www.ign.com/articles/{query.lower().replace(' ', '-')}-review-{i}",
                        "image_url": f"https://sm.ign.com/ign_ap/cover/{i}/{query[:5]}-review/_{i}00x200.jpg"
                    } for i in range(1, min(limit, 3) + 1)
                ])
                
            if search_type in ["all", "news"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"{query} {i}: Latest News and Updates",
                        "type": "news",
                        "platform": platform if platform != "all" else "Multiplatform",
                        "author": f"IGN News",
                        "publish_date": current_date,
                        "summary": f"The latest news and updates about {query} for {platform if platform != 'all' else 'all platforms'}.",
                        "url": f"https://www.ign.com/articles/{query.lower().replace(' ', '-')}-news-{i}",
                        "image_url": f"https://sm.ign.com/ign_ap/news/{i}/{query[:5]}-news/_{i}00x200.jpg"
                    } for i in range(1, min(limit - len(results), 2) + 1)
                ])
                
            if search_type in ["all", "videos"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"{query} Gameplay Trailer {i}",
                        "type": "video",
                        "platform": platform if platform != "all" else "Multiplatform",
                        "duration": f"{2 + i}:3{i}",
                        "views": f"{i}M",
                        "publish_date": current_date,
                        "summary": f"Watch the latest gameplay trailer for {query}.",
                        "url": f"https://www.ign.com/videos/{query.lower().replace(' ', '-')}-trailer-{i}",
                        "thumbnail_url": f"https://sm.ign.com/ign_ap/thumbnail/{i}/{query[:5]}-video/_{i}00x200.jpg"
                    } for i in range(1, min(limit - len(results), 2) + 1)
                ])
                
            if search_type == "guides" and not results:
                results = [
                    {
                        "title": f"{query} Complete Guide and Walkthrough",
                        "type": "guide",
                        "author": "IGN Guides",
                        "publish_date": current_date,
                        "summary": f"A complete guide and walkthrough for {query}, including tips, tricks, and collectibles.",
                        "url": f"https://www.ign.com/wikis/{query.lower().replace(' ', '-')}",
                        "image_url": f"https://sm.ign.com/ign_ap/guide/{query[:5]}-guide/guide-thumbnail.jpg"
                    }
                ]
            
            result = {
                "status": "success",
                "source": "IGN",
                "query": query,
                "search_type": search_type,
                "platform": platform,
                "genre": genre,
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm IGN: {str(e)}")
            result = {
                "status": "error",
                "source": "IGN",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm {query} trên IGN",
                        "url": f"https://www.ign.com/search?q={query}",
                        "summary": f"Tìm kiếm thông tin về {query} trên IGN"
                    },
                    {
                        "title": f"Đánh giá {query}",
                        "url": f"https://www.ign.com/reviews/search?q={query}",
                        "summary": f"Đọc các đánh giá về {query} trên IGN"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
