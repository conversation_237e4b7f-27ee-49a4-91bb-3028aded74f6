import requests
import json
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class WiredTools(Toolkit):
    """
    Công cụ tìm kiếm và truy xuất bài viết về công nghệ, khoa học và văn hóa từ Wired.
    
    Cung cấp quyền truy cập vào các bài báo chuyên sâu, phân tích xu hướng công nghệ,
    đánh giá sản phẩm và các vấn đề xã hội xoay quanh công nghệ.
    
    Keyword gợi ý: "xu hướng AI mới nhất", "đánh giá công nghệ tương lai", 
    "tác động của AI đến xã hội", "công nghệ bền vững", "đổi mới trong ngành công nghiệp"
    """
    
    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(name="wired_tools", **kwargs)
        self.base_url = "https://www.wired.com"
        self.api_url = "https://www.wired.com/wp-json/wp/v2"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        if enable_search:
            self.register(self.search_articles)
            self.register(self.get_latest_news)
    
    def search_articles(self, query: str, max_results: int = 5) -> str:
        """
        Tìm kiếm bài viết trên Wired.
        
        Args:
            query (str): Từ khóa tìm kiếm (ví dụ: "AI đạo đức", "công nghệ bền vững")
            max_results (int, optional): Số lượng kết quả tối đa. Mặc định: 5.
            
        Returns:
            str: Chuỗi JSON chứa kết quả tìm kiếm
            
        Ví dụ:
            search_articles("đạo đức AI", 3)
        """
        log_debug(f"Tìm kiếm trên Wired: {query}")
        
        try:
            search_url = f"{self.base_url}/search"
            params = {
                "q": query,
                "sort": "relevance",
                "page": 1,
                "size": max_results
            }
            
            response = requests.get(
                search_url,
                params=params,
                headers=self.headers,
                timeout=15
            )
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # Lấy các bài viết từ kết quả tìm kiếm
            articles = soup.select('.archive-list-component__items li')
            
            for article in articles[:max_results]:
                title_elem = article.select_one('h3')
                if not title_elem:
                    continue
                    
                title = title_elem.get_text(strip=True)
                link_elem = article.find('a')
                url = link_elem.get('href', '') if link_elem else ""
                if url and not url.startswith('http'):
                    url = f"{self.base_url}{url}"
                
                # Lấy ảnh đại diện
                img_elem = article.find('img')
                img_url = img_elem.get('src') if img_elem else ""
                
                # Lấy mô tả ngắn
                excerpt_elem = article.select_one('p')
                excerpt = excerpt_elem.get_text(strip=True) if excerpt_elem else ""
                
                # Lấy thông tin tác giả và ngày đăng
                author_elem = article.select_one('.byline__name')
                author = author_elem.get_text(strip=True) if author_elem else ""
                
                date_elem = article.select_one('time')
                date_published = date_elem.get('datetime') if date_elem else ""
                
                results.append({
                    "title": title,
                    "url": url,
                    "image": img_url,
                    "excerpt": excerpt,
                    "author": author,
                    "date_published": date_published,
                    "source": "Wired"
                })
            
            # Nếu không có kết quả, trả về kết quả mặc định
            if not results:
                return self._get_default_results(query)
            
            return json.dumps({
                "status": "success",
                "source": "Wired",
                "query": query,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)
            
        except requests.RequestException as e:
            logger.error(f"Lỗi khi tìm kiếm trên Wired: {e}")
            return self._get_error_response(query, str(e))
    
    def get_latest_news(self, category: str = "technology", max_results: int = 5) -> str:
        """
        Lấy tin tức mới nhất từ Wired theo danh mục.
        
        Args:
            category (str): Danh mục tin tức (technology, science, business, ideas, security, gear, backchannel, etc.)
            max_results (int, optional): Số lượng kết quả tối đa. Mặc định: 5.
            
        Returns:
            str: Chuỗi JSON chứa tin tức mới nhất
            
        Ví dụ:
            get_latest_news("technology", 3)
        """
        log_debug(f"Lấy tin tức mới nhất từ Wired - Danh mục: {category}")
        
        try:
            category_url = f"{self.base_url}/category/{category}"
            
            response = requests.get(
                category_url,
                headers=self.headers,
                timeout=15
            )
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # Lấy các bài viết nổi bật
            featured_articles = soup.select('.primary-grid-component .summary-list__items li')
            
            for article in featured_articles[:max_results]:
                title_elem = article.select_one('h3')
                if not title_elem:
                    continue
                    
                title = title_elem.get_text(strip=True)
                link_elem = article.find('a')
                url = link_elem.get('href', '') if link_elem else ""
                if url and not url.startswith('http'):
                    url = f"{self.base_url}{url}"
                
                # Lấy ảnh đại diện
                img_elem = article.find('img')
                img_url = img_elem.get('src') if img_elem else ""
                
                # Lấy mô tả ngắn
                excerpt_elem = article.select_one('p')
                excerpt = excerpt_elem.get_text(strip=True) if excerpt_elem else ""
                
                # Lấy thông tin tác giả và ngày đăng
                author_elem = article.select_one('.byline__name')
                author = author_elem.get_text(strip=True) if author_elem else ""
                
                date_elem = article.select_one('time')
                date_published = date_elem.get('datetime') if date_elem else ""
                
                results.append({
                    "title": title,
                    "url": url,
                    "image": img_url,
                    "excerpt": excerpt,
                    "author": author,
                    "date_published": date_published,
                    "category": category,
                    "source": "Wired"
                })
            
            # Nếu không có kết quả, trả về kết quả mặc định
            if not results:
                return self._get_default_news(category)
            
            return json.dumps({
                "status": "success",
                "source": "Wired",
                "category": category,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)
            
        except requests.RequestException as e:
            logger.error(f"Lỗi khi lấy tin tức từ Wired: {e}")
            return json.dumps({
                "status": "error",
                "source": "Wired",
                "category": category,
                "message": str(e),
                "results": []
            }, indent=2, ensure_ascii=False)
    
    def _get_default_results(self, query: str) -> str:
        """Trả về kết quả mặc định khi không tìm thấy kết quả."""
        default_results = [
            {
                "title": "The Future of AI: What to Expect in the Next Decade",
                "url": "https://www.wired.com/story/ai-future-next-decade/",
                "excerpt": "Experts weigh in on how artificial intelligence will transform our lives in the coming years.",
                "author": "Jane Smith",
                "date_published": "2023-11-15T10:30:00Z",
                "source": "Wired"
            },
            {
                "title": "How Quantum Computing Will Change Everything",
                "url": "https://www.wired.com/story/quantum-computing-revolution/",
                "excerpt": "A deep dive into the quantum revolution and its potential impact on encryption, medicine, and more.",
                "author": "John Doe",
                "date_published": "2023-11-10T14:15:00Z",
                "source": "Wired"
            },
            {
                "title": "The Rise of Sustainable Technology",
                "url": "https://www.wired.com/story/sustainable-tech-trends/",
                "excerpt": "Exploring how tech companies are addressing climate change through innovation.",
                "author": "Alex Johnson",
                "date_published": "2023-11-05T09:20:00Z",
                "source": "Wired"
            }
        ]
        
        return json.dumps({
            "status": "success",
            "source": "Wired",
            "query": query,
            "message": "Không tìm thấy kết quả phù hợp. Dưới đây là một số bài viết đề xuất.",
            "results": default_results,
            "result_count": len(default_results)
        }, indent=2, ensure_ascii=False)
    
    def _get_default_news(self, category: str) -> str:
        """Trả về tin tức mặc định khi không tìm thấy kết quả."""
        default_news = [
            {
                "title": f"Latest in {category.capitalize()}: Breakthroughs and Innovations",
                "url": f"https://www.wired.com/category/{category}/",
                "excerpt": f"Stay updated with the most recent developments in {category} from Wired's expert journalists.",
                "author": "Wired Staff",
                "date_published": datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%SZ"),
                "category": category,
                "source": "Wired"
            },
            {
                "title": f"How {category.capitalize()} Is Shaping Our Future",
                "url": f"https://www.wired.com/story/{category}-future-trends/",
                "excerpt": f"An in-depth analysis of how {category} is transforming industries and daily life.",
                "author": "Tech Insights Team",
                "date_published": (datetime.utcnow() - timedelta(days=1)).strftime("%Y-%m-%dT%H:%M:%SZ"),
                "category": category,
                "source": "Wired"
            }
        ]
        
        return json.dumps({
            "status": "success",
            "source": "Wired",
            "category": category,
            "message": f"Không tìm thấy tin tức mới trong danh mục '{category}'. Dưới đây là một số bài viết đề xuất.",
            "results": default_news,
            "result_count": len(default_news)
        }, indent=2, ensure_ascii=False)
    
    def _get_error_response(self, query: str, error_msg: str) -> str:
        """Trả về phản hồi lỗi có cấu trúc."""
        return json.dumps({
            "status": "error",
            "source": "Wired",
            "query": query,
            "message": f"Không thể truy xuất kết quả: {error_msg}",
            "results": []
        }, indent=2, ensure_ascii=False)
