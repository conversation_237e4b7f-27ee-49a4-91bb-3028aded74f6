from typing import Dict, Any, List, Optional, Union
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime, timedelta
from urllib.parse import quote
import random

class WikipediaGreenTool(Toolkit):
    """
    Công cụ tìm kiếm Wikipedia cho các chủ đề bền vững, mô<PERSON> tr<PERSON>, sống xanh, năng lư<PERSON> tái tạo, tiêu dùng xanh.
    
    <PERSON><PERSON><PERSON> từ khóa tìm kiếm gợi ý:
    - Sống bền vững (sustainable living)
    - Năng lượng tái tạo (renewable energy)
    - <PERSON><PERSON><PERSON> chế (recycling, upcycling)
    - <PERSON><PERSON>ng nghiệp bền vững (sustainable agriculture)
    - Thời trang bền vững (sustainable fashion)
    - <PERSON><PERSON>n trúc xanh (green architecture)
    - Giao thông bền vững (sustainable transport)
    - <PERSON>nh tế tuần hoàn (circular economy)
    """

    def __init__(self):
        super().__init__(
            name="<PERSON>ông cụ tìm kiếm Wikipedia Xanh",
            tools=[self.search_wikipedia_green]
        )
        self.languages = ["en", "vi", "fr", "es", "de", "ja", "zh", "ru"]
        self.categories = [
            "Sustainability", "Renewable_energy", "Eco-friendly_products",
            "Sustainable_agriculture", "Green_building", "Waste_management",
            "Climate_change_mitigation", "Sustainable_transport"
        ]

    def _get_random_related_topics(self, base_topic: str, count: int = 3) -> List[Dict[str, str]]:
        """Tạo danh sách các chủ đề liên quan ngẫu nhiên"""
        related_topics = [
            {"title": f"Lợi ích của {base_topic}", "url": f"https://wikipedia.org/wiki/{base_topic}_benefits"},
            {"title": f"Công nghệ {base_topic} mới nhất", "url": f"https://wikipedia.org/wiki/Technology_in_{base_topic}"},
            {"title": f"Lịch sử phát triển {base_topic}", "url": f"https://wikipedia.org/wiki/History_of_{base_topic}"},
            {"title": f"{base_topic.capitalize()} ở Việt Nam", "url": f"https://wikipedia.org/wiki/{base_topic}_in_Vietnam"},
            {"title": f"So sánh các giải pháp {base_topic}", "url": f"https://wikipedia.org/wiki/Comparison_of_{base_topic}_solutions"}
        ]
        return random.sample(related_topics, min(count, len(related_topics)))

    def search_wikipedia_green(self, query: str, language: str = "en", 
                             category: str = "", limit: int = 5) -> str:
        """
        Tìm kiếm Wikipedia cho các chủ đề bền vững và môi trường.

        Args:
            query: Từ khóa tìm kiếm (vd: 'năng lượng mặt trời', 'nhà thông minh', 'vật liệu tái chế')
            language: Mã ngôn ngữ Wikipedia (mặc định: 'en')
            category: Lọc theo danh mục (để trống cho tất cả)
            limit: Số lượng kết quả trả về (tối đa 10)
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm Wikipedia ({language}) với từ khóa: {query}")
        
        # Xác thực tham số
        if language not in self.languages:
            language = "en"
        limit = max(1, min(limit, 10))  # Giới hạn trong khoảng 1-10
        
        try:
            # Mã hóa truy vấn cho URL
            encoded_query = quote(query.replace(' ', '_'))
            
            # Tạo URL API Wikipedia
            if category and category in self.categories:
                search_url = f"https://{language}.wikipedia.org/w/api.php?action=query&list=search&srsearch={encoded_query}+incategory:{category}&format=json&srlimit={limit}"
            else:
                search_url = f"https://{language}.wikipedia.org/w/api.php?action=query&list=search&srsearch={encoded_query}&format=json&srlimit={limit}"
            
            # Gửi yêu cầu tìm kiếm
            response = requests.get(search_url, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            # Xử lý kết quả
            results = []
            if 'query' in data and 'search' in data['query']:
                for item in data['query']['search'][:limit]:
                    page_id = item['pageid']
                    title = item['title']
                    snippet = item['snippet'].replace('<span class="searchmatch">', '').replace('</span>', '')
                    
                    # Lấy thêm thông tin chi tiết
                    detail_url = f"https://{language}.wikipedia.org/api/rest_v1/page/summary/{quote(title)}"
                    detail_response = requests.get(detail_url, timeout=10)
                    
                    if detail_response.status_code == 200:
                        detail = detail_response.json()
                        result = {
                            "title": title,
                            "extract": detail.get('extract', snippet),
                            "description": detail.get('description', ''),
                            "url": detail.get('content_urls', {}).get('desktop', {}).get('page', f"https://{language}.wikipedia.org/wiki/{quote(title)}"),
                            "thumbnail": detail.get('thumbnail', {}).get('source', '') if 'thumbnail' in detail else '',
                            "related_topics": self._get_random_related_topics(query)
                        }
                        results.append(result)
            
            # Nếu không có kết quả, trả về gợi ý tìm kiếm
            if not results:
                return json.dumps({
                    "status": "success",
                    "source": "Wikipedia",
                    "query": query,
                    "language": language,
                    "message": "Không tìm thấy kết quả phù hợp. Bạn có thể thử với từ khóa khác hoặc xem các gợi ý bên dưới.",
                    "suggestions": [
                        f"{query} là gì?",
                        f"Lợi ích của {query}",
                        f"Công nghệ {query} mới nhất",
                        f"{query} ở Việt Nam"
                    ],
                    "results": []
                }, ensure_ascii=False, indent=2)
            
            # Tạo kết quả cuối cùng
            result = {
                "status": "success",
                "source": "Wikipedia",
                "query": query,
                "language": language,
                "category": category if category else "All Categories",
                "results_count": len(results),
                "results": results
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except requests.exceptions.RequestException as e:
            log_debug(f"Lỗi kết nối đến Wikipedia: {str(e)}")
            result = {
                "status": "error",
                "source": "Wikipedia",
                "message": f"Lỗi kết nối: {str(e)}",
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm '{query}' trên Wikipedia",
                        "url": f"https://{language}.wikipedia.org/w/index.php?search={quote(query)}",
                        "summary": f"Tìm kiếm thông tin về {query} trên Wikipedia"
                    },
                    {
                        "title": f"Danh mục Bền vững trên Wikipedia",
                        "url": f"https://{language}.wikipedia.org/wiki/Category:Sustainability",
                        "summary": "Khám phá các bài viết về chủ đề bền vững và môi trường"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
        except requests.exceptions.HTTPError as e:
            log_debug(f"Lỗi HTTP từ Wikipedia API: {e.response.status_code}")
            return json.dumps({
                "status": "error",
                "source": "Wikipedia",
                "message": f"Lỗi API Wikipedia với mã trạng thái {e.response.status_code}",
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm '{query}' trên Google",
                        "url": f"https://www.google.com/search?q={quote(query)}+site:wikipedia.org",
                        "summary": f"Thử tìm kiếm thông tin về {query} thông qua Google"
                    }
                ]
            }, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Error searching Wikipedia Green: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "query": query
            }
