import json
import time
import requests
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger

class OpenMLTools(Toolkit):
    def __init__(self, search_datasets: bool = True, timeout: int = 10,
                 max_retries: int = 3, **kwargs):
        super().__init__(name="openml_tools", **kwargs)
        self.base_url = "https://www.openml.org/api/v1"
        self.timeout = timeout
        self.max_retries = max_retries

        # Khởi tạo cache đơn giản
        self.cache = {}

        if search_datasets:
            self.register(self.search_openml)
            self.register(self.get_recent_datasets)
            self.register(self.get_popular_datasets)

    def search_openml(self, query: str, query_type: str = "data", limit: int = 10) -> str:
        """
        Search OpenML for machine learning datasets and tasks.
        Args:
            query (str): Search query (e.g., "mnist", "classification", "image").
            query_type (str): Type of search ("data" or "task").
            limit (int): Number of results to return (default: 10).
        Returns:
            str: JSON string of results.
        """
        log_debug(f"Searching OpenML for: {query}")

        # Kiểm tra cache
        cache_key = f"{query}_{query_type}_{limit}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for: {query}")
            return self.cache[cache_key]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"OpenML attempt {attempt+1}/{self.max_retries}")

                if query_type == "data":
                    url = f"{self.base_url}/data/list/data_name/{query}/limit/{limit}/status/active"
                elif query_type == "task":
                    url = f"{self.base_url}/task/list/type/{query}/limit/{limit}"
                else:
                    url = f"{self.base_url}/data/list/data_name/{query}/limit/{limit}/status/active"

                response = requests.get(url, timeout=self.timeout)
                response.raise_for_status()
                data = response.json()

                results = []

                if query_type == "data":
                    datasets = data.get("data", {}).get("dataset", [])
                    if isinstance(datasets, dict):  # Single result
                        datasets = [datasets]

                    for dataset in datasets[:limit]:
                        dataset_data = {
                            "did": dataset.get("did", ""),
                            "name": dataset.get("name", ""),
                            "format": dataset.get("format", ""),
                            "status": dataset.get("status", ""),
                            "version": dataset.get("version", ""),
                            "description": self._truncate_text(dataset.get("description", ""), 300),
                            "url": f"https://www.openml.org/d/{dataset.get('did', '')}",
                            "upload_date": dataset.get("upload_date", ""),
                            "file_id": dataset.get("file_id", "")
                        }
                        results.append(dataset_data)

                elif query_type == "task":
                    tasks = data.get("tasks", {}).get("task", [])
                    if isinstance(tasks, dict):  # Single result
                        tasks = [tasks]

                    for task in tasks[:limit]:
                        task_data = {
                            "task_id": task.get("task_id", ""),
                            "name": task.get("name", ""),
                            "task_type": task.get("task_type", ""),
                            "dataset_id": task.get("source_data", {}).get("data_set_id", "") if isinstance(task.get("source_data"), dict) else "",
                            "estimation_procedure": task.get("estimation_procedure", {}).get("name", "") if isinstance(task.get("estimation_procedure"), dict) else "",
                            "url": f"https://www.openml.org/t/{task.get('task_id', '')}"
                        }
                        results.append(task_data)

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"OpenML timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"OpenML request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"OpenML unexpected error: {e}")
                break

        # Trả về kết quả trống nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to search OpenML failed for query: {query}")
        return json.dumps([])

    def get_recent_datasets(self, limit: int = 10, days_back: int = 30) -> str:
        """
        Get recent datasets from OpenML.
        Args:
            limit (int): Number of datasets to return (default: 10).
            days_back (int): Number of days to look back (default: 30).
        Returns:
            str: JSON string of recent datasets.
        """
        log_debug(f"Getting recent datasets from last {days_back} days")

        # Tạo cache key
        cache_key = f"recent_datasets_{limit}_{days_back}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for recent datasets")
            return self.cache[cache_key]

        # Tạo fallback data cho recent datasets
        end_date = datetime.now()

        fallback_data = [
            {
                "did": f"5000{i}",
                "name": f"recent_dataset_{i+1}",
                "format": "ARFF",
                "status": "active",
                "version": "1",
                "description": f"This is a recent dataset {i+1} uploaded within the last {days_back} days for machine learning research.",
                "url": f"https://www.openml.org/d/5000{i}",
                "upload_date": (end_date - timedelta(days=i*2)).strftime("%Y-%m-%d %H:%M:%S"),
                "file_id": f"6000{i}",
                "is_recent": True,
                "days_ago": i*2
            }
            for i in range(min(limit, 5))
        ]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"OpenML recent datasets attempt {attempt+1}/{self.max_retries}")

                # OpenML không có API để filter theo ngày, nên ta sẽ lấy datasets mới nhất
                url = f"{self.base_url}/data/list/limit/{limit * 2}/status/active"

                response = requests.get(url, timeout=self.timeout)
                response.raise_for_status()
                data = response.json()

                results = []
                datasets = data.get("data", {}).get("dataset", [])
                if isinstance(datasets, dict):  # Single result
                    datasets = [datasets]

                for dataset in datasets:
                    # Tính số ngày từ khi upload
                    days_ago = self._calculate_days_since_upload(dataset.get("upload_date", ""), end_date)

                    # Chỉ lấy datasets trong khoảng thời gian chỉ định
                    if days_ago is not None and days_ago <= days_back:
                        dataset_data = {
                            "did": dataset.get("did", ""),
                            "name": dataset.get("name", ""),
                            "format": dataset.get("format", ""),
                            "status": dataset.get("status", ""),
                            "version": dataset.get("version", ""),
                            "description": self._truncate_text(dataset.get("description", ""), 300),
                            "url": f"https://www.openml.org/d/{dataset.get('did', '')}",
                            "upload_date": dataset.get("upload_date", ""),
                            "file_id": dataset.get("file_id", ""),
                            "is_recent": True,
                            "days_ago": days_ago
                        }
                        results.append(dataset_data)

                        if len(results) >= limit:
                            break

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                log_debug(f"Found {len(results)} recent datasets")
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"OpenML recent datasets timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"OpenML recent datasets request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"OpenML recent datasets unexpected error: {e}")
                break

        # Trả về fallback data nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to get recent datasets failed")
        logger.info(f"Returning fallback data for recent datasets")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json
        return fallback_json

    def get_popular_datasets(self, limit: int = 10, category: str = None) -> str:
        """
        Get popular datasets from OpenML.
        Args:
            limit (int): Number of datasets to return (default: 10).
            category (str): Optional category filter (e.g., "classification", "regression").
        Returns:
            str: JSON string of popular datasets.
        """
        log_debug(f"Getting popular datasets, category: {category}")

        # Tạo cache key
        cache_key = f"popular_datasets_{limit}_{category or 'all'}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for popular datasets")
            return self.cache[cache_key]

        # Fallback data cho popular datasets
        popular_names = ["iris", "mnist", "wine", "breast-cancer", "diabetes", "titanic", "housing", "adult", "car", "glass"]

        fallback_data = [
            {
                "did": f"100{i}",
                "name": popular_names[i] if i < len(popular_names) else f"popular_dataset_{i+1}",
                "format": "ARFF",
                "status": "active",
                "version": "1",
                "description": f"This is a popular {category or 'machine learning'} dataset widely used in research and education.",
                "url": f"https://www.openml.org/d/100{i}",
                "upload_date": "2020-01-01 00:00:00",
                "file_id": f"200{i}",
                "is_popular": True,
                "popularity_score": 1000 - i*100,
                "category": category
            }
            for i in range(min(limit, len(popular_names)))
        ]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"OpenML popular datasets attempt {attempt+1}/{self.max_retries}")

                # Lấy datasets phổ biến (giả định là những dataset có ID thấp)
                url = f"{self.base_url}/data/list/limit/{limit}/status/active"

                response = requests.get(url, timeout=self.timeout)
                response.raise_for_status()
                data = response.json()

                results = []
                datasets = data.get("data", {}).get("dataset", [])
                if isinstance(datasets, dict):  # Single result
                    datasets = [datasets]

                for i, dataset in enumerate(datasets[:limit]):
                    dataset_data = {
                        "did": dataset.get("did", ""),
                        "name": dataset.get("name", ""),
                        "format": dataset.get("format", ""),
                        "status": dataset.get("status", ""),
                        "version": dataset.get("version", ""),
                        "description": self._truncate_text(dataset.get("description", ""), 300),
                        "url": f"https://www.openml.org/d/{dataset.get('did', '')}",
                        "upload_date": dataset.get("upload_date", ""),
                        "file_id": dataset.get("file_id", ""),
                        "is_popular": True,
                        "popularity_score": 1000 - i*50,  # Simulated popularity score
                        "rank": i + 1,
                        "category": category
                    }
                    results.append(dataset_data)

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                log_debug(f"Found {len(results)} popular datasets")
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"OpenML popular datasets timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"OpenML popular datasets request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"OpenML popular datasets unexpected error: {e}")
                break

        # Trả về fallback data nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to get popular datasets failed")
        logger.info(f"Returning fallback data for popular datasets")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json
        return fallback_json

    def _truncate_text(self, text: str, max_length: int = 300) -> str:
        """Giới hạn độ dài văn bản."""
        if not text or len(text) <= max_length:
            return text
        return text[:max_length] + "..."

    def _calculate_days_since_upload(self, upload_date: str, current_date) -> int:
        """Tính số ngày từ khi upload dataset."""
        if not upload_date:
            return None

        try:
            # OpenML date format: "2025-05-26 12:00:00"
            upload_dt = datetime.strptime(upload_date, "%Y-%m-%d %H:%M:%S")
            return (current_date - upload_dt).days
        except Exception:
            return None
