from typing import Dict, Any, Optional, List, Union, Tuple
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger, log_warning
import requests
import re
import json
import asyncio
from datetime import datetime, timedelta
from urllib.parse import quote_plus, urlparse, parse_qs
from dataclasses import dataclass
from enum import Enum

# Các hằng số
CIA_BASE_URL = "https://www.cia.gov/the-world-factbook"
CIA_API_URL = f"{CIA_BASE_URL}/api"
CACHE_EXPIRY_DAYS = 7  # Thời gian hết hạn cache

class FactbookField(str, Enum):
    """Các lĩnh vực dữ liệu trong CIA Factbook"""
    INTRODUCTION = "introduction"
    GEOGRAPHY = "geography"
    PEOPLE_AND_SOCIETY = "people-and-society"
    ENVIRONMENT = "environment"
    GOVERNMENT = "government"
    ECONOMY = "economy"
    ENERGY = "energy"
    COMMUNICATIONS = "communications"
    TRANSPORTATION = "transportation"
    MILITARY = "military"
    TRANSNATIONAL_ISSUES = "transnational-issues"

@dataclass
class FactbookCache:
    data: Dict[str, Any]
    last_updated: datetime
    expiry_days: int = 7
    
    def is_expired(self) -> bool:
        return datetime.now() > (self.last_updated + timedelta(days=self.expiry_days))

class CIAFactbookTool(Toolkit):
    """
    CIA Factbook Tool cho tìm kiếm dữ liệu quốc gia, chỉ số, thông tin tổng quan từ CIA World Factbook.
    Hỗ trợ tìm kiếm thông tin chi tiết về các quốc gia, vùng lãnh thổ trên toàn thế giới.
    """

    def __init__(self):
        super().__init__(
            name="CIA World Factbook Search Tool",
            description="Tool cho tìm kiếm dữ liệu quốc gia, chỉ số, thông tin tổng quan từ CIA World Factbook.",
            tools=[
                self.search_cia_factbook,
                self.get_country_info,
                self.list_countries,
                self.get_field_info
            ]
        )
        self.base_url = CIA_BASE_URL
        self.api_url = CIA_API_URL
        self.cache: Dict[str, FactbookCache] = {}
        self.timeout = 15
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        self._load_cache()

    def _load_cache(self) -> None:
        """Tải dữ liệu cache từ file nếu có"""
        try:
            with open(".cia_factbook_cache.json", 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                for key, data in cache_data.items():
                    self.cache[key] = FactbookCache(
                        data=data['data'],
                        last_updated=datetime.fromisoformat(data['last_updated']),
                        expiry_days=data.get('expiry_days', 7)
                    )
        except (FileNotFoundError, json.JSONDecodeError):
            self.cache = {}

    def _save_cache(self) -> None:
        """Lưu dữ liệu cache vào file"""
        cache_data = {}
        for key, cache in self.cache.items():
            cache_data[key] = {
                'data': cache.data,
                'last_updated': cache.last_updated.isoformat(),
                'expiry_days': cache.expiry_days
            }
        with open(".cia_factbook_cache.json", 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)

    def _get_cache(self, key: str) -> Optional[Dict[str, Any]]:
        """Lấy dữ liệu từ cache nếu còn hiệu lực"""
        if key in self.cache and not self.cache[key].is_expired():
            return self.cache[key].data
        return None

    def _set_cache(self, key: str, data: Any, expiry_days: int = 7) -> None:
        """Lưu dữ liệu vào cache"""
        self.cache[key] = FactbookCache(
            data=data,
            last_updated=datetime.now(),
            expiry_days=expiry_days
        )
        self._save_cache()

    async def _fetch_with_retry(self, url: str, params: Optional[Dict] = None, max_retries: int = 3) -> Optional[requests.Response]:
        """Gửi yêu cầu HTTP với cơ chế thử lại"""
        for attempt in range(max_retries):
            try:
                response = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: requests.get(url, params=params, headers=self.headers, timeout=self.timeout)
                )
                response.raise_for_status()
                return response
            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    log_warning(f"Request failed after {max_retries} attempts: {str(e)}")
                    return None
                await asyncio.sleep(1 * (attempt + 1))
        return None

    async def list_countries(self) -> Dict[str, Any]:
        """
        Lấy danh sách tất cả các quốc gia và vùng lãnh thổ trong CIA Factbook
        
        Returns:
        - Dict chứa danh sách các quốc gia và thông tin cơ bản
        """
        cache_key = "countries_list"
        cached_data = self._get_cache(cache_key)
        if cached_data:
            return {"status": "success", "source": "CIA Factbook (cached)", "countries": cached_data}

        try:
            response = await self._fetch_with_retry(f"{self.api_url}/countries/")
            if not response or response.status_code != 200:
                return {
                    "status": "error",
                    "source": "CIA Factbook",
                    "message": f"Failed to fetch countries list (HTTP {response.status_code if response else 'timeout'})"
                }

            countries = response.json().get("countries", {})
            result = [{"name": name, "code": code} for code, name in countries.items()]
            
            # Lưu vào cache
            self._set_cache(cache_key, result)
            
            return {
                "status": "success",
                "source": "CIA Factbook",
                "count": len(result),
                "countries": result
            }

        except Exception as e:
            error_msg = f"Lỗi khi lấy danh sách quốc gia: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "source": "CIA Factbook",
                "message": error_msg
            }

    async def get_country_info(self, country: str, fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Lấy thông tin chi tiết về một quốc gia từ CIA Factbook
        
        Parameters:
        - country: Tên hoặc mã quốc gia (vd: 'Vietnam', 'VN', 'United States', 'US')
        - fields: Danh sách các lĩnh vực cần lấy (vd: ['geography', 'economy', 'military'])
        
        Returns:
        - Dict chứa thông tin chi tiết về quốc gia
        """
        cache_key = f"country_{country.lower()}_{'_'.join(sorted(fields)) if fields else 'all'}"
        cached_data = self._get_cache(cache_key)
        if cached_data:
            return {"status": "success", "source": "CIA Factbook (cached)", "data": cached_data}

        try:
            # Lấy danh sách quốc gia để tìm mã quốc gia
            countries_resp = await self.list_countries()
            if countries_resp["status"] != "success":
                return countries_resp
                
            # Tìm quốc gia phù hợp
            country_code = None
            country_name = None
            
            for c in countries_resp["countries"]:
                if (country.lower() in c["name"].lower() or 
                    country.lower() == c["code"].lower()):
                    country_code = c["code"]
                    country_name = c["name"]
                    break
            
            if not country_code:
                return {
                    "status": "error",
                    "source": "CIA Factbook",
                    "message": f"Không tìm thấy thông tin cho quốc gia: {country}",
                    "suggested_actions": [
                        "Kiểm tra lại tên quốc gia",
                        "Sử dụng tên tiếng Anh chính thức",
                        "Xem danh sách quốc gia bằng cách gọi list_countries()"
                    ]
                }
            
            # Nếu không chỉ định fields, lấy tất cả
            if not fields:
                fields = [f.value for f in FactbookField]
            
            # Lấy dữ liệu cho từng field
            result = {"name": country_name, "code": country_code, "fields": {}}
            
            for field in fields:
                field = field.lower().replace(" ", "-")
                field_url = f"{self.api_url}/countries/{country_code}/{field}"
                
                field_response = await self._fetch_with_retry(field_url)
                if field_response and field_response.status_code == 200:
                    result["fields"][field] = field_response.json()
            
            # Lưu vào cache
            self._set_cache(cache_key, result)
            
            return {
                "status": "success",
                "source": "CIA Factbook",
                "data": result
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy thông tin quốc gia: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "source": "CIA Factbook",
                "message": error_msg
            }

    async def get_field_info(self, field: Union[str, FactbookField]) -> Dict[str, Any]:
        """
        Lấy thông tin về một lĩnh vực cụ thể trong CIA Factbook
        
        Parameters:
        - field: Tên lĩnh vực (vd: 'economy', 'military', 'geography')
        
        Returns:
        - Dict chứa mô tả và các chỉ số liên quan đến lĩnh vực
        """
        try:
            field = field.lower().replace(" ", "-")
            field_enum = FactbookField(field)
            
            # Lấy thông tin từ API
            response = await self._fetch_with_retry(f"{self.api_url}/fields/{field}")
            if not response or response.status_code != 200:
                return {
                    "status": "error",
                    "source": "CIA Factbook",
                    "message": f"Không tìm thấy thông tin cho lĩnh vực: {field}"
                }
            
            data = response.json()
            return {
                "status": "success",
                "source": "CIA Factbook",
                "field": field_enum.value,
                "field_name": field_enum.name.replace("_", " ").title(),
                "description": data.get("description", ""),
                "indicators": data.get("indicators", []),
                "last_updated": data.get("last_updated", "")
            }
            
        except ValueError:
            return {
                "status": "error",
                "source": "CIA Factbook",
                "message": f"Lĩnh vực không hợp lệ: {field}",
                "available_fields": [f.value for f in FactbookField]
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy thông tin lĩnh vực: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "source": "CIA Factbook",
                "message": error_msg
            }

    async def search_cia_factbook(self, query: str, country: Optional[str] = None, 
                               field: Optional[Union[str, FactbookField]] = None, 
                               limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm thông tin từ CIA World Factbook.
        
        Parameters:
        - query: Từ khóa tìm kiếm (tên quốc gia, chỉ số, chủ đề)
        - country: Tên quốc gia cụ thể (tùy chọn)
        - field: Lĩnh vực cụ thể (tùy chọn, xem FactbookField enum)
        - limit: Số lượng kết quả tối đa (mặc định: 5)
        
        Returns:
        - Dict chứa kết quả tìm kiếm
        """
        logger.info(f"Tìm kiếm CIA Factbook: query={query}, country={country}, field={field}, limit={limit}")
        
        try:
            # Nếu có chỉ định quốc gia, ưu tiên lấy thông tin chi tiết
            if country:
                country_info = await self.get_country_info(country, [field] if field else None)
                if country_info["status"] == "success":
                    return {
                        "status": "success",
                        "source": "CIA Factbook",
                        "query": query,
                        "results": [country_info["data"]],
                        "results_count": 1,
                        "search_metadata": {
                            "query_type": "country_info",
                            "country": country,
                            "field": field
                        },
                        "keyword_guide": self._get_search_guides()
                    }
            
            # Nếu không có quốc gia cụ thể, tìm kiếm chung
            search_results = []
            
            # Tìm trong danh sách quốc gia trước
            countries_resp = await self.list_countries()
            if countries_resp["status"] == "success":
                query_lower = query.lower()
                matching_countries = [
                    c for c in countries_resp["countries"] 
                    if query_lower in c["name"].lower() or query_lower == c["code"].lower()
                ][:limit]
                
                for country_match in matching_countries:
                    country_info = await self.get_country_info(country_match["code"])
                    if country_info["status"] == "success":
                        search_results.append(country_info["data"])
            
            # Nếu có kết quả, trả về
            if search_results:
                return {
                    "status": "success",
                    "source": "CIA Factbook",
                    "query": query,
                    "results": search_results[:limit],
                    "results_count": len(search_results),
                    "search_metadata": {
                        "query_type": "country_search",
                        "matched_countries": [r["name"] for r in search_results]
                    },
                    "keyword_guide": self._get_search_guides()
                }
            
            # Nếu không tìm thấy kết quả, trả về thông báo
            return {
                "status": "not_found",
                "source": "CIA Factbook",
                "message": "Không tìm thấy kết quả phù hợp",
                "query": query,
                "suggested_actions": [
                    "Kiểm tra lại chính tả",
                    "Thử tìm kiếm với tên tiếng Anh chính thức",
                    f"Xem danh sách quốc gia bằng cách gọi list_countries()"
                ],
                "keyword_guide": self._get_search_guides()
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi tìm kiếm CIA Factbook: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "source": "CIA Factbook",
                "message": error_msg,
                "query": query,
                "suggested_actions": [
                    "Kiểm tra kết nối mạng",
                    "Thử lại sau ít phút"
                ]
            }
    
    def _get_search_guides(self) -> List[Dict[str, str]]:
        """Trả về danh sách hướng dẫn tìm kiếm"""
        return [
            {"query": "<country>", "description": "Xem thông tin tổng quan về quốc gia"},
            {"query": "<country> economy", "description": "Thông tin kinh tế của quốc gia"},
            {"query": "<country> population", "description": "Thông tin dân số"},
            {"query": "<country> military", "description": "Thông tin quân sự"},
            {"query": "<country> government", "description": "Thông tin chính phủ"},
            {"query": "list_countries()", "description": "Xem danh sách tất cả quốc gia"},
            {"query": "get_field_info('economy')", "description": "Xem thông tin về lĩnh vực cụ thể"}
        ]
