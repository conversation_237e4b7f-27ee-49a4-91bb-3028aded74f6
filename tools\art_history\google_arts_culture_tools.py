from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import re

class GoogleArtsCultureTool(Toolkit):
    """
    Google Arts & Culture Tool cho tìm kiếm nghệ sĩ, t<PERSON><PERSON> phẩm, b<PERSON><PERSON> tàng, phong tr<PERSON><PERSON> nghệ thuật từ Google Arts & Culture.
    """

    def __init__(self):
        super().__init__(
            name="Google Arts & Culture Search Tool",
            description="Tool cho tìm kiếm nghệ sĩ, t<PERSON><PERSON> phẩm, b<PERSON><PERSON> tàng, phong trào nghệ thuật từ Google Arts & Culture.",
            tools=[self.search_google_arts_culture]
        )

    async def search_google_arts_culture(self, query: str, type_: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm Google Arts & Culture cho nghệ sĩ, t<PERSON><PERSON> phẩm, b<PERSON><PERSON> tà<PERSON>, phong trào nghệ thuật.

        Parameters:
        - query: <PERSON><PERSON> kh<PERSON>a tên nghệ sĩ, <PERSON><PERSON><PERSON>h<PERSON>, b<PERSON><PERSON>à<PERSON>, phong trào (vd: '<PERSON>', '<PERSON><PERSON> Night', '<PERSON>vre', 'Impressionism')
        - type_: 'artist', 'artwork', 'museum', 'movement' (lọc kết quả nếu muốn)
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với tiêu đề, mô tả, loại, url Google Arts & Culture
        """
        logger.info(f"Tìm kiếm Google Arts & Culture: query={query}, type={type_}, limit={limit}")

        try:
            base_url = "https://artsandculture.google.com"
            search_url = f"{base_url}/search"
            params = {
                "q": query
            }
            headers = {
                "User-Agent": "Mozilla/5.0 (compatible; GoogleArtsCultureBot/1.0)"
            }
            response = requests.get(search_url, params=params, headers=headers, timeout=10)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Google Arts & Culture",
                    "message": f"Google Arts & Culture search returned status code {response.status_code}",
                    "query": query
                }

            # Đơn giản: lấy các link kết quả đầu tiên (dùng regex)
            results = []
            # Mỗi kết quả nằm trong <a href="/entity/..." ...><div ...>...</div></a>
            for match in re.finditer(r'<a href="(/entity/[^"]+)"[^>]*>(.*?)</a>', response.text, re.DOTALL):
                if len(results) >= limit:
                    break
                url = base_url + match.group(1)
                block = match.group(2)
                # Lấy tiêu đề
                title_match = re.search(r'<div[^>]*class="[^"]*title[^"]*"[^>]*>(.*?)</div>', block)
                title = re.sub(r'<.*?>', '', title_match.group(1)).strip() if title_match else None
                # Lấy mô tả (nếu có)
                desc_match = re.search(r'<div[^>]*class="[^"]*description[^"]*"[^>]*>(.*?)</div>', block)
                description = re.sub(r'<.*?>', '', desc_match.group(1)).strip() if desc_match else None
                # Lấy loại (nếu có)
                type_match = re.search(r'<div[^>]*class="[^"]*subtitle[^"]*"[^>]*>(.*?)</div>', block)
                result_type = re.sub(r'<.*?>', '', type_match.group(1)).strip() if type_match else None
                # Lọc theo type nếu cần
                if type_ and result_type and type_.lower() not in result_type.lower():
                    continue
                results.append({
                    "title": title,
                    "description": description,
                    "type": result_type,
                    "google_arts_culture_url": url
                })

            return {
                "status": "success",
                "source": "Google Arts & Culture",
                "query": query,
                "type_filter": type_,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "Van Gogh",
                    "Starry Night",
                    "Louvre",
                    "Impressionism",
                    "Picasso",
                    "Mona Lisa",
                    "Renaissance",
                    "MoMA",
                    "Japanese art"
                ],
                "official_data_url": "https://artsandculture.google.com/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Google Arts & Culture: {str(e)}")
            return {
                "status": "error",
                "source": "Google Arts & Culture",
                "message": str(e),
                "query": query
            }
