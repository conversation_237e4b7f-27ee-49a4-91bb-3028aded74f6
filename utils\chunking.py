"""
<PERSON><PERSON><PERSON> chiến lược chunking cho văn bản.
"""

import re
import logging
import tiktoken
import langdetect
from typing import List, Dict, Any, Optional, Callable, Union, Tuple

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_chunking_strategy(strategy_name: str) -> Callable:
    """
    Lấy chiến lược chunking dựa trên tên.

    Args:
        strategy_name: Tên chiến lược chunking

    Returns:
        Hàm chunking tương ứng
    """
    strategies = {
        "fixed": fixed_size_chunking,
        "semantic": semantic_chunking,
        "recursive": recursive_chunking,
        "document": document_chunking,
        "token": token_chunking,
        "auto": auto_chunking
    }

    if strategy_name not in strategies:
        logger.warning(f"Unknown chunking strategy: {strategy_name}. Using fixed size chunking.")
        return fixed_size_chunking

    return strategies[strategy_name]

def chunk_text(text: str, strategy: str = "fixed", **kwargs) -> List[str]:
    """
    Chia văn bản thành các chunk nhỏ hơn.

    Args:
        text: Văn bản cần chia
        strategy: Chiến lược chunking (fixed, semantic, recursive)
        **kwargs: Các tham số khác cho chiến lược chunking

    Returns:
        Danh sách các chunk
    """
    chunking_func = get_chunking_strategy(strategy)
    return chunking_func(text, **kwargs)

def chunk_document(document: Dict[str, Any], strategy: str = "document", **kwargs) -> List[Dict[str, Any]]:
    """
    Chia tài liệu thành các chunk nhỏ hơn.

    Args:
        document: Tài liệu cần chia (dict với key "text" và "metadata")
        strategy: Chiến lược chunking
        **kwargs: Các tham số khác cho chiến lược chunking

    Returns:
        Danh sách các chunk tài liệu
    """
    if "text" not in document:
        logger.error("Document must have a 'text' field")
        return []

    text = document["text"]
    metadata = document.get("metadata", {})

    # Chia văn bản
    text_chunks = chunk_text(text, strategy, **kwargs)

    # Tạo các chunk tài liệu
    document_chunks = []
    for i, chunk in enumerate(text_chunks):
        chunk_doc = {
            "text": chunk,
            "metadata": {
                **metadata,
                "chunk_index": i,
                "chunk_count": len(text_chunks)
            }
        }
        document_chunks.append(chunk_doc)

    return document_chunks

def fixed_size_chunking(text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
    """
    Chia văn bản thành các chunk có kích thước cố định.

    Args:
        text: Văn bản cần chia
        chunk_size: Kích thước mỗi chunk (ký tự)
        overlap: Số ký tự chồng lấp giữa các chunk

    Returns:
        Danh sách các chunk
    """
    if not text:
        return []

    chunks = []
    start = 0
    text_length = len(text)

    while start < text_length:
        # Tính toán end
        end = min(start + chunk_size, text_length)

        # Lấy chunk
        chunk = text[start:end]
        chunks.append(chunk)

        # Cập nhật start cho chunk tiếp theo
        start = end - overlap if end < text_length else text_length

    return chunks

def semantic_chunking(text: str, max_chunk_size: int = 1000, min_chunk_size: int = 200) -> List[str]:
    """
    Chia văn bản thành các chunk dựa trên ngữ nghĩa.

    Args:
        text: Văn bản cần chia
        max_chunk_size: Kích thước tối đa của mỗi chunk
        min_chunk_size: Kích thước tối thiểu của mỗi chunk

    Returns:
        Danh sách các chunk
    """
    if not text:
        return []

    # Chia văn bản thành các đoạn
    paragraphs = re.split(r'\n\s*\n', text)

    chunks = []
    current_chunk = ""

    for paragraph in paragraphs:
        # Nếu đoạn quá dài, chia nhỏ hơn
        if len(paragraph) > max_chunk_size:
            # Thêm chunk hiện tại nếu không rỗng
            if current_chunk:
                chunks.append(current_chunk)
                current_chunk = ""

            # Chia đoạn dài thành các chunk nhỏ hơn
            paragraph_chunks = fixed_size_chunking(paragraph, max_chunk_size, min(200, max_chunk_size // 5))
            chunks.extend(paragraph_chunks)
        else:
            # Nếu thêm đoạn mới vượt quá kích thước tối đa, tạo chunk mới
            if len(current_chunk) + len(paragraph) > max_chunk_size and len(current_chunk) >= min_chunk_size:
                chunks.append(current_chunk)
                current_chunk = paragraph
            else:
                # Thêm đoạn vào chunk hiện tại
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph

    # Thêm chunk cuối cùng nếu không rỗng
    if current_chunk:
        chunks.append(current_chunk)

    return chunks

def recursive_chunking(text: str, max_chunk_size: int = 1000, min_chunk_size: int = 200) -> List[str]:
    """
    Chia văn bản đệ quy dựa trên cấu trúc.

    Args:
        text: Văn bản cần chia
        max_chunk_size: Kích thước tối đa của mỗi chunk
        min_chunk_size: Kích thước tối thiểu của mỗi chunk

    Returns:
        Danh sách các chunk
    """
    if not text:
        return []

    # Nếu văn bản đủ nhỏ, trả về nguyên văn
    if len(text) <= max_chunk_size:
        return [text]

    # Tìm các tiêu đề
    headings = re.finditer(r'(^|\n)(#{1,6})\s+(.+)$', text, re.MULTILINE)
    heading_positions = [(m.start(), m.group()) for m in headings]

    # Nếu không có tiêu đề, sử dụng semantic chunking
    if not heading_positions:
        return semantic_chunking(text, max_chunk_size, min_chunk_size)

    # Chia văn bản theo tiêu đề
    chunks = []
    start_pos = 0

    for i, (pos, heading) in enumerate(heading_positions):
        # Nếu đây là tiêu đề đầu tiên và nó không ở đầu văn bản
        if i == 0 and pos > 0:
            # Xử lý phần văn bản trước tiêu đề đầu tiên
            intro_text = text[:pos]
            if len(intro_text) > max_chunk_size:
                intro_chunks = semantic_chunking(intro_text, max_chunk_size, min_chunk_size)
                chunks.extend(intro_chunks)
            else:
                chunks.append(intro_text)

        # Tìm vị trí kết thúc của phần hiện tại
        end_pos = heading_positions[i+1][0] if i < len(heading_positions) - 1 else len(text)

        # Lấy phần văn bản
        section_text = text[pos:end_pos]

        # Nếu phần này quá lớn, chia nhỏ hơn
        if len(section_text) > max_chunk_size:
            # Chia đệ quy
            section_chunks = recursive_chunking(section_text, max_chunk_size, min_chunk_size)
            chunks.extend(section_chunks)
        else:
            chunks.append(section_text)

    return chunks

def document_chunking(text: str, doc_type: str = "markdown", max_chunk_size: int = 1000, min_chunk_size: int = 200) -> List[str]:
    """
    Chia tài liệu dựa trên loại tài liệu.

    Args:
        text: Văn bản cần chia
        doc_type: Loại tài liệu (markdown, html, code, text)
        max_chunk_size: Kích thước tối đa của mỗi chunk
        min_chunk_size: Kích thước tối thiểu của mỗi chunk

    Returns:
        Danh sách các chunk
    """
    if doc_type == "markdown":
        return recursive_chunking(text, max_chunk_size, min_chunk_size)
    elif doc_type == "html":
        # Xử lý HTML (có thể sử dụng BeautifulSoup)
        return semantic_chunking(text, max_chunk_size, min_chunk_size)
    elif doc_type == "code":
        # Xử lý code (chia theo hàm, lớp, v.v.)
        return semantic_chunking(text, max_chunk_size, min_chunk_size)
    else:
        # Mặc định sử dụng semantic chunking
        return semantic_chunking(text, max_chunk_size, min_chunk_size)

def count_tokens(text: str, model: str = "cl100k_base") -> int:
    """
    Đếm số lượng tokens trong văn bản.

    Args:
        text: Văn bản cần đếm tokens
        model: Mô hình tokenizer (mặc định: cl100k_base cho GPT-4)

    Returns:
        Số lượng tokens
    """
    try:
        encoding = tiktoken.get_encoding(model)
        tokens = encoding.encode(text)
        return len(tokens)
    except Exception as e:
        logger.error(f"Error counting tokens: {e}")
        # Ước tính tokens (khoảng 4 ký tự/token)
        return len(text) // 4

def token_chunking(text: str, max_tokens: int = 1000, overlap_tokens: int = 100, model: str = "cl100k_base") -> List[str]:
    """
    Chia văn bản thành các chunk dựa trên số lượng tokens.

    Args:
        text: Văn bản cần chia
        max_tokens: Số lượng tokens tối đa cho mỗi chunk
        overlap_tokens: Số lượng tokens chồng lấp giữa các chunk
        model: Mô hình tokenizer

    Returns:
        Danh sách các chunk
    """
    if not text:
        return []

    try:
        encoding = tiktoken.get_encoding(model)
        tokens = encoding.encode(text)
        total_tokens = len(tokens)

        chunks = []
        start_idx = 0

        while start_idx < total_tokens:
            # Tính toán end_idx
            end_idx = min(start_idx + max_tokens, total_tokens)

            # Lấy chunk tokens
            chunk_tokens = tokens[start_idx:end_idx]

            # Chuyển đổi tokens thành văn bản
            chunk_text = encoding.decode(chunk_tokens)
            chunks.append(chunk_text)

            # Cập nhật start_idx cho chunk tiếp theo
            start_idx = end_idx - overlap_tokens if end_idx < total_tokens else total_tokens

        return chunks
    except Exception as e:
        logger.error(f"Error in token chunking: {e}")
        # Fallback to fixed size chunking
        return fixed_size_chunking(text, max_tokens * 4, overlap_tokens * 4)

def detect_language(text: str) -> str:
    """
    Phát hiện ngôn ngữ của văn bản.

    Args:
        text: Văn bản cần phát hiện ngôn ngữ

    Returns:
        Mã ngôn ngữ (en, vi, ja, ...)
    """
    try:
        # Lấy mẫu văn bản (tối đa 1000 ký tự) để tăng tốc độ phát hiện
        sample = text[:1000]
        return langdetect.detect(sample)
    except Exception as e:
        logger.error(f"Error detecting language: {e}")
        return "en"  # Mặc định là tiếng Anh

def auto_chunking(text: str, max_chunk_size: int = 1000, min_chunk_size: int = 200) -> List[str]:
    """
    Tự động chọn chiến lược chunking phù hợp dựa trên đặc điểm văn bản.

    Args:
        text: Văn bản cần chia
        max_chunk_size: Kích thước tối đa của mỗi chunk (ký tự hoặc tokens)
        min_chunk_size: Kích thước tối thiểu của mỗi chunk (ký tự hoặc tokens)

    Returns:
        Danh sách các chunk
    """
    if not text:
        return []

    # Phát hiện ngôn ngữ
    language = detect_language(text)

    # Phát hiện loại tài liệu
    has_markdown = bool(re.search(r'(^|\n)#{1,6}\s+.+$', text, re.MULTILINE))
    has_html = bool(re.search(r'<\/?[a-z][\s\S]*>', text, re.IGNORECASE))
    has_code_blocks = bool(re.search(r'```[\s\S]*?```', text))

    # Đếm tokens
    token_count = count_tokens(text)
    char_count = len(text)

    # Tỷ lệ tokens/ký tự
    token_char_ratio = token_count / char_count if char_count > 0 else 0.25

    # Quyết định chiến lược chunking
    if has_markdown:
        logger.info(f"Auto-detected markdown document, using recursive chunking")
        return recursive_chunking(text, max_chunk_size, min_chunk_size)
    elif has_html:
        logger.info(f"Auto-detected HTML document, using semantic chunking")
        return semantic_chunking(text, max_chunk_size, min_chunk_size)
    elif has_code_blocks or language in ['ja', 'zh-cn', 'zh-tw', 'ko', 'th']:
        # Ngôn ngữ châu Á hoặc code blocks: sử dụng token chunking
        logger.info(f"Auto-detected language {language} or code blocks, using token chunking")
        max_tokens = max(max_chunk_size // 4, 100)  # Ước tính tokens từ ký tự
        overlap_tokens = max(min_chunk_size // 4, 20)
        return token_chunking(text, max_tokens, overlap_tokens)
    else:
        # Văn bản thông thường: sử dụng semantic chunking
        logger.info(f"Auto-detected regular text in {language}, using semantic chunking")
        return semantic_chunking(text, max_chunk_size, min_chunk_size)