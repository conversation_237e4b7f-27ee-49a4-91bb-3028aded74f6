#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script cho Philosophy Tools
"""

import sys
import os
import json
import random
import asyncio
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_stanford_encyclopedia_tools():
    """Test Stanford Encyclopedia Tools"""
    print("📚 Testing Stanford Encyclopedia Tools...")
    try:
        from tools.philosophy.stanford_encyclopedia_tools import StanfordEncyclopediaTool

        sep_tool = StanfordEncyclopediaTool()

        print("  - Testing Stanford Encyclopedia instantiation...")
        print("    ✅ Stanford Encyclopedia Tools instantiated")

        # Test get_top_new (async)
        print("  - Testing Stanford Encyclopedia get_top_new...")
        assert hasattr(sep_tool, 'get_top_new')
        print("    ✅ Stanford Encyclopedia get_top_new method exists")

        return True

    except Exception as e:
        print(f"    ❌ Stanford Encyclopedia Tools failed: {str(e)}")
        return False

def test_philosophy_search_toolkit():
    """Test Philosophy Search Toolkit"""
    print("🔍 Testing Philosophy Search Toolkit...")
    try:
        from tools.philosophy.philosophy_search_toolkit import PhilosophySearchToolkit

        toolkit = PhilosophySearchToolkit()

        print("  - Testing philosophical concepts search...")
        result = toolkit.search_philosophical_concepts("consciousness", "philosophy_of_mind", "advanced", "contemporary")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Philosophical concepts search works")

        print("  - Testing philosophers search...")
        result = toolkit.search_philosophers("Kant", "German_Idealism", "modern", "German")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Philosophers search works")

        print("  - Testing philosophical movements search...")
        result = toolkit.search_philosophical_movements("Existentialism", "modern", "France", "ethics")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Philosophical movements search works")

        return True

    except Exception as e:
        print(f"    ❌ Philosophy Search Toolkit failed: {str(e)}")
        return False

def test_other_philosophy_tools():
    """Test other philosophy tools"""
    print("📖 Testing Other Philosophy Tools...")
    try:
        # Test Internet Encyclopedia Tools
        from tools.philosophy.internet_encyclopedia_tools import InternetEncyclopediaPhilosophyTool

        iep_tool = InternetEncyclopediaPhilosophyTool()

        print("  - Testing Internet Encyclopedia Tools...")
        print("    ✅ Internet Encyclopedia Tools instantiated")

        # Test Project Gutenberg Tools
        from tools.philosophy.project_gutenberg_tools import ProjectGutenbergPhilosophyTool

        gutenberg_tool = ProjectGutenbergPhilosophyTool()

        print("  - Testing Project Gutenberg Tools...")
        print("    ✅ Project Gutenberg Tools instantiated")

        return True

    except Exception as e:
        print(f"    ❌ Other Philosophy Tools failed: {str(e)}")
        return False

def test_random_philosophy_functionality():
    """Test random philosophy functionality"""
    print("\n🎲 Testing Random Philosophy Functionality...")

    try:
        # Random concept search test
        from tools.philosophy.philosophy_search_toolkit import PhilosophySearchToolkit
        toolkit = PhilosophySearchToolkit()

        concepts = ["consciousness", "free will", "justice", "knowledge", "truth"]
        concept = random.choice(concepts)
        result = toolkit.search_philosophical_concepts(concept, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random concept {concept} search test passed")

        # Random philosopher search test
        philosophers = ["Aristotle", "Kant", "Nietzsche", "Wittgenstein", "Sartre"]
        philosopher = random.choice(philosophers)
        result = toolkit.search_philosophers(philosopher, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random philosopher {philosopher} search test passed")

        # Random ethical theory test
        theories = ["Utilitarianism", "Deontology", "Virtue Ethics", "Existentialism"]
        theory = random.choice(theories)
        result = toolkit.search_ethical_theories(theory, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random ethical theory {theory} test passed")

        return True

    except Exception as e:
        print(f"    ❌ Random Philosophy Functionality failed: {str(e)}")
        return False

def test_philosophy_search_variations():
    """Test various philosophy search variations"""
    print("\n💭 Testing Philosophy Search Variations...")

    try:
        from tools.philosophy.philosophy_search_toolkit import PhilosophySearchToolkit
        toolkit = PhilosophySearchToolkit()

        # Test comprehensive philosophy search
        print("  - Testing comprehensive philosophy search...")
        result = toolkit.comprehensive_philosophy_search("ethics", "all", "western", "graduate")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive philosophy search works")

        # Test ethical theories with different parameters
        print("  - Testing ethical theories variations...")
        result = toolkit.search_ethical_theories("", "consequentialist", "medical", "high")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Ethical theories variations work")

        return True

    except Exception as e:
        print(f"    ❌ Philosophy Search Variations failed: {str(e)}")
        return False

async def test_async_stanford_encyclopedia():
    """Test async Stanford Encyclopedia functionality"""
    print("\n⚡ Testing Async Stanford Encyclopedia...")

    try:
        from tools.philosophy.stanford_encyclopedia_tools import StanfordEncyclopediaTool

        sep_tool = StanfordEncyclopediaTool()

        # Test async get_top_new
        print("  - Testing async get_top_new...")
        result = await sep_tool.get_top_new("articles", 5, "month", "ethics")
        assert result["status"] == "success"
        print("    ✅ Async get_top_new works")

        return True

    except Exception as e:
        print(f"    ❌ Async Stanford Encyclopedia failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 PHILOSOPHY TOOLS TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing Philosophy channel tools...")
    print()

    test_results = []

    # Test all philosophy tools
    test_functions = [
        ("Stanford Encyclopedia Tools", test_stanford_encyclopedia_tools),
        ("Philosophy Search Toolkit", test_philosophy_search_toolkit),
        ("Other Philosophy Tools", test_other_philosophy_tools),
        ("Random Philosophy Functionality", test_random_philosophy_functionality),
        ("Philosophy Search Variations", test_philosophy_search_variations)
    ]

    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()

    # Test async functionality
    print(f"\n{'='*20} Async Tests {'='*20}")
    try:
        async_result = asyncio.run(test_async_stanford_encyclopedia())
        test_results.append(("Async Stanford Encyclopedia", async_result))
    except Exception as e:
        print(f"❌ Async tests failed: {str(e)}")
        test_results.append(("Async Stanford Encyclopedia", False))

    # Summary
    print("\n" + "="*60)
    print("📋 PHILOSOPHY TOOLS TEST SUMMARY")
    print("="*60)

    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")

    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 All philosophy tools are working correctly!")
        print("✨ Philosophy channel fully functional!")
        print("📚 Ready for philosophical inquiry and research!")
    elif passed >= total * 0.8:
        print("✅ Excellent performance - most functionality working!")
    elif passed >= total * 0.6:
        print("✅ Good performance - majority working!")
    else:
        print("⚠️  Some issues detected. Please check the error messages above.")

    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
