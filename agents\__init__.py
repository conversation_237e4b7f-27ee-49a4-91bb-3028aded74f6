"""
Agent module initialization.
This module defines the base agent classes and provides import shortcuts.
"""

from agno.agent import Agent
from agno.models.ollama import Ollama

# Export các lớp và hàm cần thiết
__all__ = [
    "Agent",
    "<PERSON>llama",
    "create_base_agent"
]

def create_base_agent(name, instructions=None, tools=None):
    """
    Create a base agent with Ollama Qwen3:4B model.
    
    Args:
        name (str): Name of the agent
        instructions (list, optional): List of instruction strings
        tools (list, optional): List of tools
        
    Returns:
        Agent: An initialized Agno agent
    """
    return Agent(
        name=name,
        model=Ollama(id="qwen3:4b"),
        instructions=instructions or [],
        tools=tools or [],
        markdown=True
    )