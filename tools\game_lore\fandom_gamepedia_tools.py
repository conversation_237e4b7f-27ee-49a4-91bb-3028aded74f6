from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime

class FandomGamepediaTools(Toolkit):
    """
    Fandom/Gamepedia Tools cho tìm kiếm nhân vật, faction, lore, vũ trụ game từ Fandom/Gamepedia.
    """

    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(
            name="fandom_gamepedia_tools",
            **kwargs
        )

        if enable_search:
            self.register(self.search_fandom_gamepedia)
            self.register(self.get_top_new_articles)
            self.register(self.get_wiki_statistics)
            self.register(self.search_by_category)
            self.register(self.get_recent_changes)

    def search_fandom_gamepedia(self, query: str, wiki: Optional[str] = None, limit: int = 5) -> str:
        """
        Tìm kiếm Fandom/Gamepedia cho nhân vật, faction, lore, vũ trụ game.

        Args:
        - query: <PERSON><PERSON><PERSON> <PERSON>h<PERSON> vật, faction, sự kiện, lore, vũ trụ game (ví dụ: 'Elden Ring Ranni', 'Halo UNSC', 'Dark Souls boss')
        - wiki: Tên wiki cụ thể (ví dụ: 'eldenring', 'halo', 'darksouls') nếu muốn giới hạn tìm kiếm
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON string với tiêu đề, mô tả, url, thumbnail, và wiki liên quan
        """
        log_debug(f"Tìm kiếm Fandom/Gamepedia: query={query}, wiki={wiki}")

        try:
            # Nếu chỉ định wiki, dùng API của wiki đó, ngược lại dùng API search toàn cầu
            if wiki:
                api_url = f"https://{wiki}.fandom.com/api.php"
                params = {
                    "action": "query",
                    "list": "search",
                    "srsearch": query,
                    "format": "json",
                    "srlimit": limit
                }
            else:
                # Dùng API search toàn cầu của Fandom
                api_url = "https://www.fandom.com/api/v1/Search/List"
                params = {
                    "query": query,
                    "limit": limit
                }

            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "source": "Fandom/Gamepedia",
                    "message": f"API returned status code {response.status_code}",
                    "query": query
                }, indent=2)

            results = []
            if wiki:
                data = response.json()
                for item in data.get("query", {}).get("search", []):
                    title = item.get("title")
                    snippet = item.get("snippet", "").replace("<span class=\"searchmatch\">", "").replace("</span>", "")
                    page_url = f"https://{wiki}.fandom.com/wiki/{title.replace(' ', '_')}"
                    results.append({
                        "title": title,
                        "snippet": snippet,
                        "wiki": wiki,
                        "url": page_url,
                        "relevance_score": self._calculate_relevance(title, query)
                    })
            else:
                data = response.json()
                for item in data.get("items", []):
                    results.append({
                        "title": item.get("title"),
                        "snippet": item.get("snippet"),
                        "wiki": item.get("wiki", {}).get("name"),
                        "url": item.get("url"),
                        "thumbnail": item.get("thumbnail"),
                        "relevance_score": self._calculate_relevance(item.get("title", ""), query)
                    })

            return json.dumps({
                "status": "success",
                "source": "Fandom/Gamepedia",
                "query": query,
                "wiki": wiki,
                "results_count": len(results),
                "results": results,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Fandom/Gamepedia: {str(e)}")
            return json.dumps({
                "status": "error",
                "source": "Fandom/Gamepedia",
                "message": str(e),
                "query": query
            }, indent=2)

    def get_top_new_articles(self, wiki: str, limit: int = 10, time_period: str = "week") -> str:
        """
        Lấy các bài viết mới nhất và phổ biến từ một wiki cụ thể.

        Args:
        - wiki: Tên wiki (ví dụ: 'eldenring', 'halo', 'minecraft')
        - limit: Số lượng bài viết (default: 10)
        - time_period: Khoảng thời gian ('day', 'week', 'month')

        Returns:
        - JSON string với danh sách bài viết mới và hot nhất
        """
        log_debug(f"Lấy top new articles từ {wiki} wiki")

        try:
            # Lấy recent changes
            api_url = f"https://{wiki}.fandom.com/api.php"
            params = {
                "action": "query",
                "list": "recentchanges",
                "format": "json",
                "rclimit": limit * 2,  # Lấy nhiều hơn để filter
                "rctype": "new",  # Chỉ lấy trang mới
                "rcnamespace": "0"  # Chỉ lấy main namespace
            }

            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to get recent articles: {response.status_code}"
                }, indent=2)

            data = response.json()
            articles = []

            for change in data.get("query", {}).get("recentchanges", []):
                if len(articles) >= limit:
                    break

                title = change.get("title")
                timestamp = change.get("timestamp")
                user = change.get("user")

                # Lấy thêm thông tin về trang
                page_info = self._get_page_info(wiki, title)

                articles.append({
                    "title": title,
                    "url": f"https://{wiki}.fandom.com/wiki/{title.replace(' ', '_')}",
                    "created_by": user,
                    "created_date": timestamp,
                    "page_info": page_info,
                    "wiki": wiki
                })

            return json.dumps({
                "status": "success",
                "source": "Fandom/Gamepedia",
                "wiki": wiki,
                "time_period": time_period,
                "results_count": len(articles),
                "top_new_articles": articles,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy top new articles: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def get_wiki_statistics(self, wiki: str) -> str:
        """
        Lấy thống kê của một wiki.

        Args:
        - wiki: Tên wiki

        Returns:
        - JSON string với thống kê wiki
        """
        log_debug(f"Lấy thống kê wiki: {wiki}")

        try:
            api_url = f"https://{wiki}.fandom.com/api.php"
            params = {
                "action": "query",
                "meta": "siteinfo",
                "siprop": "statistics",
                "format": "json"
            }

            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to get wiki statistics: {response.status_code}"
                }, indent=2)

            data = response.json()
            stats = data.get("query", {}).get("statistics", {})

            return json.dumps({
                "status": "success",
                "wiki": wiki,
                "statistics": {
                    "total_pages": stats.get("pages", 0),
                    "total_articles": stats.get("articles", 0),
                    "total_edits": stats.get("edits", 0),
                    "total_users": stats.get("users", 0),
                    "active_users": stats.get("activeusers", 0),
                    "total_files": stats.get("images", 0)
                },
                "wiki_health": self._assess_wiki_health(stats),
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy thống kê wiki: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_by_category(self, wiki: str, category: str, limit: int = 10) -> str:
        """
        Tìm kiếm theo category trong wiki.

        Args:
        - wiki: Tên wiki
        - category: Tên category
        - limit: Số lượng kết quả

        Returns:
        - JSON string với kết quả tìm kiếm theo category
        """
        log_debug(f"Tìm kiếm theo category {category} trong {wiki}")

        try:
            api_url = f"https://{wiki}.fandom.com/api.php"
            params = {
                "action": "query",
                "list": "categorymembers",
                "cmtitle": f"Category:{category}",
                "format": "json",
                "cmlimit": limit
            }

            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to search by category: {response.status_code}"
                }, indent=2)

            data = response.json()
            members = data.get("query", {}).get("categorymembers", [])

            results = []
            for member in members:
                title = member.get("title")
                results.append({
                    "title": title,
                    "url": f"https://{wiki}.fandom.com/wiki/{title.replace(' ', '_')}",
                    "page_id": member.get("pageid"),
                    "namespace": member.get("ns")
                })

            return json.dumps({
                "status": "success",
                "wiki": wiki,
                "category": category,
                "results_count": len(results),
                "results": results,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm theo category: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def get_recent_changes(self, wiki: str, limit: int = 20, change_type: str = "all") -> str:
        """
        Lấy các thay đổi gần đây trong wiki.

        Args:
        - wiki: Tên wiki
        - limit: Số lượng thay đổi
        - change_type: Loại thay đổi ('all', 'edit', 'new', 'log')

        Returns:
        - JSON string với danh sách thay đổi gần đây
        """
        log_debug(f"Lấy recent changes từ {wiki}")

        try:
            api_url = f"https://{wiki}.fandom.com/api.php"
            params = {
                "action": "query",
                "list": "recentchanges",
                "format": "json",
                "rclimit": limit,
                "rcprop": "title|timestamp|user|comment|sizes"
            }

            if change_type != "all":
                params["rctype"] = change_type

            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to get recent changes: {response.status_code}"
                }, indent=2)

            data = response.json()
            changes = data.get("query", {}).get("recentchanges", [])

            results = []
            for change in changes:
                results.append({
                    "title": change.get("title"),
                    "user": change.get("user"),
                    "timestamp": change.get("timestamp"),
                    "comment": change.get("comment", ""),
                    "size_change": change.get("newlen", 0) - change.get("oldlen", 0),
                    "url": f"https://{wiki}.fandom.com/wiki/{change.get('title', '').replace(' ', '_')}",
                    "change_type": change.get("type", "edit")
                })

            return json.dumps({
                "status": "success",
                "wiki": wiki,
                "change_type": change_type,
                "results_count": len(results),
                "recent_changes": results,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy recent changes: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    # Helper methods
    def _calculate_relevance(self, title: str, query: str) -> float:
        """Calculate relevance score between title and query."""
        if not title or not query:
            return 0.0

        title_lower = title.lower()
        query_lower = query.lower()

        # Exact match
        if query_lower == title_lower:
            return 1.0

        # Contains query
        if query_lower in title_lower:
            return 0.8

        # Word overlap
        query_words = set(query_lower.split())
        title_words = set(title_lower.split())
        overlap = len(query_words.intersection(title_words))

        if overlap > 0:
            return 0.3 + (overlap / max(len(query_words), len(title_words))) * 0.4

        return 0.1

    def _get_page_info(self, wiki: str, title: str) -> dict:
        """Get additional page information."""
        try:
            api_url = f"https://{wiki}.fandom.com/api.php"
            params = {
                "action": "query",
                "titles": title,
                "prop": "info|extracts",
                "format": "json",
                "exintro": True,
                "explaintext": True,
                "exsectionformat": "plain"
            }

            response = requests.get(api_url, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                pages = data.get("query", {}).get("pages", {})

                for page_id, page_data in pages.items():
                    return {
                        "page_id": page_id,
                        "extract": page_data.get("extract", "")[:200] + "..." if page_data.get("extract") else "",
                        "length": page_data.get("length", 0),
                        "last_modified": page_data.get("touched", "")
                    }
        except:
            pass

        return {"extract": "", "length": 0, "last_modified": ""}

    def _assess_wiki_health(self, stats: dict) -> dict:
        """Assess wiki health based on statistics."""
        total_pages = stats.get("pages", 0)
        total_articles = stats.get("articles", 0)
        active_users = stats.get("activeusers", 0)

        # Simple health assessment
        if active_users > 50 and total_articles > 1000:
            health = "Excellent"
        elif active_users > 20 and total_articles > 500:
            health = "Good"
        elif active_users > 5 and total_articles > 100:
            health = "Fair"
        else:
            health = "Poor"

        return {
            "overall_health": health,
            "activity_level": "High" if active_users > 30 else "Medium" if active_users > 10 else "Low",
            "content_richness": "Rich" if total_articles > 1000 else "Moderate" if total_articles > 200 else "Limited",
            "community_size": "Large" if active_users > 50 else "Medium" if active_users > 15 else "Small"
        }
