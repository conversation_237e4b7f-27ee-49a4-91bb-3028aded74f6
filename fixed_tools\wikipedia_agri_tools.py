from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger, log_warning
import aiohttp
import asyncio
import json

class WikipediaAgriTools(Toolkit):
    """
    Công cụ tìm kiếm thông tin nông nghiệp hiện đại từ Wikipedia.
    Hỗ trợ tìm kiếm thông tin về các chủ đề như nông nghiệp chính xác, thủy canh, nông nghiệp bền vững, v.v.
    """

    def __init__(self, **kwargs):
        super().__init__(
            name="wikipedia_agri_tools",
            **kwargs
        )
        self.register(self.search_wikipedia_agri)
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=15, connect=5)
        self.base_url = "https://{language}.wikipedia.org"
        self.retry_attempts = 3
        self.retry_delay = 1  # giây

    async def _get_session(self) -> aiohttp.ClientSession:
        """Tạo hoặc trả về session hiện có"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                timeout=self.timeout,
                headers={
                    "User-Agent": "AgriDataBot/1.0 (https://github.com/your-repo)",
                    "Accept": "application/json",
                }
            )
        return self.session

    async def _make_request(self, url: str, params: Dict[str, Any] = None, attempt: int = 1) -> Dict[str, Any]:
        """Thực hiện HTTP request với cơ chế retry"""
        session = await self._get_session()
        
        try:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    return {"status": "success", "data": await response.json()}
                elif response.status == 429 and attempt <= self.retry_attempts:  # Too Many Requests
                    retry_after = int(response.headers.get('Retry-After', self.retry_delay))
                    await asyncio.sleep(retry_after)
                    return await self._make_request(url, params, attempt + 1)
                else:
                    return {"status": "error", "message": f"HTTP {response.status}"}
        except asyncio.TimeoutError:
            if attempt <= self.retry_attempts:
                await asyncio.sleep(self.retry_delay * attempt)
                return await self._make_request(url, params, attempt + 1)
            return {"status": "error", "message": "Request timeout after multiple retries"}
        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def search_wikipedia_agri(
        self, 
        query: str, 
        language: str = "en"
    ) -> str:
        """
        Tìm kiếm thông tin nông nghiệp hiện đại trên Wikipedia.

        Parameters:
        - query: Chủ đề cần tìm kiếm (vd: 'precision agriculture', 'hydroponics')
        - language: Mã ngôn ngữ (mặc định: 'en')

        Returns:
        - JSON string chứa kết quả tìm kiếm hoặc thông báo lỗi
        """
        logger.info(f"Tìm kiếm Wikipedia ({language}): {query}")

        try:
            # Lấy thông tin trang
            api_url = f"{self.base_url.format(language=language)}/api/rest_v1/page/summary/{query.replace(' ', '_')}"
            
            result = await self._make_request(api_url)
            
            if result["status"] != "success":
                return json.dumps({"status": "error", "message": "Không thể lấy dữ liệu từ Wikipedia"})

            data = result["data"]
            response_data = {
                "status": "success",
                "query": query,
                "language": language,
                "title": data.get("title"),
                "summary": data.get("extract"),
                "url": data.get("content_urls", {}).get("desktop", {}).get("page"),
                "thumbnail": data.get("thumbnail", {}).get("source"),
                "timestamp": datetime.now().isoformat()
            }
            
            return json.dumps(response_data)

        except Exception as e:
            error_msg = f"Lỗi khi tìm kiếm Wikipedia: {str(e)}"
            logger.error(error_msg)
            return json.dumps({"status": "error", "message": error_msg})

    async def close(self):
        """Đóng session khi không còn sử dụng"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

# Tạo instance của tool để import
wikipedia_agri_tool = WikipediaAgriTools()