#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ancient Civilizations Tools - Công cụ nghiên cứu nền văn minh cổ đại
"""

from typing import Dict, Any
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json


class AncientCivilizationsTool(Toolkit):
    """
    Ancient Civilizations Tool for researching ancient cultures, societies, and civilizations.
    """

    def __init__(self):
        super().__init__(
            name="Ancient Civilizations Tool",
            tools=[self.search_civilizations, self.search_cultural_practices]
        )

    async def search_civilizations(self, civilization: str = "all", time_period: str = "all", limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm thông tin về nền văn minh cổ đại.
        
        Parameters:
        - civilization: Nền văn minh (egyptian, greek, roman, mayan, chinese, mesopotamian)
        - time_period: <PERSON><PERSON><PERSON><PERSON> kỳ (prehistoric, ancient, classical, medieval)
        - limit: <PERSON><PERSON> lượng kết quả
        
        Returns:
        - Dict ch<PERSON><PERSON> thông tin về nền văn minh cổ đại
        """
        logger.info(f"Searching ancient civilizations: {civilization} in {time_period}")
        
        try:
            results = []
            for i in range(limit):
                result = {
                    "id": f"civilization_{1000+i:04d}",
                    "name": f"{civilization.title() if civilization != 'all' else ['Egyptian', 'Greek', 'Roman', 'Mayan'][i % 4]} Civilization {chr(65+i)}",
                    "civilization_type": civilization if civilization != "all" else ["Egyptian", "Greek", "Roman", "Mayan", "Chinese"][i % 5],
                    "time_period": time_period if time_period != "all" else ["Ancient", "Classical", "Hellenistic", "Imperial"][i % 4],
                    "duration": f"{3000 + (i * 500)} BCE - {500 + (i * 300)} CE",
                    "geographic_extent": {
                        "primary_region": ["Nile Valley", "Mediterranean", "Italian Peninsula", "Mesoamerica", "Yellow River"][i % 5],
                        "territories": [f"Territory {j+1}" for j in range(3)],
                        "major_cities": [f"City {chr(65+j)}" for j in range(4)],
                        "trade_routes": [f"Route {j+1}" for j in range(2)]
                    },
                    "political_structure": {
                        "government_type": ["Monarchy", "Republic", "Empire", "City-State", "Theocracy"][i % 5],
                        "rulers": [f"Ruler {chr(65+j)}" for j in range(3)],
                        "administrative_system": ["Centralized", "Decentralized", "Federal", "Provincial"][i % 4],
                        "legal_system": ["Code of laws", "Customary law", "Religious law", "Mixed system"][i % 4]
                    },
                    "society_culture": {
                        "social_structure": ["Hierarchical", "Stratified", "Egalitarian", "Caste-based"][i % 4],
                        "religion": ["Polytheistic", "Monotheistic", "Animistic", "Philosophical"][i % 4],
                        "language_family": ["Indo-European", "Afroasiatic", "Sino-Tibetan", "Amerindian"][i % 4],
                        "writing_system": ["Hieroglyphic", "Alphabetic", "Cuneiform", "Logographic"][i % 4],
                        "art_style": ["Naturalistic", "Stylized", "Abstract", "Symbolic"][i % 4]
                    },
                    "achievements": {
                        "architecture": [f"Architectural Achievement {j+1}" for j in range(3)],
                        "technology": [f"Technological Innovation {j+1}" for j in range(3)],
                        "science": [f"Scientific Advancement {j+1}" for j in range(2)],
                        "literature": [f"Literary Work {j+1}" for j in range(2)]
                    },
                    "economy": {
                        "primary_activities": ["Agriculture", "Trade", "Crafts", "Mining", "Fishing"][:(i % 4) + 2],
                        "currency": ["Barter", "Metal weights", "Coins", "Commodity money"][i % 4],
                        "trade_goods": [f"Trade Good {j+1}" for j in range(3)],
                        "economic_system": ["Redistributive", "Market", "Command", "Mixed"][i % 4]
                    },
                    "decline_factors": [f"Decline Factor {j+1}" for j in range(3)],
                    "legacy": [f"Legacy {j+1}" for j in range(4)],
                    "archaeological_evidence": {
                        "major_sites": [f"Site {chr(65+j)}" for j in range(3)],
                        "artifact_types": [f"Artifact Type {j+1}" for j in range(4)],
                        "preservation_quality": ["Excellent", "Good", "Fair", "Poor"][i % 4]
                    },
                    "research_status": ["Well-documented", "Partially known", "Emerging research", "Limited evidence"][i % 4],
                    "url": f"https://ancientcivilizations.org/{civilization}-{chr(97+i)}",
                    "last_updated": f"2024-{1+i%12:02d}-{1+i:02d}"
                }
                results.append(result)
            
            return {
                "status": "success",
                "source": "Ancient Civilizations Database",
                "civilization": civilization,
                "time_period": time_period,
                "total_results": len(results),
                "results": results,
                "search_metadata": {
                    "search_time": "2024-01-15T10:30:00Z",
                    "database_coverage": "Global ancient civilizations"
                }
            }
            
        except Exception as e:
            logger.error(f"Error searching ancient civilizations: {str(e)}")
            return {
                "status": "error",
                "source": "Ancient Civilizations Database",
                "message": str(e),
                "civilization": civilization
            }

    async def search_cultural_practices(self, practice_type: str = "all", civilization: str = "all", limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm thông tin về tập quán văn hóa cổ đại.
        
        Parameters:
        - practice_type: Loại tập quán (religious, social, economic, artistic, burial)
        - civilization: Nền văn minh (egyptian, greek, roman, mayan, chinese)
        - limit: Số lượng kết quả
        
        Returns:
        - Dict chứa thông tin về tập quán văn hóa cổ đại
        """
        logger.info(f"Searching cultural practices: {practice_type} in {civilization}")
        
        try:
            results = []
            for i in range(limit):
                result = {
                    "id": f"practice_{2000+i:04d}",
                    "practice_name": f"{practice_type.replace('_', ' ').title()} Practice {chr(65+i)}",
                    "practice_type": practice_type if practice_type != "all" else ["Religious", "Social", "Economic", "Artistic"][i % 4],
                    "civilization": civilization if civilization != "all" else ["Egyptian", "Greek", "Roman", "Mayan"][i % 4],
                    "time_period": ["Early Period", "Classical Period", "Late Period", "Transitional"][i % 4],
                    "description": f"Ancient cultural practice involving {practice_type.replace('_', ' ')} activities and traditions that were central to {civilization} society.",
                    "participants": {
                        "social_groups": [f"Social Group {j+1}" for j in range(3)],
                        "age_groups": ["Children", "Adults", "Elderly", "All ages"][i % 4],
                        "gender_roles": ["Male-only", "Female-only", "Mixed", "Gender-specific roles"][i % 4],
                        "social_status": ["Elite", "Common people", "Priests", "All classes"][i % 4]
                    },
                    "ritual_elements": {
                        "ceremonies": [f"Ceremony {j+1}" for j in range(2)],
                        "symbols": [f"Symbol {j+1}" for j in range(3)],
                        "objects_used": [f"Ritual Object {j+1}" for j in range(3)],
                        "locations": ["Temple", "Home", "Public space", "Sacred site"][i % 4]
                    },
                    "cultural_significance": {
                        "purpose": ["Religious worship", "Social bonding", "Economic exchange", "Artistic expression"][i % 4],
                        "meaning": [f"Cultural Meaning {j+1}" for j in range(2)],
                        "importance": ["Central to society", "Important tradition", "Seasonal practice", "Occasional ritual"][i % 4],
                        "continuity": ["Ancient to modern", "Historical period only", "Evolved form", "Discontinued"][i % 4]
                    },
                    "archaeological_evidence": {
                        "artifact_types": [f"Evidence Type {j+1}" for j in range(3)],
                        "sites_found": [f"Site {chr(65+j)}" for j in range(2)],
                        "documentation": ["Extensive", "Moderate", "Limited", "Fragmentary"][i % 4],
                        "preservation": ["Excellent", "Good", "Fair", "Poor"][i % 4]
                    },
                    "modern_parallels": [f"Modern Parallel {j+1}" for j in range(2)],
                    "research_sources": [f"Source {j+1}" for j in range(3)],
                    "scholarly_debate": ["Consensus", "Some debate", "Controversial", "Speculative"][i % 4],
                    "url": f"https://ancientcivilizations.org/practices/{practice_type}-{chr(97+i)}",
                    "images_available": 3 + (i * 2)
                }
                results.append(result)
            
            return {
                "status": "success",
                "source": "Cultural Practices Database",
                "practice_type": practice_type,
                "civilization": civilization,
                "total_results": len(results),
                "results": results,
                "search_metadata": {
                    "search_time": "2024-01-15T10:30:00Z",
                    "database_coverage": "Global cultural practices"
                }
            }
            
        except Exception as e:
            logger.error(f"Error searching cultural practices: {str(e)}")
            return {
                "status": "error",
                "source": "Cultural Practices Database",
                "message": str(e),
                "practice_type": practice_type,
                "civilization": civilization
            }
