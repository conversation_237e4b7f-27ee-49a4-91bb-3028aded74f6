#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script cho Paleontology Tools
"""

import sys
import os
import json
import random
import asyncio
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_fossilworks_tools():
    """Test Fossilworks Tools"""
    print("🦕 Testing Fossilworks Tools...")
    try:
        from tools.paleontology.fossilworks_tools import FossilworksTool
        
        fossilworks_tool = FossilworksTool()
        
        print("  - Testing Fossilworks instantiation...")
        print("    ✅ Fossilworks Tools instantiated")
        
        # Test get_top_new (async)
        print("  - Testing Fossilworks get_top_new...")
        assert hasattr(fossilworks_tool, 'get_top_new')
        print("    ✅ Fossilworks get_top_new method exists")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Fossilworks Tools failed: {str(e)}")
        return False

def test_paleontology_search_toolkit():
    """Test Paleontology Search Toolkit"""
    print("🔍 Testing Paleontology Search Toolkit...")
    try:
        from tools.paleontology.paleontology_search_toolkit import PaleontologySearchToolkit
        
        toolkit = PaleontologySearchToolkit()
        
        print("  - Testing fossil specimens search...")
        result = toolkit.search_fossil_specimens("dinosaur", "cretaceous", "Montana", "excellent")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Fossil specimens search works")
        
        print("  - Testing geological formations search...")
        result = toolkit.search_geological_formations("Hell Creek", "Late Cretaceous", "sandstone", "dinosaurs")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Geological formations search works")
        
        print("  - Testing prehistoric life search...")
        result = toolkit.search_prehistoric_life("vertebrate", "mesozoic", "terrestrial", "transitional")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Prehistoric life search works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Paleontology Search Toolkit failed: {str(e)}")
        return False

def test_other_paleontology_tools():
    """Test other paleontology tools"""
    print("📚 Testing Other Paleontology Tools...")
    try:
        # Test Paleobiology DB Tools
        from tools.paleontology.paleobiology_db_tools import PaleobiologyDBTool
        
        paleo_db_tool = PaleobiologyDBTool()
        
        print("  - Testing Paleobiology DB Tools...")
        print("    ✅ Paleobiology DB Tools instantiated")
        
        # Test Natural History Museum Tools
        from tools.paleontology.natural_history_museum_tools import NaturalHistoryMuseumTool
        
        museum_tool = NaturalHistoryMuseumTool()
        
        print("  - Testing Natural History Museum Tools...")
        print("    ✅ Natural History Museum Tools instantiated")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Other Paleontology Tools failed: {str(e)}")
        return False

def test_random_paleontology_functionality():
    """Test random paleontology functionality"""
    print("\n🎲 Testing Random Paleontology Functionality...")
    
    try:
        # Random fossil search test
        from tools.paleontology.paleontology_search_toolkit import PaleontologySearchToolkit
        toolkit = PaleontologySearchToolkit()
        
        specimens = ["dinosaur", "mammal", "marine_reptile", "plant", "trilobite"]
        specimen = random.choice(specimens)
        result = toolkit.search_fossil_specimens(specimen, "", "", "all")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random fossil {specimen} search test passed")
        
        # Random formation search test
        formations = ["Hell Creek", "Morrison", "Burgess Shale", "Solnhofen", "Green River"]
        formation = random.choice(formations)
        result = toolkit.search_geological_formations(formation, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random formation {formation} search test passed")
        
        # Random extinction event test
        events = ["K-Pg extinction", "Permian-Triassic", "Late Ordovician", "Late Devonian"]
        event = random.choice(events)
        result = toolkit.search_extinction_events(event, "", "mass", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random extinction {event} test passed")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Random Paleontology Functionality failed: {str(e)}")
        return False

def test_paleontology_search_variations():
    """Test various paleontology search variations"""
    print("\n🦴 Testing Paleontology Search Variations...")
    
    try:
        from tools.paleontology.paleontology_search_toolkit import PaleontologySearchToolkit
        toolkit = PaleontologySearchToolkit()
        
        # Test comprehensive paleontology search
        print("  - Testing comprehensive paleontology search...")
        result = toolkit.comprehensive_paleontology_search("Tyrannosaurus rex", "all", "mesozoic", "global")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive paleontology search works")
        
        # Test extinction events with different parameters
        print("  - Testing extinction events variations...")
        result = toolkit.search_extinction_events("", "cretaceous", "mass", "dinosaurs")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Extinction events variations work")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Paleontology Search Variations failed: {str(e)}")
        return False

async def test_async_fossilworks():
    """Test async Fossilworks functionality"""
    print("\n⚡ Testing Async Fossilworks...")
    
    try:
        from tools.paleontology.fossilworks_tools import FossilworksTool
        
        fossilworks_tool = FossilworksTool()
        
        # Test async get_top_new
        print("  - Testing async get_top_new...")
        result = await fossilworks_tool.get_top_new("fossils", 5, "month", "dinosaurs")
        assert result["status"] == "success"
        print("    ✅ Async get_top_new works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Async Fossilworks failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 PALEONTOLOGY TOOLS TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing Paleontology channel tools...")
    print()
    
    test_results = []
    
    # Test all paleontology tools
    test_functions = [
        ("Fossilworks Tools", test_fossilworks_tools),
        ("Paleontology Search Toolkit", test_paleontology_search_toolkit),
        ("Other Paleontology Tools", test_other_paleontology_tools),
        ("Random Paleontology Functionality", test_random_paleontology_functionality),
        ("Paleontology Search Variations", test_paleontology_search_variations)
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()
    
    # Test async functionality
    print(f"\n{'='*20} Async Tests {'='*20}")
    try:
        async_result = asyncio.run(test_async_fossilworks())
        test_results.append(("Async Fossilworks", async_result))
    except Exception as e:
        print(f"❌ Async tests failed: {str(e)}")
        test_results.append(("Async Fossilworks", False))
    
    # Summary
    print("\n" + "="*60)
    print("📋 PALEONTOLOGY TOOLS TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All paleontology tools are working correctly!")
        print("✨ Paleontology channel fully functional!")
    elif passed >= total * 0.8:
        print("✅ Excellent performance - most functionality working!")
    elif passed >= total * 0.6:
        print("✅ Good performance - majority working!")
    else:
        print("⚠️  Some issues detected. Please check the error messages above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
