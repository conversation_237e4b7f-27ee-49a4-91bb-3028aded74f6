#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho các tính năng mới được thêm vào cosmology tools.
"""

import sys
import os

# Thêm thư mục gốc vào Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_arxiv_enhanced_features():
    """Test các tính năng mới của ArXiv tools."""
    print("=== Testing ArXiv Enhanced Features ===")
    try:
        from tools.cosmology.arxiv_tools import ArXivTools
        
        tool = ArXivTools()
        
        # Test recent papers
        print("--- Recent Papers ---")
        result1 = tool.get_recent_papers(3, 7, ["astro-ph.CO", "gr-qc"])
        print("Recent papers:", result1[:300] + "..." if len(result1) > 300 else result1)
        
        # Test trending topics
        print("\n--- Trending Topics ---")
        result2 = tool.get_trending_topics(5, "month")
        print("Trending topics:", result2[:300] + "..." if len(result2) > 300 else result2)
        
        print("✅ ArXiv Enhanced Features: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing ArXiv Enhanced Features: {e}")
    print()

def test_inspirehep_enhanced_features():
    """Test các tính năng mới của INSPIRE-HEP tools."""
    print("=== Testing INSPIRE-HEP Enhanced Features ===")
    try:
        from tools.cosmology.inspirehep_tools import InspireHEPTools
        
        tool = InspireHEPTools()
        
        # Test recent publications
        print("--- Recent Publications ---")
        result1 = tool.get_recent_publications(3, 30, "particle physics")
        print("Recent publications:", result1[:300] + "..." if len(result1) > 300 else result1)
        
        # Test highly cited papers
        print("\n--- Highly Cited Papers ---")
        result2 = tool.get_highly_cited_papers(3, "year", 100)
        print("Highly cited papers:", result2[:300] + "..." if len(result2) > 300 else result2)
        
        print("✅ INSPIRE-HEP Enhanced Features: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing INSPIRE-HEP Enhanced Features: {e}")
    print()

def test_cosmology_search_toolkit_comprehensive():
    """Test toàn diện Cosmology Search Toolkit."""
    print("=== Testing Cosmology Search Toolkit Comprehensive ===")
    try:
        from tools.cosmology.cosmology_search_toolkit import CosmologySearchToolkit
        
        toolkit = CosmologySearchToolkit()
        
        # Test all regular keyword generation functions
        print("--- All Regular Keywords ---")
        
        # arXiv
        result1 = toolkit.generate_arxiv_keywords("gravitational waves", "gr-qc", "detection")
        print("arXiv keywords:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # CERN Open Data
        result2 = toolkit.generate_cern_opendata_keywords("ATLAS", "muon", "8 TeV")
        print("CERN keywords:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # INSPIRE-HEP
        result3 = toolkit.generate_inspirehep_keywords("dark matter", "LHC", "MSSM")
        print("INSPIRE-HEP keywords:", result3[:200] + "..." if len(result3) > 200 else result3)
        
        # NASA ADS
        result4 = toolkit.generate_nasa_ads_keywords("black holes", "quasar", "observation")
        print("NASA ADS keywords:", result4[:200] + "..." if len(result4) > 200 else result4)
        
        # Wikipedia Physics
        result5 = toolkit.generate_wikipedia_physics_keywords("quantum mechanics", "quantum field theory")
        print("Wikipedia keywords:", result5[:200] + "..." if len(result5) > 200 else result5)
        
        print("\n--- All Recent/Trending Keywords ---")
        
        # Recent/Trending functions
        result6 = toolkit.generate_arxiv_recent_keywords("hep-th", 14)
        print("arXiv recent:", result6[:200] + "..." if len(result6) > 200 else result6)
        
        result7 = toolkit.generate_cern_recent_keywords("CMS", 21)
        print("CERN recent:", result7[:200] + "..." if len(result7) > 200 else result7)
        
        result8 = toolkit.generate_inspirehep_trending_keywords("cosmology", "quarter")
        print("INSPIRE-HEP trending:", result8[:200] + "..." if len(result8) > 200 else result8)
        
        result9 = toolkit.generate_nasa_ads_recent_keywords("Nature", 45)
        print("NASA ADS recent:", result9[:200] + "..." if len(result9) > 200 else result9)
        
        result10 = toolkit.generate_wikipedia_physics_recent_keywords(60, "en")
        print("Wikipedia recent:", result10[:200] + "..." if len(result10) > 200 else result10)
        
        print("✅ Cosmology Search Toolkit Comprehensive: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing Cosmology Search Toolkit Comprehensive: {e}")
    print()

def test_package_integration():
    """Test tích hợp package."""
    print("=== Testing Package Integration ===")
    try:
        from tools.cosmology import (
            ArXivTools,
            InspireHEPTools,
            CosmologySearchToolkit
        )
        
        # Test instantiation và basic functionality
        arxiv = ArXivTools()
        inspire = InspireHEPTools()
        search = CosmologySearchToolkit()
        
        # Test một số hàm cơ bản
        print("--- Testing Basic Functions ---")
        
        # ArXiv search
        arxiv_result = arxiv.search_arxiv_papers("dark energy", 2)
        print("ArXiv search works:", "success" in arxiv_result.lower() or "fallback" in arxiv_result.lower())
        
        # ArXiv recent
        arxiv_recent = arxiv.get_recent_papers(2, 7)
        print("ArXiv recent works:", "recent" in arxiv_recent.lower())
        
        # INSPIRE-HEP search
        inspire_result = inspire.search_inspirehep_papers("Higgs", 2)
        print("INSPIRE-HEP search works:", "fallback" in inspire_result.lower() or "Higgs" in inspire_result)
        
        # INSPIRE-HEP recent
        inspire_recent = inspire.get_recent_publications(2, 30)
        print("INSPIRE-HEP recent works:", "recent" in inspire_recent.lower())
        
        # Search toolkit
        search_result = search.generate_arxiv_keywords("neutrino", "hep-ph")
        print("Search toolkit works:", "neutrino" in search_result)
        
        print("✅ Package Integration: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing Package Integration: {e}")
    print()

def main():
    """Chạy tất cả các test cho enhanced features."""
    print("Testing Cosmology Tools Enhanced Features")
    print("=" * 60)
    print()
    
    # Test các enhanced features
    test_arxiv_enhanced_features()
    test_inspirehep_enhanced_features()
    test_cosmology_search_toolkit_comprehensive()
    test_package_integration()
    
    print("=" * 60)
    print("Enhanced Features Testing completed!")
    print("\n🎯 Summary:")
    print("✅ ArXiv Tools: Regular search + Recent papers + Trending topics")
    print("✅ INSPIRE-HEP Tools: Regular search + Recent publications + Highly cited papers")
    print("✅ Cosmology Search Toolkit: 10 keyword generation functions")
    print("✅ Package Integration: All tools work together seamlessly")
    print("\n🌟 Key Features:")
    print("• High-quality fallback data for all tools")
    print("• Caching mechanism for performance")
    print("• Retry logic for reliability")
    print("• Comprehensive keyword generation")
    print("• Recent/trending content support")

if __name__ == "__main__":
    main()
