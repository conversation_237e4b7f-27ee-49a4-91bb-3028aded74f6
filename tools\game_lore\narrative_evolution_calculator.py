# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math
from datetime import datetime

class NarrativeEvolutionCalculator(Toolkit):
    """
    Narrative Evolution Calculator cho tính toán story evolution, character development và lore divergence trong game narratives.
    """

    def __init__(self, enable_calculations: bool = True, **kwargs):
        super().__init__(
            name="narrative_evolution_calculator",
            **kwargs
        )
        
        # Narrative development stages
        self.narrative_stages = {
            "origin": {"complexity": 1, "character_depth": 0.2, "world_building": 0.3},
            "expansion": {"complexity": 3, "character_depth": 0.5, "world_building": 0.6},
            "maturation": {"complexity": 6, "character_depth": 0.8, "world_building": 0.9},
            "legacy": {"complexity": 10, "character_depth": 1.0, "world_building": 1.0},
            "reboot": {"complexity": 4, "character_depth": 0.6, "world_building": 0.7}
        }
        
        # Story evolution rates (per game/installment)
        self.evolution_rates = {
            "character_development": 0.3,
            "plot_complexity": 0.25,
            "world_expansion": 0.2,
            "lore_depth": 0.15,
            "narrative_branching": 0.4,
            "thematic_evolution": 0.1
        }
        
        # Franchise complexity factors
        self.franchise_factors = {
            "single_game": {"narrative_scope": 1, "continuity_complexity": 0.1, "canon_stability": 0.9},
            "trilogy": {"narrative_scope": 3, "continuity_complexity": 0.4, "canon_stability": 0.7},
            "long_series": {"narrative_scope": 8, "continuity_complexity": 0.8, "canon_stability": 0.5},
            "expanded_universe": {"narrative_scope": 15, "continuity_complexity": 1.0, "canon_stability": 0.3},
            "multimedia_franchise": {"narrative_scope": 25, "continuity_complexity": 1.2, "canon_stability": 0.2}
        }
        
        if enable_calculations:
            self.register(self.calculate_narrative_divergence)
            self.register(self.estimate_character_evolution)
            self.register(self.analyze_lore_development)
            self.register(self.predict_story_trends)

    def calculate_narrative_divergence(self, story1: str, story2: str,
                                     divergence_installments: int = None,
                                     narrative_aspect: str = "character") -> str:
        """
        Tính toán narrative divergence giữa hai storylines hoặc character arcs.
        
        Args:
            story1: Storyline hoặc character thứ nhất
            story2: Storyline hoặc character thứ hai
            divergence_installments: Số installments tách biệt
            narrative_aspect: Khía cạnh narrative ('character', 'plot', 'world', 'theme')
            
        Returns:
            Chuỗi JSON chứa tính toán narrative divergence
        """
        log_debug(f"Calculating narrative divergence between {story1} and {story2}")
        
        try:
            if divergence_installments is None:
                divergence_installments = 3  # Default 3 installments
            
            # Calculate divergence metrics
            evolution_rate = self.evolution_rates.get(f"{narrative_aspect}_development", 0.25)
            divergence_index = 1 - math.exp(-evolution_rate * divergence_installments)
            
            # Narrative distance analysis
            narrative_distance = divergence_installments * evolution_rate
            thematic_similarity = max(0, 1 - narrative_distance)
            
            # Character/plot development analysis
            development_analysis = {
                "character_arc_divergence": round(divergence_index * 0.8, 3),
                "plot_thread_separation": round(divergence_index * 0.7, 3),
                "thematic_drift": round(divergence_index * 0.5, 3),
                "world_building_variance": round(divergence_index * 0.6, 3)
            }
            
            # Narrative branching patterns
            branching_patterns = {
                "linear_progression": max(0, 1 - divergence_index),
                "parallel_development": round(divergence_index * 0.6, 3),
                "convergent_storylines": round(math.exp(-divergence_installments * 0.2), 3),
                "alternate_timelines": round(divergence_index * 0.9, 3)
            }
            
            # Fan reception and canon impact
            canon_impact = {
                "canon_stability": max(0.3, 1 - (divergence_index * 0.5)),
                "fan_theory_generation": round(divergence_index * 1.2, 3),
                "interpretation_variance": round(divergence_index * 0.8, 3),
                "retcon_probability": round(divergence_index * 0.4, 3)
            }
            
            # Narrative coherence
            coherence_analysis = {
                "internal_consistency": max(0.2, 1 - (divergence_index * 0.6)),
                "character_consistency": max(0.3, 1 - (divergence_index * 0.4)),
                "world_logic_consistency": max(0.4, 1 - (divergence_index * 0.3)),
                "thematic_coherence": max(0.5, 1 - (divergence_index * 0.2))
            }
            
            result = {
                "narrative_comparison": {
                    "story1": story1,
                    "story2": story2,
                    "divergence_installments": divergence_installments,
                    "narrative_aspect": narrative_aspect
                },
                "divergence_analysis": {
                    "divergence_index": round(divergence_index, 3),
                    "narrative_distance": round(narrative_distance, 3),
                    "thematic_similarity": round(thematic_similarity, 3),
                    "evolution_rate": evolution_rate
                },
                "development_analysis": development_analysis,
                "branching_patterns": branching_patterns,
                "canon_impact": canon_impact,
                "coherence_analysis": coherence_analysis,
                "narrative_complexity": self._assess_narrative_complexity(divergence_index, divergence_installments),
                "fan_engagement_prediction": self._predict_fan_engagement(divergence_index, narrative_aspect),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error calculating narrative divergence: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate narrative divergence: {str(e)}"
            }, indent=4)

    def estimate_character_evolution(self, character_name: str, development_span: int = 5,
                                   franchise_type: str = "trilogy") -> str:
        """
        Ước tính character evolution qua thời gian và installments.
        
        Args:
            character_name: Tên nhân vật
            development_span: Số installments phát triển
            franchise_type: Loại franchise
            
        Returns:
            Chuỗi JSON chứa ước tính character evolution
        """
        log_debug(f"Estimating character evolution for {character_name} over {development_span} installments")
        
        try:
            # Get franchise parameters
            franchise_params = self.franchise_factors.get(franchise_type, self.franchise_factors["trilogy"])
            
            # Calculate character development trajectory
            development_rate = 0.2 * franchise_params["narrative_scope"] / 5  # Normalized
            character_growth = (1 + development_rate) ** development_span
            
            # Character development phases
            development_phases = []
            phases_count = max(3, development_span)
            
            for phase in range(phases_count):
                phase_installments = phase + 1
                phase_growth = (1 + development_rate) ** phase_installments
                
                development_phases.append({
                    "phase": phase + 1,
                    "installment": phase_installments,
                    "character_depth": round(min(1.0, phase_growth * 0.2), 2),
                    "narrative_importance": round(min(1.0, phase_growth * 0.15), 2),
                    "development_milestones": self._generate_character_milestones(character_name, phase),
                    "relationship_complexity": round(min(1.0, phase_growth * 0.1), 2)
                })
            
            # Character arc analysis
            arc_analysis = {
                "arc_type": self._determine_arc_type(character_growth, development_span),
                "character_consistency": max(0.3, 1 - (development_span * 0.1)),
                "growth_trajectory": "Exponential" if development_rate > 0.15 else "Linear",
                "narrative_role_evolution": self._analyze_role_evolution(character_growth)
            }
            
            # Relationship dynamics
            relationship_dynamics = {
                "interpersonal_complexity": round(character_growth * 0.3, 2),
                "romantic_subplot_probability": round(min(0.8, development_span * 0.15), 2),
                "mentor_relationship_evolution": round(min(1.0, development_span * 0.12), 2),
                "antagonist_relationship_depth": round(min(1.0, development_span * 0.18), 2)
            }
            
            # Fan reception metrics
            fan_metrics = {
                "character_popularity_trend": "Increasing" if character_growth > 2 else "Stable",
                "fan_art_generation_potential": round(character_growth * 0.4, 2),
                "shipping_potential": round(min(1.0, development_span * 0.2), 2),
                "meme_potential": round(character_growth * 0.25, 2)
            }
            
            result = {
                "character_evolution": {
                    "character_name": character_name,
                    "development_span": development_span,
                    "franchise_type": franchise_type,
                    "final_character_growth": round(character_growth, 2)
                },
                "development_trajectory": {
                    "development_rate": development_rate,
                    "growth_factor": round(character_growth, 2),
                    "complexity_increase": round((character_growth - 1) * 100, 1),
                    "narrative_weight": "Major" if character_growth > 3 else "Supporting"
                },
                "development_phases": development_phases,
                "arc_analysis": arc_analysis,
                "relationship_dynamics": relationship_dynamics,
                "fan_metrics": fan_metrics,
                "future_potential": self._assess_character_future_potential(character_name, character_growth),
                "narrative_impact": self._calculate_narrative_impact(character_growth, franchise_params),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error estimating character evolution: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to estimate character evolution: {str(e)}"
            }, indent=4)

    def analyze_lore_development(self, lore_element: str, development_period: int = 10,
                               narrative_scope: str = "expanded_universe") -> str:
        """
        Phân tích lore development và world-building evolution.
        
        Args:
            lore_element: Phần tử lore cần phân tích
            development_period: Thời gian phát triển (installments/years)
            narrative_scope: Phạm vi narrative
            
        Returns:
            Chuỗi JSON chứa phân tích lore development
        """
        log_debug(f"Analyzing lore development for {lore_element}")
        
        try:
            scope_params = self.franchise_factors.get(narrative_scope, self.franchise_factors["expanded_universe"])
            
            # Lore complexity evolution
            complexity_growth = scope_params["narrative_scope"] * (development_period / 10)
            canon_stability = scope_params["canon_stability"] * (1 - development_period * 0.02)
            
            # Lore development layers
            development_layers = {
                "core_mythology": {
                    "stability": round(max(0.5, canon_stability), 2),
                    "expansion_rate": round(development_period * 0.05, 2),
                    "retcon_risk": round((1 - canon_stability) * 0.3, 2)
                },
                "historical_events": {
                    "detail_level": round(complexity_growth * 0.4, 2),
                    "timeline_consistency": round(max(0.3, canon_stability * 0.8), 2),
                    "new_revelations": round(development_period * 0.08, 2)
                },
                "character_backstories": {
                    "depth_increase": round(complexity_growth * 0.6, 2),
                    "interconnection_complexity": round(development_period * 0.12, 2),
                    "revelation_potential": round(development_period * 0.15, 2)
                },
                "world_mechanics": {
                    "system_complexity": round(complexity_growth * 0.5, 2),
                    "rule_consistency": round(max(0.4, canon_stability * 0.9), 2),
                    "expansion_opportunities": round(development_period * 0.1, 2)
                }
            }
            
            # Fan engagement with lore
            fan_engagement = {
                "theory_crafting_potential": round(complexity_growth * 0.7, 2),
                "wiki_complexity": round(development_period * 0.2, 2),
                "discussion_volume": round(complexity_growth * 0.5, 2),
                "speculation_accuracy": round(max(0.1, canon_stability * 0.6), 2)
            }
            
            # Narrative coherence challenges
            coherence_challenges = {
                "continuity_errors": round((1 - canon_stability) * development_period * 0.1, 2),
                "plot_hole_probability": round((1 - canon_stability) * 0.4, 2),
                "retcon_necessity": round((1 - canon_stability) * 0.5, 2),
                "fan_confusion_level": round(complexity_growth * (1 - canon_stability) * 0.3, 2)
            }
            
            result = {
                "lore_analysis": {
                    "lore_element": lore_element,
                    "development_period": development_period,
                    "narrative_scope": narrative_scope,
                    "complexity_growth_factor": round(complexity_growth, 2)
                },
                "stability_metrics": {
                    "canon_stability": round(canon_stability, 2),
                    "continuity_complexity": scope_params["continuity_complexity"],
                    "narrative_scope_factor": scope_params["narrative_scope"]
                },
                "development_layers": development_layers,
                "fan_engagement": fan_engagement,
                "coherence_challenges": coherence_challenges,
                "expansion_opportunities": self._identify_lore_expansion_opportunities(lore_element, complexity_growth),
                "preservation_strategies": self._suggest_lore_preservation_strategies(canon_stability),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error analyzing lore development: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze lore development: {str(e)}"
            }, indent=4)

    def predict_story_trends(self, current_narrative: str, prediction_installments: int = 5,
                           genre_context: str = "fantasy_rpg") -> str:
        """
        Dự đoán story trends và narrative evolution.
        
        Args:
            current_narrative: Narrative hiện tại
            prediction_installments: Số installments dự đoán
            genre_context: Bối cảnh thể loại
            
        Returns:
            Chuỗi JSON chứa dự đoán story trends
        """
        log_debug(f"Predicting story trends for {current_narrative} over {prediction_installments} installments")
        
        try:
            # Genre-specific trend factors
            genre_factors = {
                "fantasy_rpg": {"complexity_growth": 0.3, "character_focus": 0.8, "world_expansion": 0.9},
                "sci_fi_rpg": {"complexity_growth": 0.4, "character_focus": 0.7, "world_expansion": 0.8},
                "action_adventure": {"complexity_growth": 0.2, "character_focus": 0.6, "world_expansion": 0.5},
                "horror_survival": {"complexity_growth": 0.25, "character_focus": 0.9, "world_expansion": 0.4},
                "mystery_thriller": {"complexity_growth": 0.35, "character_focus": 0.8, "world_expansion": 0.3}
            }
            
            genre_params = genre_factors.get(genre_context, genre_factors["fantasy_rpg"])
            
            # Trend predictions
            trend_predictions = {
                "narrative_complexity": {
                    "growth_rate": genre_params["complexity_growth"],
                    "predicted_increase": round(genre_params["complexity_growth"] * prediction_installments * 100, 1),
                    "complexity_ceiling": "High" if genre_params["complexity_growth"] > 0.3 else "Moderate"
                },
                "character_development": {
                    "focus_intensity": genre_params["character_focus"],
                    "arc_completion_probability": round(min(1.0, prediction_installments * 0.2), 2),
                    "new_character_introduction_rate": round(prediction_installments * 0.15, 2)
                },
                "world_building": {
                    "expansion_potential": genre_params["world_expansion"],
                    "new_location_probability": round(prediction_installments * genre_params["world_expansion"] * 0.2, 2),
                    "lore_depth_increase": round(prediction_installments * 0.1, 2)
                }
            }
            
            # Narrative evolution scenarios
            evolution_scenarios = [
                {
                    "scenario": "Linear Progression",
                    "probability": 0.4,
                    "description": "Story continues current trajectory with incremental development",
                    "characteristics": ["Consistent tone", "Gradual character growth", "Predictable pacing"]
                },
                {
                    "scenario": "Dramatic Escalation",
                    "probability": 0.3,
                    "description": "Major plot developments and character revelations",
                    "characteristics": ["Plot twists", "Character revelations", "Raised stakes"]
                },
                {
                    "scenario": "Narrative Branching",
                    "probability": 0.2,
                    "description": "Multiple storylines and player choice consequences",
                    "characteristics": ["Multiple endings", "Choice consequences", "Parallel narratives"]
                },
                {
                    "scenario": "Soft Reboot",
                    "probability": 0.1,
                    "description": "Significant narrative reset or timeline changes",
                    "characteristics": ["New setting", "Character reinvention", "Fresh start"]
                }
            ]
            
            # Fan expectation analysis
            fan_expectations = {
                "character_arc_completion": round(min(1.0, prediction_installments * 0.25), 2),
                "plot_resolution_demand": round(min(1.0, prediction_installments * 0.2), 2),
                "new_content_hunger": round(prediction_installments * 0.3, 2),
                "nostalgia_factor": round(max(0, prediction_installments * 0.1 - 0.2), 2)
            }
            
            result = {
                "prediction_parameters": {
                    "current_narrative": current_narrative,
                    "prediction_installments": prediction_installments,
                    "genre_context": genre_context,
                    "analysis_scope": "Narrative trajectory forecasting"
                },
                "trend_predictions": trend_predictions,
                "evolution_scenarios": evolution_scenarios,
                "fan_expectations": fan_expectations,
                "risk_factors": [
                    "Narrative complexity overload",
                    "Character development fatigue",
                    "Canon inconsistency accumulation",
                    "Fan expectation divergence"
                ],
                "opportunity_areas": self._identify_narrative_opportunities(genre_params, prediction_installments),
                "strategic_recommendations": self._generate_narrative_recommendations(current_narrative, genre_params),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error predicting story trends: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to predict story trends: {str(e)}"
            }, indent=4)

    # Helper methods
    def _assess_narrative_complexity(self, divergence: float, installments: int) -> dict:
        """Assess narrative complexity level."""
        complexity_score = divergence * installments
        if complexity_score > 2.0:
            return {"level": "High", "description": "Complex branching narrative"}
        elif complexity_score > 1.0:
            return {"level": "Moderate", "description": "Developing narrative complexity"}
        else:
            return {"level": "Low", "description": "Simple linear narrative"}

    def _predict_fan_engagement(self, divergence: float, aspect: str) -> dict:
        """Predict fan engagement based on narrative divergence."""
        engagement_score = divergence * 0.8
        return {
            "engagement_level": "High" if engagement_score > 0.6 else "Moderate" if engagement_score > 0.3 else "Low",
            "discussion_potential": round(engagement_score, 2),
            "theory_crafting": "Active" if aspect in ["character", "plot"] else "Limited"
        }

    def _generate_character_milestones(self, character: str, phase: int) -> list:
        """Generate character development milestones."""
        milestones = [
            ["Introduction", "Basic characterization", "Initial motivation"],
            ["Development", "Relationship building", "Skill growth"],
            ["Challenge", "Major conflict", "Character testing"],
            ["Growth", "Revelation", "Transformation"],
            ["Resolution", "Arc completion", "Legacy establishment"]
        ]
        return milestones[min(phase, len(milestones)-1)]

    def _determine_arc_type(self, growth: float, span: int) -> str:
        """Determine character arc type."""
        if growth > 4 and span > 3:
            return "Epic Hero's Journey"
        elif growth > 2:
            return "Character Growth Arc"
        elif span > 5:
            return "Slow Burn Development"
        else:
            return "Supporting Character Arc"

    def _analyze_role_evolution(self, growth: float) -> str:
        """Analyze how character role evolves."""
        if growth > 3:
            return "Supporting to Protagonist"
        elif growth > 2:
            return "Expanded Supporting Role"
        else:
            return "Consistent Role"

    def _assess_character_future_potential(self, character: str, growth: float) -> dict:
        """Assess character's future narrative potential."""
        return {
            "spin_off_potential": "High" if growth > 3 else "Moderate",
            "sequel_importance": "Major" if growth > 2.5 else "Supporting",
            "fan_favorite_probability": round(min(1.0, growth * 0.3), 2)
        }

    def _calculate_narrative_impact(self, growth: float, franchise_params: dict) -> dict:
        """Calculate character's impact on overall narrative."""
        impact_score = growth * franchise_params["narrative_scope"] / 10
        return {
            "overall_impact": "High" if impact_score > 2 else "Moderate" if impact_score > 1 else "Low",
            "franchise_significance": round(impact_score, 2),
            "legacy_potential": "Strong" if growth > 2.5 else "Moderate"
        }

    def _identify_lore_expansion_opportunities(self, lore_element: str, complexity: float) -> list:
        """Identify opportunities for lore expansion."""
        opportunities = ["Character backstory expansion", "Historical event details"]
        if complexity > 2:
            opportunities.extend(["Parallel timeline exploration", "Prequel potential"])
        if complexity > 3:
            opportunities.extend(["Spin-off universe", "Cross-media adaptation"])
        return opportunities

    def _suggest_lore_preservation_strategies(self, stability: float) -> list:
        """Suggest strategies for preserving lore consistency."""
        strategies = ["Maintain core canon documentation", "Regular continuity reviews"]
        if stability < 0.7:
            strategies.extend(["Establish canon hierarchy", "Retcon guidelines"])
        if stability < 0.5:
            strategies.extend(["Narrative reboot consideration", "Fan community engagement"])
        return strategies

    def _identify_narrative_opportunities(self, genre_params: dict, installments: int) -> list:
        """Identify narrative opportunities."""
        opportunities = ["Character development focus", "World building expansion"]
        if genre_params["complexity_growth"] > 0.3:
            opportunities.extend(["Multi-layered storytelling", "Narrative experimentation"])
        if installments > 3:
            opportunities.extend(["Long-term character arcs", "Epic storyline development"])
        return opportunities

    def _generate_narrative_recommendations(self, narrative: str, genre_params: dict) -> list:
        """Generate strategic narrative recommendations."""
        recommendations = ["Maintain character consistency", "Balance complexity with accessibility"]
        if genre_params["character_focus"] > 0.7:
            recommendations.extend(["Invest in character development", "Explore relationship dynamics"])
        if genre_params["world_expansion"] > 0.7:
            recommendations.extend(["Expand world lore", "Create interconnected storylines"])
        return recommendations
