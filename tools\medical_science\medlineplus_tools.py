from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class MedlinePlusTool(Toolkit):
    """
    MedlinePlus Tool cho tìm kiếm thông tin sức khỏe, b<PERSON><PERSON>, thuố<PERSON>, triệu chứng từ MedlinePlus.
    """

    def __init__(self):
        super().__init__(
            name="MedlinePlus Search Tool",
            tools=[self.search_medlineplus]
        )

    async def search_medlineplus(self, query: str, limit: int = 5, language: str = "en") -> Dict[str, Any]:
        """
        Tìm kiếm MedlinePlus cho thông tin sức khỏe, bệnh, thuố<PERSON>, triệu chứng.

        Parameters:
        - query: Từ khóa về bệnh, triệu chứng, thuốc, chủ đề sức khỏe (ví dụ: 'diabetes', 'hypertension', 'aspirin', 'childhood asthma')
        - limit: <PERSON><PERSON> lư<PERSON> kết quả tối đa (default: 5)
        - language: <PERSON><PERSON><PERSON> (en hoặc es)

        Returns:
        - JSO<PERSON> với tiêu đ<PERSON>, mô tả, chủ đề, url MedlinePlus
        """
        logger.info(f"Tìm kiếm MedlinePlus: query={query}, language={language}")

        try:
            # MedlinePlus không có API public chính thức, dùng endpoint RSS search hoặc scraping nhẹ
            search_url = f"https://medlineplus.gov/{'spanish/' if language == 'es' else ''}search.html"
            params = {
                "q": query
            }
            headers = {
                "User-Agent": "Mozilla/5.0 (compatible; MedlinePlusBot/1.0)"
            }
            response = requests.get(search_url, params=params, headers=headers, timeout=10)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "MedlinePlus",
                    "message": f"MedlinePlus search returned status code {response.status_code}",
                    "query": query
                }

            # Đơn giản: lấy các link kết quả đầu tiên (dùng regex)
            import re
            results = []
            # Mỗi kết quả nằm trong <a class="result-title" href="...">...</a>
            for match in re.finditer(r'<a class="result-title" href="([^"]+)".*?>(.*?)</a>', response.text):
                if len(results) >= limit:
                    break
                url = match.group(1)
                title = re.sub(r'<.*?>', '', match.group(2)).strip()
                # Lấy mô tả nếu có
                desc_match = re.search(rf'<a class="result-title" href="{re.escape(url)}".*?</a>.*?<div class="result-desc">(.*?)</div>', response.text, re.DOTALL)
                description = re.sub(r'<.*?>', '', desc_match.group(1)).strip() if desc_match else None
                # Chuẩn hóa url
                if url.startswith("/"):
                    url = f"https://medlineplus.gov{url}"
                results.append({
                    "title": title,
                    "description": description,
                    "medlineplus_url": url
                })

            return {
                "status": "success",
                "source": "MedlinePlus",
                "query": query,
                "language": language,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm MedlinePlus: {str(e)}")
            return {
                "status": "error",
                "source": "MedlinePlus",
                "message": str(e),
                "query": query
            }
