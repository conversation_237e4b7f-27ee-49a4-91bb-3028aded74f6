#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script cho Analyzer & Calculator Package
Kiểm tra toàn diện 4 tools mới: <PERSON><PERSON> Analyzer, <PERSON>steries Calculator, 
Mythology Religion Analyzer, Mythology Religion Calculator
"""

import sys
import os
import json
import random
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_mysteries_analyzer():
    """Test Mysteries Analyzer"""
    print("🔍 Testing Mysteries Analyzer...")
    try:
        from tools.mysteries.mysteries_analyzer import MysteriesAnalyzer
        
        analyzer = MysteriesAnalyzer()
        
        print("  - Testing mystery credibility analysis...")
        result = analyzer.analyze_mystery_credibility("Roswell Incident", "all", "1940s", "comprehensive")
        data = json.loads(result)
        assert "analysis_parameters" in data
        assert "credibility_overview" in data
        print("    ✅ Mystery credibility analysis works")
        
        print("  - Testing UFO pattern analysis...")
        result = analyzer.analyze_ufo_patterns("global", "decade", "all", "comprehensive")
        data = json.loads(result)
        assert "analysis_parameters" in data
        assert "pattern_overview" in data
        print("    ✅ UFO pattern analysis works")
        
        print("  - Testing paranormal evidence analysis...")
        result = analyzer.analyze_paranormal_evidence("ghost", "all", "scientific", "standard")
        data = json.loads(result)
        assert "analysis_parameters" in data
        assert "evidence_overview" in data
        print("    ✅ Paranormal evidence analysis works")
        
        print("  - Testing conspiracy validity analysis...")
        result = analyzer.analyze_conspiracy_validity("Area 51", "comprehensive", "rigorous", "formal")
        data = json.loads(result)
        assert "analysis_parameters" in data
        assert "validity_overview" in data
        print("    ✅ Conspiracy validity analysis works")
        
        print("  - Testing mystery insights generation...")
        result = analyzer.generate_mystery_insights("comprehensive", "patterns", "current", "global")
        data = json.loads(result)
        assert "insight_generation" in data
        assert "synthesis_overview" in data
        print("    ✅ Mystery insights generation works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Mysteries Analyzer failed: {str(e)}")
        return False

def test_mysteries_calculator():
    """Test Mysteries Calculator"""
    print("🧮 Testing Mysteries Calculator...")
    try:
        from tools.mysteries.mysteries_calculator import MysteriesCalculator
        
        calculator = MysteriesCalculator()
        
        print("  - Testing credibility score calculation...")
        result = calculator.calculate_credibility_score("Bigfoot sightings", "balanced", "weighted", "standard")
        data = json.loads(result)
        assert "calculation_parameters" in data
        assert "credibility_score" in data
        print("    ✅ Credibility score calculation works")
        
        print("  - Testing UFO probability calculation...")
        result = calculator.calculate_ufo_probability("Phoenix Lights", "standard", "bayesian", "conservative")
        data = json.loads(result)
        assert "calculation_parameters" in data
        assert "probability_assessment" in data
        print("    ✅ UFO probability calculation works")
        
        print("  - Testing evidence strength calculation...")
        result = calculator.calculate_evidence_strength("Physical traces", "statistical", "scientific", "composite")
        data = json.loads(result)
        assert "calculation_parameters" in data
        assert "evidence_strength" in data
        print("    ✅ Evidence strength calculation works")
        
        print("  - Testing witness reliability calculation...")
        result = calculator.calculate_witness_reliability("Military pilots", "comprehensive", "multi_factor", "standard")
        data = json.loads(result)
        assert "calculation_parameters" in data
        assert "reliability_assessment" in data
        print("    ✅ Witness reliability calculation works")
        
        print("  - Testing mystery complexity calculation...")
        result = calculator.calculate_mystery_complexity("Bermuda Triangle", "comprehensive", "entropy", "standard")
        data = json.loads(result)
        assert "calculation_parameters" in data
        assert "complexity_assessment" in data
        print("    ✅ Mystery complexity calculation works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Mysteries Calculator failed: {str(e)}")
        return False

def test_mythology_religion_analyzer():
    """Test Mythology Religion Analyzer"""
    print("🏛️ Testing Mythology Religion Analyzer...")
    try:
        from tools.mythology_religion.mythology_religion_analyzer import MythologyReligionAnalyzer
        
        analyzer = MythologyReligionAnalyzer()
        
        print("  - Testing mythological patterns analysis...")
        result = analyzer.analyze_mythological_patterns("global", "archetypal", "all", "comprehensive")
        data = json.loads(result)
        assert "analysis_parameters" in data
        assert "pattern_overview" in data
        print("    ✅ Mythological patterns analysis works")
        
        print("  - Testing religious evolution analysis...")
        result = analyzer.analyze_religious_evolution("Christianity", "comprehensive", "historical", "comparative")
        data = json.loads(result)
        assert "analysis_parameters" in data
        assert "evolution_overview" in data
        print("    ✅ Religious evolution analysis works")
        
        print("  - Testing cultural influence analysis...")
        result = analyzer.analyze_cultural_influence("Greek mythology", "comprehensive", "regional", "historical")
        data = json.loads(result)
        assert "analysis_parameters" in data
        assert "influence_overview" in data
        print("    ✅ Cultural influence analysis works")
        
        print("  - Testing deity relationships analysis...")
        result = analyzer.analyze_deity_relationships("Greek", "all", "network", "standard")
        data = json.loads(result)
        assert "analysis_parameters" in data
        assert "relationship_overview" in data
        print("    ✅ Deity relationships analysis works")
        
        print("  - Testing mythology insights generation...")
        result = analyzer.generate_mythology_insights("comprehensive", "patterns", "global", "advanced")
        data = json.loads(result)
        assert "insight_generation" in data
        assert "synthesis_overview" in data
        print("    ✅ Mythology insights generation works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Mythology Religion Analyzer failed: {str(e)}")
        return False

def test_mythology_religion_calculator():
    """Test Mythology Religion Calculator"""
    print("⚖️ Testing Mythology Religion Calculator...")
    try:
        from tools.mythology_religion.mythology_religion_calculator import MythologyReligionCalculator
        
        calculator = MythologyReligionCalculator()
        
        print("  - Testing deity power level calculation...")
        result = calculator.calculate_deity_power_level("Zeus", "comprehensive", "weighted", "standard")
        data = json.loads(result)
        assert "calculation_parameters" in data
        assert "power_assessment" in data
        print("    ✅ Deity power level calculation works")
        
        print("  - Testing cultural influence calculation...")
        result = calculator.calculate_cultural_influence("Norse mythology", "comprehensive", "multi_dimensional", "balanced")
        data = json.loads(result)
        assert "calculation_parameters" in data
        assert "influence_assessment" in data
        print("    ✅ Cultural influence calculation works")
        
        print("  - Testing mythology similarity calculation...")
        result = calculator.calculate_mythology_similarity("Greek", "Roman", "comprehensive", "multi_factor")
        data = json.loads(result)
        assert "calculation_parameters" in data
        assert "similarity_assessment" in data
        print("    ✅ Mythology similarity calculation works")
        
        print("  - Testing religious spread calculation...")
        result = calculator.calculate_religious_spread("Buddhism", "comprehensive", "diffusion", "historical")
        data = json.loads(result)
        assert "calculation_parameters" in data
        assert "spread_assessment" in data
        print("    ✅ Religious spread calculation works")
        
        print("  - Testing mythological complexity calculation...")
        result = calculator.calculate_mythological_complexity("Hindu mythology", "comprehensive", "entropy", "standard")
        data = json.loads(result)
        assert "calculation_parameters" in data
        assert "complexity_assessment" in data
        print("    ✅ Mythological complexity calculation works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Mythology Religion Calculator failed: {str(e)}")
        return False

def test_random_analyzer_calculator_functionality():
    """Test random analyzer and calculator functionality"""
    print("\n🎲 Testing Random Analyzer & Calculator Functionality...")
    
    try:
        # Random mysteries analyzer test
        from tools.mysteries.mysteries_analyzer import MysteriesAnalyzer
        mysteries_analyzer = MysteriesAnalyzer()
        
        mysteries = ["UFO sightings", "Bigfoot", "Loch Ness Monster", "Chupacabra"]
        mystery = random.choice(mysteries)
        result = mysteries_analyzer.analyze_mystery_credibility(mystery, "all", "modern", "standard")
        data = json.loads(result)
        assert "analysis_parameters" in data
        print(f"  🎯 Random mystery {mystery} credibility analysis passed")
        
        # Random mythology calculator test
        from tools.mythology_religion.mythology_religion_calculator import MythologyReligionCalculator
        mythology_calculator = MythologyReligionCalculator()
        
        deities = ["Zeus", "Odin", "Ra", "Shiva", "Amaterasu"]
        deity = random.choice(deities)
        result = mythology_calculator.calculate_deity_power_level(deity, "comprehensive", "weighted", "standard")
        data = json.loads(result)
        assert "calculation_parameters" in data
        print(f"  🎯 Random deity {deity} power calculation passed")
        
        # Random cross-functionality test
        mythologies = ["Greek", "Norse", "Egyptian", "Hindu", "Celtic"]
        myth_a, myth_b = random.sample(mythologies, 2)
        result = mythology_calculator.calculate_mythology_similarity(myth_a, myth_b, "comprehensive", "multi_factor")
        data = json.loads(result)
        assert "calculation_parameters" in data
        print(f"  🎯 Random mythology similarity {myth_a} vs {myth_b} passed")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Random Analyzer & Calculator Functionality failed: {str(e)}")
        return False

def test_integration_functionality():
    """Test integration between analyzers and calculators"""
    print("\n🔗 Testing Integration Functionality...")
    
    try:
        # Test mysteries integration
        from tools.mysteries.mysteries_analyzer import MysteriesAnalyzer
        from tools.mysteries.mysteries_calculator import MysteriesCalculator
        
        mysteries_analyzer = MysteriesAnalyzer()
        mysteries_calculator = MysteriesCalculator()
        
        print("  - Testing mysteries analyzer + calculator integration...")
        # Analyze then calculate
        analysis_result = mysteries_analyzer.analyze_mystery_credibility("Area 51", "all", "modern", "standard")
        calculation_result = mysteries_calculator.calculate_credibility_score("Area 51", "balanced", "weighted", "standard")
        
        analysis_data = json.loads(analysis_result)
        calculation_data = json.loads(calculation_result)
        
        assert "analysis_parameters" in analysis_data
        assert "calculation_parameters" in calculation_data
        print("    ✅ Mysteries integration works")
        
        # Test mythology integration
        from tools.mythology_religion.mythology_religion_analyzer import MythologyReligionAnalyzer
        from tools.mythology_religion.mythology_religion_calculator import MythologyReligionCalculator
        
        mythology_analyzer = MythologyReligionAnalyzer()
        mythology_calculator = MythologyReligionCalculator()
        
        print("  - Testing mythology analyzer + calculator integration...")
        # Analyze then calculate
        analysis_result = mythology_analyzer.analyze_deity_relationships("Greek", "all", "network", "standard")
        calculation_result = mythology_calculator.calculate_deity_power_level("Zeus", "comprehensive", "weighted", "standard")
        
        analysis_data = json.loads(analysis_result)
        calculation_data = json.loads(calculation_result)
        
        assert "analysis_parameters" in analysis_data
        assert "calculation_parameters" in calculation_data
        print("    ✅ Mythology integration works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Integration Functionality failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 ANALYZER & CALCULATOR PACKAGE TEST SUITE")
    print("=" * 70)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing comprehensive Analyzer & Calculator package...")
    print("📦 Package includes: Mysteries Analyzer, Mysteries Calculator,")
    print("   Mythology Religion Analyzer, Mythology Religion Calculator")
    print()
    
    test_results = []
    
    # Test all analyzer and calculator tools
    test_functions = [
        ("Mysteries Analyzer", test_mysteries_analyzer),
        ("Mysteries Calculator", test_mysteries_calculator),
        ("Mythology Religion Analyzer", test_mythology_religion_analyzer),
        ("Mythology Religion Calculator", test_mythology_religion_calculator),
        ("Random Analyzer & Calculator Functionality", test_random_analyzer_calculator_functionality),
        ("Integration Functionality", test_integration_functionality)
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n{'='*25} {test_name} {'='*25}")
        result = test_func()
        test_results.append((test_name, result))
        print()
    
    # Summary
    print("\n" + "="*70)
    print("📋 ANALYZER & CALCULATOR PACKAGE TEST SUMMARY")
    print("="*70)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ANALYZER & CALCULATOR PACKAGE FULLY FUNCTIONAL!")
        print("✨ All 4 tools working perfectly:")
        print("   🔍 Mysteries Analyzer - 5/5 methods working")
        print("   🧮 Mysteries Calculator - 5/5 methods working") 
        print("   🏛️ Mythology Religion Analyzer - 5/5 methods working")
        print("   ⚖️ Mythology Religion Calculator - 5/5 methods working")
        print("🚀 Package ready for production use!")
    elif passed >= total * 0.8:
        print("✅ Excellent performance - most functionality working correctly!")
        print("🔧 Minor issues detected, but package is largely functional.")
    elif passed >= total * 0.6:
        print("✅ Good performance - majority of functionality working!")
        print("⚠️ Some issues detected. Review failed tests above.")
    else:
        print("⚠️ Significant issues detected. Please check the error messages above.")
        print("🔧 Package needs debugging before production use.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("📦 Analyzer & Calculator Package Testing Complete!")

if __name__ == "__main__":
    main()
