from agno.agent import Agent
from tools.writer.writer_research import WriterResearchTools
from agno.models.openrouter import OpenRouter
import asyncio
import os

from dotenv import load_dotenv
load_dotenv()

agent = Agent(
    name="Writer Professional",
    model=OpenRouter(id="meta-llama/llama-4-maverick:free", api_key=os.getenv("OPENROUTER_API_KEY")),
    instructions="""
        You are a researcher professional specializing in technology and writing comprehensive reports.
        Your task is to write a detailed report on the applications of rare earth elements and their impact over the next 10 years.
        Use the WriterResearchTools to gather information and write a report of at least 20,000 tokens.
    """,
    tools=[WriterResearchTools()],
    show_tool_calls=True,
    markdown=True
)

if __name__ == "__main__":
    async def main():
        await agent.aprint_response(
            "Viết báo cáo về ứng dụng của đất hiếm, ảnh hưởng trong 10 năm tới, sử dụng hướng dẫn trong WriterResearchTools để viết ít nhất 1000 tokens",
            stream=True
        )
    asyncio.run(main())