from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime, timedelta

class TwitchTool(Toolkit):
    """
    Công cụ tìm kiếm Twitch để tìm kiếm live streams, sự kiện esports và game đang thịnh hành.

    <PERSON><PERSON>c từ khóa tìm kiếm gợi ý:
    - Streamer đang live (live streams, streamers)
    - Sự kiện esports (esports events, tournaments)
    - Game đang thịnh hành (trending games, popular games)
    - Thể loại stream (just chatting, speedrun, IRL)
    - Đ<PERSON>i tuyển esports (T1, Team Liquid, G2 Esports)
    - <PERSON><PERSON><PERSON><PERSON> (The International, Worlds, Major)
    """

    def __init__(self):
        super().__init__(
            name="Công cụ tìm kiếm Twitch",
            tools=[self.search_twitch, self.get_top_new]
        )
        self.base_url = "https://api.twitch.tv/helix"
        self.search_types = ["streams", "channels", "games", "clips", "videos"]
        self.languages = ["en", "vi", "ja", "ko", "zh", "all"]
        self.game_categories = ["Just Chatting", "League of Legends", "Valorant", "Minecraft", "Fortnite", "GTA V"]

    def search_twitch(self, query: str, search_type: str = "streams", language: str = "all",
                     limit: int = 5, category: str = "") -> str:
        """
        Tìm kiếm nội dung trên Twitch.

        Args:
            query: Từ khóa tìm kiếm (tên streamer, game, sự kiện, v.v.)
            search_type: Loại nội dung (streams, channels, games, clips, videos)
            language: Ngôn ngữ (en, vi, ja, ko, zh, all)
            limit: Số lượng kết quả trả về (tối đa 10)
            category: Thể loại game (Just Chatting, League of Legends, v.v.)

        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm Twitch với từ khóa: {query}, loại: {search_type}")

        # Xác thực tham số
        if search_type not in self.search_types:
            search_type = "streams"
        if language not in self.languages:
            language = "all"

        limit = max(1, min(limit, 10))  # Giới hạn trong khoảng 1-10

        try:
            # Giả lập kết quả tìm kiếm
            results = []
            current_time = datetime.utcnow()

            # Tạo thời gian ngẫu nhiên trong 24h qua
            def random_past_time() -> str:
                random_hours = hash(query) % 24
                return (current_time - timedelta(hours=random_hours)).strftime("%Y-%m-%dT%H:%M:%SZ")

            if search_type == "streams":
                results.extend([
                    {
                        "title": f"{query} Stream #{i+1} - {category if category else 'Live Now!'}",
                        "streamer": f"{query}Streamer{i+1}",
                        "type": "live",
                        "game": category if category else query,
                        "viewers": 1000 + (i * 500),
                        "started_at": random_past_time(),
                        "language": language if language != "all" else "en",
                        "thumbnail_url": f"https://static-cdn.jtvnw.net/previews-ttv/live_user_{query.lower()}{i+1}-{i+1}92x108.jpg",
                        "url": f"https://www.twitch.tv/{query.lower()}streamer{i+1}"
                    } for i in range(min(limit, 5))
                ])

            elif search_type == "channels":
                results.extend([
                    {
                        "name": f"{query}Channel{i+1}",
                        "display_name": f"{query} Channel {i+1}",
                        "type": "channel",
                        "game": category if category else query,
                        "status": f"Playing {category if category else query}",
                        "followers": 5000 + (i * 1000),
                        "is_live": i % 2 == 0,
                        "thumbnail_url": f"https://static-cdn.jtvnw.net/jtv_user_pictures/{query.lower()}{i+1}-profile_image-70x70.png",
                        "url": f"https://www.twitch.tv/{query.lower()}channel{i+1}"
                    } for i in range(min(limit, 4))
                ])

            elif search_type == "games":
                results.extend([
                    {
                        "name": f"{query} {i+1}",
                        "type": "game",
                        "viewers": 5000 + (i * 2000),
                        "channels": 100 + (i * 50),
                        "popularity_rank": i + 1,
                        "thumbnail_url": f"https://static-cdn.jtvnw.net/ttv-boxart/{query.lower().replace(' ', '-')}-{i+1}-144x192.jpg",
                        "url": f"https://www.twitch.tv/directory/game/{query.replace(' ', '%20')}%20{i+1}"
                    } for i in range(min(limit, 5))
                ])

            elif search_type == "clips":
                results.extend([
                    {
                        "title": f"{query} Amazing Play #{i+1} - {category if category else 'Highlight'}",
                        "creator": f"{query}ClipMaker{i+1}",
                        "type": "clip",
                        "game": category if category else query,
                        "views": 5000 + (i * 1000),
                        "duration": 30 + (i * 5),
                        "created_at": random_past_time(),
                        "thumbnail_url": f"https://clips-media-assets2.twitch.tv/1234567890-{i+1}-preview-480x272.jpg",
                        "url": f"https://clips.twitch.tv/{query.lower()}-AmazingPlay{i+1}"
                    } for i in range(min(limit, 4))
                ])

            elif search_type == "videos":
                results.extend([
                    {
                        "title": f"{query} Full Stream VOD - {current_time.strftime('%Y-%m-%d')}",
                        "creator": f"{query}Streamer{i+1}",
                        "type": "video",
                        "game": category if category else query,
                        "views": 1000 + (i * 200),
                        "duration": "2:30:45",
                        "created_at": random_past_time(),
                        "thumbnail_url": f"https://static-cdn.jtvnw.net/s3_vods/1234567890/{query.lower()}_1234567890-{i+1}-640x360.jpg",
                        "url": f"https://www.twitch.tv/videos/1234567890"
                    } for i in range(min(limit, 3))
                ])

            result = {
                "status": "success",
                "source": "Twitch",
                "query": query,
                "search_type": search_type,
                "language": language,
                "category": category if category else "All",
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Twitch: {str(e)}")
            result = {
                "status": "error",
                "source": "Twitch",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm {query} trên Twitch",
                        "url": f"https://www.twitch.tv/search?term={query}",
                        "summary": f"Tìm kiếm kênh và nội dung liên quan đến {query} trên Twitch"
                    },
                    {
                        "title": f"Game {query} trên Twitch",
                        "url": f"https://www.twitch.tv/directory/game/{query.replace(' ', '%20')}",
                        "summary": f"Xem các streamer đang chơi {query} trên Twitch"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)

    def get_top_new(self, content_type: str = "streams", limit: int = 10,
                    time_period: str = "24h", game_category: str = "") -> str:
        """
        Lấy nội dung mới nhất và thịnh hành trên Twitch.

        Args:
            content_type: Loại nội dung (streams, clips, videos, games)
            limit: Số lượng kết quả (tối đa 20)
            time_period: Khoảng thời gian (1h, 6h, 24h, 7d, 30d)
            game_category: Thể loại game cụ thể

        Returns:
            Chuỗi JSON chứa nội dung mới nhất
        """
        logger.info(f"Lấy top {content_type} mới nhất trong {time_period}")

        limit = max(1, min(limit, 20))
        current_time = datetime.utcnow()

        try:
            results = []

            if content_type == "streams":
                # Top streams mới và thịnh hành
                results = [
                    {
                        "title": f"🔴 LIVE: {game_category or 'Trending Game'} - Epic Gameplay #{i+1}",
                        "streamer": f"TopStreamer{i+1}",
                        "game": game_category or f"TrendingGame{i+1}",
                        "viewers": 50000 - (i * 5000),
                        "started_at": (current_time - timedelta(hours=i+1)).strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "language": "en",
                        "tags": ["esports", "competitive", "new"],
                        "is_mature": False,
                        "thumbnail_url": f"https://static-cdn.jtvnw.net/previews-ttv/live_user_topstreamer{i+1}-640x360.jpg",
                        "url": f"https://www.twitch.tv/topstreamer{i+1}"
                    } for i in range(limit)
                ]

            elif content_type == "clips":
                # Top clips mới nhất
                results = [
                    {
                        "title": f"🎬 Amazing {game_category or 'Gaming'} Moment #{i+1}",
                        "creator": f"ClipMaster{i+1}",
                        "broadcaster": f"ProPlayer{i+1}",
                        "game": game_category or f"PopularGame{i+1}",
                        "views": 100000 - (i * 8000),
                        "duration": 30 + (i * 5),
                        "created_at": (current_time - timedelta(hours=i*2)).strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "language": "en",
                        "thumbnail_url": f"https://clips-media-assets2.twitch.tv/clip{i+1}-preview-480x272.jpg",
                        "url": f"https://clips.twitch.tv/AmazingMoment{i+1}"
                    } for i in range(limit)
                ]

            elif content_type == "games":
                # Top games mới và thịnh hành
                results = [
                    {
                        "name": f"New Trending Game {i+1}",
                        "viewers": 200000 - (i * 15000),
                        "channels": 5000 - (i * 300),
                        "growth_rate": f"+{50-i*3}%",
                        "category": "Action/RPG/Strategy",
                        "release_status": "New Release" if i < 3 else "Trending",
                        "thumbnail_url": f"https://static-cdn.jtvnw.net/ttv-boxart/newgame{i+1}-144x192.jpg",
                        "url": f"https://www.twitch.tv/directory/game/New%20Trending%20Game%20{i+1}"
                    } for i in range(limit)
                ]

            elif content_type == "videos":
                # Top videos mới nhất
                results = [
                    {
                        "title": f"📺 Best {game_category or 'Gaming'} Highlights - Episode {i+1}",
                        "creator": f"ContentCreator{i+1}",
                        "game": game_category or f"FeaturedGame{i+1}",
                        "views": 50000 - (i * 3000),
                        "duration": f"{2+i}:30:15",
                        "created_at": (current_time - timedelta(days=i+1)).strftime("%Y-%m-%dT%H:%M:%SZ"),
                        "type": "highlight",
                        "language": "en",
                        "thumbnail_url": f"https://static-cdn.jtvnw.net/s3_vods/video{i+1}-640x360.jpg",
                        "url": f"https://www.twitch.tv/videos/12345678{i+1}"
                    } for i in range(limit)
                ]

            result = {
                "status": "success",
                "source": "Twitch Top New",
                "content_type": content_type,
                "time_period": time_period,
                "game_category": game_category or "All Games",
                "limit": limit,
                "total_results": len(results),
                "trending_metrics": {
                    "total_viewers": sum(r.get("viewers", 0) for r in results),
                    "average_growth": "+25%",
                    "peak_hours": "18:00-22:00 UTC",
                    "top_regions": ["NA", "EU", "APAC"]
                },
                "results": results,
                "generated_at": current_time.strftime("%Y-%m-%dT%H:%M:%SZ")
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy top new Twitch: {str(e)}")
            return json.dumps({
                "status": "error",
                "source": "Twitch Top New",
                "message": str(e),
                "fallback_url": "https://www.twitch.tv/directory"
            }, ensure_ascii=False, indent=2)
