from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime, timedelta

class TwitchTool(Toolkit):
    """
    Công cụ tìm kiếm Twitch để tìm kiếm live streams, sự kiện esports và game đang thịnh hành.
    
    <PERSON><PERSON>c từ khóa tìm kiếm gợi ý:
    - Streamer đang live (live streams, streamers)
    - Sự kiện esports (esports events, tournaments)
    - Game đang thịnh hành (trending games, popular games)
    - Thể loại stream (just chatting, speedrun, IRL)
    - Đ<PERSON>i tuyển esports (T1, Team Liquid, G2 Esports)
    - <PERSON><PERSON><PERSON><PERSON> (The International, Worlds, Major)
    """
    
    def __init__(self):
        super().__init__(
            name="Công cụ tìm kiếm Twitch",
            tools=[self.search_twitch]
        )
        self.base_url = "https://api.twitch.tv/helix"
        self.search_types = ["streams", "channels", "games", "clips", "videos"]
        self.languages = ["en", "vi", "ja", "ko", "zh", "all"]
        self.game_categories = ["Just Chatting", "League of Legends", "Valorant", "Minecraft", "Fortnite", "GTA V"]

    def search_twitch(self, query: str, search_type: str = "streams", language: str = "all", 
                     limit: int = 5, category: str = "") -> str:
        """
        Tìm kiếm nội dung trên Twitch.
        
        Args:
            query: Từ khóa tìm kiếm (tên streamer, game, sự kiện, v.v.)
            search_type: Loại nội dung (streams, channels, games, clips, videos)
            language: Ngôn ngữ (en, vi, ja, ko, zh, all)
            limit: Số lượng kết quả trả về (tối đa 10)
            category: Thể loại game (Just Chatting, League of Legends, v.v.)
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm Twitch với từ khóa: {query}, loại: {search_type}")
        
        # Xác thực tham số
        if search_type not in self.search_types:
            search_type = "streams"
        if language not in self.languages:
            language = "all"
            
        limit = max(1, min(limit, 10))  # Giới hạn trong khoảng 1-10
        
        try:
            # Giả lập kết quả tìm kiếm
            results = []
            current_time = datetime.utcnow()
            
            # Tạo thời gian ngẫu nhiên trong 24h qua
            def random_past_time() -> str:
                random_hours = hash(query) % 24
                return (current_time - timedelta(hours=random_hours)).strftime("%Y-%m-%dT%H:%M:%SZ")
                
            if search_type == "streams":
                results.extend([
                    {
                        "title": f"{query} Stream #{i+1} - {category if category else 'Live Now!'}",
                        "streamer": f"{query}Streamer{i+1}",
                        "type": "live",
                        "game": category if category else query,
                        "viewers": 1000 + (i * 500),
                        "started_at": random_past_time(),
                        "language": language if language != "all" else "en",
                        "thumbnail_url": f"https://static-cdn.jtvnw.net/previews-ttv/live_user_{query.lower()}{i+1}-{i+1}92x108.jpg",
                        "url": f"https://www.twitch.tv/{query.lower()}streamer{i+1}"
                    } for i in range(min(limit, 5))
                ])
                
            elif search_type == "channels":
                results.extend([
                    {
                        "name": f"{query}Channel{i+1}",
                        "display_name": f"{query} Channel {i+1}",
                        "type": "channel",
                        "game": category if category else query,
                        "status": f"Playing {category if category else query}",
                        "followers": 5000 + (i * 1000),
                        "is_live": i % 2 == 0,
                        "thumbnail_url": f"https://static-cdn.jtvnw.net/jtv_user_pictures/{query.lower()}{i+1}-profile_image-70x70.png",
                        "url": f"https://www.twitch.tv/{query.lower()}channel{i+1}"
                    } for i in range(min(limit, 4))
                ])
                
            elif search_type == "games":
                results.extend([
                    {
                        "name": f"{query} {i+1}",
                        "type": "game",
                        "viewers": 5000 + (i * 2000),
                        "channels": 100 + (i * 50),
                        "popularity_rank": i + 1,
                        "thumbnail_url": f"https://static-cdn.jtvnw.net/ttv-boxart/{query.lower().replace(' ', '-')}-{i+1}-144x192.jpg",
                        "url": f"https://www.twitch.tv/directory/game/{query.replace(' ', '%20')}%20{i+1}"
                    } for i in range(min(limit, 5))
                ])
                
            elif search_type == "clips":
                results.extend([
                    {
                        "title": f"{query} Amazing Play #{i+1} - {category if category else 'Highlight'}",
                        "creator": f"{query}ClipMaker{i+1}",
                        "type": "clip",
                        "game": category if category else query,
                        "views": 5000 + (i * 1000),
                        "duration": 30 + (i * 5),
                        "created_at": random_past_time(),
                        "thumbnail_url": f"https://clips-media-assets2.twitch.tv/1234567890-{i+1}-preview-480x272.jpg",
                        "url": f"https://clips.twitch.tv/{query.lower()}-AmazingPlay{i+1}"
                    } for i in range(min(limit, 4))
                ])
                
            elif search_type == "videos":
                results.extend([
                    {
                        "title": f"{query} Full Stream VOD - {current_time.strftime('%Y-%m-%d')}",
                        "creator": f"{query}Streamer{i+1}",
                        "type": "video",
                        "game": category if category else query,
                        "views": 1000 + (i * 200),
                        "duration": "2:30:45",
                        "created_at": random_past_time(),
                        "thumbnail_url": f"https://static-cdn.jtvnw.net/s3_vods/1234567890/{query.lower()}_1234567890-{i+1}-640x360.jpg",
                        "url": f"https://www.twitch.tv/videos/1234567890"
                    } for i in range(min(limit, 3))
                ])
            
            result = {
                "status": "success",
                "source": "Twitch",
                "query": query,
                "search_type": search_type,
                "language": language,
                "category": category if category else "All",
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Twitch: {str(e)}")
            result = {
                "status": "error",
                "source": "Twitch",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm {query} trên Twitch",
                        "url": f"https://www.twitch.tv/search?term={query}",
                        "summary": f"Tìm kiếm kênh và nội dung liên quan đến {query} trên Twitch"
                    },
                    {
                        "title": f"Game {query} trên Twitch",
                        "url": f"https://www.twitch.tv/directory/game/{query.replace(' ', '%20')}",
                        "summary": f"Xem các streamer đang chơi {query} trên Twitch"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
