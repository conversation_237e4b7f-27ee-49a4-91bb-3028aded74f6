# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math
from datetime import datetime

class StellarEvolutionCalculator(Toolkit):
    """
    Stellar Evolution Calculator cho tính toán stellar evolution, binary system development và galactic evolution.
    """

    def __init__(self, enable_calculations: bool = True, **kwargs):
        super().__init__(
            name="stellar_evolution_calculator",
            **kwargs
        )
        
        # Stellar evolution phases (main sequence lifetime fractions)
        self.evolution_phases = {
            "protostar": {"duration_fraction": 0.001, "luminosity_factor": 0.1, "temperature_factor": 0.8},
            "main_sequence": {"duration_fraction": 0.9, "luminosity_factor": 1.0, "temperature_factor": 1.0},
            "subgiant": {"duration_fraction": 0.05, "luminosity_factor": 2.0, "temperature_factor": 0.9},
            "red_giant": {"duration_fraction": 0.04, "luminosity_factor": 100.0, "temperature_factor": 0.6},
            "horizontal_branch": {"duration_fraction": 0.005, "luminosity_factor": 50.0, "temperature_factor": 1.2},
            "asymptotic_giant": {"duration_fraction": 0.004, "luminosity_factor": 1000.0, "temperature_factor": 0.5},
            "white_dwarf": {"duration_fraction": float('inf'), "luminosity_factor": 0.001, "temperature_factor": 2.0}
        }
        
        # Stellar mass categories (solar masses)
        self.mass_categories = {
            "brown_dwarf": {"mass_range": (0.01, 0.08), "lifetime_gyr": float('inf'), "end_state": "brown_dwarf"},
            "red_dwarf": {"mass_range": (0.08, 0.5), "lifetime_gyr": 100.0, "end_state": "white_dwarf"},
            "solar_type": {"mass_range": (0.5, 2.0), "lifetime_gyr": 10.0, "end_state": "white_dwarf"},
            "intermediate": {"mass_range": (2.0, 8.0), "lifetime_gyr": 1.0, "end_state": "white_dwarf"},
            "massive": {"mass_range": (8.0, 25.0), "lifetime_gyr": 0.05, "end_state": "neutron_star"},
            "supermassive": {"mass_range": (25.0, 100.0), "lifetime_gyr": 0.01, "end_state": "black_hole"}
        }
        
        # Galactic evolution timescales (billion years)
        self.galactic_timescales = {
            "star_formation_peak": 10.0,
            "galaxy_merger": 5.0,
            "stellar_population_aging": 13.0,
            "supermassive_black_hole_growth": 8.0,
            "chemical_enrichment": 12.0
        }
        
        if enable_calculations:
            self.register(self.calculate_stellar_evolution)
            self.register(self.estimate_binary_system_evolution)
            self.register(self.analyze_galactic_evolution)
            self.register(self.predict_stellar_population_trends)

    def calculate_stellar_evolution(self, star_mass_solar: float, current_age_gyr: float = 0.0,
                                  metallicity: float = 0.02, companion_mass: float = None) -> str:
        """
        Tính toán stellar evolution trajectory và lifetime phases.
        
        Args:
            star_mass_solar: Khối lượng sao (solar masses)
            current_age_gyr: Tuổi hiện tại (billion years)
            metallicity: Độ kim loại (solar metallicity = 0.02)
            companion_mass: Khối lượng sao đồng hành (nếu có)
            
        Returns:
            Chuỗi JSON chứa tính toán stellar evolution
        """
        log_debug(f"Calculating stellar evolution for {star_mass_solar} solar mass star")
        
        try:
            # Determine stellar category
            stellar_category = self._categorize_star(star_mass_solar)
            category_data = self.mass_categories[stellar_category]
            
            # Calculate main sequence lifetime
            ms_lifetime_gyr = 10.0 * (star_mass_solar ** -2.5)  # Mass-luminosity relation
            total_lifetime_gyr = ms_lifetime_gyr / 0.9  # Main sequence is 90% of total
            
            # Current evolutionary phase
            age_fraction = current_age_gyr / total_lifetime_gyr if total_lifetime_gyr != float('inf') else 0
            current_phase = self._determine_current_phase(age_fraction)
            
            # Evolution timeline
            evolution_timeline = []
            cumulative_time = 0
            
            for phase_name, phase_data in self.evolution_phases.items():
                if phase_name == "white_dwarf" and stellar_category in ["brown_dwarf", "red_dwarf"]:
                    continue
                if phase_name in ["horizontal_branch", "asymptotic_giant"] and star_mass_solar < 0.5:
                    continue
                
                phase_duration = total_lifetime_gyr * phase_data["duration_fraction"]
                if phase_duration == float('inf'):
                    phase_duration = "Indefinite"
                
                evolution_timeline.append({
                    "phase": phase_name.replace("_", " ").title(),
                    "start_age_gyr": round(cumulative_time, 3),
                    "duration_gyr": phase_duration if isinstance(phase_duration, str) else round(phase_duration, 3),
                    "luminosity_factor": phase_data["luminosity_factor"],
                    "temperature_factor": phase_data["temperature_factor"],
                    "is_current": phase_name == current_phase
                })
                
                if isinstance(phase_duration, (int, float)):
                    cumulative_time += phase_duration
            
            # Stellar properties evolution
            current_phase_data = self.evolution_phases[current_phase]
            stellar_properties = {
                "current_luminosity": round(current_phase_data["luminosity_factor"], 2),
                "current_temperature_factor": current_phase_data["temperature_factor"],
                "surface_temperature_k": int(5778 * current_phase_data["temperature_factor"]),  # Solar temp = 5778K
                "radius_factor": round(math.sqrt(current_phase_data["luminosity_factor"] / (current_phase_data["temperature_factor"] ** 4)), 2)
            }
            
            # Nuclear burning processes
            nuclear_processes = self._analyze_nuclear_processes(star_mass_solar, current_phase)
            
            # Metallicity effects
            metallicity_effects = {
                "opacity_enhancement": round(metallicity / 0.02, 2),
                "stellar_wind_strength": round((metallicity / 0.02) ** 0.5, 2),
                "planetary_nebula_enrichment": "Significant" if metallicity > 0.01 else "Minimal",
                "supernova_yields": "Enhanced heavy elements" if star_mass_solar > 8 else "Minimal"
            }
            
            # Binary system effects
            binary_effects = None
            if companion_mass:
                binary_effects = self._analyze_binary_effects(star_mass_solar, companion_mass, current_phase)
            
            # Future evolution
            future_evolution = self._predict_future_evolution(star_mass_solar, current_age_gyr, total_lifetime_gyr)
            
            result = {
                "stellar_parameters": {
                    "mass_solar": star_mass_solar,
                    "current_age_gyr": current_age_gyr,
                    "metallicity": metallicity,
                    "stellar_category": stellar_category,
                    "main_sequence_lifetime_gyr": round(ms_lifetime_gyr, 3)
                },
                "current_state": {
                    "evolutionary_phase": current_phase.replace("_", " ").title(),
                    "age_fraction": round(age_fraction, 3),
                    "remaining_lifetime_gyr": round(max(0, total_lifetime_gyr - current_age_gyr), 3)
                },
                "stellar_properties": stellar_properties,
                "evolution_timeline": evolution_timeline,
                "nuclear_processes": nuclear_processes,
                "metallicity_effects": metallicity_effects,
                "binary_effects": binary_effects,
                "future_evolution": future_evolution,
                "final_fate": category_data["end_state"],
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error calculating stellar evolution: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate stellar evolution: {str(e)}"
            }, indent=4)

    def estimate_binary_system_evolution(self, primary_mass: float, secondary_mass: float,
                                       orbital_period_days: float = 365.0,
                                       evolution_time_gyr: float = 5.0) -> str:
        """
        Ước tính binary system evolution và mass transfer processes.
        """
        log_debug(f"Estimating binary system evolution: {primary_mass} + {secondary_mass} solar masses")
        
        try:
            # Binary system parameters
            mass_ratio = secondary_mass / primary_mass
            total_mass = primary_mass + secondary_mass
            
            # Orbital evolution
            orbital_decay_rate = 0.01 if orbital_period_days < 10 else 0.001  # Tidal effects
            final_period = orbital_period_days * (1 - orbital_decay_rate * evolution_time_gyr)
            
            # Evolution phases
            evolution_phases = []
            
            # Phase 1: Detached phase
            detached_duration = min(evolution_time_gyr, 2.0)
            evolution_phases.append({
                "phase": "Detached Binary",
                "duration_gyr": detached_duration,
                "characteristics": ["Independent evolution", "Minimal interaction", "Stable orbits"],
                "mass_transfer_rate": 0.0
            })
            
            # Phase 2: Mass transfer phase (if applicable)
            if primary_mass > 1.0 and evolution_time_gyr > 2.0:
                mt_duration = min(evolution_time_gyr - 2.0, 1.0)
                mt_rate = 1e-8 * (primary_mass / secondary_mass)  # Solar masses per year
                
                evolution_phases.append({
                    "phase": "Mass Transfer",
                    "duration_gyr": mt_duration,
                    "characteristics": ["Roche lobe overflow", "Accretion disk", "System brightening"],
                    "mass_transfer_rate": mt_rate
                })
            
            # Phase 3: Common envelope (if applicable)
            if primary_mass > 8.0 and evolution_time_gyr > 3.0:
                ce_duration = 0.01  # Very short phase
                evolution_phases.append({
                    "phase": "Common Envelope",
                    "duration_gyr": ce_duration,
                    "characteristics": ["Envelope ejection", "Orbital shrinkage", "System merger risk"],
                    "mass_transfer_rate": 1e-3
                })
            
            # Tidal effects
            tidal_effects = {
                "orbital_circularization": "Complete" if orbital_period_days < 30 else "Partial",
                "spin_orbit_synchronization": "Achieved" if orbital_period_days < 100 else "Ongoing",
                "tidal_heating": round(1.0 / orbital_period_days, 4),
                "orbital_decay_timescale": f"{1.0 / orbital_decay_rate:.1f} Gyr"
            }
            
            # Mass transfer stability
            stability_analysis = {
                "mass_ratio_stability": "Stable" if 0.3 < mass_ratio < 3.0 else "Unstable",
                "thermal_timescale_instability": "Possible" if primary_mass > 3.0 else "Unlikely",
                "dynamical_instability": "Risk" if mass_ratio > 10 else "Stable",
                "nova_potential": "High" if secondary_mass > 0.6 and primary_mass < 1.4 else "Low"
            }
            
            # Observational signatures
            observational_signatures = {
                "eclipsing_binary": "Yes" if orbital_period_days < 1000 else "Unlikely",
                "x_ray_emission": "Strong" if any("Mass Transfer" in phase["phase"] for phase in evolution_phases) else "Weak",
                "gravitational_waves": "Detectable" if orbital_period_days < 1 else "Below threshold",
                "spectroscopic_binary": "Detectable" if orbital_period_days < 10000 else "Challenging"
            }
            
            # Final outcomes
            final_outcomes = self._predict_binary_outcomes(primary_mass, secondary_mass, evolution_phases)
            
            result = {
                "binary_parameters": {
                    "primary_mass": primary_mass,
                    "secondary_mass": secondary_mass,
                    "mass_ratio": round(mass_ratio, 2),
                    "initial_period_days": orbital_period_days,
                    "evolution_time_gyr": evolution_time_gyr
                },
                "orbital_evolution": {
                    "final_period_days": round(final_period, 1),
                    "orbital_decay_rate": orbital_decay_rate,
                    "period_change": round((final_period - orbital_period_days) / orbital_period_days * 100, 1)
                },
                "evolution_phases": evolution_phases,
                "tidal_effects": tidal_effects,
                "stability_analysis": stability_analysis,
                "observational_signatures": observational_signatures,
                "final_outcomes": final_outcomes,
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error estimating binary system evolution: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to estimate binary system evolution: {str(e)}"
            }, indent=4)

    def analyze_galactic_evolution(self, galaxy_type: str, evolution_time_gyr: float = 10.0,
                                 merger_history: int = 2) -> str:
        """
        Phân tích galactic evolution và stellar population changes.
        """
        log_debug(f"Analyzing galactic evolution for {galaxy_type} galaxy")
        
        try:
            # Galaxy evolution parameters
            galaxy_params = {
                "spiral": {"star_formation_rate": 1.0, "gas_fraction": 0.1, "metallicity_gradient": 0.05},
                "elliptical": {"star_formation_rate": 0.1, "gas_fraction": 0.01, "metallicity_gradient": 0.02},
                "irregular": {"star_formation_rate": 0.5, "gas_fraction": 0.3, "metallicity_gradient": 0.01},
                "dwarf": {"star_formation_rate": 0.1, "gas_fraction": 0.5, "metallicity_gradient": 0.005}
            }
            
            params = galaxy_params.get(galaxy_type, galaxy_params["spiral"])
            
            # Stellar population evolution
            population_evolution = []
            for gyr in range(0, int(evolution_time_gyr) + 1, 2):
                # Calculate stellar populations
                young_stars = params["star_formation_rate"] * math.exp(-gyr / 5.0)  # Exponential decline
                intermediate_stars = params["star_formation_rate"] * 0.5 * (1 - math.exp(-gyr / 3.0))
                old_stars = params["star_formation_rate"] * 0.3 * gyr / evolution_time_gyr
                
                population_evolution.append({
                    "time_gyr": gyr,
                    "young_stars_fraction": round(young_stars / (young_stars + intermediate_stars + old_stars), 2),
                    "intermediate_stars_fraction": round(intermediate_stars / (young_stars + intermediate_stars + old_stars), 2),
                    "old_stars_fraction": round(old_stars / (young_stars + intermediate_stars + old_stars), 2),
                    "total_stellar_mass": round((young_stars + intermediate_stars + old_stars) * evolution_time_gyr, 1)
                })
            
            # Chemical evolution
            chemical_evolution = {
                "initial_metallicity": 0.001,  # Primordial
                "current_metallicity": round(0.001 + (evolution_time_gyr * 0.002), 3),
                "alpha_enhancement": round(0.3 - (evolution_time_gyr * 0.02), 2),
                "metallicity_gradient": params["metallicity_gradient"],
                "enrichment_rate": "Rapid" if params["star_formation_rate"] > 0.5 else "Gradual"
            }
            
            # Merger effects
            merger_effects = {
                "stellar_population_mixing": "Significant" if merger_history > 1 else "Minimal",
                "star_formation_triggering": f"{merger_history * 50}% increase during mergers",
                "morphological_transformation": "Likely" if merger_history > 2 else "Minimal",
                "central_black_hole_growth": f"{merger_history * 2}x mass increase"
            }
            
            # Supernova feedback
            supernova_feedback = {
                "type_ia_rate": round(params["star_formation_rate"] * 0.001, 4),
                "core_collapse_rate": round(params["star_formation_rate"] * 0.01, 3),
                "metal_enrichment": "Significant" if params["star_formation_rate"] > 0.3 else "Moderate",
                "gas_heating_cooling": "Balanced" if galaxy_type == "spiral" else "Heating dominated"
            }
            
            # Future evolution
            future_evolution = {
                "star_formation_fate": "Declining" if params["gas_fraction"] < 0.2 else "Continuing",
                "stellar_population_aging": f"{evolution_time_gyr + 5} Gyr for significant aging",
                "gas_depletion_timescale": f"{params['gas_fraction'] / params['star_formation_rate']:.1f} Gyr",
                "final_state": "Red and dead" if galaxy_type == "elliptical" else "Slowly evolving"
            }
            
            result = {
                "galaxy_evolution": {
                    "galaxy_type": galaxy_type,
                    "evolution_time_gyr": evolution_time_gyr,
                    "merger_history": merger_history,
                    "current_star_formation_rate": params["star_formation_rate"]
                },
                "stellar_population_evolution": population_evolution,
                "chemical_evolution": chemical_evolution,
                "merger_effects": merger_effects,
                "supernova_feedback": supernova_feedback,
                "future_evolution": future_evolution,
                "observational_predictions": self._generate_observational_predictions(galaxy_type, evolution_time_gyr),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error analyzing galactic evolution: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze galactic evolution: {str(e)}"
            }, indent=4)

    def predict_stellar_population_trends(self, population_age_gyr: float = 5.0,
                                        initial_mass_function: str = "salpeter",
                                        prediction_time_gyr: float = 10.0) -> str:
        """
        Dự đoán stellar population trends và evolutionary endpoints.
        """
        log_debug(f"Predicting stellar population trends over {prediction_time_gyr} Gyr")
        
        try:
            # Initial mass function parameters
            imf_params = {
                "salpeter": {"slope": -2.35, "low_mass_cutoff": 0.1, "high_mass_cutoff": 100},
                "kroupa": {"slope": -2.3, "low_mass_cutoff": 0.08, "high_mass_cutoff": 150},
                "chabrier": {"slope": -2.3, "low_mass_cutoff": 0.1, "high_mass_cutoff": 120}
            }
            
            imf = imf_params.get(initial_mass_function, imf_params["salpeter"])
            
            # Population evolution predictions
            evolution_predictions = {}
            
            # Low mass stars (0.1-0.8 solar masses)
            low_mass_fraction = 0.7
            evolution_predictions["low_mass_stars"] = {
                "current_fraction": low_mass_fraction,
                "future_fraction": low_mass_fraction,  # Unchanged
                "evolution_status": "Still on main sequence",
                "contribution": "Dominant by number, minimal by luminosity"
            }
            
            # Solar-type stars (0.8-2.0 solar masses)
            solar_type_fraction = 0.25
            evolved_fraction = min(1.0, (population_age_gyr + prediction_time_gyr) / 10.0)
            evolution_predictions["solar_type_stars"] = {
                "current_fraction": solar_type_fraction,
                "future_fraction": round(solar_type_fraction * (1 - evolved_fraction), 2),
                "evolution_status": f"{evolved_fraction * 100:.0f}% evolved to white dwarfs",
                "contribution": "Significant luminosity contribution"
            }
            
            # Massive stars (>8 solar masses)
            massive_fraction = 0.05
            massive_evolved = min(1.0, (population_age_gyr + prediction_time_gyr) / 0.1)
            evolution_predictions["massive_stars"] = {
                "current_fraction": massive_fraction,
                "future_fraction": round(massive_fraction * (1 - massive_evolved), 3),
                "evolution_status": f"{massive_evolved * 100:.0f}% evolved to compact objects",
                "contribution": "Dominated early luminosity, now compact remnants"
            }
            
            # Stellar remnant populations
            remnant_populations = {
                "white_dwarfs": {
                    "current_number": round(evolved_fraction * solar_type_fraction * 1000),
                    "future_number": round((evolved_fraction + 0.2) * solar_type_fraction * 1000),
                    "cooling_age": f"{population_age_gyr:.1f} Gyr average",
                    "mass_distribution": "0.6 ± 0.2 solar masses"
                },
                "neutron_stars": {
                    "current_number": round(massive_evolved * massive_fraction * 100),
                    "future_number": round((massive_evolved + 0.1) * massive_fraction * 100),
                    "typical_mass": "1.4 solar masses",
                    "magnetic_field_decay": "Significant after 1 Gyr"
                },
                "black_holes": {
                    "current_number": round(massive_evolved * massive_fraction * 20),
                    "future_number": round((massive_evolved + 0.05) * massive_fraction * 20),
                    "mass_range": "3-50 solar masses",
                    "accretion_activity": "Minimal in isolation"
                }
            }
            
            # Luminosity evolution
            luminosity_evolution = {
                "total_luminosity_change": round(-0.5 * (prediction_time_gyr / 10.0), 2),
                "color_evolution": "Redder due to stellar evolution",
                "surface_brightness": "Decreasing",
                "mass_to_light_ratio": "Increasing"
            }
            
            # Chemical enrichment
            chemical_enrichment = {
                "metallicity_increase": round(prediction_time_gyr * 0.0001, 4),
                "alpha_element_ratio": "Decreasing with time",
                "iron_peak_elements": "Increasing from Type Ia SNe",
                "neutron_capture_elements": "Slow increase"
            }
            
            result = {
                "population_parameters": {
                    "population_age_gyr": population_age_gyr,
                    "initial_mass_function": initial_mass_function,
                    "prediction_time_gyr": prediction_time_gyr,
                    "total_evolution_time": population_age_gyr + prediction_time_gyr
                },
                "evolution_predictions": evolution_predictions,
                "remnant_populations": remnant_populations,
                "luminosity_evolution": luminosity_evolution,
                "chemical_enrichment": chemical_enrichment,
                "observational_consequences": self._predict_observational_consequences(population_age_gyr, prediction_time_gyr),
                "comparison_with_observations": self._compare_with_observations(population_age_gyr),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error predicting stellar population trends: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to predict stellar population trends: {str(e)}"
            }, indent=4)

    # Helper methods
    def _categorize_star(self, mass: float) -> str:
        """Categorize star based on mass."""
        for category, data in self.mass_categories.items():
            if data["mass_range"][0] <= mass < data["mass_range"][1]:
                return category
        return "supermassive"

    def _determine_current_phase(self, age_fraction: float) -> str:
        """Determine current evolutionary phase based on age fraction."""
        cumulative = 0
        for phase, data in self.evolution_phases.items():
            cumulative += data["duration_fraction"]
            if age_fraction <= cumulative or phase == "white_dwarf":
                return phase
        return "white_dwarf"

    def _analyze_nuclear_processes(self, mass: float, phase: str) -> Dict[str, str]:
        """Analyze nuclear burning processes."""
        if phase == "main_sequence":
            return {"primary": "Hydrogen burning", "location": "Core", "products": "Helium"}
        elif phase in ["red_giant", "horizontal_branch"]:
            return {"primary": "Helium burning", "location": "Core/Shell", "products": "Carbon, Oxygen"}
        elif phase == "asymptotic_giant" and mass > 2.0:
            return {"primary": "Carbon burning", "location": "Shell", "products": "Neon, Magnesium"}
        else:
            return {"primary": "No active burning", "location": "None", "products": "None"}

    def _analyze_binary_effects(self, primary: float, secondary: float, phase: str) -> Dict[str, Any]:
        """Analyze binary system effects."""
        return {
            "mass_transfer": "Active" if phase in ["red_giant", "asymptotic_giant"] else "Inactive",
            "orbital_evolution": "Shrinking" if primary > 1.0 else "Stable",
            "tidal_effects": "Significant" if abs(primary - secondary) < 0.5 else "Minimal"
        }

    def _predict_future_evolution(self, mass: float, current_age: float, total_lifetime: float) -> Dict[str, Any]:
        """Predict future stellar evolution."""
        remaining_time = max(0, total_lifetime - current_age)
        return {
            "remaining_main_sequence": f"{max(0, total_lifetime * 0.9 - current_age):.2f} Gyr",
            "time_to_red_giant": f"{max(0, total_lifetime * 0.95 - current_age):.2f} Gyr",
            "final_mass": f"{max(0.5, mass * 0.6):.1f} solar masses"
        }

    def _predict_binary_outcomes(self, primary: float, secondary: float, phases: List) -> Dict[str, str]:
        """Predict binary system final outcomes."""
        if primary > 8.0:
            return {"outcome": "Double compact object", "merger_time": "1-10 Gyr", "gravitational_waves": "Yes"}
        else:
            return {"outcome": "White dwarf binary", "merger_time": ">10 Gyr", "gravitational_waves": "Weak"}

    def _generate_observational_predictions(self, galaxy_type: str, age: float) -> Dict[str, str]:
        """Generate observational predictions for galaxy evolution."""
        return {
            "color": "Red" if galaxy_type == "elliptical" else "Blue-red",
            "star_formation_indicators": "Weak" if age > 8 else "Strong",
            "metallicity_signatures": "Solar" if age > 5 else "Sub-solar"
        }

    def _predict_observational_consequences(self, age: float, prediction: float) -> Dict[str, str]:
        """Predict observational consequences of stellar population evolution."""
        return {
            "integrated_color": "Redder with time",
            "luminosity_function": "Fewer bright stars",
            "spectral_features": "Stronger metal lines",
            "white_dwarf_luminosity_function": "Well-developed cooling sequence"
        }

    def _compare_with_observations(self, age: float) -> Dict[str, str]:
        """Compare predictions with observations."""
        return {
            "globular_clusters": "Consistent with old populations",
            "galactic_disk": "Matches intermediate-age populations",
            "stellar_remnants": "Agrees with local white dwarf census"
        }
