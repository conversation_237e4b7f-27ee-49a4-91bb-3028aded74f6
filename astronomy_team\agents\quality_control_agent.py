from typing import Dict, Any, List, Optional
from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.common.cot import MCoTTools
from ..config import MODEL_CONFIG
import logging
import json

logger = logging.getLogger(__name__)

class QualityControlAgent(Agent):
    """Agent kiểm định chất lượng và đánh giá kết quả tìm kiếm."""
    
    def __init__(self, **kwargs):
        super().__init__(
            model=Ollama(
                id=MODEL_CONFIG["base_model"],
            ),
            name="Quality Control Agent",
            description="""
            Bạn là một chuyên gia đánh giá chất lượng thông tin khoa học.
            Nhiệm vụ của bạn là kiểm tra và đánh giá độ chính xác, độ tin cậy 
            và mức độ phù hợp của các tài liệu được cung cấp.
            """,
            **kwargs
        )
        
        # Khởi tạo công cụ MCoT
        self.mcot_tools = MCoTTools(
            add_instructions=True,
            add_few_shot=True,
            max_iterations=3
        )
        self.tools = [self.mcot_tools]
        
        logger.info("Khởi tạo Quality Control Agent thành công")
    
    async def arun(self, message: dict, **kwargs) -> dict:
        """Đánh giá chất lượng kết quả tìm kiếm."""
        query = message.get("query")
        search_results = message.get("search_results")
        analysis = message.get("analysis")
        logger.info("Bắt đầu đánh giá chất lượng kết quả")
        
        # Chuẩn bị dữ liệu cho đánh giá
        evaluation_data = {
            "query": query,
            "analysis": analysis or {},
            "search_results": search_results,
            "evaluation_metrics": {
                "relevance": 0.0,
                "reliability": 0.0,
                "recency": 0.0,
                "completeness": 0.0
            },
            "suggestions": []
        }
        
        try:
            # Tạo prompt đánh giá
            prompt = self._create_evaluation_prompt(query, search_results, analysis)
            
            # Sử dụng MCoT để đánh giá
            mcot_result = await self.mcot_reflect(prompt)
            
            # Phân tích kết quả MCoT
            evaluation = self._parse_mcot_result(mcot_result)
            
            # Cập nhật đánh giá
            evaluation_data["evaluation_metrics"].update(evaluation.get("metrics", {}))
            evaluation_data["suggestions"] = evaluation.get("suggestions", [])
            evaluation_data["overall_quality"] = self._calculate_overall_quality(
                evaluation_data["evaluation_metrics"]
            )
            
            logger.info(f"Đánh giá hoàn tất. Điểm tổng thể: {evaluation_data['overall_quality']:.1f}/10")
            
            return {
                "status": "success",
                "evaluation": evaluation_data,
                "is_acceptable": evaluation_data["overall_quality"] >= 6.0
            }
            
        except Exception as e:
            logger.error(f"Lỗi khi đánh giá chất lượng: {str(e)}")
            return {
                "status": "error",
                "message": f"Lỗi khi đánh giá chất lượng: {str(e)}",
                "evaluation": evaluation_data,
                "is_acceptable": False
            }
    
    async def run(self, message: dict, **kwargs) -> dict:
        return await self.arun(message, **kwargs)

    async def mcot_reflect(self, prompt: str) -> str:
        """Sử dụng MCoT để phản ánh và đánh giá."""
        try:
            # Gọi MCoT tools để phản ánh
            mcot_response = await self.mcot_tools.mcot_reflect(
                query=prompt,
                agent=self,
                lcot_output=None  # Không sử dụng LCoT output trong trường hợp này
            )
            
            # Parse kết quả
            if isinstance(mcot_response, str):
                try:
                    mcot_data = json.loads(mcot_response)
                    return mcot_data.get("output", mcot_response)
                except json.JSONDecodeError:
                    return mcot_response
            return str(mcot_response)
            
        except Exception as e:
            logger.error(f"Lỗi khi gọi MCoT: {str(e)}")
            return f"Không thể thực hiện đánh giá MCoT: {str(e)}"
    
    def _create_evaluation_prompt(self, query: str, search_results: Dict[str, Any], analysis: Dict[str, Any] = None) -> str:
        """Tạo prompt cho việc đánh giá."""
        prompt_parts = [
            "# ĐÁNH GIÁ CHẤT LƯỢNG KẾT QUẢ TÌM KIẾM",
            f"## Câu hỏi gốc: {query}\n",
            "## Thông tin phân tích:",
            json.dumps(analysis, indent=2, ensure_ascii=False) if analysis else "Không có thông tin phân tích",
            "\n## Kết quả tìm kiếm:",
            json.dumps(search_results, indent=2, ensure_ascii=False),
            "\n## Yêu cầu đánh giá:",
            "1. Đánh giá mức độ phù hợp của kết quả với câu hỏi (1-10)",
            "2. Đánh giá độ tin cậy của nguồn (1-10)",
            "3. Đánh giá mức độ cập nhật của thông tin (1-10)",
            "4. Đánh giá độ đầy đủ của thông tin (1-10)",
            "5. Đề xuất cải thiện (nếu có)",
            "\nPhản hồi dưới dạng JSON với cấu trúc:",
            "{\n                \"evaluation\": {\n                    \"relevance\": điểm_số,\n                    \"reliability\": điểm_số,\n                    \"recency\": điểm_số,\n                    \"completeness\": điểm_số\n                },\n                \"suggestions\": [\"đề xuất 1\", \"đề xuất 2\"]\n            }"
        ]
        
        return "\n".join(prompt_parts)
    
    def _parse_mcot_result(self, mcot_output: str) -> Dict[str, Any]:
        """Phân tích kết quả từ MCoT."""
        try:
            # Tìm và trích xuất JSON từ output
            json_str = mcot_output[mcot_output.find('{'):mcot_output.rfind('}')+1]
            return json.loads(json_str)
        except (json.JSONDecodeError, ValueError) as e:
            logger.warning(f"Không thể parse MCoT output: {str(e)}")
            # Trả về giá trị mặc định nếu không parse được
            return {
                "metrics": {
                    "relevance": 5.0,
                    "reliability": 5.0,
                    "recency": 5.0,
                    "completeness": 5.0
                },
                "suggestions": ["Không có đề xuất cụ thể"]
            }
    
    def _calculate_overall_quality(self, metrics: Dict[str, float]) -> float:
        """Tính điểm tổng thể dựa trên các chỉ số đánh giá."""
        weights = {
            "relevance": 0.4,
            "reliability": 0.3,
            "recency": 0.2,
            "completeness": 0.1
        }
        
        total_score = 0.0
        total_weight = 0.0
        
        for metric, weight in weights.items():
            if metric in metrics:
                total_score += metrics[metric] * weight
                total_weight += weight
        
        # Chuyển về thang điểm 10
        return (total_score / total_weight) if total_weight > 0 else 0.0
