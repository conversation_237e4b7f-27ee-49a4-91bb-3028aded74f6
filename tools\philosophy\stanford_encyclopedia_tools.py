from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json
import re
import aiohttp
import asyncio
from urllib.parse import urljoin, quote
from bs4 import BeautifulSoup

from agno.tools import Toolkit
from agno.utils.log import logger

# Các hằng số
SEP_BASE_URL = "https://plato.stanford.edu"
SEP_SEARCH_URL = f"{SEP_BASE_URL}/search/searcher.py"
CACHE_EXPIRY_DAYS = 7

@dataclass
class SEPArticle:
    """Lớp đại diện cho một bài viết trong SEP"""
    title: str
    url: str
    authors: List[str]
    abstract: str = ""
    categories: List[str] = None
    last_updated: str = ""
    related_entries: List[Dict[str, str]] = None

class StanfordEncyclopediaTool(Toolkit):
    """
    Công cụ tìm kiếm thông tin triết học từ Stanford Encyclopedia of Philosophy (SEP)
    """

    def __init__(self):
        super().__init__(
            name="Công cụ SEP Triết học",
            description="""
            Công cụ tìm kiếm và phân tích thông tin triết học từ 
            Stanford Encyclopedia of Philosophy (SEP). Hỗ trợ tìm kiếm 
            bài viết học thuật, triết gia, khái niệm và lý thuyết triết học.
            """,
            tools=[
                self.search_sep_articles,
                self.get_article_details,
                self.get_philosopher_info,
                self.get_philosophical_concepts,
                self.get_related_articles
            ]
        )
        self.session = None
        self.cache = {}
        self._load_cache()

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def _load_cache(self) -> None:
        """Tải dữ liệu cache từ file"""
        try:
            with open(".sep_cache.json", 'r', encoding='utf-8') as f:
                self.cache = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            self.cache = {}

    def _save_cache(self) -> None:
        """Lưu dữ liệu cache vào file"""
        with open(".sep_cache.json", 'w', encoding='utf-8') as f:
            json.dump(self.cache, f, ensure_ascii=False, indent=2)

    def _get_cache(self, key: str) -> Any:
        """Lấy dữ liệu từ cache"""
        cached = self.cache.get(key)
        if cached and datetime.now().timestamp() < cached.get('expires', 0):
            return cached['data']
        return None

    def _set_cache(self, key: str, data: Any, ttl: int = 604800) -> None:
        """Lưu dữ liệu vào cache (mặc định 7 ngày)"""
        self.cache[key] = {
            'data': data,
            'expires': datetime.now().timestamp() + ttl
        }
        self._save_cache()

    async def _make_request(self, url: str, use_cache: bool = True) -> str:
        """Gửi yêu cầu HTTP với cơ chế cache"""
        cache_key = f"req_{url}"
        
        # Kiểm tra cache
        if use_cache:
            cached = self._get_cache(cache_key)
            if cached is not None:
                return cached
        
        # Gửi yêu cầu mới
        try:
            async with self.session.get(url) as response:
                response.raise_for_status()
                content = await response.text()
                
                # Lưu vào cache
                self._set_cache(cache_key, content)
                return content
                
        except Exception as e:
            logger.error(f"Lỗi khi gửi yêu cầu đến {url}: {str(e)}")
            raise

    async def search_sep_articles(
        self, 
        query: str, 
        limit: int = 5,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Tìm kiếm bài viết trong SEP
        
        Parameters:
        - query: Từ khóa tìm kiếm
        - limit: Số lượng kết quả tối đa
        - use_cache: Sử dụng cache hay không
        
        Returns:
        - Dict chứa kết quả tìm kiếm
        """
        logger.info(f"Tìm kiếm SEP: {query}")
        
        try:
            # Tạo URL tìm kiếm
            search_url = f"{SEP_SEARCH_URL}?query={quote(query)}"
            
            # Gửi yêu cầu tìm kiếm
            content = await self._make_request(search_url, use_cache)
            soup = BeautifulSoup(content, 'html.parser')
            
            # Phân tích kết quả
            results = []
            result_items = soup.select('div#main-text ul li')
            
            for item in result_items[:limit]:
                link = item.find('a')
                if not link:
                    continue
                    
                title = link.get_text(strip=True)
                url = urljoin(SEP_BASE_URL, link['href'])
                
                # Lấy tác giả nếu có
                author_span = item.find('span', class_='author')
                authors = [a.get_text(strip=True) for a in author_span.find_all('a')] if author_span else []
                
                results.append({
                    "title": title,
                    "url": url,
                    "authors": authors
                })
            
            return {
                "status": "success",
                "query": query,
                "results_count": len(results),
                "results": results,
                "search_url": search_url,
                "suggested_searches": self._get_search_suggestions()
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi tìm kiếm SEP: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "query": query
            }
    
    async def get_article_details(
        self, 
        article_url: str,
        include_related: bool = True
    ) -> Dict[str, Any]:
        """
        Lấy thông tin chi tiết của một bài viết SEP
        
        Parameters:
        - article_url: URL của bài viết
        - include_related: Có bao gồm các bài viết liên quan không
        """
        try:
            # Kiểm tra URL hợp lệ
            if not article_url.startswith(SEP_BASE_URL):
                return {
                    "status": "error",
                    "message": f"URL không hợp lệ. URL phải bắt đầu với: {SEP_BASE_URL}",
                    "article_url": article_url
                }
            
            # Lấy nội dung bài viết
            content = await self._make_request(article_url)
            soup = BeautifulSoup(content, 'html.parser')
            
            # Trích xuất thông tin cơ bản
            title = soup.select_one('h1.entry-title')
            title = title.get_text(strip=True) if title else ""
            
            authors = []
            author_elements = soup.select('a[rel*="author"]')
            for author in author_elements:
                authors.append({
                    "name": author.get_text(strip=True),
                    "url": urljoin(SEP_BASE_URL, author['href'])
                })
            
            # Trích xuất tóm tắt
            abstract = ""
            abstract_div = soup.select_one('div#preamble')
            if abstract_div:
                abstract = "\n\n".join([p.get_text() for p in abstract_div.find_all('p')])
            
            # Trích xuất mục lục
            toc = []
            toc_div = soup.select_one('div#toc')
            if toc_div:
                for li in toc_div.select('li'):
                    link = li.find('a')
                    if link:
                        toc.append({
                            "title": link.get_text(strip=True),
                            "id": link.get('href', '').lstrip('#')
                        })
            
            # Lấy các mục liên quan
            related_articles = []
            if include_related:
                related_div = soup.select_one('div.related-entries')
                if related_div:
                    for link in related_div.select('a'):
                        related_articles.append({
                            "title": link.get_text(strip=True),
                            "url": urljoin(SEP_BASE_URL, link['href'])
                        })
            
            # Tạo đối tượng kết quả
            article = {
                "title": title,
                "url": article_url,
                "authors": authors,
                "abstract": abstract,
                "sections": toc,
                "related_articles": related_articles,
                "last_updated": self._extract_last_updated(soup),
                "citation": self._generate_citation(title, authors, article_url)
            }
            
            return {
                "status": "success",
                "article": article
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy thông tin bài viết: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "article_url": article_url
            }
    
    def _extract_last_updated(self, soup) -> str:
        """Trích xuất ngày cập nhật cuối cùng"""
        try:
            footer = soup.select_one('div#footer')
            if footer:
                text = footer.get_text()
                # Tìm chuỗi ngày tháng trong footer
                match = re.search(r'(?:Last (?:updated|modified) on|revised on)\s+(.+?)(?:\.|$)', text, re.IGNORECASE)
                if match:
                    return match.group(1).strip()
        except Exception:
            pass
        return ""
    
    def _generate_citation(self, title: str, authors: List[Dict], url: str) -> Dict[str, str]:
        """Tạo trích dẫn theo nhiều định dạng"""
        if not authors:
            return {}
            
        author_names = ", ".join([a["name"] for a in authors])
        year = datetime.now().year
        
        return {
            "apa": f"{author_names} ({year}). {title}. In E. N. Zalta (Ed.), The Stanford Encyclopedia of Philosophy. {url}",
            "mla": f"\"{title}.\" Stanford Encyclopedia of Philosophy, {year}, {url}.",
            "chicago": f"{author_names}. \"{title}.\" Stanford Encyclopedia of Philosophy. Last modified {datetime.now().strftime('%B %d, %Y')}. {url}."
        }
    
    async def get_philosopher_info(
        self, 
        philosopher_name: str,
        detailed: bool = False
    ) -> Dict[str, Any]:
        """
        Lấy thông tin về một triết gia từ SEP
        
        Parameters:
        - philosopher_name: Tên triết gia
        - detailed: Có lấy thông tin chi tiết hay không
        """
        try:
            # Tìm kiếm bài viết về triết gia
            search_result = await self.search_sep_articles(philosopher_name)
            
            if search_result["results_count"] == 0:
                return {
                    "status": "not_found",
                    "message": f"Không tìm thấy thông tin về triết gia: {philosopher_name}",
                    "suggestions": self._get_philosopher_suggestions()
                }
            
            # Lấy thông tin chi tiết nếu cần
            result = search_result["results"][0]
            if detailed:
                article_details = await self.get_article_details(result["url"])
                if article_details["status"] == "success":
                    result.update(article_details["article"])
            
            return {
                "status": "success",
                "philosopher": philosopher_name,
                "data": result,
                "related_philosophers": self._get_related_philosophers(philosopher_name)
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy thông tin triết gia: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "philosopher": philosopher_name
            }
    
    async def get_philosophical_concepts(
        self, 
        category: str = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        Lấy danh sách các khái niệm triết học theo danh mục
        
        Parameters:
        - category: Danh mục khái niệm (tùy chọn)
        - limit: Số lượng kết quả tối đa
        """
        try:
            # Đây là dữ liệu mẫu, có thể thay thế bằng dữ liệu thực từ SEP
            concepts = {
                "ethics": [
                    "Utilitarianism", "Deontological Ethics", "Virtue Ethics",
                    "Justice", "Moral Responsibility", "Applied Ethics"
                ],
                "metaphysics": [
                    "Causation", "Free Will", "Personal Identity",
                    "Time", "Universals and Particulars", "Possible Worlds"
                ],
                "epistemology": [
                    "Knowledge", "Skepticism", "Theories of Truth",
                    "Rationalism vs. Empiricism", "Social Epistemology"
                ],
                "logic": [
                    "Classical Logic", "Modal Logic", "Inductive Logic",
                    "Paradoxes", "Logical Consequence"
                ],
                "philosophy_of_mind": [
                    "Consciousness", "Qualia", "Intentionality",
                    "The Mind-Body Problem", "Artificial Intelligence"
                ]
            }
            
            if category and category.lower() in concepts:
                results = concepts[category.lower()][:limit]
            else:
                # Trả về tất cả nếu không có danh mục cụ thể
                results = [item for sublist in concepts.values() for item in sublist][:limit]
            
            return {
                "status": "success",
                "category": category or "all",
                "concepts": results,
                "available_categories": list(concepts.keys())
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy danh sách khái niệm: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "category": category
            }
    
    async def get_related_articles(
        self, 
        article_url: str,
        max_related: int = 5
    ) -> Dict[str, Any]:
        """
        Lấy các bài viết liên quan từ SEP
        
        Parameters:
        - article_url: URL của bài viết gốc
        - max_related: Số lượng bài viết liên quan tối đa
        """
        try:
            # Lấy thông tin chi tiết bài viết
            article_details = await self.get_article_details(article_url, include_related=True)
            
            if article_details["status"] != "success":
                return {
                    "status": "error",
                    "message": "Không thể lấy thông tin bài viết",
                    "article_url": article_url
                }
            
            # Lấy danh sách bài viết liên quan
            related_articles = article_details["article"].get("related_articles", [])[:max_related]
            
            # Lấy thông tin chi tiết của từng bài viết liên quan
            detailed_related = []
            for article in related_articles:
                try:
                    details = await self.get_article_details(article["url"], include_related=False)
                    if details["status"] == "success":
                        detailed_related.append(details["article"])
                except Exception:
                    pass
            
            return {
                "status": "success",
                "article_url": article_url,
                "related_articles": detailed_related,
                "related_count": len(detailed_related)
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy bài viết liên quan: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "article_url": article_url
            }
        