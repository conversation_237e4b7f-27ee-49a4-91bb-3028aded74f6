#!/usr/bin/env python3
"""
Demo script cho Cosmology Facebook Team
Thử nghiệm workflow với một số câu hỏi mẫu về vũ trụ học
"""

import sys
import logging

# Add current directory to path
sys.path.append('.')

# Setup logging
logging.basicConfig(level=logging.INFO)

def demo_cosmology_workflow():
    """Demo workflow với các câu hỏi mẫu"""
    print("🌌 Cosmology Facebook Team Demo")
    print("="*50)

    try:
        from cosmology_fb import CosmologyFbWorkflow

        # Tạo workflow
        print("📝 Initializing Cosmology Workflow...")
        workflow = CosmologyFbWorkflow()
        print("✅ Workflow initialized successfully!")

        # Danh sách câu hỏi demo
        demo_questions = [
            "What is dark matter and why is it important in cosmology?",
            "Explain the cosmic microwave background radiation",
            "What are gravitational waves and how do they help us understand the universe?",
            "Tell me about the Big Bang theory and recent evidence",
            "What is dark energy and how does it affect universe expansion?"
        ]

        print(f"\n🎯 Available Demo Questions:")
        for i, question in enumerate(demo_questions, 1):
            print(f"{i}. {question}")

        print(f"\n💡 You can also ask your own cosmology questions!")
        print(f"📝 Type 'demo X' to run demo question X (1-{len(demo_questions)})")
        print(f"🚪 Type 'exit' to quit")
        print("="*50)

        while True:
            try:
                user_input = input("\n🌌 Cosmology Query: ").strip()

                if user_input.lower() == "exit":
                    print("👋 Goodbye! Thanks for exploring cosmology!")
                    break

                if user_input.lower().startswith("demo "):
                    try:
                        demo_num = int(user_input.split()[1])
                        if 1 <= demo_num <= len(demo_questions):
                            user_input = demo_questions[demo_num - 1]
                            print(f"🎯 Running Demo Question {demo_num}: {user_input}")
                        else:
                            print(f"❌ Invalid demo number. Please use 1-{len(demo_questions)}")
                            continue
                    except (IndexError, ValueError):
                        print(f"❌ Invalid format. Use 'demo X' where X is 1-{len(demo_questions)}")
                        continue

                if not user_input:
                    print("❌ Please enter a question or 'exit' to quit")
                    continue

                print(f"\n🔍 Processing: {user_input}")
                print("⏳ This may take a moment as the team researches and writes...")
                print("-" * 50)

                # Run workflow
                try:
                    result = workflow.process_cosmology_query(user_input)

                    # Handle result - it's a string
                    print("📄 Team Response:")
                    print(result)

                except Exception as e:
                    print(f"❌ Error during workflow execution: {e}")
                    print("💡 This might be due to API limitations or network issues.")
                    print("🔄 You can try again or ask a different question.")

                print("-" * 50)
                print("✅ Query completed!")

            except KeyboardInterrupt:
                print("\n\n👋 Interrupted by user. Goodbye!")
                break
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
                print("🔄 You can try again or ask a different question.")

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed and cosmology_fb.py is available")
        return False
    except Exception as e:
        print(f"❌ Initialization error: {e}")
        print("💡 Check if all tools and dependencies are properly configured")
        return False

    return True

def show_team_info():
    """Hiển thị thông tin về team"""
    print("\n" + "="*60)
    print("🌌 COSMOLOGY FACEBOOK TEAM INFORMATION")
    print("="*60)
    print("""
🤖 TEAM MEMBERS:
├── 🔍 Cosmology Sub Question Agent - Generates research sub-questions
├── 📚 ArXiv Cosmology Agent - Searches ArXiv for recent papers
├── ⚛️  CERN Open Data Agent - Accesses particle physics data
├── 🔬 INSPIRE-HEP Agent - Searches high-energy physics literature
├── 🚀 NASA ADS Physics Agent - Searches NASA ADS database
├── 📖 Wikipedia Physics Agent - Provides accessible explanations
├── 🧮 Cosmic Evolution Calculator Agent - Performs cosmological calculations
└── ✍️  Cosmology Writer Agent - Creates engaging Facebook posts

🎯 CAPABILITIES:
• Research latest cosmology developments
• Access multiple scientific databases
• Perform cosmological calculations
• Create engaging, accessible content
• Collaborate to provide comprehensive answers

🌟 TOPICS COVERED:
• Dark Matter & Dark Energy
• Cosmic Microwave Background
• Big Bang Theory & Cosmic Inflation
• Gravitational Waves
• Galaxy Formation & Evolution
• Black Holes & Neutron Stars
• Quantum Cosmology
• Observable Universe & Beyond
    """)
    print("="*60)

def main():
    """Main function"""
    show_team_info()

    print("\n🚀 Starting Cosmology Facebook Team Demo...")
    success = demo_cosmology_workflow()

    if success:
        print("\n🎉 Demo completed successfully!")
    else:
        print("\n❌ Demo encountered issues.")
        return 1

    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted by user. Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)
