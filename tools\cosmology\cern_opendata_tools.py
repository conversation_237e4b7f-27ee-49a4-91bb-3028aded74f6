"""
Công cụ tìm kiếm CERN Open Data cho vật lý hạt.
"""

import json
import time
import requests
import logging
from typing import List, Optional, Dict, Any

from agno.tools import Toolkit
from agno.utils.log import log_debug, logger

class CERNOpenDataTools(Toolkit):
    """Công cụ tìm kiếm CERN Open Data cho vật lý hạt."""
    
    def __init__(self, search_cern_data: bool = True, timeout: int = 10, max_retries: int = 3, **kwargs):
        """
        Khởi tạo công cụ tìm kiếm CERN Open Data.
        
        Args:
            search_cern_data: C<PERSON> đăng ký phương thức tìm kiếm hay không
            timeout: Thời gian timeout cho request (giây)
            max_retries: Số lần thử lại tối đa
            **kwargs: <PERSON><PERSON><PERSON> tham số khác
        """
        super().__init__(name="cern_opendata_tools", **kwargs)
        self.base_url = "http://opendata.cern.ch/api/records"
        self.timeout = timeout
        self.max_retries = max_retries
        
        # Khởi tạo cache đơn giản
        self.cache = {}
        
        if search_cern_data:
            self.register(self.search_cern_opendata)
    
    def search_cern_opendata(self, query: str, max_results: int = 5, experiment: Optional[str] = None) -> str:
        """
        Tìm kiếm dữ liệu trên CERN Open Data.
        
        Args:
            query: Từ khóa tìm kiếm
            max_results: Số lượng kết quả tối đa
            experiment: Tên thí nghiệm (ví dụ: "CMS", "ATLAS", "LHCb", "ALICE")
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        log_debug(f"Searching CERN Open Data for: {query}")
        
        # Kiểm tra cache
        cache_key = f"{query}_{max_results}_{experiment}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for: {query}")
            return self.cache[cache_key]
        
        # Tạo truy vấn
        q = query
        if experiment:
            q = f"{q} AND experiment:{experiment}"
        
        # Tạo tham số truy vấn
        params = {
            "q": q,
            "size": max_results,
            "sort": "mostrecent"
        }
        
        # Fallback data nếu API không hoạt động
        fallback_data = [
            {
                "title": f"Fallback data for query: {query}",
                "experiment": experiment or "Unknown",
                "description": "This is fallback data due to API unavailability",
                "date_created": "",
                "date_published": "",
                "record_id": "fallback_id",
                "doi": None,
                "url": "https://opendata.cern.ch/",
                "type": "Dataset",
                "keywords": ["fallback"],
                "authors": ["API Unavailable"]
            }
        ]
        
        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"CERN Open Data attempt {attempt+1}/{self.max_retries}")
                response = requests.get(
                    self.base_url,
                    params=params,
                    timeout=self.timeout
                )
                response.raise_for_status()
                
                # Phân tích kết quả
                data = response.json()
                results = self._parse_cern_response(data)
                
                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                return result_json
                
            except requests.exceptions.Timeout:
                logger.warning(f"CERN Open Data timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)  # Chờ 1 giây trước khi thử lại
            except requests.exceptions.RequestException as e:
                logger.warning(f"CERN Open Data request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"CERN Open Data unexpected error: {e}")
                break
        
        # Trả về dữ liệu fallback nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to search CERN Open Data failed for query: {query}")
        logger.info(f"Returning fallback data for query: {query}")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json  # Cache fallback data
        return fallback_json
    
    def _parse_cern_response(self, response_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Phân tích kết quả từ CERN Open Data API.
        
        Args:
            response_data: Dữ liệu phản hồi từ CERN Open Data API
            
        Returns:
            Danh sách các bản ghi
        """
        try:
            results = []
            
            # Lặp qua các hit
            for hit in response_data.get("hits", {}).get("hits", []):
                metadata = hit.get("metadata", {})
                
                # Lấy tiêu đề
                title = metadata.get("title", "")
                
                # Lấy thí nghiệm
                experiment = ""
                if "experiment" in metadata:
                    experiment = metadata["experiment"]
                
                # Lấy mô tả
                description = ""
                if "description" in metadata:
                    if isinstance(metadata["description"], dict):
                        description = metadata["description"].get("value", "")
                    elif isinstance(metadata["description"], str):
                        description = metadata["description"]
                
                # Lấy ngày tạo và xuất bản
                date_created = metadata.get("date_created", "")
                date_published = metadata.get("date_published", "")
                
                # Lấy ID bản ghi
                record_id = hit.get("id", "")
                
                # Lấy DOI
                doi = metadata.get("doi", "")
                
                # Lấy URL
                url = f"https://opendata.cern.ch/record/{record_id}" if record_id else ""
                
                # Lấy loại bản ghi
                record_type = metadata.get("type", {}).get("primary", "")
                
                # Lấy từ khóa
                keywords = []
                if "keywords" in metadata:
                    if isinstance(metadata["keywords"], list):
                        for keyword in metadata["keywords"]:
                            if isinstance(keyword, dict) and "value" in keyword:
                                keywords.append(keyword["value"])
                            elif isinstance(keyword, str):
                                keywords.append(keyword)
                
                # Lấy tác giả
                authors = []
                if "authors" in metadata:
                    for author in metadata["authors"][:3]:  # Giới hạn số lượng tác giả
                        if isinstance(author, dict) and "name" in author:
                            authors.append(author["name"])
                        elif isinstance(author, str):
                            authors.append(author)
                
                # Tạo kết quả
                record = {
                    "title": title,
                    "experiment": experiment,
                    "description": self._truncate_text(description, 500),  # Giới hạn độ dài mô tả
                    "date_created": date_created,
                    "date_published": date_published,
                    "record_id": record_id,
                    "doi": doi,
                    "url": url,
                    "type": record_type,
                    "keywords": keywords[:5],  # Giới hạn số lượng từ khóa
                    "authors": authors
                }
                
                results.append(record)
            
            return results
        except Exception as e:
            logger.error(f"Error parsing CERN Open Data response: {e}")
            return []
    
    def _truncate_text(self, text: str, max_length: int = 500) -> str:
        """Giới hạn độ dài văn bản."""
        if not text or len(text) <= max_length:
            return text
        return text[:max_length] + "..."


if __name__ == "__main__":
    # Test công cụ
    tools = CERNOpenDataTools()
    result = tools.search_cern_opendata("Higgs boson", max_results=3, experiment="CMS")
    print(result)