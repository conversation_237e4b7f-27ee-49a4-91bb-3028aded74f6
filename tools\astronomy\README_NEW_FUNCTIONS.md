# Astronomy Tools - New "Get Top New" Functions

Tài liệu này mô tả các hàm mới được thêm vào astronomy tools để lấy nội dung mới nhất từ các thư viện thiên văn học.

## Tổng quan

Đã thêm các hàm `get_top_new` cho 4 trong 5 tool astronomy:

1. **NASA ADS Tools** - `get_top_new_papers()`
2. **Wikipedia Astronomy Tools** - `get_recent_astronomy_articles()`
3. **ESA Archives Tools** - `get_recent_observations()`
4. **ESDC Crawler Tools** - `get_recent_metadata()`
5. **Astronomy Search Toolkit** - 4 hàm tạo từ khóa mới

**Lưu ý:** SIMBAD Tools không có hàm này vì đây là cơ sở dữ liệu đối tượng thiên thể, không có khái niệm "bài mới".

## Chi tiết các hàm

### 1. NASA ADS Tools

#### `get_top_new_papers(limit=10, days_back=30, category=None)`

**<PERSON><PERSON><PERSON> đích:** Lấy các bài báo khoa học mới nhất từ NASA ADS.

**Tham số:**
- `limit` (int): Số lượng bài báo trả về (mặc định: 10)
- `days_back` (int): Số ngày quay lại để tìm (mặc định: 30)
- `category` (str): Danh mục lọc tùy chọn (ví dụ: 'exoplanet', 'galaxy')

**Ví dụ sử dụng:**
```python
from tools.astronomy.nasa_ads_tools import NasaAdsTools

tool = NasaAdsTools()
result = tool.get_top_new_papers(limit=5, days_back=30, category="exoplanet")
```

### 2. Wikipedia Astronomy Tools

#### `get_recent_astronomy_articles(limit=10, days_back=30, language="en")`

**Mục đích:** Lấy các bài viết thiên văn học mới được tạo hoặc cập nhật gần đây.

**Tham số:**
- `limit` (int): Số lượng bài viết tối đa (mặc định: 10)
- `days_back` (int): Số ngày quay lại để tìm (mặc định: 30)
- `language` (str): Ngôn ngữ Wikipedia (mặc định: "en")

**Ví dụ sử dụng:**
```python
from tools.astronomy.wikipedia_astronomy_tools import WikipediaAstronomyTools

tool = WikipediaAstronomyTools()
result = tool.get_recent_astronomy_articles(limit=5, language="en")
```

### 3. ESA Archives Tools

#### `get_recent_observations(limit=10, days_back=30, instrument=None)`

**Mục đích:** Lấy các quan sát mới nhất từ ESA Hubble Science Archive.

**Tham số:**
- `limit` (int): Số lượng quan sát trả về (mặc định: 10)
- `days_back` (int): Số ngày quay lại để tìm (mặc định: 30)
- `instrument` (str): Lọc theo thiết bị tùy chọn (ví dụ: 'WFC3', 'ACS')

**Ví dụ sử dụng:**
```python
from tools.astronomy.esa_archives_tools import EsaArchivesTools

tool = EsaArchivesTools()
result = tool.get_recent_observations(limit=5, instrument="WFC3")
```

### 4. ESDC Crawler Tools

#### `get_recent_metadata(limit=10, days_back=30)`

**Mục đích:** Lấy các metadata mới nhất từ ESDC.

**Tham số:**
- `limit` (int): Số lượng metadata tối đa (mặc định: 10)
- `days_back` (int): Số ngày quay lại để tìm (mặc định: 30)

**Ví dụ sử dụng:**
```python
from tools.astronomy.esdc_crawler_tools import EsdcCrawlerTools

tool = EsdcCrawlerTools()
result = tool.get_recent_metadata(limit=5)
```

### 5. Astronomy Search Toolkit - Hàm tạo từ khóa mới

#### `generate_nasa_ads_new_keywords(category=None, days_back=30)`
Tạo từ khóa cho tìm bài báo mới trên NASA ADS.

#### `generate_wikipedia_new_keywords(language="en", days_back=30)`
Tạo từ khóa cho tìm bài viết thiên văn mới trên Wikipedia.

#### `generate_esa_new_keywords(instrument=None, days_back=30)`
Tạo từ khóa cho tìm quan sát mới từ ESA Archives.

#### `generate_esdc_new_keywords(days_back=30)`
Tạo từ khóa cho tìm metadata mới từ ESDC.

**Ví dụ sử dụng:**
```python
from tools.astronomy.astronomy_search_toolkit import AstronomySearchToolkits

toolkit = AstronomySearchToolkits()
keywords = toolkit.generate_nasa_ads_new_keywords("exoplanet", 30)
```

## Tính năng chung

### Fallback Data
Tất cả các hàm đều có cơ chế fallback data khi API không khả dụng:
- NASA ADS: Trả về mảng rỗng
- Wikipedia: Sử dụng tìm kiếm thông thường với từ khóa thiên văn
- ESA Archives: Trả về dữ liệu mẫu
- ESDC: Trả về dữ liệu mẫu

### Caching
Các tool đều sử dụng cache để tránh gọi API quá nhiều lần.

### Error Handling
Tất cả các hàm đều có xử lý lỗi và logging chi tiết.

## Test

Chạy file test để kiểm tra các hàm:
```bash
python test_new_functions.py
```

## Kết quả trả về

Tất cả các hàm đều trả về JSON string với cấu trúc tương tự:
- Thông tin bài viết/quan sát/metadata
- Metadata về tìm kiếm (số lượng, thời gian thực thi, tham số)
- Trạng thái và thông báo lỗi (nếu có)

## Cập nhật trong Astronomy Search Toolkit

Đã cập nhật:
- Thêm 4 hàm tạo từ khóa mới vào danh sách tools
- Cập nhật instructions để bao gồm hướng dẫn về "top new" functions
- Thêm test cases cho các hàm mới

Các hàm này giúp người dùng dễ dàng tìm kiếm và truy cập nội dung mới nhất từ các nguồn dữ liệu thiên văn học khác nhau.
