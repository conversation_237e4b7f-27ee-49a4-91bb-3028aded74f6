from textwrap   import dedent
import asyncio
from typing import Iterator

from agno.agent import Agent
from agno.models.ollama import Ollama
from agno.team import Team

from agents.astronomy.specialized_agents import simbad_agent
from agents.astronomy.specialized_agents import esa_archives_agent
from agents.astronomy.specialized_agents import esdc_crawler_agent
from agents.astronomy.specialized_agents import wikipedia_astronomy_agent
from agents.astronomy.specialized_agents import lcot_reasoning_agent    
from agents.astronomy.specialized_agents import cot_encyclopedia_agent
from agents.astronomy.specialized_agents import mcot_agent

class ResearchTeam(Team):
    async def aprint_response(self, query, **kwargs):
        # Nếu print_response là async thì await, nếu không thì gọi sync
        pr = super().print_response
        import asyncio
        if asyncio.iscoroutinefunction(pr):
            result = await pr(query)
        else:
            result = pr(query)
        if result is None or (isinstance(result, str) and result.strip() == ""):
            print("⚠️ Không có nội dung trả lời từ AI (None hoặc rỗng).")
        return result

    async def apredict_response(self, query, **kwargs):
        return await self.aprint_response(query, **kwargs)

research_team = ResearchTeam(
    name="Astronomy Research Team",
    mode="coordinate",
    model=Ollama(id="qwen3:4b"),
    members=[
        simbad_agent,
        esa_archives_agent,
        esdc_crawler_agent,
        wikipedia_astronomy_agent,
        lcot_reasoning_agent,
        cot_encyclopedia_agent,
        mcot_agent
    ],
    instructions=dedent("""
        You are a research team specializing in astronomy.
        Collaborate to analyze complex queries and provide detailed insights.
        Ensure scientific accuracy and clarity in your responses.
    """),
    show_members_responses=True,
    markdown=True,
)

# ==== Hàm chat_and_save cho run_astronomy.py ====
async def chat_and_save():
    print("Nhập câu hỏi về thiên văn học (hoặc 'exit' để thoát):")
    while True:
        user_input = input("\nBạn: ")
        if user_input.lower() in ["exit", "quit", "thoát"]:
            print("Tạm biệt!")
            break
        response = await research_team.apredict_response(user_input)
        print("\nTeam trả lời:\n")
        print(response)
        # Kiểm tra response/model trả về None hoặc rỗng
        if response is None or (isinstance(response, str) and response.strip() == ""):
            print("⚠️ Không có nội dung trả lời từ AI. Vui lòng thử lại hoặc kiểm tra model.")
            continue
        # Nếu muốn lưu lên Qdrant, có thể tích hợp tại đây



if __name__ == "__main__":
    asyncio.run(chat_and_save())