#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho các hàm get_top_new mới được thêm vào astronomy tools.
"""

import sys
import os
import json

# Thêm thư mục gốc vào Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_nasa_ads_new():
    """Test hàm get_top_new_papers của NASA ADS."""
    print("=== Testing NASA ADS New Papers ===")
    try:
        from tools.astronomy.nasa_ads_tools import NasaAdsTools
        
        tool = NasaAdsTools()
        result = tool.get_top_new_papers(limit=5, days_back=30, category="exoplanet")
        
        print("Result:")
        print(result)
        print()
        
        # Parse JSON để kiểm tra
        data = json.loads(result)
        print(f"Number of papers found: {len(data)}")
        
    except Exception as e:
        print(f"Error testing NASA ADS: {e}")
        print()

def test_wikipedia_new():
    """Test hàm get_recent_astronomy_articles của Wikipedia."""
    print("=== Testing Wikipedia Recent Articles ===")
    try:
        from tools.astronomy.wikipedia_astronomy_tools import WikipediaAstronomyTools
        
        tool = WikipediaAstronomyTools()
        result = tool.get_recent_astronomy_articles(limit=5, days_back=30, language="en")
        
        print("Result:")
        print(result)
        print()
        
        # Parse JSON để kiểm tra
        data = json.loads(result)
        if "recent_articles" in data:
            print(f"Number of articles found: {len(data['recent_articles'])}")
        
    except Exception as e:
        print(f"Error testing Wikipedia: {e}")
        print()

def test_esa_new():
    """Test hàm get_recent_observations của ESA."""
    print("=== Testing ESA Recent Observations ===")
    try:
        from tools.astronomy.esa_archives_tools import EsaArchivesTools
        
        tool = EsaArchivesTools()
        result = tool.get_recent_observations(limit=5, days_back=30, instrument="WFC3")
        
        print("Result:")
        print(result)
        print()
        
        # Parse JSON để kiểm tra
        data = json.loads(result)
        print(f"Number of observations found: {len(data)}")
        
    except Exception as e:
        print(f"Error testing ESA: {e}")
        print()

def test_esdc_new():
    """Test hàm get_recent_metadata của ESDC."""
    print("=== Testing ESDC Recent Metadata ===")
    try:
        from tools.astronomy.esdc_crawler_tools import EsdcCrawlerTools
        
        tool = EsdcCrawlerTools()
        result = tool.get_recent_metadata(limit=5, days_back=30)
        
        print("Result:")
        print(result)
        print()
        
        # Parse JSON để kiểm tra
        data = json.loads(result)
        print(f"Number of metadata entries found: {len(data)}")
        
    except Exception as e:
        print(f"Error testing ESDC: {e}")
        print()

def test_search_toolkit_new():
    """Test các hàm generate keywords mới của Search Toolkit."""
    print("=== Testing Search Toolkit New Keywords ===")
    try:
        from tools.astronomy.astronomy_search_toolkit import AstronomySearchToolkits
        
        toolkit = AstronomySearchToolkits()
        
        # Test NASA ADS new keywords
        print("--- NASA ADS New Keywords ---")
        result1 = toolkit.generate_nasa_ads_new_keywords("exoplanet", 30)
        print(result1)
        print()
        
        # Test Wikipedia new keywords
        print("--- Wikipedia New Keywords ---")
        result2 = toolkit.generate_wikipedia_new_keywords("en", 30)
        print(result2)
        print()
        
        # Test ESA new keywords
        print("--- ESA New Keywords ---")
        result3 = toolkit.generate_esa_new_keywords("WFC3", 30)
        print(result3)
        print()
        
        # Test ESDC new keywords
        print("--- ESDC New Keywords ---")
        result4 = toolkit.generate_esdc_new_keywords(30)
        print(result4)
        print()
        
    except Exception as e:
        print(f"Error testing Search Toolkit: {e}")
        print()

def main():
    """Chạy tất cả các test."""
    print("Testing New Astronomy Tools Functions")
    print("=" * 50)
    print()
    
    # Test từng tool
    test_nasa_ads_new()
    test_wikipedia_new()
    test_esa_new()
    test_esdc_new()
    test_search_toolkit_new()
    
    print("=" * 50)
    print("Testing completed!")

if __name__ == "__main__":
    main()
