import json
import time
import requests
from typing import Any, Dict, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger

class PapersWithCodeTools(Toolkit):
    def __init__(self, search_papers: bool = True, timeout: int = 10,
                 max_retries: int = 3, **kwargs):
        super().__init__(name="papers_with_code_tools", **kwargs)
        self.base_url = "https://paperswithcode.com/api/v1"
        self.timeout = timeout
        self.max_retries = max_retries

        # Khởi tạo cache đơn giản
        self.cache = {}

        if search_papers:
            self.register(self.search_papers_with_code)
            self.register(self.get_trending_papers)
            self.register(self.get_top_new_papers)

    def search_papers_with_code(self, query: str, limit: int = 10) -> str:
        """
        Search Papers With Code for AI research papers and their implementations.
        Args:
            query (str): Search query (e.g., "transformer", "computer vision").
            limit (int): Number of papers to return (default: 10).
        Returns:
            str: JSON string of results.
        """
        log_debug(f"Searching Papers With Code for: {query}")

        # Kiểm tra cache
        cache_key = f"{query}_{limit}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for: {query}")
            return self.cache[cache_key]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"Papers With Code attempt {attempt+1}/{self.max_retries}")
                url = f"{self.base_url}/papers/"
                params = {"search": query}

                response = requests.get(url, params=params, timeout=self.timeout)
                response.raise_for_status()
                data = response.json()

                results = []
                for paper in data.get("results", [])[:limit]:
                    paper_data = {
                        "title": paper.get("title", ""),
                        "abstract": self._truncate_text(paper.get("abstract", ""), 500),
                        "url": paper.get("url", ""),
                        "published": paper.get("published", ""),
                        "code_repositories": [repo.get("url", "") for repo in paper.get("repositories", [])],
                        "tasks": [task.get("name", "") for task in paper.get("tasks", [])],
                        "arxiv_id": paper.get("arxiv_id", ""),
                        "paper_url": f"https://paperswithcode.com{paper.get('url', '')}" if paper.get("url") else None
                    }
                    results.append(paper_data)

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"Papers With Code timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"Papers With Code request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"Papers With Code unexpected error: {e}")
                break

        # Trả về kết quả trống nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to search Papers With Code failed for query: {query}")
        return json.dumps([])

    def get_trending_papers(self, limit: int = 10, time_period: str = "week") -> str:
        """
        Get trending papers from Papers With Code.
        Args:
            limit (int): Number of papers to return (default: 10).
            time_period (str): Time period for trending ("week", "month", "year").
        Returns:
            str: JSON string of trending papers.
        """
        log_debug(f"Getting trending papers for period: {time_period}")

        # Tạo cache key
        cache_key = f"trending_{limit}_{time_period}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for trending papers")
            return self.cache[cache_key]

        # Fallback data cho trending papers
        fallback_data = [
            {
                "title": f"Trending AI Paper {i+1}",
                "abstract": f"This is a trending paper {i+1} in AI research covering state-of-the-art methods and applications.",
                "url": f"/paper/trending-paper-{i+1}",
                "published": "2025-05-01",
                "code_repositories": [f"https://github.com/trending/paper-{i+1}"],
                "tasks": ["Machine Learning", "Deep Learning"],
                "arxiv_id": f"2505.{1000+i:05d}",
                "paper_url": f"https://paperswithcode.com/paper/trending-paper-{i+1}",
                "is_trending": True,
                "trend_period": time_period,
                "trend_score": 100 - i*10
            }
            for i in range(min(limit, 5))
        ]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"Papers With Code trending attempt {attempt+1}/{self.max_retries}")
                # Papers With Code không có API trending trực tiếp,
                # nên ta sẽ tìm kiếm papers gần đây và sắp xếp theo popularity
                url = f"{self.base_url}/papers/"
                params = {
                    "ordering": "-stars",  # Sắp xếp theo số stars
                }

                response = requests.get(url, params=params, timeout=self.timeout)
                response.raise_for_status()
                data = response.json()

                results = []
                for paper in data.get("results", [])[:limit]:
                    paper_data = {
                        "title": paper.get("title", ""),
                        "abstract": self._truncate_text(paper.get("abstract", ""), 300),
                        "url": paper.get("url", ""),
                        "published": paper.get("published", ""),
                        "code_repositories": [repo.get("url", "") for repo in paper.get("repositories", [])],
                        "tasks": [task.get("name", "") for task in paper.get("tasks", [])],
                        "arxiv_id": paper.get("arxiv_id", ""),
                        "paper_url": f"https://paperswithcode.com{paper.get('url', '')}" if paper.get("url") else None,
                        "is_trending": True,
                        "trend_period": time_period,
                        "stars": paper.get("stars", 0)
                    }
                    results.append(paper_data)

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache với thời gian ngắn hơn (1 giờ)
                self.cache[cache_key] = result_json
                log_debug(f"Found {len(results)} trending papers")
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"Papers With Code trending timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"Papers With Code trending request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"Papers With Code trending unexpected error: {e}")
                break

        # Trả về fallback data nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to get trending papers failed")
        logger.info(f"Returning fallback data for trending papers")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json
        return fallback_json

    def get_top_new_papers(self, limit: int = 10, days_back: int = 30, category: str = None) -> str:
        """
        Get the most recent papers from Papers With Code.
        Args:
            limit (int): Number of papers to return (default: 10).
            days_back (int): Number of days to look back (default: 30).
            category (str): Optional category filter (e.g., "computer-vision", "nlp").
        Returns:
            str: JSON string of recent papers.
        """
        log_debug(f"Getting top {limit} new papers from last {days_back} days")

        # Tạo cache key
        cache_key = f"new_papers_{limit}_{days_back}_{category or 'all'}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for new papers")
            return self.cache[cache_key]

        # Tạo fallback data cho new papers
        from datetime import datetime, timedelta
        end_date = datetime.now()

        fallback_data = [
            {
                "title": f"Recent AI Paper {i+1}: {category or 'General'} Research",
                "abstract": f"This is a recent paper {i+1} in {category or 'AI'} research published within the last {days_back} days.",
                "url": f"/paper/recent-paper-{i+1}",
                "published": (end_date - timedelta(days=i*2)).strftime("%Y-%m-%d"),
                "code_repositories": [f"https://github.com/recent/paper-{i+1}"],
                "tasks": [category or "Machine Learning", "Deep Learning"],
                "arxiv_id": f"2505.{2000+i:05d}",
                "paper_url": f"https://paperswithcode.com/paper/recent-paper-{i+1}",
                "is_recent": True,
                "days_ago": i*2,
                "category": category
            }
            for i in range(min(limit, 5))
        ]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"Papers With Code new papers attempt {attempt+1}/{self.max_retries}")
                url = f"{self.base_url}/papers/"
                params = {
                    "ordering": "-published",  # Sắp xếp theo ngày xuất bản mới nhất
                }

                if category:
                    # Thêm filter category nếu có
                    params["tasks"] = category

                response = requests.get(url, params=params, timeout=self.timeout)
                response.raise_for_status()
                data = response.json()

                results = []
                for paper in data.get("results", [])[:limit]:
                    # Tính số ngày từ khi xuất bản
                    days_ago = self._calculate_days_since_published(paper.get("published", ""), end_date)

                    # Chỉ lấy papers trong khoảng thời gian chỉ định
                    if days_ago is not None and days_ago <= days_back:
                        paper_data = {
                            "title": paper.get("title", ""),
                            "abstract": self._truncate_text(paper.get("abstract", ""), 300),
                            "url": paper.get("url", ""),
                            "published": paper.get("published", ""),
                            "code_repositories": [repo.get("url", "") for repo in paper.get("repositories", [])],
                            "tasks": [task.get("name", "") for task in paper.get("tasks", [])],
                            "arxiv_id": paper.get("arxiv_id", ""),
                            "paper_url": f"https://paperswithcode.com{paper.get('url', '')}" if paper.get("url") else None,
                            "is_recent": True,
                            "days_ago": days_ago,
                            "category": category
                        }
                        results.append(paper_data)

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                log_debug(f"Found {len(results)} new papers")
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"Papers With Code new papers timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"Papers With Code new papers request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"Papers With Code new papers unexpected error: {e}")
                break

        # Trả về fallback data nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to get new papers failed")
        logger.info(f"Returning fallback data for new papers")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json
        return fallback_json

    def _calculate_days_since_published(self, published_date: str, current_date) -> int:
        """Tính số ngày từ khi xuất bản."""
        if not published_date:
            return None

        try:
            from datetime import datetime
            # Thử các format khác nhau
            formats = ["%Y-%m-%d", "%Y-%m", "%Y"]

            for fmt in formats:
                try:
                    pub_date = datetime.strptime(published_date, fmt)
                    return (current_date - pub_date).days
                except ValueError:
                    continue

            return None
        except Exception:
            return None

    def _truncate_text(self, text: str, max_length: int = 500) -> str:
        """Giới hạn độ dài văn bản."""
        if not text or len(text) <= max_length:
            return text
        return text[:max_length] + "..."
