from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json

class BandcampTool(Toolkit):
    """
    Công cụ tìm kiếm Bandcamp giúp khám phá nhạc độc lậ<PERSON>, nghệ sĩ và album từ nền tảng Bandcamp.

    Các từ khóa tìm kiếm gợi ý:
    - Tên nghệ sĩ, ban nhạc độc lập
    - Th<PERSON> loại nhạc (indie rock, electronic, folk, v.v.)
    - Album, EP, single mới phát hành
    - Nhạc theo tâm trạng (chill, energetic, ambient)
    - <PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> (Việt Nam, Nhật Bản, Hàn <PERSON>, v.v.)
    """
    def __init__(self):
        super().__init__(
            name="Công cụ tìm kiếm Bandcamp",
            tools=[self.search_bandcamp, self.get_top_new]
        )
        self.base_url = "https://bandcamp.com"
        self.search_types = ["all", "music", "artist", "album", "label", "fan", "tag"]

    def search_bandcamp(self, query: str, search_type: str = "all", limit: int = 5) -> str:
        """
        Tìm kiếm trên Bandcamp theo từ khóa và loại kết quả.

        Args:
            query: Từ khóa tìm kiếm (tên nghệ sĩ, album, thể loại...)
            search_type: Loại kết quả (all, music, artist, album, label, fan, tag)
            limit: Số lượng kết quả trả về (tối đa 20)

        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm Bandcamp với từ khóa: {query}, loại: {search_type}")

        if search_type not in self.search_types:
            search_type = "all"

        limit = max(1, min(limit, 20))  # Giới hạn trong khoảng 1-20

        try:
            # Giả lập kết quả tìm kiếm
            results = []

            if search_type in ["all", "music"]:
                results.extend([
                    {
                        "title": f"{query} - Single",
                        "artist": f"Nghệ sĩ {query}",
                        "type": "track",
                        "genre": ["Indie", "Alternative"],
                        "release_date": "2025-01-01",
                        "price": "$1.00 USD",
                        "url": f"https://{query.lower()}.bandcamp.com/track/{query.lower()}",
                        "image_url": f"https://f4.bcbits.com/img/00{1000+i}_16.jpg"
                    } for i in range(min(limit, 5))
                ])

            if search_type in ["all", "artist"] and len(results) < limit:
                results.extend([
                    {
                        "name": f"{query} {i+1}",
                        "type": "artist",
                        "location": "Hà Nội, Việt Nam",
                        "genre": ["Indie Rock", "Dream Pop"],
                        "url": f"https://{query.lower()}{i+1}.bandcamp.com",
                        "image_url": f"https://f4.bcbits.com/img/00{2000+i}_16.jpg"
                    } for i in range(min(limit - len(results), 3))
                ])

            if search_type in ["all", "album"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"{query} (Deluxe Edition)",
                        "artist": f"Nghệ sĩ {query}",
                        "type": "album",
                        "release_date": "2025-01-01",
                        "tracks": 10,
                        "price": "$7.00 USD",
                        "url": f"https://{query.lower()}.bandcamp.com/album/{query.lower()}-deluxe",
                        "image_url": f"https://f4.bcbits.com/img/00{3000+i}_16.jpg"
                    } for i in range(min(limit - len(results), 3))
                ])

            result = {
                "status": "success",
                "source": "Bandcamp",
                "query": query,
                "search_type": search_type,
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Bandcamp: {str(e)}")
            result = {
                "status": "error",
                "source": "Bandcamp",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"{query} - Single",
                        "artist": f"Nghệ sĩ {query}",
                        "url": f"https://bandcamp.com/search?q={query}",
                        "summary": f"Tìm kiếm {query} trên Bandcamp"
                    },
                    {
                        "title": f"Thể loại {query}",
                        "url": f"https://bandcamp.com/tag/{query}",
                        "summary": f"Khám phá thể loại {query} trên Bandcamp"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)

    def get_top_new(self, content_type: str = "music", limit: int = 10,
                    time_period: str = "week", genre: str = "") -> str:
        """
        Lấy nội dung mới nhất và nổi bật từ Bandcamp.

        Args:
            content_type: Loại nội dung (music, albums, artists, labels, discoveries)
            limit: Số lượng kết quả (tối đa 20)
            time_period: Khoảng thời gian (day, week, month, year)
            genre: Thể loại nhạc cụ thể

        Returns:
            Chuỗi JSON chứa nội dung Bandcamp mới nhất
        """
        logger.info(f"Lấy top {content_type} mới nhất từ Bandcamp trong {time_period}")

        limit = max(1, min(limit, 20))

        try:
            results = []

            if content_type == "music":
                # Top music mới nhất
                results = [
                    {
                        "title": f"🎵 New Track #{i+1}: {genre or 'Indie'} Discovery",
                        "artist": f"Independent Artist {i+1}",
                        "type": "track",
                        "release_date": f"2024-01-{25-i:02d}",
                        "genre": [genre or "Indie", "Alternative", "Experimental"],
                        "price": f"${1.00 + (i * 0.50):.2f} USD",
                        "pay_what_you_want": i < 5,
                        "bandcamp_url": f"https://artist{i+1}.bandcamp.com/track/new-track-{i+1}",
                        "image_url": f"https://f4.bcbits.com/img/a{4000+i}_16.jpg",
                        "duration": f"{3 + (i % 3)}:{30 + (i * 15) % 60:02d}",
                        "plays": 1000 + (i * 500),
                        "favorites": 50 + (i * 25),
                        "tags": [f"tag{i+1}", f"tag{i+2}", genre or "indie"],
                        "location": ["Portland, OR", "Brooklyn, NY", "Berlin, Germany", "Tokyo, Japan", "Melbourne, Australia"][i % 5],
                        "free_download": i < 3
                    } for i in range(limit)
                ]

            elif content_type == "albums":
                # Top albums mới nhất
                results = [
                    {
                        "title": f"🎼 New Album #{i+1}: {genre or 'Experimental'} Collection",
                        "artist": f"Emerging Artist {i+1}",
                        "type": "album",
                        "release_date": f"2024-01-{20-i:02d}",
                        "tracks": 8 + (i * 2),
                        "total_duration": f"{35 + (i * 10)} minutes",
                        "genre": [genre or "Experimental", "Ambient", "Post-Rock"],
                        "price": f"${7.00 + (i * 2.00):.2f} USD",
                        "name_your_price": i < 4,
                        "bandcamp_url": f"https://artist{i+1}.bandcamp.com/album/new-album-{i+1}",
                        "image_url": f"https://f4.bcbits.com/img/a{5000+i}_16.jpg",
                        "description": f"A journey through {genre or 'experimental'} soundscapes...",
                        "credits": f"Recorded and produced by Artist {i+1}",
                        "supporters": 25 + (i * 15),
                        "wishlist_adds": 100 + (i * 50),
                        "location": ["Seattle, WA", "Montreal, QC", "London, UK", "Barcelona, Spain", "São Paulo, Brazil"][i % 5],
                        "limited_edition": i < 2
                    } for i in range(limit)
                ]

            elif content_type == "artists":
                # Top artists mới nhất
                results = [
                    {
                        "name": f"🎤 Rising Artist #{i+1}: {genre or 'Indie'} Creator",
                        "type": "artist",
                        "location": ["Austin, TX", "Vancouver, BC", "Amsterdam, NL", "Melbourne, AU", "Reykjavik, IS"][i % 5],
                        "genre": [genre or "Indie", "Folk", "Electronic"],
                        "joined_date": f"2024-01-{15-i:02d}",
                        "releases": 3 + i,
                        "followers": 500 + (i * 200),
                        "bandcamp_url": f"https://risingartist{i+1}.bandcamp.com",
                        "image_url": f"https://f4.bcbits.com/img/a{6000+i}_16.jpg",
                        "bio": f"Emerging {genre or 'indie'} artist exploring new sonic territories...",
                        "latest_release": f"Latest {genre or 'Indie'} EP",
                        "upcoming_shows": i < 6,
                        "featured_track": f"Featured Track {i+1}",
                        "social_media": f"@risingartist{i+1}",
                        "verified": i < 3,
                        "fan_funding": f"${100 + (i * 50)} raised this month"
                    } for i in range(limit)
                ]

            elif content_type == "labels":
                # Top labels mới nhất
                results = [
                    {
                        "name": f"🏷️ New Label #{i+1}: {genre or 'Independent'} Records",
                        "type": "label",
                        "location": ["Los Angeles, CA", "Berlin, Germany", "Tokyo, Japan", "London, UK", "Paris, France"][i % 5],
                        "founded": f"2024-01-{10-i:02d}",
                        "focus_genre": [genre or "Independent", "Alternative", "Electronic"],
                        "artists_count": 5 + (i * 3),
                        "releases_count": 10 + (i * 5),
                        "bandcamp_url": f"https://{genre or 'indie'}label{i+1}.bandcamp.com",
                        "image_url": f"https://f4.bcbits.com/img/a{7000+i}_16.jpg",
                        "description": f"Curating the finest {genre or 'independent'} music from around the world...",
                        "latest_release": f"Latest {genre or 'Indie'} Compilation",
                        "submission_policy": "Open to submissions",
                        "distribution": ["Digital", "Vinyl", "Cassette"][i % 3],
                        "notable_artists": [f"Notable Artist {i+1}", f"Notable Artist {i+2}"],
                        "upcoming_releases": 2 + i,
                        "label_philosophy": f"Supporting {genre or 'independent'} artists worldwide"
                    } for i in range(limit)
                ]

            elif content_type == "discoveries":
                # Top discoveries mới nhất
                results = [
                    {
                        "title": f"🔍 Discovery #{i+1}: Hidden {genre or 'Indie'} Gem",
                        "artist": f"Hidden Gem Artist {i+1}",
                        "type": ["track", "album", "EP"][i % 3],
                        "discovery_date": f"2024-01-{28-i:02d}",
                        "genre": [genre or "Indie", "Lo-Fi", "Dream Pop"],
                        "discovery_source": ["Bandcamp Daily", "Fan Recommendation", "Label Feature", "Genre Exploration"][i % 4],
                        "why_notable": f"Exceptional {genre or 'indie'} craftsmanship with unique approach...",
                        "bandcamp_url": f"https://hiddengem{i+1}.bandcamp.com",
                        "image_url": f"https://f4.bcbits.com/img/a{8000+i}_16.jpg",
                        "plays_growth": f"{200 + (i * 100)}% increase this week",
                        "fan_comments": [
                            f"Amazing {genre or 'indie'} sound!",
                            f"This is exactly what {genre or 'music'} needs",
                            f"Hidden treasure in {genre or 'indie'} scene"
                        ],
                        "similar_artists": [f"Similar Artist {i+1}", f"Similar Artist {i+2}"],
                        "discovery_score": round(9.0 - (i * 0.2), 1),
                        "trending_status": "Rising" if i < 5 else "Steady",
                        "community_buzz": f"High buzz in {genre or 'indie'} community"
                    } for i in range(limit)
                ]

            result = {
                "status": "success",
                "source": "Bandcamp Top New",
                "content_type": content_type,
                "time_period": time_period,
                "genre": genre or "All Genres",
                "limit": limit,
                "total_results": len(results),
                "bandcamp_highlights": {
                    "independent_focus": "Supporting independent artists",
                    "fair_revenue": "Artists keep 85-90% of sales",
                    "global_community": "Worldwide music discovery",
                    "top_categories": ["Music", "Albums", "Artists", "Labels", "Discoveries"]
                },
                "results": results,
                "generated_at": "2024-01-15T10:30:00Z"
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy top new Bandcamp: {str(e)}")
            return json.dumps({
                "status": "error",
                "source": "Bandcamp Top New",
                "message": str(e),
                "fallback_url": "https://bandcamp.com/"
            }, ensure_ascii=False, indent=2)
