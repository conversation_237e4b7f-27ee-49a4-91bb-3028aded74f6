from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json

class BandcampTool(Toolkit):
    """
    Công cụ tìm kiếm Bandcamp giúp khám phá nhạc độc lậ<PERSON>, nghệ sĩ và album từ nền tảng Bandcamp.
    
    Các từ khóa tìm kiếm gợi ý:
    - Tên nghệ sĩ, ban nhạc độc lập
    - Th<PERSON> loại nhạc (indie rock, electronic, folk, v.v.)
    - Album, EP, single mới phát hành
    - Nhạc theo tâm trạng (chill, energetic, ambient)
    - <PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> (Việt Nam, Nhật Bản, Hàn <PERSON>, v.v.)
    """
    def __init__(self):
        super().__init__(
            name="Công cụ tìm kiếm Bandcamp",
            tools=[self.search_bandcamp]
        )
        self.base_url = "https://bandcamp.com"
        self.search_types = ["all", "music", "artist", "album", "label", "fan", "tag"]

    def search_bandcamp(self, query: str, search_type: str = "all", limit: int = 5) -> str:
        """
        Tìm kiếm trên Bandcamp theo từ khóa và loại kết quả.
        
        Args:
            query: Từ khóa tìm kiếm (tên nghệ sĩ, album, thể loại...)
            search_type: Loại kết quả (all, music, artist, album, label, fan, tag)
            limit: Số lượng kết quả trả về (tối đa 20)
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm Bandcamp với từ khóa: {query}, loại: {search_type}")
        
        if search_type not in self.search_types:
            search_type = "all"
            
        limit = max(1, min(limit, 20))  # Giới hạn trong khoảng 1-20
        
        try:
            # Giả lập kết quả tìm kiếm
            results = []
            
            if search_type in ["all", "music"]:
                results.extend([
                    {
                        "title": f"{query} - Single",
                        "artist": f"Nghệ sĩ {query}",
                        "type": "track",
                        "genre": ["Indie", "Alternative"],
                        "release_date": "2025-01-01",
                        "price": "$1.00 USD",
                        "url": f"https://{query.lower()}.bandcamp.com/track/{query.lower()}",
                        "image_url": f"https://f4.bcbits.com/img/00{1000+i}_16.jpg"
                    } for i in range(min(limit, 5))
                ])
                
            if search_type in ["all", "artist"] and len(results) < limit:
                results.extend([
                    {
                        "name": f"{query} {i+1}",
                        "type": "artist",
                        "location": "Hà Nội, Việt Nam",
                        "genre": ["Indie Rock", "Dream Pop"],
                        "url": f"https://{query.lower()}{i+1}.bandcamp.com",
                        "image_url": f"https://f4.bcbits.com/img/00{2000+i}_16.jpg"
                    } for i in range(min(limit - len(results), 3))
                ])
                
            if search_type in ["all", "album"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"{query} (Deluxe Edition)",
                        "artist": f"Nghệ sĩ {query}",
                        "type": "album",
                        "release_date": "2025-01-01",
                        "tracks": 10,
                        "price": "$7.00 USD",
                        "url": f"https://{query.lower()}.bandcamp.com/album/{query.lower()}-deluxe",
                        "image_url": f"https://f4.bcbits.com/img/00{3000+i}_16.jpg"
                    } for i in range(min(limit - len(results), 3))
                ])
            
            result = {
                "status": "success",
                "source": "Bandcamp",
                "query": query,
                "search_type": search_type,
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Bandcamp: {str(e)}")
            result = {
                "status": "error",
                "source": "Bandcamp",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"{query} - Single",
                        "artist": f"Nghệ sĩ {query}",
                        "url": f"https://bandcamp.com/search?q={query}",
                        "summary": f"Tìm kiếm {query} trên Bandcamp"
                    },
                    {
                        "title": f"Thể loại {query}",
                        "url": f"https://bandcamp.com/tag/{query}",
                        "summary": f"Khám phá thể loại {query} trên Bandcamp"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
