from typing import Dict, Any, List, Optional, Union
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime, timedelta
from urllib.parse import quote
import random

class ClimateDataTool(Toolkit):
    """
    Công cụ lấy dữ liệu biến đổi khí hậu từ các nguồn mở.
    
    <PERSON><PERSON><PERSON> từ khóa tìm kiếm gợi ý:
    - Nhiệt độ toàn cầu (global temperature)
    - Mực nước biển dâng (sea level rise)
    - Lượng khí thải CO2 (CO2 emissions)
    - <PERSON><PERSON><PERSON> tan (ice melt, arctic sea ice)
    - <PERSON>hời tiết cực đoan (extreme weather events)
    - Chỉ số chất lượng không khí (air quality index)
    - <PERSON>ũ lụt và hạn hán (floods and droughts)
    """
    
    def __init__(self):
        super().__init__(
            name="Công cụ dữ liệu khí hậu",
            tools=[self.get_climate_data]
        )
        self.data_sources = {
            "nasa": "NASA Climate Data",
            "noaa": "NOAA Climate Data",
            "copernicus": "Copernicus Climate Change Service",
            "worldbank": "World Bank Climate Data"
        }
        self.metrics = [
            "temperature", "co2", "sea_level", "arctic_ice", 
            "extreme_events", "precipitation", "air_quality"
        ]
        self.time_periods = ["day", "week", "month", "year", "decade", "all"]

    def _generate_sample_data(self, metric: str, location: str, start_date: str, end_date: str) -> List[Dict]:
        """Tạo dữ liệu mẫu cho mục đích minh họa"""
        try:
            start = datetime.strptime(start_date, "%Y-%m-%d")
            end = datetime.strptime(end_date, "%Y-%m-%d")
            delta = end - start
            
            # Xác định đơn vị và phạm vi dựa trên metric
            if metric == "temperature":
                unit = "°C"
                base_value = random.uniform(10, 30)
                variation = 5
            elif metric == "co2":
                unit = "ppm"
                base_value = random.uniform(350, 420)
                variation = 20
            elif metric == "sea_level":
                unit = "mm"
                base_value = random.uniform(0, 100)
                variation = 10
            elif metric == "arctic_ice":
                unit = "million km²"
                base_value = random.uniform(3, 15)
                variation = 2
            elif metric == "extreme_events":
                unit = "events"
                base_value = random.uniform(5, 20)
                variation = 5
            elif metric == "precipitation":
                unit = "mm"
                base_value = random.uniform(0, 200)
                variation = 50
            else:  # air_quality
                unit = "AQI"
                base_value = random.uniform(0, 300)
                variation = 50
            
            # Tạo dữ liệu theo ngày
            data = []
            for i in range(delta.days + 1):
                current_date = start + timedelta(days=i)
                value = base_value + random.uniform(-variation, variation)
                
                # Đảm bảo giá trị không âm
                value = max(0, value)
                
                data.append({
                    "date": current_date.strftime("%Y-%m-%d"),
                    "value": round(value, 2),
                    "unit": unit,
                    "location": location,
                    "source": random.choice(list(self.data_sources.values()))
                })
                
                # Thêm xu hướng tăng dần cho một số chỉ số
                if metric in ["temperature", "co2", "sea_level"]:
                    base_value += random.uniform(0, 0.05)
                    
            return data
            
        except Exception as e:
            log_debug(f"Lỗi khi tạo dữ liệu mẫu: {str(e)}")
            return []

    def get_climate_data(self, metric: str, location: str = "global", 
                        start_date: str = "2020-01-01", 
                        end_date: str = "2023-12-31",
                        source: str = "nasa") -> str:
        """
        Lấy dữ liệu biến đổi khí hậu từ các nguồn mở.
        
        Args:
            metric: Chỉ số khí hậu cần lấy (temperature, co2, sea_level, arctic_ice, extreme_events, precipitation, air_quality)
            location: Vị trí địa lý (quốc gia, thành phố, hoặc 'global')
            start_date: Ngày bắt đầu (YYYY-MM-DD)
            end_date: Ngày kết thúc (YYYY-MM-DD)
            source: Nguồn dữ liệu (nasa, noaa, copernicus, worldbank)
            
        Returns:
            Chuỗi JSON chứa dữ liệu khí hậu
        """
        logger.info(f"Đang lấy dữ liệu {metric} cho {location} từ {start_date} đến {end_date}")
        
        # Xác thực tham số
        if metric not in self.metrics:
            metric = "temperature"
        if source not in self.data_sources:
            source = "nasa"
            
        try:
            # Trong thực tế, đây sẽ là lời gọi API thực tế
            # Đây chỉ là dữ liệu mẫu để minh họa
            
            # Tạo dữ liệu mẫu
            sample_data = self._generate_sample_data(metric, location, start_date, end_date)
            
            if not sample_data:
                raise ValueError("Không thể tạo dữ liệu mẫu")
            
            # Tạo thông tin phân tích
            values = [item["value"] for item in sample_data]
            avg_value = sum(values) / len(values)
            min_value = min(values)
            max_value = max(values)
            trend = "tăng" if values[-1] > values[0] else "giảm"
            change_pct = abs((values[-1] - values[0]) / values[0] * 100) if values[0] != 0 else 0
            
            # Tạo kết quả
            result = {
                "status": "success",
                "source": self.data_sources[source],
                "metric": metric,
                "location": location,
                "time_period": f"{start_date} to {end_date}",
                "data_points": len(sample_data),
                "summary": {
                    "average": round(avg_value, 2),
                    "min": round(min_value, 2),
                    "max": round(max_value, 2),
                    "trend": trend,
                    "change_percentage": round(change_pct, 2),
                    "unit": sample_data[0]["unit"] if sample_data else "N/A"
                },
                "data": sample_data[:100],  # Giới hạn số lượng điểm dữ liệu trả về
                "attribution": [
                    {
                        "name": self.data_sources[source],
                        "url": f"https://climate.nasa.gov/" if source == "nasa" else 
                               f"https://www.ncei.noaa.gov/" if source == "noaa" else
                               f"https://climate.copernicus.eu/" if source == "copernicus" else
                               f"https://climateknowledgeportal.worldbank.org/"
                    }
                ],
                "related_metrics": [
                    m for m in self.metrics if m != metric
                ][:3]
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy dữ liệu khí hậu: {str(e)}")
            result = {
                "status": "error",
                "source": self.data_sources.get(source, "Climate Data"),
                "message": f"Không thể lấy dữ liệu: {str(e)}",
                "query": f"{metric} for {location} ({start_date} to {end_date})",
                "suggestions": [
                    f"Kiểm tra lại tên chỉ số khí hậu (ví dụ: {', '.join(self.metrics[:3])}...)",
                    f"Thử với một khoảng thời gian ngắn hơn",
                    f"Kiểm tra tên địa điểm hoặc thử 'global'"
                ],
                "example_queries": [
                    {"metric": "temperature", "location": "Vietnam", "period": "last 5 years"},
                    {"metric": "co2", "location": "global", "period": "2000-2023"},
                    {"metric": "sea_level", "location": "coastal cities", "period": "1990-2023"}
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
