from typing import Dict, Any, List, Optional, Union
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime, timedelta
from urllib.parse import quote
import random

class ClimateDataTool(Toolkit):
    """
    Công cụ lấy dữ liệu biến đổi khí hậu từ các nguồn mở.

    <PERSON><PERSON><PERSON> từ khóa tìm kiếm gợi ý:
    - Nhiệt độ toàn cầu (global temperature)
    - Mực nước biển dâng (sea level rise)
    - Lượng khí thải CO2 (CO2 emissions)
    - <PERSON><PERSON><PERSON> tan (ice melt, arctic sea ice)
    - <PERSON>hời tiết cực đoan (extreme weather events)
    - Chỉ số chất lượng không khí (air quality index)
    - Lũ lụt và hạn hán (floods and droughts)
    """

    def __init__(self):
        super().__init__(
            name="Công cụ dữ liệu khí hậu",
            tools=[self.get_climate_data, self.get_top_new]
        )
        self.data_sources = {
            "nasa": "NASA Climate Data",
            "noaa": "NOAA Climate Data",
            "copernicus": "Copernicus Climate Change Service",
            "worldbank": "World Bank Climate Data"
        }
        self.metrics = [
            "temperature", "co2", "sea_level", "arctic_ice",
            "extreme_events", "precipitation", "air_quality"
        ]
        self.time_periods = ["day", "week", "month", "year", "decade", "all"]

    def _generate_sample_data(self, metric: str, location: str, start_date: str, end_date: str) -> List[Dict]:
        """Tạo dữ liệu mẫu cho mục đích minh họa"""
        try:
            start = datetime.strptime(start_date, "%Y-%m-%d")
            end = datetime.strptime(end_date, "%Y-%m-%d")
            delta = end - start

            # Xác định đơn vị và phạm vi dựa trên metric
            if metric == "temperature":
                unit = "°C"
                base_value = random.uniform(10, 30)
                variation = 5
            elif metric == "co2":
                unit = "ppm"
                base_value = random.uniform(350, 420)
                variation = 20
            elif metric == "sea_level":
                unit = "mm"
                base_value = random.uniform(0, 100)
                variation = 10
            elif metric == "arctic_ice":
                unit = "million km²"
                base_value = random.uniform(3, 15)
                variation = 2
            elif metric == "extreme_events":
                unit = "events"
                base_value = random.uniform(5, 20)
                variation = 5
            elif metric == "precipitation":
                unit = "mm"
                base_value = random.uniform(0, 200)
                variation = 50
            else:  # air_quality
                unit = "AQI"
                base_value = random.uniform(0, 300)
                variation = 50

            # Tạo dữ liệu theo ngày
            data = []
            for i in range(delta.days + 1):
                current_date = start + timedelta(days=i)
                value = base_value + random.uniform(-variation, variation)

                # Đảm bảo giá trị không âm
                value = max(0, value)

                data.append({
                    "date": current_date.strftime("%Y-%m-%d"),
                    "value": round(value, 2),
                    "unit": unit,
                    "location": location,
                    "source": random.choice(list(self.data_sources.values()))
                })

                # Thêm xu hướng tăng dần cho một số chỉ số
                if metric in ["temperature", "co2", "sea_level"]:
                    base_value += random.uniform(0, 0.05)

            return data

        except Exception as e:
            log_debug(f"Lỗi khi tạo dữ liệu mẫu: {str(e)}")
            return []

    def get_climate_data(self, metric: str, location: str = "global",
                        start_date: str = "2020-01-01",
                        end_date: str = "2023-12-31",
                        source: str = "nasa") -> str:
        """
        Lấy dữ liệu biến đổi khí hậu từ các nguồn mở.

        Args:
            metric: Chỉ số khí hậu cần lấy (temperature, co2, sea_level, arctic_ice, extreme_events, precipitation, air_quality)
            location: Vị trí địa lý (quốc gia, thành phố, hoặc 'global')
            start_date: Ngày bắt đầu (YYYY-MM-DD)
            end_date: Ngày kết thúc (YYYY-MM-DD)
            source: Nguồn dữ liệu (nasa, noaa, copernicus, worldbank)

        Returns:
            Chuỗi JSON chứa dữ liệu khí hậu
        """
        logger.info(f"Đang lấy dữ liệu {metric} cho {location} từ {start_date} đến {end_date}")

        # Xác thực tham số
        if metric not in self.metrics:
            metric = "temperature"
        if source not in self.data_sources:
            source = "nasa"

        try:
            # Trong thực tế, đây sẽ là lời gọi API thực tế
            # Đây chỉ là dữ liệu mẫu để minh họa

            # Tạo dữ liệu mẫu
            sample_data = self._generate_sample_data(metric, location, start_date, end_date)

            if not sample_data:
                raise ValueError("Không thể tạo dữ liệu mẫu")

            # Tạo thông tin phân tích
            values = [item["value"] for item in sample_data]
            avg_value = sum(values) / len(values)
            min_value = min(values)
            max_value = max(values)
            trend = "tăng" if values[-1] > values[0] else "giảm"
            change_pct = abs((values[-1] - values[0]) / values[0] * 100) if values[0] != 0 else 0

            # Tạo kết quả
            result = {
                "status": "success",
                "source": self.data_sources[source],
                "metric": metric,
                "location": location,
                "time_period": f"{start_date} to {end_date}",
                "data_points": len(sample_data),
                "summary": {
                    "average": round(avg_value, 2),
                    "min": round(min_value, 2),
                    "max": round(max_value, 2),
                    "trend": trend,
                    "change_percentage": round(change_pct, 2),
                    "unit": sample_data[0]["unit"] if sample_data else "N/A"
                },
                "data": sample_data[:100],  # Giới hạn số lượng điểm dữ liệu trả về
                "attribution": [
                    {
                        "name": self.data_sources[source],
                        "url": f"https://climate.nasa.gov/" if source == "nasa" else
                               f"https://www.ncei.noaa.gov/" if source == "noaa" else
                               f"https://climate.copernicus.eu/" if source == "copernicus" else
                               f"https://climateknowledgeportal.worldbank.org/"
                    }
                ],
                "related_metrics": [
                    m for m in self.metrics if m != metric
                ][:3]
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy dữ liệu khí hậu: {str(e)}")
            result = {
                "status": "error",
                "source": self.data_sources.get(source, "Climate Data"),
                "message": f"Không thể lấy dữ liệu: {str(e)}",
                "query": f"{metric} for {location} ({start_date} to {end_date})",
                "suggestions": [
                    f"Kiểm tra lại tên chỉ số khí hậu (ví dụ: {', '.join(self.metrics[:3])}...)",
                    f"Thử với một khoảng thời gian ngắn hơn",
                    f"Kiểm tra tên địa điểm hoặc thử 'global'"
                ],
                "example_queries": [
                    {"metric": "temperature", "location": "Vietnam", "period": "last 5 years"},
                    {"metric": "co2", "location": "global", "period": "2000-2023"},
                    {"metric": "sea_level", "location": "coastal cities", "period": "1990-2023"}
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)

    def get_top_new(self, content_type: str = "climate_data", limit: int = 10,
                   time_period: str = "month", category: str = "") -> str:
        """
        Lấy dữ liệu khí hậu và môi trường mới nhất.

        Parameters:
        - content_type: Loại nội dung (climate_data, reports, alerts, research, events)
        - limit: Số lượng kết quả (tối đa 20)
        - time_period: Khoảng thời gian (week, month, year, all_time)
        - category: Danh mục cụ thể (temperature, co2, sea_level, extreme_weather, etc.)

        Returns:
        - Chuỗi JSON với dữ liệu khí hậu mới nhất
        """
        logger.info(f"Lấy top {content_type} mới nhất trong {time_period}")

        limit = max(1, min(limit, 20))

        try:
            results = []

            if content_type == "climate_data":
                # Dữ liệu khí hậu mới nhất
                results = [
                    {
                        "name": f"🌡️ {category or 'Climate'} Data #{i+1}",
                        "data_id": f"climate_data_{2024}_{1000+i:04d}",
                        "metric": category or ["Temperature", "CO2", "Sea Level", "Arctic Ice", "Precipitation"][i % 5],
                        "location": ["Global", "Arctic", "Tropical", "Temperate", "Polar"][i % 5],
                        "value": round(15.5 + (i * 0.3), 2),
                        "unit": ["°C", "ppm", "mm", "million km²", "mm"][i % 5],
                        "date": f"2024-{1+i%12:02d}-{15+i:02d}",
                        "trend": ["Increasing", "Stable", "Decreasing", "Fluctuating"][i % 4],
                        "change_rate": f"{0.1 + (i * 0.05):.2f}% per year",
                        "confidence_level": ["High", "Very High", "Medium", "High"][i % 4],
                        "data_source": ["NASA GISS", "NOAA", "Copernicus", "World Bank"][i % 4],
                        "measurement_method": ["Satellite", "Ground Station", "Ocean Buoy", "Weather Station"][i % 4],
                        "anomaly": f"{-0.5 + (i * 0.2):.1f} from baseline",
                        "baseline_period": "1951-1980",
                        "quality_flag": ["Good", "Excellent", "Fair", "Good"][i % 4],
                        "related_metrics": [f"Related Metric {j+1}" for j in range(2)],
                        "impact_assessment": ["Moderate", "High", "Low", "Critical"][i % 4],
                        "data_url": f"https://climate.nasa.gov/data/{category or 'climate'}-{chr(97+i)}",
                        "visualization_url": f"https://climate.nasa.gov/charts/{category or 'climate'}-{chr(97+i)}"
                    } for i in range(limit)
                ]

            elif content_type == "reports":
                # Báo cáo khí hậu mới nhất
                results = [
                    {
                        "name": f"📊 {category or 'Climate'} Report #{i+1}",
                        "report_id": f"climate_report_{2024}_{2000+i:04d}",
                        "title": f"{category or 'Global'} {['Climate', 'Environmental', 'Sustainability', 'Carbon'][i % 4]} Assessment {chr(65+i)}",
                        "organization": ["IPCC", "UNEP", "NASA", "NOAA", "WMO"][i % 5],
                        "report_type": ["Assessment Report", "Special Report", "Technical Summary", "Policy Brief"][i % 4],
                        "publication_date": f"2024-{1+i%12:02d}-{10+i:02d}",
                        "page_count": 50 + (i * 20),
                        "executive_summary": f"This report provides comprehensive analysis of {category or 'climate'} trends and their implications for global sustainability. Key findings include {['rising temperatures', 'increasing CO2 levels', 'sea level rise', 'extreme weather'][i % 4]} and recommendations for {['mitigation', 'adaptation', 'policy reform', 'technology deployment'][i % 4]}.",
                        "key_findings": [f"Finding {j+1}" for j in range(4)],
                        "recommendations": [f"Recommendation {j+1}" for j in range(3)],
                        "geographic_scope": ["Global", "Regional", "National", "Local"][i % 4],
                        "time_horizon": ["2030", "2050", "2100", "Long-term"][i % 4],
                        "confidence_level": ["Very High", "High", "Medium", "High"][i % 4],
                        "peer_reviewed": i % 2 == 0,
                        "download_url": f"https://www.ipcc.ch/report/{category or 'climate'}-{chr(97+i)}.pdf",
                        "summary_url": f"https://www.ipcc.ch/report/{category or 'climate'}-{chr(97+i)}-summary.html"
                    } for i in range(limit)
                ]

            elif content_type == "alerts":
                # Cảnh báo khí hậu mới nhất
                results = [
                    {
                        "name": f"⚠️ {category or 'Climate'} Alert #{i+1}",
                        "alert_id": f"climate_alert_{2024}_{3000+i:04d}",
                        "alert_type": ["Extreme Weather", "Temperature Record", "CO2 Milestone", "Sea Level Alert"][i % 4],
                        "severity": ["High", "Critical", "Medium", "Urgent"][i % 4],
                        "location": ["Global", "Arctic", "Pacific", "Atlantic", "Indian Ocean"][i % 5],
                        "description": f"Alert for {category or 'climate'} conditions showing {['unprecedented levels', 'record-breaking values', 'critical thresholds', 'rapid changes'][i % 4]} in the {['atmosphere', 'ocean', 'ice sheets', 'weather patterns'][i % 4]}.",
                        "issued_date": f"2024-{1+i%12:02d}-{5+i:02d}",
                        "valid_until": f"2024-{2+i%12:02d}-{10+i:02d}",
                        "threshold_exceeded": f"{100 + (i * 10)} {['°C', 'ppm', 'mm', 'km/h'][i % 4]}",
                        "historical_context": f"First time since {2000 + (i % 20)}",
                        "potential_impacts": [f"Impact {j+1}" for j in range(3)],
                        "recommended_actions": [f"Action {j+1}" for j in range(2)],
                        "monitoring_agencies": ["WMO", "NOAA", "NASA", "Copernicus"][i % 4],
                        "update_frequency": ["Hourly", "Daily", "Weekly", "Real-time"][i % 4],
                        "alert_url": f"https://alerts.climate.gov/{category or 'climate'}-{chr(97+i)}",
                        "monitoring_url": f"https://monitor.climate.gov/{category or 'climate'}-{chr(97+i)}"
                    } for i in range(limit)
                ]

            elif content_type == "research":
                # Nghiên cứu khí hậu mới nhất
                results = [
                    {
                        "name": f"🔬 {category or 'Climate'} Research #{i+1}",
                        "research_id": f"climate_research_{2024}_{4000+i:04d}",
                        "title": f"{category or 'Climate'} {['Dynamics', 'Modeling', 'Impact', 'Adaptation'][i % 4]} Study {chr(65+i)}",
                        "authors": [f"Dr. {chr(65+j)} {chr(75+j)}" for j in range(3)],
                        "institution": ["MIT", "Stanford", "Harvard", "Oxford", "Cambridge"][i % 5],
                        "journal": ["Nature Climate Change", "Science", "Climate Dynamics", "Environmental Research"][i % 4],
                        "publication_date": f"2024-{1+i%12:02d}-{20+i:02d}",
                        "research_type": ["Observational", "Modeling", "Experimental", "Review"][i % 4],
                        "methodology": ["Statistical Analysis", "Machine Learning", "Field Study", "Simulation"][i % 4],
                        "abstract": f"This study investigates {category or 'climate'} {['patterns', 'trends', 'impacts', 'feedbacks'][i % 4]} using {['advanced modeling', 'satellite data', 'field observations', 'statistical methods'][i % 4]}. Results show {['significant correlations', 'emerging trends', 'critical thresholds', 'novel insights'][i % 4]} with implications for {['policy', 'adaptation', 'mitigation', 'understanding'][i % 4]}.",
                        "key_results": [f"Result {j+1}" for j in range(3)],
                        "implications": [f"Implication {j+1}" for j in range(2)],
                        "funding_source": ["NSF", "NASA", "NOAA", "EU Horizon"][i % 4],
                        "data_availability": ["Open Access", "Upon Request", "Restricted", "Public"][i % 4],
                        "citation_count": 5 + (i * 3),
                        "doi": f"10.1038/s41558-024-{1000+i:04d}",
                        "paper_url": f"https://nature.com/articles/s41558-024-{1000+i:04d}",
                        "data_url": f"https://data.climate.gov/research/{category or 'climate'}-{chr(97+i)}"
                    } for i in range(limit)
                ]

            elif content_type == "events":
                # Sự kiện khí hậu mới nhất
                results = [
                    {
                        "name": f"🌍 {category or 'Climate'} Event #{i+1}",
                        "event_id": f"climate_event_{2024}_{5000+i:04d}",
                        "title": f"{category or 'Global'} {['Summit', 'Conference', 'Workshop', 'Symposium'][i % 4]} {chr(65+i)}",
                        "event_type": ["Conference", "Workshop", "Webinar", "Summit"][i % 4],
                        "organizer": ["UNFCCC", "IPCC", "UNEP", "WMO", "Climate Action Network"][i % 5],
                        "date": f"2024-{2+i%12:02d}-{15+i:02d}",
                        "duration": f"{1 + (i % 5)} days",
                        "location": ["Virtual", "Geneva", "New York", "Paris", "Bonn"][i % 5],
                        "format": ["Hybrid", "In-Person", "Virtual", "Online"][i % 4],
                        "theme": f"{category or 'Climate'} {['Action', 'Adaptation', 'Mitigation', 'Finance'][i % 4]} for Sustainable Future",
                        "description": f"Join global experts for discussions on {category or 'climate'} {['science', 'policy', 'technology', 'finance'][i % 4]} and {['adaptation strategies', 'mitigation approaches', 'policy frameworks', 'innovation solutions'][i % 4]}. This event will address {['current challenges', 'future opportunities', 'best practices', 'emerging trends'][i % 4]} in climate action.",
                        "target_audience": ["Policymakers", "Researchers", "NGOs", "Private Sector"][i % 4],
                        "speakers": [f"Speaker {chr(65+j)} {chr(75+j)}" for j in range(4)],
                        "sessions": [f"Session {j+1}" for j in range(3)],
                        "registration_required": i % 2 == 0,
                        "registration_fee": ["Free", "$50", "$100", "Free"][i % 4],
                        "capacity": 200 + (i * 100),
                        "language": ["English", "Multilingual", "English", "French"][i % 4],
                        "event_url": f"https://unfccc.int/events/{category or 'climate'}-{chr(97+i)}",
                        "registration_url": f"https://unfccc.int/events/{category or 'climate'}-{chr(97+i)}/register"
                    } for i in range(limit)
                ]

            result = {
                "status": "success",
                "source": "Climate Data Top New",
                "content_type": content_type,
                "category": category or "All Categories",
                "time_period": time_period,
                "limit": limit,
                "total_results": len(results),
                "climate_highlights": {
                    "global_temperature_anomaly": "+1.2°C above pre-industrial",
                    "co2_concentration": "421 ppm (highest in 3M years)",
                    "sea_level_rise": "3.4 mm/year",
                    "arctic_ice_decline": "13% per decade",
                    "top_categories": ["Climate Data", "Reports", "Alerts", "Research", "Events"]
                },
                "results": results,
                "generated_at": "2024-01-15T10:30:00Z"
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy top new climate data: {str(e)}")
            result = {
                "status": "error",
                "source": "Climate Data Top New",
                "message": str(e),
                "fallback_url": "https://climate.nasa.gov/"
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
