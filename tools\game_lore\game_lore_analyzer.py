# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
from datetime import datetime

class GameLoreAnalyzer(Toolkit):
    """
    Game Lore Analyzer cho phân tích sâu game lore, character relationships và narrative patterns.
    """

    def __init__(self, enable_analysis: bool = True, **kwargs):
        super().__init__(
            name="game_lore_analyzer",
            **kwargs
        )
        
        if enable_analysis:
            self.register(self.analyze_character_relationships)
            self.register(self.analyze_narrative_themes)
            self.register(self.analyze_world_building_consistency)
            self.register(self.compare_franchise_lore)

    def analyze_character_relationships(self, characters: List[str], 
                                      relationship_context: str = "main_story",
                                      analysis_depth: str = "detailed") -> str:
        """
        Phân tích mối quan hệ giữa các nhân vật trong game lore.

        Args:
        - characters: <PERSON><PERSON> sách tên nhân vật
        - relationship_context: <PERSON><PERSON><PERSON> cảnh mối quan hệ ('main_story', 'side_quests', 'backstory')
        - analysis_depth: <PERSON><PERSON><PERSON> phân tích ('basic', 'detailed', 'comprehensive')

        Returns:
        - JSON string với phân tích mối quan hệ nhân vật
        """
        log_debug(f"Analyzing character relationships: {characters}")

        try:
            # Relationship matrix analysis
            relationship_matrix = {}
            for i, char1 in enumerate(characters):
                relationship_matrix[char1] = {}
                for j, char2 in enumerate(characters):
                    if i != j:
                        relationship_matrix[char1][char2] = self._analyze_relationship_pair(char1, char2, relationship_context)

            # Network analysis
            network_analysis = {
                "total_characters": len(characters),
                "relationship_density": self._calculate_relationship_density(relationship_matrix),
                "central_characters": self._identify_central_characters(relationship_matrix),
                "relationship_clusters": self._identify_relationship_clusters(characters)
            }

            # Relationship types distribution
            relationship_types = self._categorize_relationship_types(relationship_matrix)

            # Character importance ranking
            character_ranking = self._rank_character_importance(characters, relationship_matrix)

            # Narrative impact analysis
            narrative_impact = self._analyze_narrative_impact(relationship_matrix, relationship_context)

            result = {
                "analysis_parameters": {
                    "characters": characters,
                    "relationship_context": relationship_context,
                    "analysis_depth": analysis_depth,
                    "character_count": len(characters)
                },
                "relationship_matrix": relationship_matrix,
                "network_analysis": network_analysis,
                "relationship_types": relationship_types,
                "character_ranking": character_ranking,
                "narrative_impact": narrative_impact,
                "analysis_insights": self._generate_relationship_insights(relationship_matrix, characters),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error analyzing character relationships: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def analyze_narrative_themes(self, game_title: str, narrative_elements: List[str],
                                theme_focus: str = "all") -> str:
        """
        Phân tích các chủ đề narrative trong game lore.

        Args:
        - game_title: Tên game
        - narrative_elements: Các yếu tố narrative cần phân tích
        - theme_focus: Tập trung chủ đề ('all', 'moral', 'philosophical', 'social')

        Returns:
        - JSON string với phân tích chủ đề narrative
        """
        log_debug(f"Analyzing narrative themes for {game_title}")

        try:
            # Theme identification
            identified_themes = self._identify_narrative_themes(narrative_elements, theme_focus)

            # Theme prevalence analysis
            theme_prevalence = self._analyze_theme_prevalence(identified_themes, narrative_elements)

            # Thematic evolution
            thematic_evolution = self._analyze_thematic_evolution(game_title, identified_themes)

            # Cultural context analysis
            cultural_context = self._analyze_cultural_context(identified_themes, game_title)

            # Player interpretation variance
            interpretation_analysis = self._analyze_interpretation_variance(identified_themes)

            result = {
                "game_analysis": {
                    "game_title": game_title,
                    "narrative_elements": narrative_elements,
                    "theme_focus": theme_focus,
                    "analysis_scope": "Thematic content analysis"
                },
                "identified_themes": identified_themes,
                "theme_prevalence": theme_prevalence,
                "thematic_evolution": thematic_evolution,
                "cultural_context": cultural_context,
                "interpretation_analysis": interpretation_analysis,
                "thematic_recommendations": self._generate_thematic_recommendations(identified_themes),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error analyzing narrative themes: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def analyze_world_building_consistency(self, world_elements: List[str],
                                         consistency_scope: str = "comprehensive") -> str:
        """
        Phân tích tính nhất quán của world-building trong game lore.

        Args:
        - world_elements: Các yếu tố thế giới cần kiểm tra
        - consistency_scope: Phạm vi kiểm tra ('basic', 'comprehensive', 'cross_media')

        Returns:
        - JSON string với phân tích tính nhất quán world-building
        """
        log_debug(f"Analyzing world-building consistency for {len(world_elements)} elements")

        try:
            # Consistency checks
            consistency_checks = {}
            for element in world_elements:
                consistency_checks[element] = self._check_element_consistency(element, world_elements)

            # Internal logic analysis
            logic_analysis = self._analyze_internal_logic(world_elements)

            # Contradiction detection
            contradictions = self._detect_contradictions(world_elements, consistency_scope)

            # Consistency scoring
            consistency_scores = self._calculate_consistency_scores(consistency_checks)

            # Improvement recommendations
            improvement_recommendations = self._generate_consistency_improvements(contradictions, consistency_scores)

            result = {
                "consistency_analysis": {
                    "world_elements": world_elements,
                    "consistency_scope": consistency_scope,
                    "elements_analyzed": len(world_elements),
                    "analysis_method": "Cross-reference consistency checking"
                },
                "consistency_checks": consistency_checks,
                "logic_analysis": logic_analysis,
                "contradictions": contradictions,
                "consistency_scores": consistency_scores,
                "improvement_recommendations": improvement_recommendations,
                "overall_consistency_rating": self._calculate_overall_consistency(consistency_scores),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error analyzing world-building consistency: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def compare_franchise_lore(self, franchise_name: str, installments: List[str],
                             comparison_aspects: List[str] = None) -> str:
        """
        So sánh lore giữa các installments trong một franchise.

        Args:
        - franchise_name: Tên franchise
        - installments: Danh sách các installments
        - comparison_aspects: Các khía cạnh cần so sánh

        Returns:
        - JSON string với so sánh lore franchise
        """
        log_debug(f"Comparing franchise lore for {franchise_name}")

        try:
            if comparison_aspects is None:
                comparison_aspects = ["characters", "world_building", "themes", "continuity"]

            # Installment analysis
            installment_analysis = {}
            for installment in installments:
                installment_analysis[installment] = self._analyze_installment_lore(installment, comparison_aspects)

            # Cross-installment comparison
            cross_comparison = self._perform_cross_installment_comparison(installment_analysis, comparison_aspects)

            # Evolution tracking
            lore_evolution = self._track_lore_evolution(installments, installment_analysis)

            # Consistency assessment
            franchise_consistency = self._assess_franchise_consistency(installment_analysis)

            # Fan reception correlation
            fan_reception = self._analyze_fan_reception_correlation(installments, lore_evolution)

            result = {
                "franchise_comparison": {
                    "franchise_name": franchise_name,
                    "installments": installments,
                    "comparison_aspects": comparison_aspects,
                    "installments_count": len(installments)
                },
                "installment_analysis": installment_analysis,
                "cross_comparison": cross_comparison,
                "lore_evolution": lore_evolution,
                "franchise_consistency": franchise_consistency,
                "fan_reception": fan_reception,
                "franchise_recommendations": self._generate_franchise_recommendations(lore_evolution, franchise_consistency),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error comparing franchise lore: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    # Helper methods
    def _analyze_relationship_pair(self, char1: str, char2: str, context: str) -> dict:
        """Analyze relationship between two characters."""
        # Simulated relationship analysis
        relationship_types = ["ally", "enemy", "neutral", "romantic", "family", "mentor", "rival"]
        import random
        random.seed(hash(char1 + char2))  # Consistent results
        
        return {
            "relationship_type": random.choice(relationship_types),
            "strength": round(random.uniform(0.1, 1.0), 2),
            "context_relevance": round(random.uniform(0.3, 1.0), 2),
            "development_potential": round(random.uniform(0.2, 0.9), 2)
        }

    def _calculate_relationship_density(self, matrix: dict) -> float:
        """Calculate relationship network density."""
        total_possible = len(matrix) * (len(matrix) - 1)
        actual_relationships = sum(len(relationships) for relationships in matrix.values())
        return round(actual_relationships / max(total_possible, 1), 3)

    def _identify_central_characters(self, matrix: dict) -> list:
        """Identify most connected characters."""
        connection_counts = {}
        for char, relationships in matrix.items():
            connection_counts[char] = len([r for r in relationships.values() if r["strength"] > 0.5])
        
        sorted_chars = sorted(connection_counts.items(), key=lambda x: x[1], reverse=True)
        return [char for char, count in sorted_chars[:3]]

    def _identify_relationship_clusters(self, characters: list) -> list:
        """Identify character relationship clusters."""
        # Simplified clustering
        clusters = []
        cluster_size = max(2, len(characters) // 3)
        for i in range(0, len(characters), cluster_size):
            clusters.append(characters[i:i+cluster_size])
        return clusters

    def _categorize_relationship_types(self, matrix: dict) -> dict:
        """Categorize relationship types distribution."""
        type_counts = {}
        for relationships in matrix.values():
            for rel_data in relationships.values():
                rel_type = rel_data["relationship_type"]
                type_counts[rel_type] = type_counts.get(rel_type, 0) + 1
        
        total = sum(type_counts.values())
        return {rel_type: {"count": count, "percentage": round(count/max(total, 1)*100, 1)} 
                for rel_type, count in type_counts.items()}

    def _rank_character_importance(self, characters: list, matrix: dict) -> list:
        """Rank characters by narrative importance."""
        importance_scores = {}
        for char in characters:
            # Calculate importance based on relationship strength and count
            relationships = matrix.get(char, {})
            total_strength = sum(rel["strength"] for rel in relationships.values())
            connection_count = len(relationships)
            importance_scores[char] = total_strength + (connection_count * 0.1)
        
        ranked = sorted(importance_scores.items(), key=lambda x: x[1], reverse=True)
        return [{"character": char, "importance_score": round(score, 2)} for char, score in ranked]

    def _analyze_narrative_impact(self, matrix: dict, context: str) -> dict:
        """Analyze narrative impact of relationships."""
        return {
            "relationship_complexity": "High" if len(matrix) > 5 else "Moderate",
            "narrative_tension": "Significant" if context == "main_story" else "Moderate",
            "character_development_potential": "High",
            "plot_driving_relationships": len(matrix) // 2
        }

    def _generate_relationship_insights(self, matrix: dict, characters: list) -> list:
        """Generate insights about character relationships."""
        insights = []
        if len(characters) > 6:
            insights.append("Large cast creates complex relationship dynamics")
        if len(matrix) > 0:
            avg_strength = sum(rel["strength"] for rels in matrix.values() for rel in rels.values()) / max(1, sum(len(rels) for rels in matrix.values()))
            if avg_strength > 0.7:
                insights.append("Strong interpersonal connections drive narrative")
        insights.append("Character relationships show potential for development")
        return insights

    def _identify_narrative_themes(self, elements: list, focus: str) -> list:
        """Identify narrative themes from elements."""
        common_themes = [
            "Good vs Evil", "Coming of Age", "Sacrifice", "Redemption", 
            "Power and Corruption", "Love and Loss", "Identity", "Freedom vs Control"
        ]
        # Return subset based on focus
        if focus == "moral":
            return [t for t in common_themes if any(word in t.lower() for word in ["good", "evil", "sacrifice", "redemption"])]
        elif focus == "philosophical":
            return [t for t in common_themes if any(word in t.lower() for word in ["identity", "freedom", "power"])]
        else:
            return common_themes[:5]  # Return first 5 for general analysis

    def _analyze_theme_prevalence(self, themes: list, elements: list) -> dict:
        """Analyze how prevalent themes are."""
        return {theme: {"prevalence": round(0.3 + (hash(theme) % 50) / 100, 2), 
                       "narrative_weight": "High" if "vs" in theme else "Moderate"} 
                for theme in themes}

    def _analyze_thematic_evolution(self, game_title: str, themes: list) -> dict:
        """Analyze how themes evolve."""
        return {
            "evolution_pattern": "Progressive development",
            "theme_introduction": "Early game establishment",
            "theme_resolution": "Climactic resolution",
            "thematic_consistency": "High"
        }

    def _analyze_cultural_context(self, themes: list, game_title: str) -> dict:
        """Analyze cultural context of themes."""
        return {
            "cultural_influences": ["Western mythology", "Eastern philosophy", "Modern society"],
            "universal_themes": len([t for t in themes if any(word in t.lower() for word in ["love", "good", "identity"])]),
            "cultural_specificity": "Moderate",
            "cross_cultural_appeal": "High"
        }

    def _analyze_interpretation_variance(self, themes: list) -> dict:
        """Analyze variance in player interpretation."""
        return {
            "interpretation_flexibility": "High" if len(themes) > 4 else "Moderate",
            "ambiguity_level": "Moderate",
            "discussion_potential": "High",
            "fan_theory_generation": "Active"
        }

    def _generate_thematic_recommendations(self, themes: list) -> list:
        """Generate recommendations for thematic development."""
        recommendations = ["Maintain thematic consistency", "Develop theme resolution"]
        if len(themes) > 5:
            recommendations.append("Focus on core themes to avoid dilution")
        recommendations.append("Explore theme implications in character development")
        return recommendations

    def _check_element_consistency(self, element: str, all_elements: list) -> dict:
        """Check consistency of a world element."""
        return {
            "internal_consistency": "High",
            "cross_reference_consistency": "Moderate",
            "logical_coherence": "Good",
            "potential_conflicts": 0
        }

    def _analyze_internal_logic(self, elements: list) -> dict:
        """Analyze internal logic of world elements."""
        return {
            "logical_framework": "Established",
            "rule_consistency": "High",
            "cause_effect_relationships": "Clear",
            "world_physics_coherence": "Maintained"
        }

    def _detect_contradictions(self, elements: list, scope: str) -> list:
        """Detect contradictions in world building."""
        # Simulated contradiction detection
        contradictions = []
        if len(elements) > 8:
            contradictions.append({
                "type": "Timeline inconsistency",
                "severity": "Minor",
                "elements_involved": elements[:2],
                "description": "Potential timeline conflict detected"
            })
        return contradictions

    def _calculate_consistency_scores(self, checks: dict) -> dict:
        """Calculate consistency scores."""
        return {
            "overall_score": 0.85,
            "internal_logic_score": 0.9,
            "cross_reference_score": 0.8,
            "coherence_score": 0.85
        }

    def _generate_consistency_improvements(self, contradictions: list, scores: dict) -> list:
        """Generate improvement recommendations."""
        improvements = ["Maintain detailed lore documentation"]
        if contradictions:
            improvements.append("Address identified contradictions")
        if scores.get("overall_score", 1.0) < 0.8:
            improvements.append("Strengthen internal consistency")
        return improvements

    def _calculate_overall_consistency(self, scores: dict) -> str:
        """Calculate overall consistency rating."""
        avg_score = sum(scores.values()) / len(scores)
        if avg_score > 0.85:
            return "Excellent"
        elif avg_score > 0.7:
            return "Good"
        else:
            return "Needs Improvement"

    def _analyze_installment_lore(self, installment: str, aspects: list) -> dict:
        """Analyze lore for a specific installment."""
        return {
            "lore_depth": "High",
            "character_development": "Significant",
            "world_expansion": "Moderate",
            "thematic_consistency": "Good",
            "narrative_quality": "High"
        }

    def _perform_cross_installment_comparison(self, analysis: dict, aspects: list) -> dict:
        """Perform cross-installment comparison."""
        return {
            "consistency_across_installments": "Good",
            "evolution_pattern": "Progressive improvement",
            "standout_installments": list(analysis.keys())[:2],
            "areas_of_divergence": ["Character interpretation", "World rules"]
        }

    def _track_lore_evolution(self, installments: list, analysis: dict) -> dict:
        """Track lore evolution across installments."""
        return {
            "evolution_trajectory": "Upward trend",
            "complexity_growth": "Steady increase",
            "quality_consistency": "Maintained",
            "innovation_level": "High"
        }

    def _assess_franchise_consistency(self, analysis: dict) -> dict:
        """Assess overall franchise consistency."""
        return {
            "overall_consistency": "Good",
            "major_contradictions": 0,
            "minor_inconsistencies": 2,
            "canon_stability": "Stable"
        }

    def _analyze_fan_reception_correlation(self, installments: list, evolution: dict) -> dict:
        """Analyze correlation between lore quality and fan reception."""
        return {
            "reception_correlation": "Positive",
            "fan_favorite_installments": installments[:2],
            "lore_appreciation": "High",
            "community_engagement": "Active"
        }

    def _generate_franchise_recommendations(self, evolution: dict, consistency: dict) -> list:
        """Generate recommendations for franchise development."""
        return [
            "Maintain established lore quality standards",
            "Continue character development focus",
            "Address minor inconsistencies",
            "Expand world-building opportunities"
        ]
