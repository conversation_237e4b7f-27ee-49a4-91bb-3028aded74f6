import asyncio
import json
import logging
from textwrap import dedent
from typing import Optional, Dict, List
from agno.tools import Toolkit
from agno.agent import Agent
from agno.reasoning.step import NextAction, ReasoningStep

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LCoTTools(Toolkit):
    """Long Chain of Thought (LCoT) tool for structured reasoning."""
    
    def __init__(self, add_instructions: bool = True, add_few_shot: bool = True, num_steps: int = 5):
        super().__init__(name="lcot")
        
        # Validate input parameters
        if not 3 <= num_steps <= 10:
            raise ValueError("Number of steps must be between 3 and 10")
        
        self.add_instructions = add_instructions
        self.add_few_shot = add_few_shot
        self.num_steps = num_steps
        self.description = "A tool for long chain of thought reasoning with structured steps."
        logger.info("Initialized LCoTTools with instructions=%s, few_shot=%s, num_steps=%s", 
                    add_instructions, add_few_shot, num_steps)
        
        # Few-shot examples
        self.few_shot_examples = dedent("""\
            Example Query: Should I invest in Company X?
            Step 1: Problem Decomposition
            - Understand the company's business model and market.
            Step 2: Identify Variables and Assumptions
            - Variables: Stock price, revenue growth, market trends.
            - Assumptions: Market stability, no major disruptions.
            Step 3: Reasoning Path
            - Analyze financial data and compare with competitors.
            Step 4: Alternative Approaches
            - Consider bonds or other stocks.
            Step 5: Validation and Conclusion
            - Cross-check data with analyst reports; conclude if investment is viable.
        """) if add_few_shot else ""
        
        self.register(self.lcot_reason)
    
    async def lcot_reason(self, query: str, agent: Agent) -> str:
        """Execute LCoT reasoning and return markdown string with JSON tool call."""
        try:
            # Validate input
            if not isinstance(query, str) or not query.strip():
                logger.error("Invalid query: %s", query)
                return json.dumps({"error": "Query must be a non-empty string"})
            
            if not isinstance(agent, Agent):
                logger.error("Invalid agent instance: %s", agent)
                return json.dumps({"error": "Invalid agent instance"})

            logger.info("Processing LCoT for query: %s", query)
            steps = []
            current_step = 1
            
            if self.few_shot_examples:
                steps.append(f"### Few-Shot Examples\n{self.few_shot_examples}\n")
            
            # Generate reasoning steps
            step_templates = [
                ("Problem Decomposition", f"{query}"),
                ("Identify Variables and Assumptions", "[Agent identifies key factors and assumptions]"),
                ("Reasoning Path", "[Agent outlines a sequential plan to solve the problem]"),
                ("Alternative Approaches", "[Agent considers alternative solutions]"),
                ("Validation and Conclusion", "[Agent validates results and provides final answer]")
            ]
            
            for title, content in step_templates[:self.num_steps]:
                steps.append(f"### Step {current_step}: {title}\n{content}\n")
                current_step += 1

            output = "\n".join(steps)
            logger.info("LCoT completed successfully for query: %s", query)
            
            # Save reasoning state
            reasoning_step = ReasoningStep(
                title="LCoT Reasoning",
                reasoning=output,
                action="Structured reasoning completed",
                next_action=NextAction.CONTINUE,
                confidence=0.9
            )
            self._save_reasoning_step(agent, reasoning_step)
            
            # Build JSON tool call
            tool_call = {
                "tool": "lcot_reason",
                "parameters": {
                    "query": query,
                    "steps": self.num_steps,
                    "metadata": {
                        "type": "application/json",
                        "version": "1.0"
                    }
                }
            }
            
            return json.dumps({
                "tool_call": tool_call,
                "output": output
            }, indent=2)
            
        except Exception as e:
            logger.error("Error in LCoT processing: %s", str(e))
            return json.dumps({"error": f"LCoT processing failed: {str(e)}"})

    def _save_reasoning_step(self, agent: Agent, step: ReasoningStep):
        """Helper method to save reasoning steps"""
        if agent.session_state is None:
            agent.session_state = {}
        
        if "reasoning_steps" not in agent.session_state:
            agent.session_state["reasoning_steps"] = {}
        
        if agent.run_id not in agent.session_state["reasoning_steps"]:
            agent.session_state["reasoning_steps"][agent.run_id] = []
        
        agent.session_state["reasoning_steps"][agent.run_id].append(
            step.model_dump_json()
        )

class MCoTTools(Toolkit):
    """Meta Chain of Thought (MCoT) tool for reflecting and optimizing reasoning."""
    
    def __init__(self, add_instructions: bool = True, add_few_shot: bool = True, max_iterations: int = 3):
        super().__init__(name="mcot")
        
        if max_iterations < 1:
            raise ValueError("Max iterations must be at least 1")
        
        self.add_instructions = add_instructions
        self.add_few_shot = add_few_shot
        self.max_iterations = max_iterations
        self.description = "A tool for meta chain of thought reasoning to optimize reasoning process."
        logger.info("Initialized MCoTTools with instructions=%s, few_shot=%s, max_iterations=%s", 
                    add_instructions, add_few_shot, max_iterations)
        
        self.few_shot_examples = dedent("""\
            Example Reflection:
            LCoT Output: [Investment analysis with missing competitor data]
            Step 1: Evaluate Reasoning
            - Missing competitor data reduces reliability.
            Step 2: Question Assumptions
            - Assumed market stability may not hold.
            Step 3: Optimization Suggestions
            - Include competitor analysis in LCoT.
            Step 4: Alternative Strategies
            - Re-run LCoT with additional data sources.
        """) if add_few_shot else ""
        
        self.register(self.mcot_reflect)
    
    async def mcot_reflect(self, query: str, agent: Agent, lcot_output: Optional[str] = None) -> str:
        """Execute MCoT reflection and return markdown string with JSON tool call."""
        try:
            # Validate input
            if not isinstance(query, str) or not query.strip():
                return json.dumps({"error": "Invalid query"})
            
            if not isinstance(agent, Agent):
                return json.dumps({"error": "Invalid agent instance"})

            logger.info("Processing MCoT for query: %s", query)
            reflections = []
            
            if self.few_shot_examples:
                reflections.append(f"### Few-Shot Examples\n{self.few_shot_examples}\n")
            
            if lcot_output:
                # Improved off-topic detection
                if self._is_off_topic(lcot_output, query):
                    logger.warning("Detected off-topic response")
                    if self.max_iterations > 0:
                        new_lcot = await agent.call_tool(
                            tool_name="lcot",
                            method="lcot_reason",
                            arguments=json.dumps({"query": query})
                        )
                        reflections.append(f"### Re-run LCoT\n{json.loads(new_lcot)['output']}\n")
                        self.max_iterations -= 1
            
            reflection_steps = [
                ("Evaluate Reasoning", "[Assess clarity, completeness, correctness]"),
                ("Question Assumptions", "[Identify biases and overlooked factors]"),
                ("Optimization Suggestions", "[Propose improvements]"),
                ("Alternative Strategies", "[Suggest new approaches]")
            ]
            
            for idx, (title, content) in enumerate(reflection_steps, 1):
                reflections.append(f"### Step {idx}: {title}\n{content}\n")
            
            output = "\n".join(reflections)
            
            # Save reasoning state
            reasoning_step = ReasoningStep(
                title="MCoT Reflection",
                reasoning=output,
                action="Reflection completed",
                next_action=NextAction.CONTINUE,
                confidence=0.85
            )
            self._save_reasoning_step(agent, reasoning_step)
            
            # Build JSON tool call
            tool_call = {
                "tool": "mcot_reflect",
                "parameters": {
                    "query": query,
                    "lcot_output": lcot_output,
                    "metadata": {
                        "type": "application/json",
                        "iterations_remaining": self.max_iterations
                    }
                }
            }
            
            return json.dumps({
                "tool_call": tool_call,
                "output": output
            }, indent=2)
            
        except Exception as e:
            logger.error("MCoT error: %s", str(e))
            return json.dumps({"error": f"MCoT processing failed: {str(e)}"})

    def _is_off_topic(self, output: str, query: str, threshold: float = 0.3) -> bool:
        """Improved off-topic detection using keyword overlap"""
        query_keywords = set(query.lower().split())
        output_keywords = set(output.lower().split())
        intersection = query_keywords & output_keywords
        
        if not query_keywords:
            return False
        
        overlap_ratio = len(intersection) / len(query_keywords)
        return overlap_ratio < threshold

    def _save_reasoning_step(self, agent: Agent, step: ReasoningStep):
        """Helper method to save reasoning steps"""
        if agent.session_state is None:
            agent.session_state = {}
        
        agent.session_state.setdefault("reasoning_steps", {}).setdefault(agent.run_id, []).append(
            step.model_dump_json()
        )


if __name__ == "__main__":
    asyncio.run(main())