from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class InternetEncyclopediaPhilosophyTool(Toolkit):
    """
    Internet Encyclopedia of Philosophy (IEP) Tool cho tìm kiếm chủ đề, triết gia, trường phái triết học.
    """

    def __init__(self):
        super().__init__(
            name="Internet Encyclopedia of Philosophy Search Tool",
            description="Tool cho tìm kiếm chủ đề, triết gia, trường phái triết học từ Internet Encyclopedia of Philosophy (IEP).",
            tools=[self.search_iep]
        )

    async def search_iep(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm IEP cho chủ đề, triết gia, trường phái triết học.

        Parameters:
        - query: Từ khóa chủ đề, tên triết gia, trư<PERSON><PERSON> phái (ví dụ: 'existentialism', 'Kant ethics', 'Daoism')
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với tiêu đề, mô tả, URL bài viết, và các chủ đề liên quan
        """
        logger.info(f"Tìm kiếm IEP: {query}")

        try:
            # IEP không có API chính thức, sử dụng Google Custom Search hoặc scraping đơn giản
            # Ở đây mô phỏng tìm kiếm bằng Google Custom Search (nếu có key thì dùng thật)
            search_url = "https://www.googleapis.com/customsearch/v1"
            api_key = "YOUR_GOOGLE_API_KEY"  # Thay bằng API key thật nếu có
            cx = "YOUR_CUSTOM_SEARCH_ENGINE_ID"  # Thay bằng CSE ID thật nếu có

            params = {
                "q": f"site:iep.utm.edu {query}",
                "key": api_key,
                "cx": cx,
                "num": limit
            }

            response = requests.get(search_url, params=params)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "IEP",
                    "message": f"Search API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            items = data.get("items", [])
            results = []
            for item in items:
                results.append({
                    "title": item.get("title"),
                    "snippet": item.get("snippet"),
                    "link": item.get("link"),
                    "displayLink": item.get("displayLink"),
                    "iep_url": item.get("link")
                })

            return {
                "status": "success",
                "source": "IEP",
                "query": query,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "existentialism",
                    "Kant ethics",
                    "Daoism",
                    "free will",
                    "phenomenology",
                    "utilitarianism",
                    "Nietzsche",
                    "Plato Republic",
                    "Stoicism",
                    "philosophy of mind"
                ],
                "official_data_url": "https://iep.utm.edu/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm IEP: {str(e)}")
            return {
                "status": "error",
                "source": "IEP",
                "message": str(e),
                "query": query
            }
