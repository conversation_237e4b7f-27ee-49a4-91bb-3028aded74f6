from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
from bs4 import BeautifulSoup

class PaleobiologyDBTool(Toolkit):
    """
    PaleobiologyDB Tool cho tìm kiếm dữ liệu hóa thạch từ Paleobiology Database.
    """

    def __init__(self):
        super().__init__(
            name="PaleobiologyDB Tools",
            description="Bộ công cụ cho làm việc với cơ sở dữ liệu Paleobiology.",
            tools=[
                self.search_paleobiology_db,
                self.get_taxon_info,
                self.get_stratigraphy,
                self.get_collection_data,
                self.get_references
            ]
        )

    async def search_paleobiology_db(self, query: str, interval: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm Paleobiology Database cho hóa thạch theo tên/taxon và khoảng thời gian.

        Parameters:
        - query: Tên taxon hoặc từ khóa (ví dụ: 'Tyrannosaurus', 'Trilobita')
        - interval: K<PERSON>ảng thời gian địa chất (ví dụ: 'Maastrichtian', 'Cambrian')
        - limit: Số lượng kết quả tối đa (default: 10)

        Returns:
        - JSON với thông tin hóa thạch, vị trí, tuổi địa chất, và liên kết PBDB
        """
        logger.info(f"Tìm kiếm PaleobiologyDB: {query}, interval={interval}")

        try:
            # Sử dụng API occurrence/taxa PBDB
            base_url = "https://paleobiodb.org/data1.2/occs/list.json"
            params = {
                "base_name": query,
                "limit": limit,
                "show": "coords,phylo,time,strat,loc,ident,attr"
            }
            if interval:
                params["interval"] = interval

            response = requests.get(base_url, params=params)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "PaleobiologyDB",
                    "message": f"API trả về mã lỗi {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for item in data.get("records", []):
                occ_id = item.get("oid")
                pbdb_url = f"https://paleobiodb.org/classic/displayCollResults?occ_id={occ_id}" if occ_id else None
                results.append({
                    "occurrence_id": occ_id,
                    "taxon_name": item.get("taxon_name"),
                    "taxon_rank": item.get("taxon_rank"),
                    "early_interval": item.get("early_interval"),
                    "late_interval": item.get("late_interval"),
                    "max_ma": item.get("max_ma"),
                    "min_ma": item.get("min_ma"),
                    "country": item.get("cc"),
                    "state": item.get("state"),
                    "county": item.get("county"),
                    "latitude": item.get("lat"),
                    "longitude": item.get("lng"),
                    "formation": item.get("formation"),
                    "member": item.get("member"),
                    "collection_no": item.get("cid"),
                    "pbdb_url": pbdb_url
                })

            return {
                "status": "success",
                "source": "PaleobiologyDB",
                "query": query,
                "interval": interval,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm PBDB: {str(e)}")
            return {
                "status": "error",
                "source": "PaleobiologyDB",
                "message": str(e),
                "query": query
            }

    async def get_taxon_info(self, taxon_name: str) -> Dict[str, Any]:
        """
        Lấy thông tin chi tiết về một taxon cụ thể từ Paleobiology Database.

        Parameters:
        - taxon_name: Tên khoa học của taxon (ví dụ: 'Tyrannosaurus rex')

        Returns:
        - JSON với thông tin phân loại, mô tả, và các chi tiết khác
        """
        logger.info(f"Lấy thông tin taxon từ PBDB: {taxon_name}")

        try:
            base_url = "https://paleobiodb.org/data1.2/taxa/single.json"
            params = {
                "name": taxon_name,
                "show": "attr,app,parent,immparent,size,prg,ecospace,abund,image"
            }
            response = requests.get(base_url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "PaleobiologyDB",
                    "message": f"API trả về mã lỗi {response.status_code}",
                    "taxon_name": taxon_name
                }

            data = response.json()
            if not data.get("records"):
                return {
                    "status": "error",
                    "source": "PaleobiologyDB",
                    "message": "Không tìm thấy thông tin cho taxon",
                    "taxon_name": taxon_name
                }
            
            taxon = data["records"][0]
            return {
                "status": "success",
                "source": "PaleobiologyDB",
                "taxon_name": taxon_name,
                "info": {
                    "rank": taxon.get("rank"),
                    "parent": taxon.get("prnt"),
                    "first_appearance": taxon.get("fst"),
                    "last_appearance": taxon.get("lst"),
                    "reference": taxon.get("rms"),
                    "url": f"https://paleobiodb.org/classic/checkTaxonInfo?taxon_no={taxon.get('oid')}"
                }
            }

        except Exception as e:
            log_debug(f"Lỗi khi lấy thông tin taxon: {str(e)}")
            return {
                "status": "error",
                "source": "PaleobiologyDB",
                "message": str(e),
                "taxon_name": taxon_name
            }

    async def get_stratigraphy(self, formation: str, interval: Optional[str] = None) -> Dict[str, Any]:
        """
        Lấy thông tin địa tầng cho một hệ tầng cụ thể.

        Parameters:
        - formation: Tên hệ tầng (ví dụ: 'Hell Creek')
        - interval: Khoảng thời gian địa chất (tùy chọn)

        Returns:
        - JSON với thông tin địa tầng và các hóa thạch liên quan
        """
        logger.info(f"Lấy thông tin địa tầng: {formation}, Khoảng: {interval or 'Tất cả'}")

        try:
            base_url = "https://paleobiodb.org/data1.2/strata/list.json"
            params = {
                "name": formation,
                "show": "lith,loc,unit,ref,collection"
            }
            if interval:
                params["interval"] = interval
            
            response = requests.get(base_url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "PaleobiologyDB",
                    "message": f"API trả về mã lỗi {response.status_code}",
                    "formation": formation
                }

            data = response.json()
            if not data.get("records"):
                return {
                    "status": "error",
                    "source": "PaleobiologyDB",
                    "message": "Không tìm thấy thông tin địa tầng",
                    "formation": formation
                }
            
            return {
                "status": "success",
                "source": "PaleobiologyDB",
                "formation": formation,
                "interval": interval,
                "records": data["records"],
                "url": f"https://paleobiodb.org/classic/displaySearchStrata?formation={formation.replace(' ', '%20')}"
            }

        except Exception as e:
            log_debug(f"Lỗi khi lấy thông tin địa tầng: {str(e)}")
            return {
                "status": "error",
                "source": "PaleobiologyDB",
                "message": str(e),
                "formation": formation
            }

    async def get_collection_data(self, collection_id: str) -> Dict[str, Any]:
        """
        Lấy dữ liệu chi tiết về một bộ sưu tập hóa thạch.

        Parameters:
        - collection_id: ID của bộ sưu tập trong PBDB

        Returns:
        - JSON với thông tin chi tiết về bộ sưu tập
        """
        logger.info(f"Lấy dữ liệu bộ sưu tập PBDB: {collection_id}")

        try:
            base_url = f"https://paleobiodb.org/data1.2/colls/single.json"
            params = {
                "id": collection_id,
                "show": "loc,paleoloc,strat,time,lithext,geo,refattr,ent,entname,crmod"
            }
            response = requests.get(base_url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "PaleobiologyDB",
                    "message": f"API trả về mã lỗi {response.status_code}",
                    "collection_id": collection_id
                }

            data = response.json()
            if not data.get("records"):
                return {
                    "status": "error",
                    "source": "PaleobiologyDB",
                    "message": "Không tìm thấy thông tin bộ sưu tập",
                    "collection_id": collection_id
                }
            
            collection = data["records"][0]
            return {
                "status": "success",
                "source": "PaleobiologyDB",
                "collection_id": collection_id,
                "info": collection,
                "url": f"https://paleobiodb.org/classic/displayCollResults?collection_no={collection_id}"
            }

        except Exception as e:
            log_debug(f"Lỗi khi lấy dữ liệu bộ sưu tập: {str(e)}")
            return {
                "status": "error",
                "source": "PaleobiologyDB",
                "message": str(e),
                "collection_id": collection_id
            }

    async def get_references(self, author: Optional[str] = None, 
                          year: Optional[int] = None,
                          limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm tài liệu tham khảo trong PBDB.

        Parameters:
        - author: Tên tác giả (tùy chọn)
        - year: Năm xuất bản (tùy chọn)
        - limit: Số kết quả tối đa (mặc định: 10)

        Returns:
        - JSON với danh sách tài liệu tham khảo phù hợp
        """
        logger.info(f"Tìm kiếm tài liệu tham khảo - Tác giả: {author}, Năm: {year}")

        try:
            base_url = "https://paleobiodb.org/data1.2/refs/list.json"
            params = {
                "limit": limit,
                "all_refs": "yes"
            }
            if author:
                params["author"] = author
            if year:
                params["year"] = year
            
            response = requests.get(base_url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "PaleobiologyDB",
                    "message": f"API trả về mã lỗi {response.status_code}"
                }

            data = response.json()
            if not data.get("records"):
                return {
                    "status": "success",
                    "source": "PaleobiologyDB",
                    "message": "Không tìm thấy tài liệu tham khảo phù hợp",
                    "filters": {"author": author, "year": year}
                }
            
            return {
                "status": "success",
                "source": "PaleobiologyDB",
                "filters": {"author": author, "year": year},
                "references_count": len(data["records"]),
                "references": data["records"]
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm tài liệu tham khảo: {str(e)}")
            return {
                "status": "error",
                "source": "PaleobiologyDB",
                "message": str(e)
            }
