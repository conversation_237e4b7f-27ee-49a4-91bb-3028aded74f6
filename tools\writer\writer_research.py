import asyncio
import json
import logging
from textwrap import dedent
from typing import Optional, Dict, List
from agno.tools import Toolkit
from agno.reasoning.step import NextAction, ReasoningStep

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WriterResearchTools(Toolkit):
    """Advanced Research Writing Toolkit for generating high-quality scientific reports."""
    
    def __init__(self, add_instructions: bool = True, add_few_shot: bool = True, min_word_count: int = 2000):
        super().__init__(name="writer_research")
        
        # Validate input parameters
        if min_word_count < 1000:
            raise ValueError("Minimum word count must be at least 1000 for quality research reports")
        
        self.add_instructions = add_instructions
        self.add_few_shot = add_few_shot
        self.min_word_count = min_word_count
        self.description = "Advanced toolkit for generating comprehensive scientific research reports with proper structure, evidence, and analysis."
        
        logger.info("Initialized WriterResearchTools with instructions=%s, few_shot=%s, min_words=%s", 
                    add_instructions, add_few_shot, min_word_count)
        
        # Detailed instructions for research writing
        self.research_instructions = dedent("""\
            ## HƯỚNG DẪN VIẾT BÁO CÁO KHOA HỌC CHUYÊN NGHIỆP
            
            ### CẤU TRÚC BÁO CÁO BẮT BUỘC:
            1. **TÓM TẮT ĐIỀU HÀNH (Executive Summary)**: 200-300 từ
               - Tóm tắt vấn đề, phương pháp, kết quả chính, khuyến nghị
               - Phải đọc độc lập và hiểu được toàn bộ báo cáo
            
            2. **GIỚI THIỆU VÀ BỐI CẢNH (Introduction & Background)**: 15-20% tổng độ dài
               - Mô tả chi tiết vấn đề nghiên cứu
               - Tầm quan trọng và tác động của vấn đề
               - Mục tiêu nghiên cứu cụ thể và câu hỏi nghiên cứu
               - Tổng quan tài liệu liên quan (Literature Review)
            
            3. **PHƯƠNG PHÁP NGHIÊN CỨU (Methodology)**: 10-15% tổng độ dài
               - Thiết kế nghiên cứu chi tiết
               - Nguồn dữ liệu và cách thu thập
               - Công cụ phân tích sử dụng
               - Hạn chế của phương pháp
            
            4. **PHÂN TÍCH VÀ KẾT QUẢ (Analysis & Results)**: 40-50% tổng độ dài
               - Trình bày dữ liệu có hệ thống
               - Phân tích định lượng và định tính
               - Biểu đồ, bảng số liệu minh họa
               - Giải thích ý nghĩa của từng kết quả
            
            5. **THẢO LUẬN VÀ ĐÁNH GIÁ (Discussion & Evaluation)**: 15-20% tổng độ dài
               - So sánh với nghiên cứu trước đây
               - Giải thích các mối quan hệ nhân quả
               - Thảo luận các yếu tố ảnh hưởng
               - Đánh giá độ tin cậy của kết quả
            
            6. **KẾT LUẬN VÀ KHUYẾN NGHỊ (Conclusions & Recommendations)**: 10-15% tổng độ dài
               - Tóm tắt các phát hiện chính
               - Trả lời các câu hỏi nghiên cứu ban đầu
               - Khuyến nghị cụ thể cho thực tiễn
               - Đề xuất hướng nghiên cứu tiếp theo
            
            ### TIÊU CHUẨN CHẤT LƯỢNG:
            - **Tính khách quan**: Trình bày dữ liệu trung thực, không thiên vị
            - **Tính logic**: Luận điểm rõ ràng, lập luận có căn cứ
            - **Tính thuyết phục**: Bằng chứng đầy đủ, đáng tin cậy
            - **Tính ứng dụng**: Kết quả có thể áp dụng thực tế
            - **Độ chính xác**: Dữ liệu, trích dẫn, tham khảo chính xác
            
            ### YÊU CẦU VỀ BẰNG CHỨNG:
            - Mỗi luận điểm phải có ít nhất 2-3 nguồn bằng chứng khác nhau
            - Ưu tiên nguồn sơ cấp (nghiên cứu gốc, dữ liệu thực nghiệm)
            - Trích dẫn đúng format và đầy đủ thông tin
            - Đánh giá độ tin cậy của từng nguồn
        """) if add_instructions else ""
        
        # Comprehensive few-shot examples
        self.few_shot_examples = dedent("""\
            ## VÍ DỤ MẪU: "Tác động của trí tuệ nhân tạo đến thị trường lao động Việt Nam"
            
            ### TÓM TẮT ĐIỀU HÀNH
            Nghiên cứu này phân tích tác động của trí tuệ nhân tạo (AI) đến thị trường lao động Việt Nam từ 2020-2024. 
            Thông qua phân tích dữ liệu từ 1,200 doanh nghiệp và khảo sát 3,500 lao động, nghiên cứu phát hiện AI 
            đã thay thế 12% việc làm trong sản xuất nhưng tạo ra 8% việc làm mới trong công nghệ. Khuyến nghị chính 
            là đầu tư vào đào tạo lại lao động và phát triển kỹ năng số.
            
            ### 1. GIỚI THIỆU VÀ BỐI CẢNH
            #### 1.1 Vấn đề nghiên cứu
            Việt Nam đang trải qua cuộc cách mạng công nghiệp 4.0 với sự bùng nổ của AI. Theo Bộ Khoa học Công nghệ 
            (2023), có hơn 3,000 doanh nghiệp đã ứng dụng AI, tăng 150% so với 2020. Tuy nhiên, tác động cụ thể 
            đến việc làm vẫn chưa được nghiên cứu đầy đủ.
            
            #### 1.2 Tầm quan trọng
            - Ảnh hưởng đến 54 triệu lao động Việt Nam
            - Quyết định chính sách phát triển kinh tế quốc gia
            - Cần thiết cho quy hoạch giáo dục và đào tạo
            
            #### 1.3 Mục tiêu nghiên cứu
            - Đo lường tác động định lượng của AI đến việc làm
            - Xác định các ngành nghề bị ảnh hưởng mạnh nhất
            - Đề xuất giải pháp thích ứng cho lao động
            
            ### 2. PHƯƠNG PHÁP NGHIÊN CỨU
            #### 2.1 Thiết kế nghiên cứu: Phương pháp hỗn hợp (Mixed-method)
            - Nghiên cứu định lượng: Khảo sát 3,500 lao động trong 12 tỉnh/thành
            - Nghiên cứu định tính: Phỏng vấn sâu 50 chuyên gia và nhà quản lý
            - Phân tích dữ liệu thứ cấp từ GSO và các bộ ngành
            
            #### 2.2 Mẫu nghiên cứu
            - Lao động: Phân tầng theo ngành, tuổi, trình độ (n=3,500, sai số ±1.6%)
            - Doanh nghiệp: Chọn mục đích theo quy mô và mức độ ứng dụng AI (n=1,200)
            
            ### 3. PHÂN TÍCH VÀ KẾT QUẢ
            #### 3.1 Tác động định lượng
            **Thay thế việc làm:**
            - Sản xuất: 12% việc làm bị thay thế (120,000 lao động)
            - Dịch vụ khách hàng: 8% (45,000 lao động)  
            - Kế toán: 15% (30,000 lao động)
            
            **Tạo việc làm mới:**
            - Phát triển AI: 25,000 việc làm (+180% so với 2020)
            - Vận hành AI: 35,000 việc làm (+220%)
            - Phân tích dữ liệu: 18,000 việc làm (+150%)
            
            [Tiếp tục với các phần phân tích chi tiết khác...]
            
            ### 4. THẢO LUẬN
            Kết quả nghiên cứu phù hợp với lý thuyết "Creative Destruction" của Schumpeter (1942) và 
            nghiên cứu của McKinsey (2023) về tác động AI toàn cầu. Tuy nhiên, tỷ lệ thay thế ở Việt Nam 
            thấp hơn dự báo do đặc thù kinh tế đang phát triển...
            
            ### 5. KẾT LUẬN VÀ KHUYẾN NGHỊ
            #### 5.1 Kết luận chính
            1. AI tạo ra hiệu ứng thay thế ròng -4% việc làm ngắn hạn
            2. Lao động có kỹ năng cao được hưởng lợi nhiều hơn
            3. Cần 3-5 năm để thị trường lao động cân bằng trở lại
            
            #### 5.2 Khuyến nghị chính sách
            1. **Đào tạo lại lao động**: Đầu tư 2% GDP cho chương trình reskilling
            2. **An sinh xã hội**: Mở rộng bảo hiểm thất nghiệp 12 tháng
            3. **Phát triển kỹ năng số**: Bắt buộc giáo dục AI từ cấp 2
        """) if add_few_shot else ""
        
        # Report quality criteria
        self.quality_criteria = {
            "structure_completeness": 0.2,  # 20% weight
            "evidence_quality": 0.25,       # 25% weight  
            "analysis_depth": 0.25,         # 25% weight
            "logical_flow": 0.15,           # 15% weight
            "writing_clarity": 0.15         # 15% weight
        }
        
        # Register tool functions with explicit names for Agno toolkit discovery
        self.register(self.generate_research_report, name='generate_research_report')
        self.register(self.analyze_report_quality, name='analyze_report_quality')
        self.register(self.enhance_report_section, name='enhance_report_section')
        self.register(self.generate_report_outline, name='generate_report_outline')
        self.register(self.generate_methodology_guide, name='generate_methodology_guide')
        self.register(self.generate_evidence_requirements, name='generate_evidence_requirements')
        self.register(self.generate_quality_checklist, name='generate_quality_checklist')
        self.register(self.calculate_initial_quality_score, name='calculate_initial_quality_score')
    
    @property
    def max_word_count(self) -> int:
        """Maximum word count for a research report (default: 2x min_word_count)."""
        return self.min_word_count * 2

    async def generate_research_report(self, topic: str, 
                                     research_data: Optional[str] = None,
                                     report_type: str = "academic") -> str:
        """Generate a comprehensive research report on the given topic."""
        try:
            # Validate inputs
            if not isinstance(topic, str) or not topic.strip():
                logger.error("Invalid topic: %s", topic)
                return json.dumps({"error": "Topic must be a non-empty string"})
            
            logger.info("Generating research report for topic: %s", topic)
            
            # Build comprehensive report structure
            report_sections = []
            
            # Add instructions and examples if enabled
            if self.research_instructions:
                report_sections.append(f"## HƯỚNG DẪN VIẾT BÁO CÁO\n{self.research_instructions}\n")
            
            if self.few_shot_examples:
                report_sections.append(f"## VÍ DỤ MẪU THAM KHẢO\n{self.few_shot_examples}\n")
            
            # Generate report outline
            report_outline = self._generate_report_outline(topic, report_type)
            report_sections.append(f"## CẤU TRÚC BÁO CÁO CHO CHỦ ĐỀ: '{topic}'\n{report_outline}\n")
            
            # Add research methodology guidance
            methodology_guide = self._generate_methodology_guide(topic, research_data)
            report_sections.append(f"## HƯỚNG DẪN PHƯƠNG PHÁP NGHIÊN CỨU\n{methodology_guide}\n")
            
            # Add evidence requirements
            evidence_guide = self._generate_evidence_requirements(topic)
            report_sections.append(f"## YÊU CẦU VỀ BẰNG CHỨNG VÀ DỮ LIỆU\n{evidence_guide}\n")
            
            # Add quality checklist
            quality_checklist = self._generate_quality_checklist()
            report_sections.append(f"## TIÊU CHUẨN ĐÁNH GIÁ CHẤT LƯỢNG\n{quality_checklist}\n")
            
            output = "\n".join(report_sections)
            
            # Build JSON tool call
            tool_call = {
                "tool": "generate_research_report",
                "parameters": {
                    "topic": topic,
                    "report_type": report_type,
                    "min_word_count": self.min_word_count,
                    "research_data_provided": bool(research_data),
                    "metadata": {
                        "type": "application/json",
                        "version": "1.0",
                        "timestamp": logger.name
                    }
                }
            }
            
            logger.info("Research report framework generated successfully for: %s", topic)
            
            return json.dumps({
                "tool_call": tool_call,
                "output": output,
                "word_count_target": self.min_word_count,
                "quality_score": self._calculate_initial_quality_score(output)
            }, indent=2)
            
        except Exception as e:
            logger.error("Error generating research report: %s", str(e))
            return json.dumps({"error": f"Research report generation failed: {str(e)}"})
    
    async def analyze_report_quality(self, report_content: str) -> str:
        """Analyze the quality of a research report and provide improvement suggestions."""
        try:
            if not isinstance(report_content, str) or not report_content.strip():
                return json.dumps({"error": "Report content must be provided"})
            
            logger.info("Analyzing report quality")
            
            quality_analysis = []
            quality_scores = {}
            
            # Analyze structure completeness
            structure_score = self._analyze_structure(report_content)
            quality_scores["structure"] = structure_score
            quality_analysis.append(f"### Phân tích cấu trúc (Điểm: {structure_score}/10)")
            quality_analysis.append(self._get_structure_feedback(structure_score))
            
            # Analyze evidence quality
            evidence_score = self._analyze_evidence(report_content)
            quality_scores["evidence"] = evidence_score
            quality_analysis.append(f"### Chất lượng bằng chứng (Điểm: {evidence_score}/10)")
            quality_analysis.append(self._get_evidence_feedback(evidence_score))
            
            # Analyze depth of analysis
            analysis_score = self._analyze_depth(report_content)
            quality_scores["analysis"] = analysis_score
            quality_analysis.append(f"### Độ sâu phân tích (Điểm: {analysis_score}/10)")
            quality_analysis.append(self._get_analysis_feedback(analysis_score))
            
            # Calculate overall score
            overall_score = sum(quality_scores.values()) / len(quality_scores)
            quality_analysis.insert(0, f"## ĐÁNH GIÁ TỔNG QUAN (Điểm: {overall_score:.1f}/10)\n")
            
            # Add improvement suggestions
            improvements = self._generate_improvement_suggestions(quality_scores)
            quality_analysis.append(f"### ĐỀ XUẤT CẢI THIỆN\n{improvements}")
            
            output = "\n".join(quality_analysis)
            
            tool_call = {
                "tool": "analyze_report_quality",
                "parameters": {
                    "overall_score": overall_score,
                    "detailed_scores": quality_scores,
                    "word_count": len(report_content.split()),
                    "metadata": {"type": "quality_analysis"}
                }
            }
            
            return json.dumps({
                "tool_call": tool_call,
                "output": output,
                "quality_scores": quality_scores,
                "overall_score": overall_score
            }, indent=2)
            
        except Exception as e:
            logger.error("Error analyzing report quality: %s", str(e))
            return json.dumps({"error": f"Quality analysis failed: {str(e)}"})
    
    async def enhance_report_section(self, section_title: str, current_content: str, 
                                   topic: str) -> str:
        """Enhance a specific section of the research report."""
        try:
            if not all([section_title.strip(), current_content.strip(), topic.strip()]):
                return json.dumps({"error": "All parameters must be provided"})
            
            logger.info("Enhancing report section: %s", section_title)
            
            enhancement_guide = []
            
            # Section-specific enhancement guidelines
            section_guides = {
                "introduction": self._get_introduction_enhancement(),
                "methodology": self._get_methodology_enhancement(),
                "analysis": self._get_analysis_enhancement(),
                "discussion": self._get_discussion_enhancement(),
                "conclusion": self._get_conclusion_enhancement()
            }
            
            section_key = section_title.lower().replace(" ", "_")
            specific_guide = section_guides.get(section_key, self._get_general_enhancement())
            
            enhancement_guide.append(f"## CẢI THIỆN PHẦN: {section_title.upper()}")
            enhancement_guide.append(f"### Hướng dẫn cụ thể:\n{specific_guide}")
            
            # Add content analysis
            content_analysis = self._analyze_section_content(current_content, section_title)
            enhancement_guide.append(f"### Phân tích nội dung hiện tại:\n{content_analysis}")
            
            # Add enhancement suggestions
            enhancement_suggestions = self._generate_section_improvements(current_content, section_title, topic)
            enhancement_guide.append(f"### Đề xuất cải thiện cụ thể:\n{enhancement_suggestions}")
            
            output = "\n".join(enhancement_guide)
            
            tool_call = {
                "tool": "enhance_report_section",
                "parameters": {
                    "section": section_title,
                    "topic": topic,
                    "current_length": len(current_content.split()),
                    "metadata": {"type": "section_enhancement"}
                }
            }
            
            return json.dumps({
                "tool_call": tool_call,
                "output": output
            }, indent=2)
            
        except Exception as e:
            logger.error("Error enhancing report section: %s", str(e))
            return json.dumps({"error": f"Section enhancement failed: {str(e)}"})
    
    def generate_report_outline(self, topic: str, report_type: str) -> str:
        """Generate a detailed report outline as JSON."""
        outline = dedent(f"""
            ### 1. TÓM TẮT ĐIỀU HÀNH (300-400 từ)
            - Vấn đề nghiên cứu chính về {topic}
            - Phương pháp nghiên cứu được sử dụng
            - 3-4 phát hiện quan trọng nhất
            - Khuyến nghị hành động cụ thể
            
            ### 2. GIỚI THIỆU VÀ BỐI CẢNH ({int(self.min_word_count * 0.2)} từ)
            - Mô tả chi tiết vấn đề {topic}
            - Bối cảnh lịch sử và hiện tại
            - Tầm quan trọng và tác động của vấn đề
            - Mục tiêu nghiên cứu và câu hỏi cụ thể
            - Tổng quan nghiên cứu liên quan
            
            ### 3. PHƯƠNG PHÁP NGHIÊN CỨU ({int(self.min_word_count * 0.15)} từ)
            - Thiết kế nghiên cứu và lý do lựa chọn
            - Nguồn dữ liệu và phương pháp thu thập
            - Mẫu nghiên cứu và cách chọn mẫu
            - Công cụ và kỹ thuật phân tích
            - Hạn chế của phương pháp
            
            ### 4. PHÂN TÍCH VÀ KẾT QUẢ ({int(self.min_word_count * 0.4)} từ)
            - Trình bày dữ liệu có hệ thống
            - Phân tích xu hướng và mô hình
            - So sánh các nhóm/giai đoạn khác nhau
            - Phát hiện bất ngờ hoặc ngoại lệ
            - Minh họa bằng biểu đồ/bảng số liệu
            
            ### 5. THẢO LUẬN ({int(self.min_word_count * 0.15)} từ)
            - Giải thích ý nghĩa của kết quả
            - So sánh với nghiên cứu trước đây
            - Thảo luận các yếu tố ảnh hưởng
            - Đánh giá độ tin cậy
            
            ### 6. KẾT LUẬN VÀ KHUYẾN NGHỊ ({int(self.min_word_count * 0.1)} từ)
            - Tóm tắt các phát hiện chính
            - Trả lời câu hỏi nghiên cứu
            - Khuyến nghị hướng nghiên cứu tiếp theo
            - Đề xuất giải pháp thực tiễn
        """)
        return json.dumps({"outline": outline, "topic": topic, "report_type": report_type})

    def generate_methodology_guide(self, topic: str, research_data: Optional[str] = None) -> str:
        """Generate methodology guidance as JSON."""
        data_status = "Có dữ liệu được cung cấp" if research_data else "Cần thu thập dữ liệu"
        guide = dedent(f"""
            ### Phương pháp phù hợp cho chủ đề \"{topic}\":
            
            **Trạng thái dữ liệu:** {data_status}
            
            **Phương pháp đề xuất:**
            1. **Nghiên cứu tài liệu (Literature Review)**
               - Tìm kiếm 50-100 nghiên cứu liên quan
               - Ưu tiên tài liệu 5 năm gần đây
               - Bao gồm cả nguồn quốc tế và trong nước
            
            2. **Phân tích dữ liệu thứ cấp**
               - Thu thập từ các cơ quan chính thức
               - Kiểm tra độ tin cậy và tính cập nhật
               - So sánh nhiều nguồn khác nhau
            
            3. **Nghiên cứu thực nghiệm (nếu áp dụng)**
               - Thiết kế khảo sát hoặc thí nghiệm
               - Xác định mẫu đại diện
               - Sử dụng công cụ đo tin cậy
            
            **Tiêu chuẩn chất lượng dữ liệu:**
            - Tính đại diện: Mẫu phản ánh tổng thể
            - Tính tin cậy: Nguồn uy tín, phương pháp chuẩn
            - Tính cập nhật: Dữ liệu mới nhất có thể
            - Tính đầy đủ: Đủ thông tin để kết luận
        """)
        return json.dumps({"methodology_guide": guide, "topic": topic, "data_status": data_status})

    def generate_evidence_requirements(self, topic: str) -> str:
        """Generate evidence requirements as JSON."""
        requirements = dedent(f"""
            ### Yêu cầu bằng chứng cho chủ đề \"{topic}\":
            
            **Số lượng bằng chứng tối thiểu:**
            - Mỗi luận điểm chính: 3-5 nguồn độc lập
            - Mỗi số liệu quan trọng: 2-3 nguồn xác nhận
            - Tổng số tham khảo: Tối thiểu 30-50 nguồn
            
            **Loại bằng chứng ưu tiên:**
            1. **Nguồn sơ cấp (Primary Sources)**
               - Nghiên cứu khoa học peer-reviewed
               - Dữ liệu chính thức từ cơ quan nhà nước
               - Báo cáo gốc của tổ chức uy tín
            
            2. **Nguồn thứ cấp chất lượng cao**
               - Meta-analysis và systematic review
               - Báo cáo của tổ chức quốc tế
               - Sách chuyên khảo của chuyên gia
            
            3. **Bằng chứng thực nghiệm**
               - Số liệu thống kê chính thức
               - Kết quả khảo sát đại diện
               - Dữ liệu quan sát dài hạn
            
            **Tiêu chí đánh giá nguồn:**
            - Tác giả có chuyên môn phù hợp
            - Tổ chức xuất bản uy tín
            - Phương pháp nghiên cứu minh bạch
            - Kết quả có thể tái lập
            - Không có xung đột lợi ích rõ ràng
            
            **Cách trích dẫn chuẩn:**
            - Format APA hoặc Harvard
            - Đầy đủ thông tin: tác giả, năm, tiêu đề, nguồn
            - Phân biệt rõ trích dẫn trực tiếp và gián tiếp
            - Ghi rõ số trang cho trích dẫn trực tiếp
        """)
        return json.dumps({"evidence_requirements": requirements, "topic": topic})

    def generate_quality_checklist(self) -> str:
        """Generate a comprehensive quality checklist as JSON."""
        checklist = dedent("""\
            ### BẢNG KIỂM TRA CHẤT LƯỢNG BÁO CÁO:
            
            **A. CẤU TRÚC VÀ ĐỊNH DẠNG (20 điểm)**
            □ Có đầy đủ 6 phần chính theo cấu trúc chuẩn
            □ Tỷ lệ độ dài các phần hợp lý
            □ Tiêu đề và phụ đề rõ ràng, có hệ thống
            □ Định dạng nhất quán, chuyên nghiệp
            
            **B. NỘI DUNG VÀ BẰNG CHỨNG (25 điểm)**
            □ Mỗi luận điểm có ít nhất 2-3 bằng chứng
            □ Nguồn tham khảo đa dạng và uy tín
            □ Dữ liệu được cập nhật và chính xác
            □ Trích dẫn đầy đủ và chính xách
            
            **C. PHÂN TÍCH VÀ KẾT QUẢ (25 điểm)**
            □ Phân tích sâu sắc, có hệ thống
            □ Kết quả được trình bày rõ ràng, dễ hiểu
            □ So sánh và đối chiếu với nghiên cứu trước đây
            □ Thảo luận các yếu tố ảnh hưởng đến kết quả
            
            **D. THẢO LUẬN VÀ KHUYẾN NGHỊ (15 điểm)**
            □ Giải thích ý nghĩa của kết quả một cách logic
            □ Đề xuất khuyến nghị cụ thể, khả thi
            □ Đánh giá độ tin cậy của kết quả
            
            **E. KẾT LUẬN VÀ KHUYẾN NGHỊ (15 điểm)**
            □ Tóm tắt các phát hiện chính một cách ngắn gọn
            □ Trả lời câu hỏi nghiên cứu
            □ Khuyến nghị hướng nghiên cứu tiếp theo
            □ Đề xuất giải pháp thực tiễn
            □ Không có thông tin mới trong kết luận
            □ Không có lỗi chính tả, ngữ pháp
        """)
        return json.dumps({"quality_checklist": checklist})

    def calculate_initial_quality_score(self, report_content: str) -> str:
        """Calculate an initial quality score as JSON."""
        word_count = len(report_content.split())
        if word_count < self.min_word_count or word_count > self.max_word_count:
            score = 0.0
        else:
            score = 1.0
        return json.dumps({"initial_quality_score": score, "word_count": word_count})

        # Register new JSON-returning tools
        self.register(self.generate_report_outline, name='generate_report_outline')
        self.register(self.generate_methodology_guide, name='generate_methodology_guide')
        self.register(self.generate_evidence_requirements, name='generate_evidence_requirements')
        self.register(self.generate_quality_checklist, name='generate_quality_checklist')
        self.register(self.calculate_initial_quality_score, name='calculate_initial_quality_score')
    
    @property
    def max_word_count(self) -> int:
        """Maximum word count for a research report (default: 2x min_word_count)."""
        return self.min_word_count * 2

    async def generate_research_report(self, topic: str, 
                                     research_data: Optional[str] = None,
                                     report_type: str = "academic") -> str:
        """Generate a comprehensive research report on the given topic."""
        try:
            # Validate inputs
            if not isinstance(topic, str) or not topic.strip():
                logger.error("Invalid topic: %s", topic)
                return json.dumps({"error": "Topic must be a non-empty string"})
            
            logger.info("Generating research report for topic: %s", topic)
            
            # Build comprehensive report structure
            report_sections = []
            
            # Add instructions and examples if enabled
            if self.research_instructions:
                report_sections.append(f"## HƯỚNG DẪN VIẾT BÁO CÁO\n{self.research_instructions}\n")
            
            if self.few_shot_examples:
                report_sections.append(f"## VÍ DỤ MẪU THAM KHẢO\n{self.few_shot_examples}\n")
            
            # Generate report outline
            report_outline = self._generate_report_outline(topic, report_type)
            report_sections.append(f"## CẤU TRÚC BÁO CÁO CHO CHỦ ĐỀ: '{topic}'\n{report_outline}\n")
            
            # Add research methodology guidance
            methodology_guide = self._generate_methodology_guide(topic, research_data)
            report_sections.append(f"## HƯỚNG DẪN PHƯƠNG PHÁP NGHIÊN CỨU\n{methodology_guide}\n")
            
            # Add evidence requirements
            evidence_guide = self._generate_evidence_requirements(topic)
            report_sections.append(f"## YÊU CẦU VỀ BẰNG CHỨNG VÀ DỮ LIỆU\n{evidence_guide}\n")
            
            # Add quality checklist
            quality_checklist = self._generate_quality_checklist()
            report_sections.append(f"## TIÊU CHUẨN ĐÁNH GIÁ CHẤT LƯỢNG\n{quality_checklist}\n")
            
            output = "\n".join(report_sections)
            
            # Build JSON tool call
            tool_call = {
                "tool": "generate_research_report",
                "parameters": {
                    "topic": topic,
                    "report_type": report_type,
                    "min_word_count": self.min_word_count,
                    "research_data_provided": bool(research_data),
                    "metadata": {
                        "type": "application/json",
                        "version": "1.0",
                        "timestamp": logger.name
                    }
                }
            }
            
            logger.info("Research report framework generated successfully for: %s", topic)
            
            return json.dumps({
                "tool_call": tool_call,
                "output": output,
                "word_count_target": self.min_word_count,
                "quality_score": self._calculate_initial_quality_score(output)
            }, indent=2)
            
        except Exception as e:
            logger.error("Error generating research report: %s", str(e))
            return json.dumps({"error": f"Research report generation failed: {str(e)}"})
    
    async def analyze_report_quality(self, report_content: str) -> str:
        """Analyze the quality of a research report and provide improvement suggestions."""
        try:
            if not isinstance(report_content, str) or not report_content.strip():
                return json.dumps({"error": "Report content must be provided"})
            
            logger.info("Analyzing report quality")
            
            quality_analysis = []
            quality_scores = {}
            
            # Analyze structure completeness
            structure_score = self._analyze_structure(report_content)
            quality_scores["structure"] = structure_score
            quality_analysis.append(f"### Phân tích cấu trúc (Điểm: {structure_score}/10)")
            quality_analysis.append(self._get_structure_feedback(structure_score))
            
            # Analyze evidence quality
            evidence_score = self._analyze_evidence(report_content)
            quality_scores["evidence"] = evidence_score
            quality_analysis.append(f"### Chất lượng bằng chứng (Điểm: {evidence_score}/10)")
            quality_analysis.append(self._get_evidence_feedback(evidence_score))
            
            # Analyze depth of analysis
            analysis_score = self._analyze_depth(report_content)
            quality_scores["analysis"] = analysis_score
            quality_analysis.append(f"### Độ sâu phân tích (Điểm: {analysis_score}/10)")
            quality_analysis.append(self._get_analysis_feedback(analysis_score))
            
            # Calculate overall score
            overall_score = sum(quality_scores.values()) / len(quality_scores)
            quality_analysis.insert(0, f"## ĐÁNH GIÁ TỔNG QUAN (Điểm: {overall_score:.1f}/10)\n")
            
            # Add improvement suggestions
            improvements = self._generate_improvement_suggestions(quality_scores)
            quality_analysis.append(f"### ĐỀ XUẤT CẢI THIỆN\n{improvements}")
            
            output = "\n".join(quality_analysis)
            
            tool_call = {
                "tool": "analyze_report_quality",
                "parameters": {
                    "overall_score": overall_score,
                    "detailed_scores": quality_scores,
                    "word_count": len(report_content.split()),
                    "metadata": {"type": "quality_analysis"}
                }
            }
            
            return json.dumps({
                "tool_call": tool_call,
                "output": output,
                "quality_scores": quality_scores,
                "overall_score": overall_score
            }, indent=2)
            
        except Exception as e:
            logger.error("Error analyzing report quality: %s", str(e))
            return json.dumps({"error": f"Quality analysis failed: {str(e)}"})
    
    async def enhance_report_section(self, section_title: str, current_content: str, 
                                   topic: str) -> str:
        """Enhance a specific section of the research report."""
        try:
            if not all([section_title.strip(), current_content.strip(), topic.strip()]):
                return json.dumps({"error": "All parameters must be provided"})
            
            logger.info("Enhancing report section: %s", section_title)
            
            enhancement_guide = []
            
            # Section-specific enhancement guidelines
            section_guides = {
                "introduction": self._get_introduction_enhancement(),
                "methodology": self._get_methodology_enhancement(),
                "analysis": self._get_analysis_enhancement(),
                "discussion": self._get_discussion_enhancement(),
                "conclusion": self._get_conclusion_enhancement()
            }
            
            section_key = section_title.lower().replace(" ", "_")
            specific_guide = section_guides.get(section_key, self._get_general_enhancement())
            
            enhancement_guide.append(f"## CẢI THIỆN PHẦN: {section_title.upper()}")
            enhancement_guide.append(f"### Hướng dẫn cụ thể:\n{specific_guide}")
            
            # Add content analysis
            content_analysis = self._analyze_section_content(current_content, section_title)
            enhancement_guide.append(f"### Phân tích nội dung hiện tại:\n{content_analysis}")
            
            # Add enhancement suggestions
            enhancement_suggestions = self._generate_section_improvements(current_content, section_title, topic)
            enhancement_guide.append(f"### Đề xuất cải thiện cụ thể:\n{enhancement_suggestions}")
            
            output = "\n".join(enhancement_guide)
            
            tool_call = {
                "tool": "enhance_report_section",
                "parameters": {
                    "section": section_title,
                    "topic": topic,
                    "current_length": len(current_content.split()),
                    "metadata": {"type": "section_enhancement"}
                }
            }
            
            return json.dumps({
                "tool_call": tool_call,
                "output": output
            }, indent=2)
            
        except Exception as e:
            logger.error("Error enhancing report section: %s", str(e))
            return json.dumps({"error": f"Section enhancement failed: {str(e)}"})
    
    def generate_report_outline(self, topic: str, report_type: str) -> str:
        """Generate a detailed report outline as JSON."""
        outline = dedent(f"""
            ### 1. TÓM TẮT ĐIỀU HÀNH (300-400 từ)
            - Vấn đề nghiên cứu chính về {topic}
            - Phương pháp nghiên cứu được sử dụng
            - 3-4 phát hiện quan trọng nhất
            - Khuyến nghị hành động cụ thể
            
            ### 2. GIỚI THIỆU VÀ BỐI CẢNH ({int(self.min_word_count * 0.2)} từ)
            - Mô tả chi tiết vấn đề {topic}
            - Bối cảnh lịch sử và hiện tại
            - Tầm quan trọng và tác động của vấn đề
            - Mục tiêu nghiên cứu và câu hỏi cụ thể
            - Tổng quan nghiên cứu liên quan
            
            ### 3. PHƯƠNG PHÁP NGHIÊN CỨU ({int(self.min_word_count * 0.15)} từ)
            - Thiết kế nghiên cứu và lý do lựa chọn
            - Nguồn dữ liệu và phương pháp thu thập
            - Mẫu nghiên cứu và cách chọn mẫu
            - Công cụ và kỹ thuật phân tích
            - Hạn chế của phương pháp
            
            ### 4. PHÂN TÍCH VÀ KẾT QUẢ ({int(self.min_word_count * 0.4)} từ)
            - Trình bày dữ liệu có hệ thống
            - Phân tích xu hướng và mô hình
            - So sánh các nhóm/giai đoạn khác nhau
            - Phát hiện bất ngờ hoặc ngoại lệ
            - Minh họa bằng biểu đồ/bảng số liệu
            
            ### 5. THẢO LUẬN ({int(self.min_word_count * 0.15)} từ)
            - Giải thích ý nghĩa của kết quả
            - So sánh với nghiên cứu trước đây
            - Thảo luận các yếu tố ảnh hưởng
            - Đánh giá độ tin cậy
            
            ### 6. KẾT LUẬN VÀ KHUYẾN NGHỊ ({int(self.min_word_count * 0.1)} từ)
            - Tóm tắt các phát hiện chính
            - Trả lời câu hỏi nghiên cứu
            - Khuyến nghị hướng nghiên cứu tiếp theo
            - Đề xuất giải pháp thực tiễn
        """)
        return json.dumps({"outline": outline, "topic": topic, "report_type": report_type})

    def generate_methodology_guide(self, topic: str, research_data: Optional[str] = None) -> str:
        """Generate methodology guidance as JSON."""
        data_status = "Có dữ liệu được cung cấp" if research_data else "Cần thu thập dữ liệu"
        guide = dedent(f"""
            ### Phương pháp phù hợp cho chủ đề \"{topic}\":
            
            **Trạng thái dữ liệu:** {data_status}
            
            **Phương pháp đề xuất:**
            1. **Nghiên cứu tài liệu (Literature Review)**
               - Tìm kiếm 50-100 nghiên cứu liên quan
               - Ưu tiên tài liệu 5 năm gần đây
               - Bao gồm cả nguồn quốc tế và trong nước
            
            2. **Phân tích dữ liệu thứ cấp**
               - Thu thập từ các cơ quan chính thức
               - Kiểm tra độ tin cậy và tính cập nhật
               - So sánh nhiều nguồn khác nhau
            
            3. **Nghiên cứu thực nghiệm (nếu áp dụng)**
               - Thiết kế khảo sát hoặc thí nghiệm
               - Xác định mẫu đại diện
               - Sử dụng công cụ đo tin cậy
            
            **Tiêu chuẩn chất lượng dữ liệu:**
            - Tính đại diện: Mẫu phản ánh tổng thể
            - Tính tin cậy: Nguồn uy tín, phương pháp chuẩn
            - Tính cập nhật: Dữ liệu mới nhất có thể
            - Tính đầy đủ: Đủ thông tin để kết luận
        """)
        return json.dumps({"methodology_guide": guide, "topic": topic, "data_status": data_status})

    def generate_evidence_requirements(self, topic: str) -> str:
        """Generate evidence requirements as JSON."""
        requirements = dedent(f"""
            ### Yêu cầu bằng chứng cho chủ đề \"{topic}\":
            
            **Số lượng bằng chứng tối thiểu:**
            - Mỗi luận điểm chính: 3-5 nguồn độc lập
            - Mỗi số liệu quan trọng: 2-3 nguồn xác nhận
            - Tổng số tham khảo: Tối thiểu 30-50 nguồn
            
            **Loại bằng chứng ưu tiên:**
            1. **Nguồn sơ cấp (Primary Sources)**
               - Nghiên cứu khoa học peer-reviewed
               - Dữ liệu chính thức từ cơ quan nhà nước
               - Báo cáo gốc của tổ chức uy tín
            
            2. **Nguồn thứ cấp chất lượng cao**
               - Meta-analysis và systematic review
               - Báo cáo của tổ chức quốc tế
               - Sách chuyên khảo của chuyên gia
            
            3. **Bằng chứng thực nghiệm**
               - Số liệu thống kê chính thức
               - Kết quả khảo sát đại diện
               - Dữ liệu quan sát dài hạn
            
            **Tiêu chí đánh giá nguồn:**
            - Tác giả có chuyên môn phù hợp
            - Tổ chức xuất bản uy tín
            - Phương pháp nghiên cứu minh bạch
            - Kết quả có thể tái lập
            - Không có xung đột lợi ích rõ ràng
            
            **Cách trích dẫn chuẩn:**
            - Format APA hoặc Harvard
            - Đầy đủ thông tin: tác giả, năm, tiêu đề, nguồn
            - Phân biệt rõ trích dẫn trực tiếp và gián tiếp
            - Ghi rõ số trang cho trích dẫn trực tiếp
        """)
        return json.dumps({"evidence_requirements": requirements, "topic": topic})

    def generate_quality_checklist(self) -> str:
        """Generate a comprehensive quality checklist as JSON."""
        checklist = dedent("""\
            ### BẢNG KIỂM TRA CHẤT LƯỢNG BÁO CÁO:
            
            **A. CẤU TRÚC VÀ ĐỊNH DẠNG (20 điểm)**
            □ Có đầy đủ 6 phần chính theo cấu trúc chuẩn
            □ Tỷ lệ độ dài các phần hợp lý
            □ Tiêu đề và phụ đề rõ ràng, có hệ thống
            □ Định dạng nhất quán, chuyên nghiệp
            
            **B. NỘI DUNG VÀ BẰNG CHỨNG (25 điểm)**
            □ Mỗi luận điểm có ít nhất 2-3 bằng chứng
            □ Nguồn tham khảo đa dạng và uy tín
            □ Dữ liệu được cập nhật và chính xác
            □ Trích dẫn đầy đủ và chính xách
            
            **C. PHÂN TÍCH VÀ KẾT QUẢ (25 điểm)**
            □ Phân tích sâu sắc, có hệ thống
            □ Kết quả được trình bày rõ ràng, dễ hiểu
            □ So sánh và đối chiếu với nghiên cứu trước đây
            □ Thảo luận các yếu tố ảnh hưởng đến kết quả
            
            **D. THẢO LUẬN VÀ KHUYẾN NGHỊ (15 điểm)**
            □ Giải thích ý nghĩa của kết quả một cách logic
            □ Đề xuất khuyến nghị cụ thể, khả thi
            □ Đánh giá độ tin cậy của kết quả
            
            **E. KẾT LUẬN VÀ KHUYẾN NGHỊ (15 điểm)**
            □ Tóm tắt các phát hiện chính một cách ngắn gọn
            □ Trả lời câu hỏi nghiên cứu
            □ Khuyến nghị hướng nghiên cứu tiếp theo
            □ Đề xuất giải pháp thực tiễn
            □ Không có thông tin mới trong kết luận
            □ Không có lỗi chính tả, ngữ pháp
        """)
        return json.dumps({"quality_checklist": checklist})

    def calculate_initial_quality_score(self, report_content: str) -> str:
        """Calculate an initial quality score as JSON."""
        word_count = len(report_content.split())
        if word_count < self.min_word_count or word_count > self.max_word_count:
            score = 0.0
        else:
            score = 1.0
        return json.dumps({"initial_quality_score": score, "word_count": word_count})

    def _generate_report_outline(self, topic: str, report_type: str) -> str:
        # Deprecated: use generate_report_outline instead
        return self.generate_report_outline(topic, report_type)

    def _generate_methodology_guide(self, topic: str, research_data: Optional[str]) -> str:
        # Deprecated: use generate_methodology_guide instead
        return self.generate_methodology_guide(topic, research_data)

    def _generate_evidence_requirements(self, topic: str) -> str:
        # Deprecated: use generate_evidence_requirements instead
        return self.generate_evidence_requirements(topic)

    def _generate_quality_checklist(self) -> str:
        # Deprecated: use generate_quality_checklist instead
        return self.generate_quality_checklist()

    def _calculate_initial_quality_score(self, report_content: str) -> float:
        # Deprecated: use calculate_initial_quality_score instead
        try:
            result = self.calculate_initial_quality_score(report_content)
            parsed = json.loads(result)
            return parsed.get("output", 0.0)
        except Exception:
            return 0.0

    def register_all_helpers(self):
        self.register(self.generate_report_outline, name='generate_report_outline')
        self.register(self.generate_methodology_guide, name='generate_methodology_guide')
        self.register(self.generate_evidence_requirements, name='generate_evidence_requirements')
        self.register(self.generate_quality_checklist, name='generate_quality_checklist')
        self.register(self.calculate_initial_quality_score, name='calculate_initial_quality_score')

if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)
    logger.info("Starting toolkit test...")
    toolkit = WriterResearchTools()
    async def main():
        report = await toolkit.generate_research_report("AI và Việc làm trong công nghiệp")
        print(report)
        logger.info("Report generation completed.")
    asyncio.run(main())
# This code is a comprehensive toolkit for generating high-quality scientific research reports, including detailed instructions, examples, and quality criteria. It provides methods for generating reports, analyzing their quality, and enhancing specific sections based on structured guidelines. The code is designed to be extensible and adaptable for various research topics and report types.