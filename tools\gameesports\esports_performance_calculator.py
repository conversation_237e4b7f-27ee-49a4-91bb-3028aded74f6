# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math
from datetime import datetime

class EsportsPerformanceCalculator(Toolkit):
    """
    Esports Performance Calculator cho tính toán player performance, team rankings và tournament predictions.
    """

    def __init__(self, enable_calculations: bool = True, **kwargs):
        super().__init__(
            name="esports_performance_calculator",
            **kwargs
        )

        # Performance rating scales
        self.rating_scales = {
            "player": {"min": 800, "max": 3000, "average": 1500},
            "team": {"min": 1000, "max": 2500, "average": 1600},
            "tournament": {"min": 1, "max": 10, "average": 5.5}
        }

        # Game-specific factors
        self.game_factors = {
            "lol": {"skill_weight": 0.4, "team_weight": 0.6, "meta_impact": 0.3},
            "csgo": {"skill_weight": 0.6, "team_weight": 0.4, "meta_impact": 0.2},
            "dota2": {"skill_weight": 0.5, "team_weight": 0.5, "meta_impact": 0.4},
            "valorant": {"skill_weight": 0.5, "team_weight": 0.5, "meta_impact": 0.3},
            "overwatch": {"skill_weight": 0.3, "team_weight": 0.7, "meta_impact": 0.5}
        }

        # Performance metrics weights
        self.metric_weights = {
            "win_rate": 0.35,
            "kda": 0.25,
            "consistency": 0.20,
            "clutch_performance": 0.15,
            "recent_form": 0.05
        }

        if enable_calculations:
            self.register(self.calculate_player_performance)
            self.register(self.estimate_team_ranking)
            self.register(self.analyze_tournament_predictions)
            self.register(self.predict_match_outcomes)

    def calculate_player_performance(self, player_name: str, game: str = "lol",
                                   matches_played: int = 50, win_rate: float = 0.65,
                                   kda_ratio: float = 2.1) -> str:
        """
        Tính toán player performance rating và analysis.

        Args:
            player_name: Tên player
            game: Game cụ thể
            matches_played: Số matches đã chơi
            win_rate: Tỷ lệ thắng (0.0-1.0)
            kda_ratio: Tỷ lệ KDA

        Returns:
            Chuỗi JSON chứa tính toán player performance
        """
        log_debug(f"Calculating player performance for {player_name} in {game}")

        try:
            # Get game-specific factors
            game_params = self.game_factors.get(game.lower(), self.game_factors["lol"])

            # Calculate base performance score
            base_score = self._calculate_base_performance_score(win_rate, kda_ratio, matches_played)

            # Apply game-specific adjustments
            adjusted_score = base_score * (1 + game_params["skill_weight"] * 0.1)

            # Calculate performance rating
            performance_rating = self._convert_to_rating_scale(adjusted_score, "player")

            # Performance breakdown
            performance_breakdown = {
                "win_rate_score": round(win_rate * 100 * self.metric_weights["win_rate"], 2),
                "kda_score": round(min(kda_ratio * 20, 50) * self.metric_weights["kda"], 2),
                "consistency_score": round(self._calculate_consistency_score(matches_played) * self.metric_weights["consistency"], 2),
                "experience_bonus": round(self._calculate_experience_bonus(matches_played), 2)
            }

            # Performance tier classification
            performance_tier = self._classify_performance_tier(performance_rating)

            # Strengths and weaknesses analysis
            strengths_weaknesses = self._analyze_player_strengths_weaknesses(win_rate, kda_ratio, game)

            # Improvement recommendations
            improvement_areas = self._generate_improvement_recommendations(performance_breakdown, game)

            # Performance predictions
            future_performance = self._predict_future_performance(performance_rating, win_rate, matches_played)

            result = {
                "player_analysis": {
                    "player_name": player_name,
                    "game": game,
                    "matches_analyzed": matches_played,
                    "analysis_date": datetime.now().strftime("%Y-%m-%d")
                },
                "performance_metrics": {
                    "overall_rating": round(performance_rating, 0),
                    "base_score": round(base_score, 2),
                    "adjusted_score": round(adjusted_score, 2),
                    "win_rate": f"{win_rate*100:.1f}%",
                    "kda_ratio": kda_ratio
                },
                "performance_breakdown": performance_breakdown,
                "performance_tier": performance_tier,
                "strengths_weaknesses": strengths_weaknesses,
                "improvement_areas": improvement_areas,
                "future_performance": future_performance,
                "ranking_context": self._provide_ranking_context(performance_rating, game),
                "calculation_metadata": {
                    "game_factors_applied": game_params,
                    "metric_weights_used": self.metric_weights,
                    "calculation_version": "1.0"
                }
            }

            return json.dumps(result, indent=4)

        except Exception as e:
            logger.error(f"Error calculating player performance: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate player performance: {str(e)}"
            }, indent=4)

    def estimate_team_ranking(self, team_name: str, game: str = "lol",
                            team_matches: int = 30, team_win_rate: float = 0.70,
                            average_player_rating: float = 1800) -> str:
        """
        Ước tính team ranking và team strength analysis.

        Args:
            team_name: Tên team
            game: Game cụ thể
            team_matches: Số matches team đã chơi
            team_win_rate: Tỷ lệ thắng của team
            average_player_rating: Rating trung bình của players

        Returns:
            Chuỗi JSON chứa ước tính team ranking
        """
        log_debug(f"Estimating team ranking for {team_name} in {game}")

        try:
            # Get game-specific factors
            game_params = self.game_factors.get(game.lower(), self.game_factors["lol"])

            # Calculate team strength score
            team_strength = self._calculate_team_strength(team_win_rate, average_player_rating, team_matches, game_params)

            # Convert to team rating
            team_rating = self._convert_to_rating_scale(team_strength, "team")

            # Estimate ranking position
            estimated_ranking = self._estimate_ranking_position(team_rating, game)

            # Team performance analysis
            team_analysis = {
                "individual_skill_level": round(average_player_rating, 0),
                "team_coordination": round(team_win_rate * game_params["team_weight"] * 100, 1),
                "experience_factor": round(self._calculate_team_experience_factor(team_matches), 2),
                "meta_adaptation": round(game_params["meta_impact"] * 85, 1)
            }

            # Competitive tier classification
            competitive_tier = self._classify_team_tier(team_rating)

            # Tournament performance predictions
            tournament_predictions = self._predict_tournament_performance(team_rating, team_win_rate, game)

            # Team development trajectory
            development_trajectory = self._analyze_team_development(team_rating, team_matches, team_win_rate)

            result = {
                "team_analysis": {
                    "team_name": team_name,
                    "game": game,
                    "matches_analyzed": team_matches,
                    "analysis_date": datetime.now().strftime("%Y-%m-%d")
                },
                "ranking_estimation": {
                    "estimated_team_rating": round(team_rating, 0),
                    "estimated_ranking_position": estimated_ranking,
                    "team_strength_score": round(team_strength, 2),
                    "confidence_level": "High" if team_matches > 20 else "Medium"
                },
                "team_analysis": team_analysis,
                "competitive_tier": competitive_tier,
                "tournament_predictions": tournament_predictions,
                "development_trajectory": development_trajectory,
                "comparison_metrics": self._generate_team_comparisons(team_rating, game),
                "strategic_recommendations": self._generate_team_recommendations(team_analysis, competitive_tier)
            }

            return json.dumps(result, indent=4)

        except Exception as e:
            logger.error(f"Error estimating team ranking: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to estimate team ranking: {str(e)}"
            }, indent=4)

    def analyze_tournament_predictions(self, tournament_name: str, participating_teams: List[str],
                                     tournament_format: str = "double_elimination",
                                     prize_pool: float = 1000000) -> str:
        """
        Phân tích và dự đoán kết quả tournament.

        Args:
            tournament_name: Tên tournament
            participating_teams: Danh sách teams tham gia
            tournament_format: Format tournament
            prize_pool: Giải thưởng

        Returns:
            Chuỗi JSON chứa phân tích tournament predictions
        """
        log_debug(f"Analyzing tournament predictions for {tournament_name}")

        try:
            # Tournament complexity analysis
            tournament_complexity = self._analyze_tournament_complexity(len(participating_teams), tournament_format, prize_pool)

            # Team strength simulation
            team_strengths = self._simulate_team_strengths(participating_teams)

            # Tournament bracket predictions
            bracket_predictions = self._predict_tournament_bracket(team_strengths, tournament_format)

            # Prize distribution predictions
            prize_predictions = self._predict_prize_distribution(team_strengths, prize_pool)

            # Upset probability analysis
            upset_analysis = self._analyze_upset_probabilities(team_strengths, tournament_format)

            # Viewership and engagement predictions
            engagement_predictions = self._predict_tournament_engagement(tournament_complexity, team_strengths)

            result = {
                "tournament_analysis": {
                    "tournament_name": tournament_name,
                    "participating_teams_count": len(participating_teams),
                    "tournament_format": tournament_format,
                    "total_prize_pool": f"${prize_pool:,.0f}",
                    "analysis_date": datetime.now().strftime("%Y-%m-%d")
                },
                "tournament_complexity": tournament_complexity,
                "team_strength_analysis": team_strengths,
                "bracket_predictions": bracket_predictions,
                "prize_predictions": prize_predictions,
                "upset_analysis": upset_analysis,
                "engagement_predictions": engagement_predictions,
                "tournament_insights": self._generate_tournament_insights(bracket_predictions, upset_analysis),
                "betting_recommendations": self._generate_betting_recommendations(team_strengths, upset_analysis)
            }

            return json.dumps(result, indent=4)

        except Exception as e:
            logger.error(f"Error analyzing tournament predictions: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze tournament predictions: {str(e)}"
            }, indent=4)

    def predict_match_outcomes(self, team1: str, team2: str, game: str = "lol",
                             match_format: str = "bo3", recent_form1: float = 0.7,
                             recent_form2: float = 0.6) -> str:
        """
        Dự đoán kết quả match giữa hai teams.

        Args:
            team1: Tên team thứ nhất
            team2: Tên team thứ hai
            game: Game cụ thể
            match_format: Format match (bo1, bo3, bo5)
            recent_form1: Form gần đây của team1
            recent_form2: Form gần đây của team2

        Returns:
            Chuỗi JSON chứa dự đoán match outcome
        """
        log_debug(f"Predicting match outcome: {team1} vs {team2}")

        try:
            # Calculate team strengths
            team1_strength = self._estimate_team_strength_for_match(team1, recent_form1, game)
            team2_strength = self._estimate_team_strength_for_match(team2, recent_form2, game)

            # Calculate win probabilities
            win_probabilities = self._calculate_match_win_probabilities(team1_strength, team2_strength, match_format)

            # Match analysis
            match_analysis = self._analyze_match_dynamics(team1_strength, team2_strength, game)

            # Score predictions
            score_predictions = self._predict_match_scores(win_probabilities, match_format)

            # Key factors analysis
            key_factors = self._identify_match_key_factors(team1, team2, recent_form1, recent_form2)

            # Upset potential
            upset_potential = self._assess_upset_potential(team1_strength, team2_strength)

            result = {
                "match_prediction": {
                    "team1": team1,
                    "team2": team2,
                    "game": game,
                    "match_format": match_format,
                    "prediction_date": datetime.now().strftime("%Y-%m-%d %H:%M")
                },
                "team_strengths": {
                    "team1_strength": round(team1_strength, 2),
                    "team2_strength": round(team2_strength, 2),
                    "strength_difference": round(abs(team1_strength - team2_strength), 2)
                },
                "win_probabilities": win_probabilities,
                "match_analysis": match_analysis,
                "score_predictions": score_predictions,
                "key_factors": key_factors,
                "upset_potential": upset_potential,
                "betting_insights": self._generate_match_betting_insights(win_probabilities, upset_potential),
                "confidence_level": self._assess_prediction_confidence(team1_strength, team2_strength, match_format)
            }

            return json.dumps(result, indent=4)

        except Exception as e:
            logger.error(f"Error predicting match outcome: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to predict match outcome: {str(e)}"
            }, indent=4)

    # Helper methods
    def _calculate_base_performance_score(self, win_rate: float, kda: float, matches: int) -> float:
        """Calculate base performance score."""
        win_score = win_rate * 100
        kda_score = min(kda * 20, 50)
        experience_score = min(matches / 100 * 20, 20)
        return (win_score + kda_score + experience_score) / 3

    def _convert_to_rating_scale(self, score: float, scale_type: str) -> float:
        """Convert score to rating scale."""
        scale = self.rating_scales[scale_type]
        normalized_score = max(0, min(score / 100, 1))
        return scale["min"] + (scale["max"] - scale["min"]) * normalized_score

    def _calculate_consistency_score(self, matches: int) -> float:
        """Calculate consistency score based on matches played."""
        return min(matches / 50 * 100, 100)

    def _calculate_experience_bonus(self, matches: int) -> float:
        """Calculate experience bonus."""
        return min(matches / 200 * 10, 10)

    def _classify_performance_tier(self, rating: float) -> dict:
        """Classify performance tier."""
        if rating >= 2500:
            return {"tier": "Professional", "description": "Elite professional level"}
        elif rating >= 2000:
            return {"tier": "Semi-Professional", "description": "High competitive level"}
        elif rating >= 1500:
            return {"tier": "Advanced", "description": "Strong competitive player"}
        elif rating >= 1200:
            return {"tier": "Intermediate", "description": "Developing competitive skills"}
        else:
            return {"tier": "Beginner", "description": "Learning fundamentals"}

    def _analyze_player_strengths_weaknesses(self, win_rate: float, kda: float, game: str) -> dict:
        """Analyze player strengths and weaknesses."""
        strengths = []
        weaknesses = []

        if win_rate > 0.7:
            strengths.append("Excellent win rate")
        elif win_rate < 0.5:
            weaknesses.append("Below average win rate")

        if kda > 2.0:
            strengths.append("Strong KDA performance")
        elif kda < 1.5:
            weaknesses.append("Needs improvement in KDA")

        return {"strengths": strengths, "weaknesses": weaknesses}

    def _generate_improvement_recommendations(self, breakdown: dict, game: str) -> list:
        """Generate improvement recommendations."""
        recommendations = []

        if breakdown["win_rate_score"] < 20:
            recommendations.append("Focus on game knowledge and decision making")
        if breakdown["kda_score"] < 15:
            recommendations.append("Work on mechanical skills and positioning")
        if breakdown["consistency_score"] < 15:
            recommendations.append("Play more matches to build consistency")

        return recommendations

    def _predict_future_performance(self, current_rating: float, win_rate: float, matches: int) -> dict:
        """Predict future performance."""
        growth_potential = max(0, (1 - win_rate) * 0.5 + (200 - matches) / 200 * 0.3)
        projected_rating = current_rating * (1 + growth_potential * 0.2)

        return {
            "growth_potential": f"{growth_potential*100:.1f}%",
            "projected_rating_6months": round(projected_rating, 0),
            "improvement_trajectory": "Positive" if growth_potential > 0.2 else "Stable"
        }

    def _provide_ranking_context(self, rating: float, game: str) -> dict:
        """Provide ranking context."""
        percentile = min(95, max(5, (rating - 800) / (3000 - 800) * 100))
        return {
            "estimated_percentile": f"{percentile:.1f}%",
            "player_level": "Above average" if percentile > 50 else "Below average",
            "competitive_readiness": "Ready" if rating > 1800 else "Needs development"
        }

    def _calculate_team_strength(self, win_rate: float, avg_rating: float, matches: int, game_params: dict) -> float:
        """Calculate team strength score."""
        individual_component = avg_rating / 3000 * 100 * game_params["skill_weight"]
        team_component = win_rate * 100 * game_params["team_weight"]
        experience_component = min(matches / 50 * 20, 20) * 0.2

        return individual_component + team_component + experience_component

    def _estimate_ranking_position(self, team_rating: float, game: str) -> dict:
        """Estimate ranking position."""
        # Simulate ranking based on rating
        if team_rating >= 2200:
            rank_range = "Top 5"
        elif team_rating >= 2000:
            rank_range = "Top 10"
        elif team_rating >= 1800:
            rank_range = "Top 20"
        elif team_rating >= 1600:
            rank_range = "Top 50"
        else:
            rank_range = "Below Top 50"

        return {
            "estimated_rank_range": rank_range,
            "regional_ranking": f"Top {max(1, int((2500 - team_rating) / 50))}",
            "world_ranking": f"Top {max(1, int((2500 - team_rating) / 30))}"
        }

    def _calculate_team_experience_factor(self, matches: int) -> float:
        """Calculate team experience factor."""
        return min(matches / 100, 1.0)

    def _classify_team_tier(self, rating: float) -> dict:
        """Classify team tier."""
        if rating >= 2300:
            return {"tier": "Tier 1", "description": "Elite international teams"}
        elif rating >= 2000:
            return {"tier": "Tier 2", "description": "Strong regional teams"}
        elif rating >= 1700:
            return {"tier": "Tier 3", "description": "Competitive regional teams"}
        else:
            return {"tier": "Tier 4", "description": "Developing teams"}

    def _predict_tournament_performance(self, rating: float, win_rate: float, game: str) -> dict:
        """Predict tournament performance."""
        if rating >= 2200:
            expected_placement = "Top 4"
            win_probability = "High"
        elif rating >= 1900:
            expected_placement = "Top 8"
            win_probability = "Medium-High"
        elif rating >= 1600:
            expected_placement = "Top 16"
            win_probability = "Medium"
        else:
            expected_placement = "Group Stage"
            win_probability = "Low"

        return {
            "expected_placement": expected_placement,
            "tournament_win_probability": win_probability,
            "upset_potential": "High" if 1700 <= rating <= 1900 else "Medium"
        }

    def _analyze_team_development(self, rating: float, matches: int, win_rate: float) -> dict:
        """Analyze team development trajectory."""
        development_stage = "Established" if matches > 50 else "Developing"
        trajectory = "Upward" if win_rate > 0.6 else "Stable" if win_rate > 0.4 else "Declining"

        return {
            "development_stage": development_stage,
            "trajectory": trajectory,
            "potential_ceiling": round(rating * 1.2, 0),
            "time_to_peak": f"{max(6, 18 - matches//10)} months"
        }

    def _generate_team_comparisons(self, rating: float, game: str) -> dict:
        """Generate team comparisons."""
        return {
            "similar_rated_teams": [f"Team Alpha ({rating-50:.0f})", f"Team Beta ({rating+30:.0f})"],
            "rating_difference_top1": f"{max(0, 2500 - rating):.0f} points",
            "percentile_ranking": f"{min(95, (rating - 1000) / 1500 * 100):.1f}%"
        }

    def _generate_team_recommendations(self, analysis: dict, tier: dict) -> list:
        """Generate team recommendations."""
        recommendations = []

        if analysis["individual_skill_level"] < 1600:
            recommendations.append("Focus on individual player development")
        if analysis["team_coordination"] < 60:
            recommendations.append("Improve team coordination and communication")
        if tier["tier"] in ["Tier 3", "Tier 4"]:
            recommendations.append("Participate in more competitive tournaments")

        return recommendations

    # Tournament and Match Prediction Helper Methods
    def _analyze_tournament_complexity(self, team_count: int, format_type: str, prize_pool: float) -> dict:
        """Analyze tournament complexity."""
        complexity_score = team_count / 16 * 0.4 + (prize_pool / 1000000) * 0.6

        return {
            "complexity_score": round(complexity_score, 2),
            "tournament_prestige": "High" if prize_pool > 500000 else "Medium",
            "competition_level": "Elite" if team_count >= 16 else "Regional",
            "format_complexity": "High" if format_type == "double_elimination" else "Medium"
        }

    def _simulate_team_strengths(self, teams: List[str]) -> dict:
        """Simulate team strengths for tournament."""
        team_ratings = {}
        for i, team in enumerate(teams):
            # Simulate ratings with some variation
            base_rating = 2000 - (i * 50) + (hash(team) % 200 - 100)
            team_ratings[team] = max(1200, min(2400, base_rating))

        return {
            "team_ratings": team_ratings,
            "favorites": sorted(teams, key=lambda t: team_ratings[t], reverse=True)[:4],
            "underdogs": sorted(teams, key=lambda t: team_ratings[t])[:4],
            "rating_spread": max(team_ratings.values()) - min(team_ratings.values())
        }

    def _predict_tournament_bracket(self, team_strengths: dict, format_type: str) -> dict:
        """Predict tournament bracket outcomes."""
        favorites = team_strengths["favorites"]

        return {
            "predicted_finalists": favorites[:2],
            "predicted_winner": favorites[0],
            "dark_horse_candidate": team_strengths["underdogs"][-1],
            "upset_probability": "Medium" if team_strengths["rating_spread"] < 400 else "Low",
            "bracket_confidence": "High" if len(favorites) >= 4 else "Medium"
        }

    def _predict_prize_distribution(self, team_strengths: dict, total_prize: float) -> dict:
        """Predict prize distribution."""
        favorites = team_strengths["favorites"]

        return {
            "likely_winner_prize": f"${total_prize * 0.4:,.0f}",
            "runner_up_prize": f"${total_prize * 0.2:,.0f}",
            "top_4_total": f"${total_prize * 0.8:,.0f}",
            "expected_winner": favorites[0] if favorites else "TBD"
        }

    def _analyze_upset_probabilities(self, team_strengths: dict, format_type: str) -> dict:
        """Analyze upset probabilities."""
        rating_spread = team_strengths["rating_spread"]

        upset_chance = "High" if rating_spread < 300 else "Medium" if rating_spread < 600 else "Low"

        return {
            "overall_upset_probability": upset_chance,
            "major_upset_chance": "15%" if upset_chance == "High" else "8%",
            "cinderella_story_potential": "Medium" if len(team_strengths["underdogs"]) > 2 else "Low",
            "bracket_volatility": "High" if format_type == "single_elimination" else "Medium"
        }

    def _predict_tournament_engagement(self, complexity: dict, strengths: dict) -> dict:
        """Predict tournament engagement."""
        engagement_score = complexity["complexity_score"] * 0.6 + (strengths["rating_spread"] / 1000) * 0.4

        return {
            "predicted_viewership": f"{engagement_score * 2:.1f}M peak viewers",
            "social_media_buzz": "High" if engagement_score > 0.7 else "Medium",
            "betting_interest": "Very High" if complexity["tournament_prestige"] == "High" else "High",
            "storyline_potential": "Excellent" if len(strengths["favorites"]) >= 4 else "Good"
        }

    def _generate_tournament_insights(self, bracket: dict, upsets: dict) -> list:
        """Generate tournament insights."""
        insights = []

        if bracket["upset_probability"] == "High":
            insights.append("Expect multiple bracket-busting upsets")
        if upsets["cinderella_story_potential"] == "High":
            insights.append("Watch for a potential Cinderella story")
        if bracket["bracket_confidence"] == "High":
            insights.append("Top seeds likely to perform as expected")

        return insights

    def _generate_betting_recommendations(self, strengths: dict, upsets: dict) -> dict:
        """Generate betting recommendations."""
        favorites = strengths["favorites"]

        return {
            "safe_bets": favorites[:2] if favorites else [],
            "value_picks": strengths["underdogs"][-2:] if "underdogs" in strengths else [],
            "avoid": "Heavy underdogs" if upsets["overall_upset_probability"] == "Low" else "None",
            "strategy": "Conservative" if upsets["overall_upset_probability"] == "Low" else "Balanced"
        }

    def _estimate_team_strength_for_match(self, team: str, recent_form: float, game: str) -> float:
        """Estimate team strength for specific match."""
        # Base strength simulation
        base_strength = 1500 + (hash(team) % 800)
        form_adjustment = (recent_form - 0.5) * 200

        return max(1000, min(2500, base_strength + form_adjustment))

    def _calculate_match_win_probabilities(self, strength1: float, strength2: float, match_format: str) -> dict:
        """Calculate match win probabilities."""
        rating_diff = strength1 - strength2

        # Convert rating difference to probability
        prob1 = 1 / (1 + math.pow(10, -rating_diff / 400))
        prob2 = 1 - prob1

        # Adjust for match format
        format_adjustment = {"bo1": 1.0, "bo3": 0.9, "bo5": 0.8}
        adjustment = format_adjustment.get(match_format, 1.0)

        # Apply adjustment (longer series favor stronger team)
        if prob1 > 0.5:
            prob1 = min(0.95, prob1 + (1 - adjustment) * 0.1)
        else:
            prob1 = max(0.05, prob1 - (1 - adjustment) * 0.1)
        prob2 = 1 - prob1

        return {
            "team1_win_probability": f"{prob1*100:.1f}%",
            "team2_win_probability": f"{prob2*100:.1f}%",
            "confidence_level": "High" if abs(prob1 - 0.5) > 0.2 else "Medium"
        }

    def _analyze_match_dynamics(self, strength1: float, strength2: float, game: str) -> dict:
        """Analyze match dynamics."""
        strength_diff = abs(strength1 - strength2)

        return {
            "match_competitiveness": "Very Close" if strength_diff < 100 else "Close" if strength_diff < 200 else "Favored",
            "expected_game_length": "Long" if strength_diff < 150 else "Medium",
            "volatility": "High" if strength_diff < 100 else "Medium",
            "strategic_importance": "High" if strength_diff < 50 else "Medium"
        }

    def _predict_match_scores(self, probabilities: dict, match_format: str) -> dict:
        """Predict match scores."""
        team1_prob = float(probabilities["team1_win_probability"].rstrip('%')) / 100

        if match_format == "bo1":
            return {"most_likely_score": "1-0", "format": "Best of 1"}
        elif match_format == "bo3":
            if team1_prob > 0.7:
                return {"most_likely_score": "2-0", "alternative_score": "2-1"}
            elif team1_prob > 0.3:
                return {"most_likely_score": "2-1", "alternative_score": "1-2"}
            else:
                return {"most_likely_score": "0-2", "alternative_score": "1-2"}
        elif match_format == "bo5":
            if team1_prob > 0.7:
                return {"most_likely_score": "3-1", "alternative_score": "3-0"}
            elif team1_prob > 0.3:
                return {"most_likely_score": "3-2", "alternative_score": "2-3"}
            else:
                return {"most_likely_score": "1-3", "alternative_score": "2-3"}

        return {"most_likely_score": "Unknown", "format": match_format}

    def _identify_match_key_factors(self, team1: str, team2: str, form1: float, form2: float) -> list:
        """Identify key factors for the match."""
        factors = []

        if abs(form1 - form2) > 0.2:
            factors.append("Recent form difference is significant")
        if form1 > 0.8 or form2 > 0.8:
            factors.append("One team is in exceptional form")

        factors.extend([
            "Head-to-head history",
            "Map pool advantages",
            "Player matchups",
            "Strategic preparation"
        ])

        return factors

    def _assess_upset_potential(self, strength1: float, strength2: float) -> dict:
        """Assess upset potential."""
        strength_diff = abs(strength1 - strength2)

        if strength_diff < 100:
            upset_chance = "High"
        elif strength_diff < 300:
            upset_chance = "Medium"
        else:
            upset_chance = "Low"

        return {
            "upset_probability": upset_chance,
            "upset_factor": f"{min(strength_diff / 500, 1.0):.2f}",
            "surprise_potential": "High" if upset_chance in ["High", "Medium"] else "Low"
        }

    def _generate_match_betting_insights(self, probabilities: dict, upset: dict) -> dict:
        """Generate match betting insights."""
        team1_prob = float(probabilities["team1_win_probability"].rstrip('%')) / 100

        return {
            "recommended_bet": "Team 1" if team1_prob > 0.6 else "Team 2" if team1_prob < 0.4 else "Avoid",
            "value_assessment": "Good value" if upset["upset_probability"] == "High" else "Fair odds expected",
            "risk_level": "Low" if abs(team1_prob - 0.5) > 0.3 else "Medium",
            "betting_strategy": "Conservative" if upset["upset_probability"] == "Low" else "Moderate risk"
        }

    def _assess_prediction_confidence(self, strength1: float, strength2: float, match_format: str) -> dict:
        """Assess prediction confidence."""
        strength_diff = abs(strength1 - strength2)

        base_confidence = min(0.9, strength_diff / 500)
        format_bonus = {"bo1": 0.0, "bo3": 0.1, "bo5": 0.2}

        total_confidence = base_confidence + format_bonus.get(match_format, 0.0)

        return {
            "confidence_score": f"{total_confidence*100:.1f}%",
            "prediction_reliability": "High" if total_confidence > 0.7 else "Medium",
            "factors_considered": ["Team strength", "Recent form", "Match format"],
            "uncertainty_sources": ["Player substitutions", "Meta changes", "Preparation time"]
        }