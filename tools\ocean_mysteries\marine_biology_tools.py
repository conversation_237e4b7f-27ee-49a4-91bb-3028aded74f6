#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Marine Biology Tools - Công cụ sinh học biển
"""

from typing import Dict, Any
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json


class MarineBiologyTool(Toolkit):
    """
    Marine Biology Tool for searching deep sea creatures, bioluminescent organisms, and extreme adaptations.
    """

    def __init__(self):
        super().__init__(
            name="Marine Biology Tool",
            tools=[self.search_deep_sea_creatures, self.search_marine_adaptations]
        )

    async def search_deep_sea_creatures(self, query: str, depth_range: str = "all", trait: str = "all", limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm sinh vật biển sâu.
        
        Parameters:
        - query: T<PERSON> khóa tìm kiếm
        - depth_range: Đ<PERSON> sâu (mesopelagic: 200-1000m, bathypelagic: 1000-4000m, abyssopelagic: 4000-6000m, hadal: 6000m+)
        - trait: Đặc điểm (bioluminescence, pressure_adaptation, extreme_longevity, unique_feeding)
        - limit: <PERSON><PERSON> lượng kết quả
        
        Returns:
        - Dict chứa thông tin về sinh vật biển sâu
        """
        logger.info(f"Searching deep sea creatures for: {query}")
        
        try:
            results = []
            for i in range(limit):
                result = {
                    "id": f"creature_{5000+i:04d}",
                    "scientific_name": f"Abyssus {query.lower()}{chr(97+i)}us {chr(97+i)}i",
                    "common_name": f"Deep Sea {query.title()} {chr(65+i)}",
                    "taxonomic_classification": {
                        "kingdom": "Animalia",
                        "phylum": ["Chordata", "Arthropoda", "Mollusca", "Cnidaria", "Echinodermata"][i % 5],
                        "class": ["Actinopterygii", "Cephalopoda", "Malacostraca", "Hydrozoa", "Asteroidea"][i % 5],
                        "order": f"Order{chr(65+i)}",
                        "family": f"Family{chr(65+i)}idae",
                        "genus": f"Abyssus{chr(65+i)}",
                        "species": f"{query.lower()}{chr(97+i)}us {chr(97+i)}i"
                    },
                    "discovery_date": f"{1980 + (i * 5)}-{1+i%12:02d}-{1+i:02d}",
                    "discovered_by": f"Marine Biologist {chr(65+i)} {chr(75+i)}",
                    "habitat": {
                        "depth_range": depth_range if depth_range != "all" else [f"{1000 + (i * 1000)}-{2000 + (i * 1000)}m"][0],
                        "ocean_zone": ["Mesopelagic", "Bathypelagic", "Abyssopelagic", "Hadal"][i % 4],
                        "substrate": ["Water Column", "Seafloor", "Hydrothermal Vents", "Cold Seeps", "Seamounts"][i % 5],
                        "geographic_distribution": ["Global Deep Ocean", "Pacific Trenches", "Atlantic Abyssal Plains", "Polar Deep Waters"][i % 4]
                    },
                    "physical_characteristics": {
                        "size": f"{5 + (i * 10)}cm",
                        "weight": f"{50 + (i * 100)}g",
                        "coloration": ["Transparent", "Red", "Black", "Bioluminescent Blue", "Silver"][i % 5],
                        "body_shape": ["Elongated", "Globular", "Flat", "Gelatinous", "Armored"][i % 5],
                        "distinctive_features": [f"Feature {j+1}" for j in range(3)]
                    },
                    "adaptations": {
                        "bioluminescence": trait == "bioluminescence" or i % 2 == 0,
                        "pressure_adaptation": trait == "pressure_adaptation" or i % 3 == 0,
                        "extreme_longevity": trait == "extreme_longevity" or i % 4 == 0,
                        "unique_feeding": trait == "unique_feeding" or i % 2 == 1,
                        "low_metabolism": True,
                        "specialized_sensory_organs": i % 3 == 0,
                        "details": [f"Adaptation Detail {j+1}" for j in range(3)]
                    },
                    "behavior": {
                        "feeding_strategy": ["Predator", "Scavenger", "Filter Feeder", "Parasitic", "Symbiotic"][i % 5],
                        "prey_items": [f"Prey {j+1}" for j in range(2)],
                        "predators": [f"Predator {j+1}" for j in range(2)],
                        "activity_pattern": ["Continuous", "Diurnal Migration", "Seasonal", "Opportunistic"][i % 4],
                        "social_structure": ["Solitary", "Small Groups", "Swarms", "Colonial"][i % 4]
                    },
                    "reproduction": {
                        "method": ["Sexual", "Asexual", "Both", "Complex Life Cycle"][i % 4],
                        "spawning_behavior": ["Broadcast Spawning", "Brooding", "Live Birth", "Egg Deposition"][i % 4],
                        "maturation_time": f"{1 + (i % 10)} years",
                        "reproductive_rate": ["Low", "Moderate", "High", "Variable"][i % 4],
                        "larval_stage": i % 2 == 0
                    },
                    "ecological_role": ["Apex Predator", "Mesopredator", "Detritivore", "Primary Consumer", "Decomposer"][i % 5],
                    "conservation_status": ["Data Deficient", "Least Concern", "Unknown", "Not Evaluated"][i % 4],
                    "research_status": ["Well-Studied", "Limited Research", "Recently Discovered", "Ongoing Research"][i % 4],
                    "scientific_significance": ["High", "Moderate", "Revolutionary", "Interesting"][i % 4],
                    "specimens_collected": 1 + (i % 10),
                    "genetic_analysis": i % 2 == 0,
                    "research_institutions": [f"Institution {chr(65+j)}" for j in range(2)],
                    "publications": 2 + (i * 2),
                    "url": f"https://deepseacreatures.org/species/{query.lower()}-{chr(97+i)}",
                    "images_available": 3 + (i * 2)
                }
                results.append(result)
            
            return {
                "status": "success",
                "source": "Deep Sea Creatures Database",
                "query": query,
                "depth_range": depth_range,
                "trait": trait,
                "total_results": len(results),
                "results": results,
                "search_metadata": {
                    "search_time": "2024-01-15T10:30:00Z",
                    "database_coverage": "Global deep sea species",
                    "total_species": "25,000+ documented"
                }
            }
            
        except Exception as e:
            logger.error(f"Error searching deep sea creatures: {str(e)}")
            return {
                "status": "error",
                "source": "Deep Sea Creatures Database",
                "message": str(e),
                "query": query
            }

    async def search_marine_adaptations(self, adaptation_type: str = "all", environment: str = "all", limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm thích nghi của sinh vật biển.
        
        Parameters:
        - adaptation_type: Loại thích nghi (bioluminescence, pressure, temperature, chemical, sensory)
        - environment: Môi trường (deep_sea, hydrothermal_vents, polar_waters, coral_reefs, open_ocean)
        - limit: Số lượng kết quả
        
        Returns:
        - Dict chứa thông tin về thích nghi của sinh vật biển
        """
        logger.info(f"Searching marine adaptations: {adaptation_type} in {environment}")
        
        try:
            results = []
            for i in range(limit):
                result = {
                    "id": f"adaptation_{6000+i:04d}",
                    "adaptation_name": f"{adaptation_type.replace('_', ' ').title()} Adaptation {chr(65+i)}",
                    "adaptation_type": adaptation_type if adaptation_type != "all" else ["Bioluminescence", "Pressure", "Temperature", "Chemical", "Sensory"][i % 5],
                    "environment": environment if environment != "all" else ["Deep Sea", "Hydrothermal Vents", "Polar Waters", "Coral Reefs", "Open Ocean"][i % 5],
                    "species_examples": [f"Species Example {j+1}" for j in range(3)],
                    "description": f"Remarkable adaptation found in marine organisms living in {environment.replace('_', ' ')} environments. This adaptation allows organisms to {['survive extreme pressure', 'detect prey in darkness', 'withstand toxic chemicals', 'conserve energy', 'communicate underwater'][i % 5]} through specialized {['cellular mechanisms', 'organ systems', 'biochemical pathways', 'behavioral strategies', 'physiological processes'][i % 5]}.",
                    "mechanism": {
                        "cellular_level": ["Membrane Modifications", "Protein Stabilization", "Enzyme Adaptations", "Metabolic Adjustments"][i % 4],
                        "organ_level": ["Specialized Organs", "Modified Tissues", "Unique Structures", "Adapted Systems"][i % 4],
                        "genetic_basis": ["Gene Duplication", "Novel Proteins", "Regulatory Changes", "Horizontal Gene Transfer"][i % 4],
                        "evolutionary_history": f"Evolved {1 + (i * 5)} million years ago",
                        "details": [f"Mechanism Detail {j+1}" for j in range(3)]
                    },
                    "functional_benefits": {
                        "primary_function": ["Predation", "Defense", "Communication", "Navigation", "Metabolism"][i % 5],
                        "secondary_functions": [f"Secondary Function {j+1}" for j in range(2)],
                        "survival_advantage": ["Critical", "Significant", "Moderate", "Specialized"][i % 4],
                        "energy_cost": ["High", "Moderate", "Low", "Variable"][i % 4]
                    },
                    "evolutionary_significance": {
                        "uniqueness": ["Unique to Species", "Shared in Family", "Convergent Evolution", "Ancient Adaptation"][i % 4],
                        "evolutionary_pressure": ["Strong", "Moderate", "Specialized", "Relaxed"][i % 4],
                        "related_adaptations": [f"Related Adaptation {j+1}" for j in range(2)],
                        "evolutionary_timeline": f"Developed over {5 + (i * 10)} million years"
                    },
                    "research_history": {
                        "first_documented": f"{1950 + (i * 10)}-{1+i%12:02d}-{1+i:02d}",
                        "key_researchers": [f"Researcher {chr(65+j)}" for j in range(2)],
                        "major_studies": 3 + (i * 2),
                        "research_methods": ["Genetic Analysis", "Laboratory Experiments", "Field Observations", "Comparative Studies"][i % 4]
                    },
                    "applications": {
                        "biomimicry_potential": ["High", "Moderate", "Low", "Unexplored"][i % 4],
                        "medical_applications": i % 2 == 0,
                        "industrial_applications": i % 3 == 0,
                        "technological_inspiration": i % 2 == 1,
                        "details": [f"Application Detail {j+1}" for j in range(2)]
                    },
                    "research_status": ["Active Research", "Well-Understood", "Partially Characterized", "Recently Discovered"][i % 4],
                    "scientific_significance": ["Revolutionary", "High", "Moderate", "Specialized"][i % 4],
                    "research_institutions": [f"Institution {chr(65+j)}" for j in range(2)],
                    "publications": 5 + (i * 3),
                    "url": f"https://marineadaptations.org/adaptations/{adaptation_type}-{chr(97+i)}",
                    "visual_resources": 3 + (i * 2)
                }
                results.append(result)
            
            return {
                "status": "success",
                "source": "Marine Adaptations Database",
                "adaptation_type": adaptation_type,
                "environment": environment,
                "total_results": len(results),
                "results": results,
                "search_metadata": {
                    "search_time": "2024-01-15T10:30:00Z",
                    "database_coverage": "Global marine adaptations",
                    "total_adaptations": "1,000+ documented"
                }
            }
            
        except Exception as e:
            logger.error(f"Error searching marine adaptations: {str(e)}")
            return {
                "status": "error",
                "source": "Marine Adaptations Database",
                "message": str(e),
                "adaptation_type": adaptation_type,
                "environment": environment
            }
