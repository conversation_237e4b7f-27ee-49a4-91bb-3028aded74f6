#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho c<PERSON><PERSON> hàm get_top_new mới đư<PERSON><PERSON> thêm vào AI Robotics tools.
"""

import sys
import os
import json

# Thêm thư mục gốc vào Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_papers_with_code():
    """Test Papers With Code tools."""
    print("=== Testing Papers With Code Tools ===")
    try:
        from tools.ai_robotics.papers_with_code_tools import PapersWithCodeTools
        
        tool = PapersWithCodeTools()
        
        # Test regular search
        print("--- Regular Search ---")
        result1 = tool.search_papers_with_code("transformer", 3)
        print("Search result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test trending papers
        print("\n--- Trending Papers ---")
        result2 = tool.get_trending_papers(3, "week")
        print("Trending result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # Test new papers
        print("\n--- New Papers ---")
        result3 = tool.get_top_new_papers(3, 30, "computer-vision")
        print("New papers result:", result3[:200] + "..." if len(result3) > 200 else result3)
        
    except Exception as e:
        print(f"Error testing Papers With Code: {e}")
    print()

def test_huggingface():
    """Test Hugging Face tools."""
    print("=== Testing Hugging Face Tools ===")
    try:
        from tools.ai_robotics.huggingface_tools import HuggingFaceTools
        
        tool = HuggingFaceTools()
        
        # Test regular search
        print("--- Regular Search ---")
        result1 = tool.search_huggingface_hub("bert", "text-classification", 3)
        print("Search result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test trending models
        print("\n--- Trending Models ---")
        result2 = tool.get_trending_models(3, "text-classification")
        print("Trending result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # Test recent models
        print("\n--- Recent Models ---")
        result3 = tool.get_recent_models(3, 30, "text-generation")
        print("Recent models result:", result3[:200] + "..." if len(result3) > 200 else result3)
        
    except Exception as e:
        print(f"Error testing Hugging Face: {e}")
    print()

def test_arxiv():
    """Test arXiv tools."""
    print("=== Testing arXiv Tools ===")
    try:
        from tools.ai_robotics.arxiv_tools import ArxivTools
        
        tool = ArxivTools()
        
        # Test regular search
        print("--- Regular Search ---")
        result1 = tool.search_arxiv("neural networks", 3, ["cs.AI"])
        print("Search result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test recent papers
        print("\n--- Recent Papers ---")
        result2 = tool.get_recent_papers(3, 7, ["cs.AI", "cs.LG"])
        print("Recent papers result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # Test top cited papers
        print("\n--- Top Cited Papers ---")
        result3 = tool.get_top_cited_papers(3, "cs.AI")
        print("Top cited result:", result3[:200] + "..." if len(result3) > 200 else result3)
        
    except Exception as e:
        print(f"Error testing arXiv: {e}")
    print()

def test_openml():
    """Test OpenML tools."""
    print("=== Testing OpenML Tools ===")
    try:
        from tools.ai_robotics.openml_tools import OpenMLTools
        
        tool = OpenMLTools()
        
        # Test regular search
        print("--- Regular Search ---")
        result1 = tool.search_openml("classification", "data", 3)
        print("Search result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test recent datasets
        print("\n--- Recent Datasets ---")
        result2 = tool.get_recent_datasets(3, 30)
        print("Recent datasets result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # Test popular datasets
        print("\n--- Popular Datasets ---")
        result3 = tool.get_popular_datasets(3, "classification")
        print("Popular datasets result:", result3[:200] + "..." if len(result3) > 200 else result3)
        
    except Exception as e:
        print(f"Error testing OpenML: {e}")
    print()

def test_wikipedia_ai():
    """Test Wikipedia AI tools."""
    print("=== Testing Wikipedia AI Tools ===")
    try:
        from tools.ai_robotics.wikipedia_ai_tools import WikipediaAITools
        
        tool = WikipediaAITools()
        
        # Test regular search
        print("--- Regular Search ---")
        result1 = tool.search_wikipedia_ai("machine learning", 3)
        print("Search result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test recent AI articles
        print("\n--- Recent AI Articles ---")
        result2 = tool.get_recent_ai_articles(3, 30, "en")
        print("Recent articles result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
    except Exception as e:
        print(f"Error testing Wikipedia AI: {e}")
    print()

def test_search_toolkit():
    """Test AI Robotics Search Toolkit."""
    print("=== Testing AI Robotics Search Toolkit ===")
    try:
        from tools.ai_robotics.ai_robotics_search_toolkit import AIRoboticsSearchToolkit
        
        toolkit = AIRoboticsSearchToolkit()
        
        # Test regular keyword generation
        print("--- Papers With Code Keywords ---")
        result1 = toolkit.generate_papers_with_code_keywords("computer-vision", "transformer")
        print(result1)
        
        print("\n--- Hugging Face Keywords ---")
        result2 = toolkit.generate_huggingface_keywords("text-classification", "bert")
        print(result2)
        
        print("\n--- arXiv Keywords ---")
        result3 = toolkit.generate_arxiv_keywords(["cs.AI", "cs.LG"], "neural networks")
        print(result3)
        
        # Test trending/recent keyword generation
        print("\n--- Papers With Code Trending Keywords ---")
        result4 = toolkit.generate_papers_with_code_trending_keywords("nlp", "week")
        print(result4)
        
        print("\n--- Hugging Face Trending Keywords ---")
        result5 = toolkit.generate_huggingface_trending_keywords("image-classification", "month")
        print(result5)
        
        print("\n--- arXiv Recent Keywords ---")
        result6 = toolkit.generate_arxiv_recent_keywords(["cs.CV"], 7)
        print(result6)
        
    except Exception as e:
        print(f"Error testing Search Toolkit: {e}")
    print()

def main():
    """Chạy tất cả các test."""
    print("Testing New AI Robotics Tools Functions")
    print("=" * 50)
    print()
    
    # Test từng tool
    test_papers_with_code()
    test_huggingface()
    test_arxiv()
    test_openml()
    test_wikipedia_ai()
    test_search_toolkit()
    
    print("=" * 50)
    print("Testing completed!")

if __name__ == "__main__":
    main()
