from typing import Dict, Any, Optional, List, Union
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger, log_warning
import requests
import re
import json
from datetime import datetime
from urllib.parse import quote_plus
import asyncio
from functools import lru_cache

class CFRTool(Toolkit):
    """
    CFR Tool cho tìm kiếm phân tích, b<PERSON>o cáo quốc tế, ch<PERSON>h sách đối ngoại từ Council on Foreign Relations (CFR).
    """

    def __init__(self):
        super().__init__(
            name="Council on Foreign Relations Search Tool",
            description="Tool cho tìm kiếm phân tích, b<PERSON><PERSON> c<PERSON><PERSON> quốc tế, ch<PERSON>h sách đối ngoại từ Council on Foreign Relations (CFR).",
            tools=[self.search_cfr, self.get_cfr_topics, self.get_cfr_experts]
        )
        self.base_url = "https://www.cfr.org"
        self.search_url = f"{self.base_url}/search"
        self.cache_file = ".cfr_cache.json"
        self.cache = self._load_cache()
        self.timeout = 15
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }

    def _load_cache(self) -> Dict[str, Any]:
        """Tải cache từ file nếu có"""
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}

    def _save_cache(self) -> None:
        """Lưu cache vào file"""
        with open(self.cache_file, 'w', encoding='utf-8') as f:
            json.dump(self.cache, f, ensure_ascii=False, indent=2)

    async def _fetch_with_retry(self, url: str, params: Optional[Dict] = None, max_retries: int = 3) -> Optional[requests.Response]:
        """Hàm hỗ trợ gọi API với cơ chế retry"""
        for attempt in range(max_retries):
            try:
                response = requests.get(
                    url,
                    params=params,
                    headers=self.headers,
                    timeout=self.timeout
                )
                response.raise_for_status()
                return response
            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    log_warning(f"Request failed after {max_retries} attempts: {str(e)}")
                    return None
                await asyncio.sleep(1 * (attempt + 1))
        return None

    async def get_cfr_topics(self) -> List[Dict[str, str]]:
        """
        Lấy danh sách các chủ đề có sẵn trên CFR
        
        Returns:
        - Danh sách các chủ đề với id và tên
        """
        return [
            {"id": "africa", "name": "Africa"},
            {"id": "americas", "name": "Americas"},
            {"id": "asia", "name": "Asia"},
            {"id": "china", "name": "China"},
            {"id": "climate-change", "name": "Climate Change"},
            {"id": "cybersecurity", "name": "Cybersecurity"},
            {"id": "economics", "name": "Economics"},
            {"id": "europe", "name": "Europe"},
            {"id": "global", "name": "Global"},
            {"id": "middle-east", "name": "Middle East"},
            {"id": "nuclear-energy", "name": "Nuclear Energy"},
            {"id": "technology", "name": "Technology"},
            {"id": "trade", "name": "Trade"},
            {"id": "united-states", "name": "United States"}
        ]

    async def get_cfr_experts(self, query: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Tìm kiếm chuyên gia CFR theo tên hoặc lĩnh vực
        
        Parameters:
        - query: Tên chuyên gia hoặc lĩnh vực (optional)
        
        Returns:
        - Danh sách chuyên gia phù hợp
        """
        # Triển khai sau khi hoàn thiện search_cfr
        return []

    async def search_cfr(self, query: str, topic: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm CFR cho phân tích, báo cáo quốc tế, chính sách đối ngoại.

        Parameters:
        - query: Từ khóa chủ đề, quốc gia, sự kiện, chính sách (vd: 'China policy', 'Ukraine war', 'climate diplomacy')
        - topic: Chủ đề cụ thể (vd: 'asia', 'climate-change', 'trade')
        - limit: Số lượng kết quả tối đa (default: 5, tối đa 20)

        Returns:
        - Dict chứa kết quả tìm kiếm với cấu trúc:
            {
                "status": "success"|"error",
                "source": "CFR",
                "query": str,
                "topic_filter": Optional[str],
                "results_count": int,
                "results": List[Dict],
                "keyword_guide": List[str],
                "official_data_url": str
            }
        """
        logger.info(f"Tìm kiếm CFR: query={query}, topic={topic}, limit={min(limit, 20)}")

        if not query or not query.strip():
            return {
                "status": "error",
                "source": "CFR",
                "message": "Query không được để trống",
                "query": query
            }

        try:
            # Kiểm tra cache trước
            cache_key = f"{query.lower()}_{topic.lower() if topic else 'all'}"
            if cache_key in self.cache:
                cached_data = self.cache[cache_key]
                if (datetime.now().timestamp() - cached_data["timestamp"]) < 86400:  # Cache 24h
                    logger.info(f"Sử dụng dữ liệu từ cache cho: {cache_key}")
                    return {
                        "status": "success",
                        "source": "CFR (cached)",
                        "query": query,
                        "topic_filter": topic,
                        "results_count": len(cached_data["results"]),
                        "results": cached_data["results"][:limit],
                        "keyword_guide": self._get_keyword_guide(),
                        "official_data_url": self.base_url
                    }

            # Chuẩn bị tham số tìm kiếm
            params = {
                "keyword": query.strip(),
                "sort": "relevance",
                "page": 1,
                "per_page": min(limit, 20)  # Giới hạn tối đa 20 kết quả
            }
            
            if topic:
                params["topic"] = topic.strip()

            # Gọi API với cơ chế retry
            response = await self._fetch_with_retry(self.search_url, params)
            if not response or response.status_code != 200:
                status_code = response.status_code if response else "timeout"
                return {
                    "status": "error",
                    "source": "CFR",
                    "message": f"Không thể kết nối đến CFR (HTTP {status_code})",
                    "query": query,
                    "suggested_actions": [
                        "Kiểm tra kết nối mạng",
                        "Thử lại sau ít phút",
                        "Kiểm tra lại từ khóa tìm kiếm"
                    ]
                }

            # Phân tích kết quả HTML với regex pattern tối ưu hơn
            results = []
            html = response.text
            
            # Pattern regex để trích xuất thông tin hiệu quả hơn
            item_pattern = re.compile(
                r'<div class="search-result__item".*?<a href="([^"]+)"[^>]*>(.*?)</a>.*?'
                r'(?:<div class="search-result__dek">(.*?)</div>)?.*?'
                r'(?:<time[^>]*>(.*?)</time>)?.*?'
                r'(?:<div class="search-result__topic">(.*?)</div>)?',
                re.DOTALL
            )
            
            for match in item_pattern.finditer(html):
                if len(results) >= limit:
                    break
                    
                url = f"{self.base_url}{match.group(1)}" if match.group(1) else None
                title = re.sub(r'<[^>]+>', '', match.group(2)).strip() if match.group(2) else None
                description = re.sub(r'<[^>]+>', '', match.group(3)).strip() if match.group(3) else None
                date = match.group(4).strip() if match.group(4) else None
                topic_val = re.sub(r'<[^>]+>', '', match.group(5)).strip() if match.group(5) else None
                
                result_item = {
                    "title": title,
                    "description": description,
                    "date": date,
                    "topic": topic_val,
                    "cfr_url": url,
                    "search_timestamp": datetime.utcnow().isoformat()
                }
                results.append(result_item)
            
            # Lưu vào cache
            self.cache[cache_key] = {
                "timestamp": datetime.now().timestamp(),
                "results": results
            }
            self._save_cache()

            return {
                "status": "success",
                "source": "CFR",
                "query": query,
                "topic_filter": topic,
                "results_count": len(results),
                "results": results,
                "keyword_guide": self._get_keyword_guide(),
                "official_data_url": self.base_url
            }

        except Exception as e:
            error_msg = f"Lỗi khi tìm kiếm CFR: {str(e)}"
            log_debug(error_msg)
            logger.error(error_msg, exc_info=True)
            
            # Xử lý lỗi chi tiết hơn
            error_type = type(e).__name__
            error_details = str(e)
            
            if "SSLError" in error_type:
                error_msg = "Lỗi kết nối bảo mật. Vui lòng kiểm tra kết nối mạng hoặc thử lại sau."
            elif "Timeout" in error_type:
                error_msg = "Quá thời gian chờ kết nối. Vui lòng kiểm tra kết nối mạng hoặc thử lại sau."
            elif "ConnectionError" in error_type:
                error_msg = "Không thể kết nối đến máy chủ CFR. Vui lòng kiểm tra kết nối mạng."
            
            return {
                "status": "error",
                "source": "CFR",
                "error_type": error_type,
                "message": error_msg,
                "details": error_details,
                "query": query,
                "suggested_actions": [
                    "Kiểm tra kết nối mạng",
                    "Thử lại sau ít phút",
                    "Kiểm tra lại từ khóa tìm kiếm"
                ]
            }
    
    def _get_keyword_guide(self) -> List[str]:
        """
        Trả về danh sách gợi ý từ khóa tìm kiếm
        """
        return [
            # Chính sách đối ngoại các nước
            "US-China relations", "EU foreign policy", "Russia sanctions",
            "Middle East peace process", "Indo-Pacific strategy", "NATO expansion",
            "Brexit implications", "African Union agenda", "Latin America politics",
            "ASEAN regional cooperation", "Arctic policy", "Belt and Road Initiative",
            
            # Các vấn đề toàn cầu
            "Climate change policy", "Nuclear nonproliferation", "Global health security",
            "Cybersecurity threats", "Disinformation campaigns", "Human rights reports",
            "Refugee crisis", "Global trade wars", "Energy security",
            "Food security", "Water scarcity", "Space governance",
            
            # Chủ đề kinh tế
            "Global economic outlook", "Trade agreements", "Supply chain resilience",
            "Digital economy", "Cryptocurrency regulation", "Tax havens",
            "Development finance", "Debt relief", "Infrastructure investment",
            
            # An ninh quốc tế
            "Counterterrorism strategy", "Arms control", "Cybersecurity framework",
            "Maritime security", "Peacekeeping operations", "Intelligence sharing",
            "Hybrid warfare", "Disarmament talks", "Non-state actors"
        ]
