# Astronomy Team - <PERSON><PERSON><PERSON> ngũ chuyên gia thiên văn học

## Giới thiệu

Đây là một dự án xây dựng đội ngũ AI chuyên gia về thiên văn họ<PERSON>, c<PERSON> khả năng trả lời các câu hỏi chuyên sâu bằng cách phân tích câu hỏi, tìm kiếm tài liệu khoa học từ NASA ADS, kiểm định chất lượng thông tin và tổng hợp thành câu trả lời chính xác, đáng tin cậy.

## C<PERSON><PERSON> thành phần chính

1. **Analysis Agent**: <PERSON>ân tích câu hỏi và xác định từ khóa tìm kiếm
2. **NASA ADS Agent**: Tìm kiếm tài liệu khoa học từ cơ sở dữ liệu NASA ADS
3. **Quality Control Agent**: <PERSON><PERSON><PERSON> gi<PERSON> chất lượng thông tin tìm được
4. **Writer Agent**: <PERSON><PERSON><PERSON> hợ<PERSON> và viết câu trả lời
5. **Astronomy Team**: <PERSON><PERSON><PERSON><PERSON> phối hoạt động giữa các agent
6. **Astronomy Workflow**: Quy trình làm việc từ đầu đến cuối

## Yêu cầu hệ thống

- Python 3.8+
- Ollama với mô hình Qwen 4B đã được tải về
- Thư viện: agno, requests, pydantic

## Cài đặt

1. Cài đặt Ollama và tải mô hình Qwen 4B:
   ```bash
   ollama pull qwen:4b
   ```

2. Cài đặt các thư viện cần thiết:
   ```bash
   pip install agno requests pydantic
   ```

## Cách sử dụng

### Chạy ví dụ mẫu

```python
from astronomy_team import AstronomyWorkflow
import asyncio

async def main():
    # Khởi tạo workflow
    workflow = AstronomyWorkflow()
    
    # Đặt câu hỏi
    query = "Hãy giải thích về hố đen và các đặc điểm của nó"
    
    # Thực thi workflow
    response = await workflow.arun(query)
    
    # In kết quả
    print("\n=== KẾT QUẢ ===")
    print(response.content)
    
    if response.metadata.get("sources"):
        print("\n=== NGUỒN THAM KHẢO ===")
        for i, source in enumerate(response.metadata["sources"], 1):
            print(f"{i}. {source}")

# Chạy chương trình
if __name__ == "__main__":
    asyncio.run(main())
```

### Tích hợp vào ứng dụng

```python
from astronomy_team import AstronomyWorkflow

class AstronomyChatbot:
    def __init__(self):
        self.workflow = AstronomyWorkflow()
    
    async def ask(self, question: str) -> dict:
        """Đặt câu hỏi và nhận câu trả lời"""
        response = await self.workflow.arun(question)
        return {
            "answer": response.content,
            "sources": response.metadata.get("sources", []),
            "status": response.metadata.get("status", "unknown")
        }
```

## Cấu hình

Các thông số cấu hình có thể được điều chỉnh trong file `config.py`:

- `MODEL_CONFIG`: Cấu hình mô hình ngôn ngữ
- `NASA_ADS_CONFIG`: Cấu hình kết nối với NASA ADS API
- `QDANT_CONFIG`: Cấu hình kết nối với Qdrant (nếu sử dụng)
- `MEMORY_CONFIG`: Cấu hình bộ nhớ

## Giấy phép

Dự án này được phát hành theo giấy phép MIT.
