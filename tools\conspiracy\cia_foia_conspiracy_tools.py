import requests
import json
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
from datetime import datetime
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class CIAFOIAConspiracyTools(Toolkit):
    """
    Công cụ tìm kiếm và truy xuất tài liệu giải mật từ kho lưu trữ FOIA của CIA.
    
    Cung cấp quyền truy cập vào các tài liệu đã được giải mật liên quan đến
    các thuyết âm mưu, dự án bí mật và các hiện tượng bất thường.
    
    Keyword gợi ý: "UFO", "MKUltra", "mind control", "declassified", 
    "project blue book", "remote viewing", "stargate project", "operation mockingbird"
    """
    
    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(name="cia_foia_tools", **kwargs)
        self.base_url = "https://www.cia.gov/readingroom"
        self.search_url = f"{self.base_url}/search/site"
        if enable_search:
            self.register(self.search_documents)
            self.register(self.get_popular_searches)
    
    def search_documents(self, query: str, max_results: int = 5) -> str:
        """
        Tìm kiếm tài liệu giải mật từ kho lưu trữ FOIA của CIA.
        
        Args:
            query (str): Từ khóa tìm kiếm (ví dụ: "MKUltra", "UFO")
            max_results (int, optional): Số lượng kết quả tối đa. Mặc định: 5.
            
        Returns:
            str: Chuỗi JSON chứa kết quả tìm kiếm
            
        Ví dụ:
            search_documents("MKUltra", 3)
        """
        log_debug(f"Tìm kiếm tài liệu CIA FOIA: {query}")
        
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        
        try:
            params = {
                "search": query,
                "page": 0,
                "items_per_page": max_results
            }
            
            response = requests.get(
                self.search_url,
                params=params,
                headers=headers,
                timeout=15
            )
            response.raise_for_status()
            
            # Xử lý kết quả JSON
            data = response.json()
            results = []
            
            if 'list' in data and len(data['list']) > 0:
                for item in data['list'][:max_results]:
                    result = {
                        "title": item.get('title', 'Không có tiêu đề'),
                        "url": f"{self.base_url}{item.get('url', '')}",
                        "summary": item.get('snippet', '').replace('<em>', '').replace('</em>', ''),
                        "date": item.get('created', ''),
                        "type": item.get('bundle', 'document'),
                        "source": "CIA FOIA"
                    }
                    results.append(result)
            
            # Nếu không có kết quả, trả về kết quả mặc định
            if not results:
                return self._get_default_results(query)
            
            return json.dumps({
                "status": "success",
                "source": "CIA FOIA",
                "query": query,
                "results": results,
                "result_count": len(results),
                "search_url": response.url
            }, indent=2, ensure_ascii=False)
            
        except requests.RequestException as e:
            logger.error(f"Lỗi khi truy vấn CIA FOIA: {e}")
            return self._get_error_response(query, str(e))
    
    def get_popular_searches(self) -> str:
        """
        Lấy danh sách các từ khóa tìm kiếm phổ biến nhất trên CIA FOIA.
        
        Returns:
            str: Chuỗi JSON chứa các từ khóa tìm kiếm phổ biến
        """
        log_debug("Lấy danh sách tìm kiếm phổ biến từ CIA FOIA")
        
        popular_searches = [
            "MKUltra", "UFO", "Remote Viewing", "Project Stargate",
            "JFK Assassination", "Mind Control", "Project Blue Book",
            "Area 51", "Operation Mockingbird", "MKNAOMI"
        ]
        
        return json.dumps({
            "status": "success",
            "source": "CIA FOIA",
            "popular_searches": popular_searches,
            "last_updated": datetime.now().strftime("%Y-%m-%d")
        }, indent=2, ensure_ascii=False)
    
    def _get_default_results(self, query: str) -> str:
        """Trả về kết quả mặc định khi không tìm thấy kết quả."""
        default_results = [
            {
                "title": "MKUltra Declassified Files",
                "url": "https://www.cia.gov/readingroom/search/site/mkultra",
                "summary": "Các tài liệu đã giải mật về Dự án MKUltra - chương trình kiểm soát tâm trí của CIA.",
                "date": "2017-01-17",
                "type": "document_collection",
                "source": "CIA FOIA"
            },
            {
                "title": "UFO CIA Files",
                "url": "https://www.cia.gov/readingroom/search/site/ufo",
                "summary": "Hồ sơ CIA về các vật thể bay không xác định (UFO) và hiện tượng liên quan.",
                "date": "2016-08-02",
                "type": "document_collection",
                "source": "CIA FOIA"
            },
            {
                "title": "Project STARGATE",
                "url": "https://www.cia.gov/readingroom/collection/project-stargate",
                "summary": "Tài liệu về Dự án STARGATE - nghiên cứu về ngoại cảm và thấu thị từ xa.",
                "date": "2017-01-18",
                "type": "document_collection",
                "source": "CIA FOIA"
            }
        ]
        
        return json.dumps({
            "status": "success",
            "source": "CIA FOIA",
            "query": query,
            "message": "Không tìm thấy kết quả phù hợp. Dưới đây là một số tài liệu mặc định.",
            "results": default_results,
            "result_count": len(default_results)
        }, indent=2, ensure_ascii=False)
    
    def _get_error_response(self, query: str, error_msg: str) -> str:
        """Trả về phản hồi lỗi có cấu trúc."""
        return json.dumps({
            "status": "error",
            "source": "CIA FOIA",
            "query": query,
            "message": f"Không thể truy xuất kết quả: {error_msg}",
            "results": []
        }, indent=2, ensure_ascii=False)
