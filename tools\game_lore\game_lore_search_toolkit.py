# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
from datetime import datetime

class GameLoreSearchToolkit(Toolkit):
    """
    Game Lore Search Toolkit cho tìm kiếm và tổng hợp thông tin lore từ nhiều nguồn.
    """

    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(
            name="game_lore_search_toolkit",
            **kwargs
        )
        
        self.search_sources = {
            "wikia": "https://gameinfo.fandom.com",
            "wikipedia": "https://en.wikipedia.org",
            "reddit": "https://www.reddit.com/r/",
            "gamepedia": "https://gamepedia.fandom.com",
            "official_sites": "Various official game websites"
        }
        
        if enable_search:
            self.register(self.search_character_lore)
            self.register(self.search_world_lore)
            self.register(self.search_timeline_events)
            self.register(self.comprehensive_lore_search)
            self.register(self.search_fan_theories)

    def search_character_lore(self, character_name: str, game_franchise: str = "",
                            search_depth: str = "standard") -> str:
        """
        Tìm kiếm thông tin lore về một nhân vật cụ thể.

        Args:
        - character_name: Tên nhân vật
        - game_franchise: Franchise game (để thu hẹp kết quả)
        - search_depth: Mức độ tìm kiếm ('basic', 'standard', 'comprehensive')

        Returns:
        - JSON string với thông tin lore nhân vật
        """
        log_debug(f"Searching character lore for {character_name}")

        try:
            # Character basic information
            character_info = self._gather_character_basic_info(character_name, game_franchise)
            
            # Character development across games
            character_development = self._trace_character_development(character_name, game_franchise)
            
            # Relationships and connections
            relationships = self._find_character_relationships(character_name)
            
            # Fan interpretations and theories
            fan_content = self._gather_fan_character_content(character_name) if search_depth != "basic" else {}
            
            # Character quotes and memorable moments
            memorable_content = self._find_memorable_character_content(character_name)
            
            # Character impact analysis
            impact_analysis = self._analyze_character_impact(character_name, game_franchise)

            result = {
                "search_parameters": {
                    "character_name": character_name,
                    "game_franchise": game_franchise or "All franchises",
                    "search_depth": search_depth,
                    "sources_searched": list(self.search_sources.keys())
                },
                "character_info": character_info,
                "character_development": character_development,
                "relationships": relationships,
                "fan_content": fan_content,
                "memorable_content": memorable_content,
                "impact_analysis": impact_analysis,
                "search_quality": self._assess_search_quality(character_info, search_depth),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error searching character lore: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_world_lore(self, world_element: str, game_franchise: str,
                         element_type: str = "location") -> str:
        """
        Tìm kiếm thông tin lore về thế giới game.

        Args:
        - world_element: Tên địa điểm, tổ chức, hoặc khái niệm thế giới
        - game_franchise: Franchise game
        - element_type: Loại element ('location', 'organization', 'concept', 'artifact')

        Returns:
        - JSON string với thông tin lore thế giới
        """
        log_debug(f"Searching world lore for {world_element} in {game_franchise}")

        try:
            # Basic world element information
            element_info = self._gather_world_element_info(world_element, game_franchise, element_type)
            
            # Historical context
            historical_context = self._find_historical_context(world_element, game_franchise)
            
            # Related elements and connections
            related_elements = self._find_related_world_elements(world_element, element_type)
            
            # Evolution across games
            element_evolution = self._trace_element_evolution(world_element, game_franchise)
            
            # Cultural significance
            cultural_significance = self._analyze_cultural_significance(world_element, game_franchise)
            
            # Fan interpretations
            fan_interpretations = self._gather_fan_world_theories(world_element)

            result = {
                "search_parameters": {
                    "world_element": world_element,
                    "game_franchise": game_franchise,
                    "element_type": element_type,
                    "search_scope": "World lore comprehensive search"
                },
                "element_info": element_info,
                "historical_context": historical_context,
                "related_elements": related_elements,
                "element_evolution": element_evolution,
                "cultural_significance": cultural_significance,
                "fan_interpretations": fan_interpretations,
                "lore_connections": self._map_lore_connections(world_element, related_elements),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error searching world lore: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_timeline_events(self, game_franchise: str, time_period: str = "all",
                             event_type: str = "major") -> str:
        """
        Tìm kiếm các sự kiện trong timeline lore của game.

        Args:
        - game_franchise: Franchise game
        - time_period: Khoảng thời gian ('ancient', 'recent', 'all', 'specific_era')
        - event_type: Loại sự kiện ('major', 'minor', 'all', 'wars', 'discoveries')

        Returns:
        - JSON string với timeline events
        """
        log_debug(f"Searching timeline events for {game_franchise}")

        try:
            # Timeline construction
            timeline_events = self._construct_timeline(game_franchise, time_period, event_type)
            
            # Event categorization
            event_categories = self._categorize_timeline_events(timeline_events)
            
            # Cause and effect analysis
            causality_analysis = self._analyze_event_causality(timeline_events)
            
            # Historical accuracy assessment
            historical_assessment = self._assess_historical_accuracy(timeline_events, game_franchise)
            
            # Timeline gaps and mysteries
            timeline_gaps = self._identify_timeline_gaps(timeline_events)
            
            # Fan timeline theories
            fan_timeline_theories = self._gather_fan_timeline_theories(game_franchise)

            result = {
                "search_parameters": {
                    "game_franchise": game_franchise,
                    "time_period": time_period,
                    "event_type": event_type,
                    "timeline_scope": "Comprehensive chronological analysis"
                },
                "timeline_events": timeline_events,
                "event_categories": event_categories,
                "causality_analysis": causality_analysis,
                "historical_assessment": historical_assessment,
                "timeline_gaps": timeline_gaps,
                "fan_timeline_theories": fan_timeline_theories,
                "timeline_quality": self._assess_timeline_quality(timeline_events),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error searching timeline events: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def comprehensive_lore_search(self, search_query: str, game_franchise: str = "",
                                search_scope: str = "all") -> str:
        """
        Tìm kiếm toàn diện thông tin lore theo query.

        Args:
        - search_query: Từ khóa tìm kiếm
        - game_franchise: Franchise game (tùy chọn)
        - search_scope: Phạm vi tìm kiếm ('all', 'characters', 'world', 'events', 'items')

        Returns:
        - JSON string với kết quả tìm kiếm toàn diện
        """
        log_debug(f"Comprehensive lore search for: {search_query}")

        try:
            # Multi-source search results
            search_results = {}
            
            if search_scope in ["all", "characters"]:
                search_results["characters"] = self._search_characters(search_query, game_franchise)
            
            if search_scope in ["all", "world"]:
                search_results["world_elements"] = self._search_world_elements(search_query, game_franchise)
            
            if search_scope in ["all", "events"]:
                search_results["events"] = self._search_events(search_query, game_franchise)
            
            if search_scope in ["all", "items"]:
                search_results["items"] = self._search_items(search_query, game_franchise)
            
            # Cross-reference analysis
            cross_references = self._analyze_cross_references(search_results)
            
            # Relevance ranking
            relevance_ranking = self._rank_search_relevance(search_results, search_query)
            
            # Related searches
            related_searches = self._suggest_related_searches(search_query, search_results)
            
            # Search completeness assessment
            completeness_assessment = self._assess_search_completeness(search_results, search_query)

            result = {
                "search_parameters": {
                    "search_query": search_query,
                    "game_franchise": game_franchise or "All franchises",
                    "search_scope": search_scope,
                    "sources_consulted": list(self.search_sources.keys())
                },
                "search_results": search_results,
                "cross_references": cross_references,
                "relevance_ranking": relevance_ranking,
                "related_searches": related_searches,
                "completeness_assessment": completeness_assessment,
                "search_statistics": self._generate_search_statistics(search_results),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error in comprehensive lore search: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_fan_theories(self, theory_topic: str, game_franchise: str,
                          theory_type: str = "all") -> str:
        """
        Tìm kiếm fan theories về game lore.

        Args:
        - theory_topic: Chủ đề theory
        - game_franchise: Franchise game
        - theory_type: Loại theory ('all', 'character', 'plot', 'world', 'future')

        Returns:
        - JSON string với fan theories
        """
        log_debug(f"Searching fan theories about {theory_topic}")

        try:
            # Theory collection
            fan_theories = self._collect_fan_theories(theory_topic, game_franchise, theory_type)
            
            # Theory credibility analysis
            credibility_analysis = self._analyze_theory_credibility(fan_theories)
            
            # Popular vs niche theories
            popularity_analysis = self._analyze_theory_popularity(fan_theories)
            
            # Evidence assessment
            evidence_assessment = self._assess_theory_evidence(fan_theories)
            
            # Theory evolution tracking
            theory_evolution = self._track_theory_evolution(fan_theories, theory_topic)
            
            # Community reception
            community_reception = self._analyze_community_reception(fan_theories)

            result = {
                "search_parameters": {
                    "theory_topic": theory_topic,
                    "game_franchise": game_franchise,
                    "theory_type": theory_type,
                    "search_focus": "Fan community theories and speculation"
                },
                "fan_theories": fan_theories,
                "credibility_analysis": credibility_analysis,
                "popularity_analysis": popularity_analysis,
                "evidence_assessment": evidence_assessment,
                "theory_evolution": theory_evolution,
                "community_reception": community_reception,
                "theory_recommendations": self._recommend_theories(fan_theories, credibility_analysis),
                "timestamp": datetime.now().isoformat()
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            log_debug(f"Error searching fan theories: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    # Helper methods (simplified implementations)
    def _gather_character_basic_info(self, character: str, franchise: str) -> dict:
        """Gather basic character information."""
        return {
            "name": character,
            "franchise": franchise,
            "first_appearance": "Game 1",
            "character_type": "Protagonist/Antagonist/Supporting",
            "species": "Human/Other",
            "occupation": "Various roles",
            "status": "Alive/Dead/Unknown"
        }

    def _trace_character_development(self, character: str, franchise: str) -> dict:
        """Trace character development across games."""
        return {
            "development_arc": "Character growth trajectory",
            "key_moments": ["Moment 1", "Moment 2", "Moment 3"],
            "personality_evolution": "Character personality changes",
            "relationship_changes": "How relationships evolved"
        }

    def _find_character_relationships(self, character: str) -> dict:
        """Find character relationships."""
        return {
            "allies": ["Ally 1", "Ally 2"],
            "enemies": ["Enemy 1", "Enemy 2"],
            "family": ["Family member 1"],
            "romantic_interests": ["Love interest 1"],
            "mentors": ["Mentor 1"]
        }

    def _gather_fan_character_content(self, character: str) -> dict:
        """Gather fan content about character."""
        return {
            "fan_theories": ["Theory 1", "Theory 2"],
            "popular_interpretations": ["Interpretation 1", "Interpretation 2"],
            "fan_art_themes": ["Theme 1", "Theme 2"],
            "shipping_popularity": "High/Medium/Low"
        }

    def _find_memorable_character_content(self, character: str) -> dict:
        """Find memorable character content."""
        return {
            "famous_quotes": ["Quote 1", "Quote 2"],
            "iconic_moments": ["Moment 1", "Moment 2"],
            "memorable_scenes": ["Scene 1", "Scene 2"],
            "character_themes": ["Theme 1", "Theme 2"]
        }

    def _analyze_character_impact(self, character: str, franchise: str) -> dict:
        """Analyze character impact."""
        return {
            "narrative_importance": "High/Medium/Low",
            "fan_popularity": "High/Medium/Low",
            "cultural_impact": "Significant/Moderate/Minimal",
            "merchandise_presence": "Extensive/Moderate/Limited"
        }

    def _assess_search_quality(self, info: dict, depth: str) -> dict:
        """Assess search result quality."""
        return {
            "information_completeness": "High/Medium/Low",
            "source_reliability": "High/Medium/Low",
            "search_depth_achieved": depth,
            "gaps_identified": ["Gap 1", "Gap 2"]
        }

    def _gather_world_element_info(self, element: str, franchise: str, element_type: str) -> dict:
        """Gather world element information."""
        return {
            "name": element,
            "type": element_type,
            "franchise": franchise,
            "description": f"Description of {element}",
            "significance": "Major/Minor role in lore",
            "first_mentioned": "Game/Source 1"
        }

    def _find_historical_context(self, element: str, franchise: str) -> dict:
        """Find historical context."""
        return {
            "historical_period": "Ancient/Medieval/Modern/Future",
            "founding_events": ["Event 1", "Event 2"],
            "key_historical_figures": ["Figure 1", "Figure 2"],
            "historical_significance": "Major/Minor historical importance"
        }

    def _find_related_world_elements(self, element: str, element_type: str) -> list:
        """Find related world elements."""
        return [
            {"name": "Related Element 1", "relationship": "Connected to"},
            {"name": "Related Element 2", "relationship": "Part of"},
            {"name": "Related Element 3", "relationship": "Influences"}
        ]

    def _trace_element_evolution(self, element: str, franchise: str) -> dict:
        """Trace element evolution."""
        return {
            "evolution_timeline": ["Change 1", "Change 2", "Change 3"],
            "major_changes": ["Major change 1", "Major change 2"],
            "consistency_across_games": "High/Medium/Low",
            "retcons_identified": ["Retcon 1", "Retcon 2"]
        }

    def _analyze_cultural_significance(self, element: str, franchise: str) -> dict:
        """Analyze cultural significance."""
        return {
            "cultural_references": ["Reference 1", "Reference 2"],
            "real_world_inspirations": ["Inspiration 1", "Inspiration 2"],
            "symbolic_meaning": "Symbolic interpretation",
            "fan_cultural_impact": "High/Medium/Low"
        }

    def _gather_fan_world_theories(self, element: str) -> list:
        """Gather fan theories about world elements."""
        return [
            {"theory": "Fan theory 1", "popularity": "High", "evidence": "Strong"},
            {"theory": "Fan theory 2", "popularity": "Medium", "evidence": "Moderate"}
        ]

    def _map_lore_connections(self, element: str, related: list) -> dict:
        """Map lore connections."""
        return {
            "direct_connections": len(related),
            "connection_strength": "Strong/Moderate/Weak",
            "network_position": "Central/Peripheral",
            "lore_importance": "Critical/Important/Supporting"
        }

    def _construct_timeline(self, franchise: str, period: str, event_type: str) -> list:
        """Construct timeline of events."""
        return [
            {"event": "Event 1", "date": "Year 1", "type": "Major", "description": "Description 1"},
            {"event": "Event 2", "date": "Year 2", "type": "Minor", "description": "Description 2"},
            {"event": "Event 3", "date": "Year 3", "type": "Major", "description": "Description 3"}
        ]

    def _categorize_timeline_events(self, events: list) -> dict:
        """Categorize timeline events."""
        return {
            "wars": [e for e in events if "war" in e.get("type", "").lower()],
            "discoveries": [e for e in events if "discovery" in e.get("type", "").lower()],
            "political_events": [e for e in events if "political" in e.get("type", "").lower()],
            "natural_disasters": [e for e in events if "disaster" in e.get("type", "").lower()]
        }

    def _analyze_event_causality(self, events: list) -> dict:
        """Analyze event causality."""
        return {
            "causal_chains": ["Chain 1: Event A → Event B → Event C"],
            "independent_events": ["Event X", "Event Y"],
            "catalyst_events": ["Catalyst Event 1"],
            "consequence_events": ["Consequence Event 1"]
        }

    def _assess_historical_accuracy(self, events: list, franchise: str) -> dict:
        """Assess historical accuracy."""
        return {
            "internal_consistency": "High/Medium/Low",
            "chronological_accuracy": "Accurate/Some issues/Major issues",
            "contradictions_found": 0,
            "accuracy_rating": "Excellent/Good/Fair/Poor"
        }

    def _identify_timeline_gaps(self, events: list) -> list:
        """Identify timeline gaps."""
        return [
            {"gap_period": "Years X-Y", "description": "Unexplained period"},
            {"gap_period": "Years Z-W", "description": "Missing information"}
        ]

    def _gather_fan_timeline_theories(self, franchise: str) -> list:
        """Gather fan timeline theories."""
        return [
            {"theory": "Timeline theory 1", "evidence": "Strong", "popularity": "High"},
            {"theory": "Timeline theory 2", "evidence": "Moderate", "popularity": "Medium"}
        ]

    def _assess_timeline_quality(self, events: list) -> dict:
        """Assess timeline quality."""
        return {
            "completeness": "High/Medium/Low",
            "detail_level": "Comprehensive/Adequate/Basic",
            "reliability": "High/Medium/Low",
            "fan_acceptance": "Widely accepted/Debated/Controversial"
        }

    def _search_characters(self, query: str, franchise: str) -> list:
        """Search for characters."""
        return [
            {"name": "Character 1", "relevance": 0.9, "franchise": franchise},
            {"name": "Character 2", "relevance": 0.7, "franchise": franchise}
        ]

    def _search_world_elements(self, query: str, franchise: str) -> list:
        """Search for world elements."""
        return [
            {"name": "Location 1", "type": "location", "relevance": 0.8},
            {"name": "Organization 1", "type": "organization", "relevance": 0.6}
        ]

    def _search_events(self, query: str, franchise: str) -> list:
        """Search for events."""
        return [
            {"name": "Event 1", "type": "major", "relevance": 0.9},
            {"name": "Event 2", "type": "minor", "relevance": 0.5}
        ]

    def _search_items(self, query: str, franchise: str) -> list:
        """Search for items."""
        return [
            {"name": "Item 1", "type": "weapon", "relevance": 0.8},
            {"name": "Item 2", "type": "artifact", "relevance": 0.7}
        ]

    def _analyze_cross_references(self, results: dict) -> dict:
        """Analyze cross-references."""
        return {
            "character_world_connections": 5,
            "event_character_connections": 3,
            "item_character_connections": 2,
            "total_connections": 10
        }

    def _rank_search_relevance(self, results: dict, query: str) -> list:
        """Rank search results by relevance."""
        all_results = []
        for category, items in results.items():
            for item in items:
                all_results.append({
                    "item": item.get("name", "Unknown"),
                    "category": category,
                    "relevance": item.get("relevance", 0.5)
                })
        return sorted(all_results, key=lambda x: x["relevance"], reverse=True)[:10]

    def _suggest_related_searches(self, query: str, results: dict) -> list:
        """Suggest related searches."""
        return [
            f"{query} timeline",
            f"{query} relationships",
            f"{query} theories",
            f"{query} analysis"
        ]

    def _assess_search_completeness(self, results: dict, query: str) -> dict:
        """Assess search completeness."""
        total_results = sum(len(items) for items in results.values())
        return {
            "total_results_found": total_results,
            "search_coverage": "Comprehensive/Partial/Limited",
            "missing_areas": ["Area 1", "Area 2"],
            "completeness_score": min(1.0, total_results / 10)
        }

    def _generate_search_statistics(self, results: dict) -> dict:
        """Generate search statistics."""
        return {
            "categories_searched": len(results),
            "total_results": sum(len(items) for items in results.values()),
            "average_relevance": 0.75,
            "search_time": "< 1 second"
        }

    def _collect_fan_theories(self, topic: str, franchise: str, theory_type: str) -> list:
        """Collect fan theories."""
        return [
            {
                "theory": "Fan theory 1",
                "author": "Fan 1",
                "popularity": "High",
                "evidence_strength": "Strong",
                "type": theory_type
            },
            {
                "theory": "Fan theory 2", 
                "author": "Fan 2",
                "popularity": "Medium",
                "evidence_strength": "Moderate",
                "type": theory_type
            }
        ]

    def _analyze_theory_credibility(self, theories: list) -> dict:
        """Analyze theory credibility."""
        return {
            "highly_credible": len([t for t in theories if t.get("evidence_strength") == "Strong"]),
            "moderately_credible": len([t for t in theories if t.get("evidence_strength") == "Moderate"]),
            "speculative": len([t for t in theories if t.get("evidence_strength") == "Weak"]),
            "overall_quality": "High/Medium/Low"
        }

    def _analyze_theory_popularity(self, theories: list) -> dict:
        """Analyze theory popularity."""
        return {
            "mainstream_theories": len([t for t in theories if t.get("popularity") == "High"]),
            "niche_theories": len([t for t in theories if t.get("popularity") == "Low"]),
            "trending_theories": ["Theory 1", "Theory 2"],
            "controversial_theories": ["Controversial theory 1"]
        }

    def _assess_theory_evidence(self, theories: list) -> dict:
        """Assess theory evidence."""
        return {
            "well_supported": len([t for t in theories if t.get("evidence_strength") in ["Strong", "Moderate"]]),
            "lacking_evidence": len([t for t in theories if t.get("evidence_strength") == "Weak"]),
            "evidence_types": ["In-game clues", "Developer statements", "Pattern analysis"],
            "evidence_quality": "Good/Fair/Poor"
        }

    def _track_theory_evolution(self, theories: list, topic: str) -> dict:
        """Track theory evolution."""
        return {
            "theory_development": "How theories evolved over time",
            "debunked_theories": ["Debunked theory 1"],
            "confirmed_theories": ["Confirmed theory 1"],
            "emerging_theories": ["New theory 1"]
        }

    def _analyze_community_reception(self, theories: list) -> dict:
        """Analyze community reception."""
        return {
            "community_favorites": ["Favorite theory 1"],
            "divisive_theories": ["Divisive theory 1"],
            "discussion_volume": "High/Medium/Low",
            "community_consensus": "Strong/Weak/Divided"
        }

    def _recommend_theories(self, theories: list, credibility: dict) -> list:
        """Recommend theories."""
        return [
            "Most credible theory based on evidence",
            "Most popular theory in community",
            "Most interesting speculative theory",
            "Theory with best supporting evidence"
        ]
