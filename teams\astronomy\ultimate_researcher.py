import asyncio
from textwrap import dedent
from typing import Union
from pydantic import BaseModel
from rich.console import Console
from rich.panel import Panel

from agno.memory.v2.memory import Memory
from agno.team.team import Team
from agno.models.ollama import Ollama
from agno.storage.agent.sqlite import SqliteAgentStorage
from agno.embedder.ollama import <PERSON><PERSON>ma<PERSON>mbedder
from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct

from agents.astronomy.specialized_agents import (
    lcot_reasoning_agent,
    nasa_ads_agent,
    simbad_agent,
    esa_archives_agent,
    esdc_crawler_agent,
    mcot_agent,
)
from agents.astronomy.writer_thesis_agent import writer_thesis_agent
from utils.chunking import chunk_text
from utils.qdrant_optimization import get_qdrant_optimizer
from utils.reranker import rerank_chunks

MODEL = Ollama(id="qwen3:4b")
QDRANT_URL = "http://localhost:6333"
QDRANT_API_KEY = None
QDRANT_COLLECTION = "astronomy_knowledge"

embedder = OllamaEmbedder(id="nomic-embed-text:latest", dimensions=768)
memory = Memory(model=MODEL)
team_storage = SqliteAgentStorage(table_name="team_sessions", db_file="tmp/persistent_memory.db")
console = Console()
qdrant_client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY)

async def save_content_to_qdrant(content: str, query: str, writer_type: str = "ultimate_researcher"):
    """Lưu nội dung vào Qdrant với chiến lược chia đoạn."""
    if not content or not content.strip():
        console.print("⚠️ Không có nội dung để lưu vào Qdrant.", style="bold red")
        return

    chunks = chunk_text(content, strategy="auto")
    if not chunks:
        console.print("⚠️ Không tạo được chunk từ nội dung.", style="bold red")
        return

    points = []
    for idx, chunk in enumerate(chunks):
        vector = embedder.get_embedding(chunk)
        if not vector or len(vector) != 768:
            console.print(f"⚠️ Vector không hợp lệ cho chunk {idx+1}.", style="bold red")
            continue
        points.append(
            PointStruct(
                id=idx + 1,
                vector=vector,
                payload={
                    "text": chunk,
                    "source": "ultimate_researcher_team",
                    "writer_type": writer_type,
                    "query": query,
                    "chunk_index": idx,
                },
            )
        )

    if points:
        try:
            qdrant_client.upsert(collection_name=QDRANT_COLLECTION, points=points)
        except Exception as e:
            console.print(f"⚠️ Lỗi khi lưu vào Qdrant: {e}", style="bold red")

def build_ultimate_researcher_team(writer_agent):
    """Xây dựng team nghiên cứu thiên văn học tối ưu."""
    team = Team(
        name="Astronomy Ultimate Researcher Team",
        mode="coordinate",
        model=MODEL,
        members=[
            lcot_reasoning_agent,
            nasa_ads_agent,
            simbad_agent,
            esa_archives_agent,
            esdc_crawler_agent,
            mcot_agent,
            writer_agent,
        ],
        instructions=dedent(""" 
            Workflow:
            1. LCoT Reasoning Agent phân tích và chia nhỏ câu hỏi thành các sub-question.
            2. Các agent research (NASA ADS, SIMBAD, ESA, ESDC) tìm kiếm thông tin cho từng sub-question.
            3. MCoT Agent phản biện, tổng hợp đa hướng.
            4. Writer Agent reasoning lại toàn bộ và tổng hợp thành câu trả lời cuối cùng.
            Đảm bảo trả lời chi tiết, logic, có dẫn nguồn, ưu tiên tiếng Việt.
        """),
        memory=memory,
        storage=team_storage,
        markdown=True,
    )

    async def aprint_response(self, query: Union[str, list, dict], **kwargs):
        """Chạy team và hiển thị phản hồi."""
        result = await self.arun(query, **kwargs)
        if not result or not result.content:
            console.print("⚠️ Không nhận được phản hồi từ team.", style="bold red")
            return None

        content = result.content
        if isinstance(content, BaseModel):
            content = content.model_dump_json(indent=2)

        console.print(Panel(content, title="Phản hồi từ Team", border_style="blue"))

        if result.tools:
            tool_calls = [tool.model_dump_json(indent=2) if isinstance(tool, BaseModel) else str(tool) for tool in result.tools]
            console.print(Panel("\n".join(tool_calls), title="Các lệnh gọi công cụ", border_style="yellow"))

        return content

    team.aprint_response = aprint_response.__get__(team)
    return team

def chat_loop(team, writer_type: str = "ultimate_researcher"):
    """Vòng lặp trò chuyện với người dùng."""
    console.print("=" * 50)
    console.print("Chào mừng đến với Astronomy Ultimate Researcher Team!", style="bold green")
    console.print("Nhập 'exit' hoặc 'quit' để thoát.", style="italic")
    console.print("=" * 50)

    while True:
        try:
            user_input = input("\nBạn: ")
            if user_input.lower() in ["exit", "quit", "thoát"]:
                console.print("\nCảm ơn bạn đã sử dụng Astronomy Ultimate Researcher Team. Tạm biệt!", style="bold blue")
                break

            content = asyncio.run(team.aprint_response(user_input))
            if content:
                asyncio.run(save_content_to_qdrant(content, user_input, writer_type=writer_type))
            else:
                console.print("⚠️ Không nhận được phản hồi từ team.", style="bold red")

        except Exception as e:
            console.print(f"\nĐã xảy ra lỗi: {e}", style="bold red")

def main():
    """Hàm chính để chạy chương trình."""
    team = build_ultimate_researcher_team(writer_thesis_agent)
    chat_loop(team)

if __name__ == "__main__":
    main()