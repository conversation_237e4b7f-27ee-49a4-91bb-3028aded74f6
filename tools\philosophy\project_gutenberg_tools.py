from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json
import re
import aiohttp
import asyncio
from urllib.parse import urljoin, quote, unquote

from agno.tools import Toolkit
from agno.utils.log import logger

# Các hằng số
GUTENDEX_API = "https://gutendex.com/books"
GUTENBERG_BASE_URL = "https://www.gutenberg.org/ebooks"
CACHE_EXPIRY_DAYS = 30  # Cache lâu hơn vì dữ liệu ít thay đổi

@dataclass
class GutenbergBook:
    """Lớp đại diện cho một cuốn sách từ Project Gutenberg"""
    id: int
    title: str
    authors: List[Dict[str, str]]
    languages: List[str]
    subjects: List[str]
    bookshelves: List[str]
    download_count: int
    formats: Dict[str, str]
    copyright: bool
    media_type: str
    
    @property
    def gutenberg_url(self) -> str:
        return f"{GUTENBERG_BASE_URL}/{self.id}"
    
    @property
    def plain_text_url(self) -> Optional[str]:
        return self.formats.get("text/plain; charset=utf-8")
    
    @property
    def html_url(self) -> Optional[str]:
        return self.formats.get("text/html")
    
    @property
    def epub_url(self) -> Optional[str]:
        return self.formats.get("application/epub+zip")
    
    @property
    def kindle_url(self) -> Optional[str]:
        return self.formats.get("application/x-mobipocket-ebook")

class ProjectGutenbergPhilosophyTool(Toolkit):
    """
    Công cụ tìm kiếm và tải về các tác phẩm triết học kinh điển từ Project Gutenberg
    """

    def __init__(self):
        super().__init__(
            name="Công cụ Project Gutenberg Triết học",
            tools=[
                self.search_books,
                self.get_book_details,
                self.get_author_works,
                self.get_philosophy_categories,
                self.get_popular_philosophy_books
            ]
        )
        self.session = None
        self.cache = {}
        self._load_cache()

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def _load_cache(self) -> None:
        """Tải dữ liệu cache từ file"""
        try:
            with open(".gutenberg_cache.json", 'r', encoding='utf-8') as f:
                self.cache = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            self.cache = {}

    def _save_cache(self) -> None:
        """Lưu dữ liệu cache vào file"""
        with open(".gutenberg_cache.json", 'w', encoding='utf-8') as f:
            json.dump(self.cache, f, ensure_ascii=False, indent=2)

    def _get_cache(self, key: str) -> Any:
        """Lấy dữ liệu từ cache"""
        cached = self.cache.get(key)
        if cached and datetime.now().timestamp() < cached.get('expires', 0):
            return cached['data']
        return None

    def _set_cache(self, key: str, data: Any, ttl: int = 2592000) -> None:
        """Lưu dữ liệu vào cache (mặc định 30 ngày)"""
        self.cache[key] = {
            'data': data,
            'expires': datetime.now().timestamp() + ttl
        }
        self._save_cache()

    async def _make_request(self, url: str, params: Dict = None, use_cache: bool = True) -> Dict:
        """Gửi yêu cầu HTTP đến Gutendex API"""
        cache_key = f"req_{url}?{urlencode(params or {})}"
        
        # Kiểm tra cache
        if use_cache:
            cached = self._get_cache(cache_key)
            if cached is not None:
                return cached
        
        # Gửi yêu cầu mới
        try:
            async with self.session.get(url, params=params) as response:
                response.raise_for_status()
                data = await response.json()
                
                # Lưu vào cache
                self._set_cache(cache_key, data)
                return data
                
        except Exception as e:
            logger.error(f"Lỗi khi gửi yêu cầu đến {url}: {str(e)}")
            raise

    async def search_books(
        self, 
        query: str, 
        author: Optional[str] = None,
        topic: Optional[str] = "philosophy",
        language: str = "en",
        limit: int = 10,
        sort_by: str = "popular",
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Tìm kiếm sách triết học trên Project Gutenberg
        
        Parameters:
        - query: Từ khóa tìm kiếm (tên sách, tác giả, chủ đề)
        - author: Lọc theo tác giả (tùy chọn)
        - topic: Chủ đề (mặc định: 'philosophy')
        - language: Mã ngôn ngữ (mặc định: 'en')
        - limit: Số lượng kết quả tối đa
        - sort_by: Sắp xếp theo 'popular' (phổ biến) hoặc 'new' (mới nhất)
        - use_cache: Sử dụng cache hay không
        
        Returns:
        - Dict chứa kết quả tìm kiếm
        """
        logger.info(f"Tìm kiếm sách triết học: {query}, tác giả={author}, chủ đề={topic}")
        
        try:
            # Xây dựng tham số tìm kiếm
            search_terms = []
            if query:
                search_terms.append(query)
            if author:
                search_terms.append(f"inauthor:{author}")
            if topic:
                search_terms.append(f"subject:\"{topic}\"")
            
            params = {
                "search": " ".join(search_terms),
                "languages": language,
                "mime_type": "text/plain,text/html,application/epub+zip,application/x-mobipocket-ebook",
                "page": 1
            }
            
            # Sắp xếp kết quả
            if sort_by == "popular":
                params["sort"] = "popular"
            
            # Gửi yêu cầu tìm kiếm
            data = await self._make_request(GUTENDEX_API, params, use_cache)
            
            # Xử lý kết quả
            books = []
            for book_data in data.get("results", [])[:limit]:
                try:
                    book = self._parse_book_data(book_data)
                    books.append(asdict(book))
                except Exception as e:
                    logger.warning(f"Không thể phân tích dữ liệu sách: {str(e)}")
            
            return {
                "status": "success",
                "query": query,
                "author": author,
                "topic": topic,
                "language": language,
                "results_count": len(books),
                "results": books,
                "suggested_searches": self._get_search_suggestions()
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi tìm kiếm sách: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "query": query,
                "author": author,
                "topic": topic
            }
    
    def _parse_book_data(self, book_data: Dict) -> GutenbergBook:
        """Phân tích dữ liệu sách từ API"""
        return GutenbergBook(
            id=book_data.get("id"),
            title=book_data.get("title", "Không có tiêu đề"),
            authors=book_data.get("authors", []),
            languages=book_data.get("languages", []),
            subjects=book_data.get("subjects", []),
            bookshelves=book_data.get("bookshelves", []),
            download_count=book_data.get("download_count", 0),
            formats=book_data.get("formats", {}),
            copyright=book_data.get("copyright", True),
            media_type=book_data.get("media_type", "")
        )
    
    async def get_book_details(
        self, 
        book_id: int,
        include_content: bool = False,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Lấy thông tin chi tiết về một cuốn sách
        
        Parameters:
        - book_id: ID của sách trên Project Gutenberg
        - include_content: Có bao gồm nội dung sách không
        - use_cache: Sử dụng cache hay không
        """
        logger.info(f"Lấy thông tin sách ID: {book_id}")
        
        try:
            # Lấy thông tin cơ bản
            data = await self._make_request(f"{GUTENDEX_API}/{book_id}", use_cache=use_cache)
            
            if "detail" in data and data["detail"] == "Not found.":
                return {
                    "status": "not_found",
                    "message": f"Không tìm thấy sách với ID: {book_id}",
                    "book_id": book_id
                }
            
            book = self._parse_book_data(data)
            result = asdict(book)
            
            # Lấy nội dung nếu được yêu cầu
            if include_content and book.plain_text_url:
                try:
                    async with self.session.get(book.plain_text_url) as response:
                        response.raise_for_status()
                        content = await response.text()
                        result["content"] = self._clean_gutenberg_content(content)
                except Exception as e:
                    logger.warning(f"Không thể lấy nội dung sách: {str(e)}")
            
            return {
                "status": "success",
                "book": result,
                "related_books": await self._get_related_books(book)
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy thông tin sách: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "book_id": book_id
            }
    
    def _clean_gutenberg_content(self, content: str) -> str:
        """Làm sạch nội dung từ Project Gutenberg"""
        # Loại bỏ header và footer của Project Gutenberg
        start_markers = [
            "*** START OF THIS PROJECT GUTENBERG EBOOK",
            "***START OF THE PROJECT GUTENBERG EBOOK"
        ]
        end_markers = [
            "*** END OF THIS PROJECT GUTENBERG EBOOK",
            "***END OF THE PROJECT GUTENBERG EBOOK"
        ]
        
        # Tìm vị trí bắt đầu và kết thúc
        start_idx = -1
        for marker in start_markers:
            idx = content.find(marker)
            if idx != -1:
                start_idx = idx + len(marker)
                break
        
        end_idx = -1
        for marker in end_markers:
            idx = content.find(marker)
            if idx != -1:
                end_idx = idx
                break
        
        # Cắt nội dung nếu tìm thấy cả hai marker
        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            content = content[start_idx:end_idx]
        
        # Loại bỏ các dòng trống thừa
        lines = [line.strip() for line in content.splitlines() if line.strip()]
        return "\n".join(lines)
    
    async def _get_related_books(self, book: GutenbergBook) -> List[Dict]:
        """Lấy danh sách sách liên quan"""
        related_books = []
        
        # Tìm sách cùng tác giả
        if book.authors:
            author_name = book.authors[0].get("name")
            if author_name:
                try:
                    author_works = await self.get_author_works(
                        author_name,
                        exclude_id=book.id,
                        limit=3
                    )
                    if author_works["status"] == "success":
                        related_books.extend(author_works["books"])
                except Exception as e:
                    logger.warning(f"Không thể lấy sách cùng tác giả: {str(e)}")
        
        # Tìm sách cùng chủ đề
        if book.subjects:
            subject = book.subjects[0]
            try:
                similar_books = await self.search_books(
                    query=f"subject:\"{subject}\"",
                    limit=3,
                    use_cache=True
                )
                if similar_books["status"] == "success":
                    # Loại bỏ sách trùng với sách hiện tại
                    for b in similar_books["results"]:
                        if b["id"] != book.id and b not in related_books:
                            related_books.append(b)
                            if len(related_books) >= 5:  # Giới hạn số lượng
                                break
            except Exception as e:
                logger.warning(f"Không thể lấy sách cùng chủ đề: {str(e)}")
        
        return related_books[:5]  # Trả về tối đa 5 sách liên quan
    
    async def get_author_works(
        self, 
        author_name: str,
        exclude_id: Optional[int] = None,
        limit: int = 10,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Lấy danh sách tác phẩm của một tác giả
        
        Parameters:
        - author_name: Tên tác giả
        - exclude_id: ID sách cần loại trừ (nếu có)
        - limit: Số lượng kết quả tối đa
        - use_cache: Sử dụng cache hay không
        """
        logger.info(f"Lấy tác phẩm của tác giả: {author_name}")
        
        try:
            # Tìm kiếm tác giả
            search_data = await self._make_request(
                GUTENDEX_API,
                {"search": f"inauthor:\"{author_name}\""},
                use_cache
            )
            
            # Lọc kết quả
            books = []
            seen_books = set()
            
            for book_data in search_data.get("results", []):
                if exclude_id is not None and book_data.get("id") == exclude_id:
                    continue
                    
                book_id = book_data.get("id")
                if book_id in seen_books:
                    continue
                    
                book = self._parse_book_data(book_data)
                books.append(asdict(book))
                seen_books.add(book_id)
                
                if len(books) >= limit:
                    break
            
            return {
                "status": "success",
                "author": author_name,
                "books_count": len(books),
                "books": books
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy tác phẩm của tác giả: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "author": author_name
            }
    
    async def get_philosophy_categories(self) -> Dict[str, Any]:
        """Lấy danh sách các danh mục triết học"""
        try:
            # Đây là danh sách các danh mục triết học phổ biến
            categories = [
                "Ethics", "Metaphysics", "Epistemology", "Logic", "Aesthetics",
                "Political Philosophy", "Philosophy of Mind", "Existentialism",
                "Stoicism", "Utilitarianism", "Kantianism", "Phenomenology",
                "Analytic Philosophy", "Continental Philosophy", "Eastern Philosophy",
                "Ancient Philosophy", "Medieval Philosophy", "Modern Philosophy",
                "Contemporary Philosophy", "Philosophy of Science", "Philosophy of Religion"
            ]
            
            return {
                "status": "success",
                "categories_count": len(categories),
                "categories": [{"name": cat, "slug": cat.lower().replace(" ", "-")} for cat in categories]
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy danh mục triết học: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg
            }
    
    async def get_popular_philosophy_books(
        self, 
        limit: int = 10,
        time_period: str = "all",
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Lấy danh sách sách triết học phổ biến
        
        Parameters:
        - limit: Số lượng kết quả tối đa
        - time_period: Khoảng thời gian ('day', 'week', 'month', 'year', 'all')
        - use_cache: Sử dụng cache hay không
        """
        logger.info(f"Lấy sách triết học phổ biến (thời gian: {time_period})")
        
        try:
            # Lấy sách triết học sắp xếp theo số lượt tải
            data = await self._make_request(
                GUTENDEX_API,
                {
                    "topic": "philosophy",
                    "sort": "popular",
                    "languages": "en",
                    "page": 1
                },
                use_cache
            )
            
            # Xử lý kết quả
            books = []
            for book_data in data.get("results", [])[:limit]:
                try:
                    book = self._parse_book_data(book_data)
                    books.append(asdict(book))
                except Exception as e:
                    logger.warning(f"Không thể phân tích dữ liệu sách: {str(e)}")
            
            return {
                "status": "success",
                "time_period": time_period,
                "books_count": len(books),
                "books": books
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy sách phổ biến: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "time_period": time_period
            }
    
    def _get_search_suggestions(self) -> List[Dict[str, str]]:
        """Trả về danh sách gợi ý tìm kiếm"""
        return [
            {"query": "Plato", "description": "Tác phẩm của Plato"},
            {"query": "Nietzsche", "description": "Tác phẩm của Friedrich Nietzsche"},
            {"query": "Stoicism", "description": "Sách về chủ nghĩa Khắc kỷ"},
            {"query": "Ethics", "description": "Sách về đạo đức học"},
            {"query": "Metaphysics", "description": "Sách về siêu hình học"},
            {"query": "Eastern Philosophy", "description": "Triết học phương Đông"},
            {"query": "Political Philosophy", "description": "Triết học chính trị"}
        ]