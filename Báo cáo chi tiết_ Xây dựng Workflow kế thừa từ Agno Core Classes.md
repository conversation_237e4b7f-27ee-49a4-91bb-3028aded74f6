# Báo cáo chi tiết: Xây dựng Workflow kế thừa từ Agno Core Classes

## Giới thiệu

Báo cáo này trình bày chi tiết về cách xây dựng các quy trình làm việc (workflows) tùy chỉnh cho nhóm Agno bằng cách kế thừa và mở rộng từ các lớp cốt lõi (`Agent`, `Team`, `Workflow`) được cung cấp trong kho lưu trữ `agno-agi/agno`. Mục tiêu là cung cấp một hướng dẫn rõ ràng, kèm theo các ví dụ mã nguồn cụ thể, gi<PERSON>p bạn xây dựng các Agent và Team Agent mới phù hợp với nhu cầu cụ thể của dự án, đồng thời tận dụng được kiến trúc và chức năng nền tảng của Agno.

Chúng tôi sẽ đi sâu vào cấu trúc của từng lớp cốt lõi, gi<PERSON><PERSON> thích cách thức kế thừa, các phương thức quan trọng cần ghi đè hoặc triển khai, và cách tích hợp các thành phần này vào một quy trình làm việc hoàn chỉnh. Báo cáo này dành cho các nhà phát triển muốn xây dựng các hệ thống Agent phức tạp dựa trên nền tảng Agno.



## Phần 1: Tổng quan về các Lớp Cốt lõi của Agno (`Agent`, `Team`, `Workflow`)

Để xây dựng các quy trình làm việc hiệu quả bằng Agno, điều quan trọng là phải hiểu rõ cấu trúc và chức năng của ba lớp cốt lõi: `Agent`, `Team`, và `Workflow`. Các lớp này cung cấp nền tảng cho việc tạo ra các tác nhân tự trị, quản lý sự hợp tác giữa chúng và điều phối các quy trình phức tạp.

### 1.1. Lớp `Agent` (`agno.agent.Agent`)

Lớp `Agent` là đơn vị cơ bản nhất trong Agno, đại diện cho một tác nhân tự trị có khả năng thực hiện các nhiệm vụ, sử dụng công cụ, tương tác với mô hình ngôn ngữ lớn (LLM), và duy trì bộ nhớ.

**Cấu trúc chính:**

Lớp `Agent` được triển khai dưới dạng một `dataclass` trong Python, cho phép cấu hình linh hoạt thông qua các tham số khởi tạo. Một số thuộc tính quan trọng bao gồm:

*   `model`: Mô hình LLM mà Agent sẽ sử dụng (ví dụ: `OpenAIChat`).
*   `name`: Tên định danh cho Agent.
*   `agent_id`: UUID duy nhất cho Agent (tự động tạo nếu không cung cấp).
*   `introduction`: Lời giới thiệu của Agent, được thêm vào lịch sử tin nhắn khi bắt đầu một lượt chạy.
*   `memory`: Đối tượng quản lý bộ nhớ của Agent (ví dụ: `agno.memory.v2.memory.Memory`). Agent có thể lưu trữ lịch sử tương tác, tóm tắt phiên, và thông tin về người dùng.
*   `knowledge`: Đối tượng quản lý cơ sở tri thức của Agent (`agno.knowledge.agent.AgentKnowledge`), cho phép thực hiện Retrieval-Augmented Generation (RAG).
*   `tools`: Danh sách các công cụ (functions, toolkits) mà Agent có thể sử dụng để tương tác với môi trường bên ngoài hoặc thực hiện các tác vụ cụ thể.
*   `system_message`: Thông điệp hệ thống định hướng hành vi và vai trò của Agent.
*   `instructions`: Các chỉ dẫn cụ thể cho Agent về mục tiêu hoặc cách thực hiện nhiệm vụ.
*   `team`: Danh sách các Agent khác mà Agent này có thể chuyển giao nhiệm vụ trong một cấu trúc `Team`.
*   `role`: Vai trò của Agent khi là thành viên của một `Team`.

**Kế thừa và Mở rộng:**

Cách phổ biến nhất để tạo Agent tùy chỉnh là kế thừa từ lớp `agno.agent.Agent`. Khi kế thừa, bạn có thể:

1.  **Cấu hình mặc định:** Đặt các giá trị mặc định cho các thuộc tính như `model`, `name`, `system_message`, `instructions`, `tools` trong phương thức `__init__` của lớp con.
2.  **Ghi đè phương thức:** Mặc dù `Agent` cung cấp phương thức `run` và `arun` cốt lõi, bạn thường không cần ghi đè chúng trực tiếp. Thay vào đó, bạn tập trung vào việc cung cấp cấu hình và công cụ phù hợp.
3.  **Thêm logic tùy chỉnh:** Bạn có thể thêm các phương thức trợ giúp hoặc logic xử lý cụ thể vào lớp Agent con của mình.

*Ví dụ cơ bản về kế thừa `Agent`:*

```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat

class MyCustomAgent(Agent):
    def __init__(self, **kwargs):
        super().__init__(
            model=OpenAIChat(id="gpt-4o"),
            name="My Custom Agent",
            system_message="You are a helpful assistant designed for custom tasks.",
            instructions=["Follow the user's request carefully."],
            # Thêm các cấu hình khác nếu cần
            **kwargs
        )
        # Thêm logic khởi tạo tùy chỉnh nếu cần
```

### 1.2. Lớp `Team` (`agno.team.Team`)

Lớp `Team` cho phép tổ chức nhiều `Agent` (hoặc thậm chí các `Team` con) để cộng tác giải quyết một vấn đề phức tạp. `Team` quản lý việc điều phối nhiệm vụ giữa các thành viên.

**Cấu trúc chính:**

*   `members`: Danh sách các `Agent` hoặc `Team` con là thành viên của nhóm này.
*   `mode`: Chế độ hoạt động của Team, xác định cách nhiệm vụ được phân phối và xử lý:
    *   `route`: Team leader (sử dụng `model` của Team) quyết định *một* thành viên phù hợp nhất để xử lý toàn bộ nhiệm vụ.
    *   `coordinate`: Team leader điều phối, có thể chuyển giao nhiệm vụ cho *nhiều* thành viên và tổng hợp kết quả.
    *   `collaborate`: Các thành viên chạy song song hoặc tuần tự dựa trên cấu hình, và Team leader tổng hợp kết quả cuối cùng.
*   `model`: Mô hình LLM mà Team leader sử dụng để điều phối (nếu cần, tùy thuộc vào `mode`).
*   `name`, `team_id`: Tương tự như `Agent`.
*   `memory`: Bộ nhớ dành riêng cho Team, lưu trữ lịch sử tương tác ở cấp độ Team.
*   `storage`: Lưu trữ trạng thái của Team.
*   Các thuộc tính cấu hình khác tương tự `Agent` (ví dụ: `instructions`, `knowledge`, `tools` cho Team leader).

**Kế thừa và Mở rộng:**

Tương tự như `Agent`, bạn có thể kế thừa từ `agno.team.Team` để tạo các cấu trúc nhóm tùy chỉnh:

1.  **Định nghĩa thành viên:** Xác định danh sách các `Agent` hoặc `Team` con cụ thể trong phương thức `__init__`.
2.  **Chọn chế độ hoạt động:** Thiết lập `mode` phù hợp (`route`, `coordinate`, `collaborate`).
3.  **Cấu hình Team Leader:** Cung cấp `model`, `system_message`, `instructions` cho vai trò điều phối của Team.

*Ví dụ cơ bản về kế thừa `Team`:*

```python
from agno.team import Team
from agno.agent import Agent # Giả sử bạn đã định nghĩa CustomAgent1, CustomAgent2
# from .my_agents import CustomAgent1, CustomAgent2

# Giả lập Agent tùy chỉnh
class CustomAgent1(Agent):
    def __init__(self, **kwargs):
        super().__init__(name="Agent 1", role="Task Executor 1", **kwargs)

class CustomAgent2(Agent):
     def __init__(self, **kwargs):
        super().__init__(name="Agent 2", role="Task Executor 2", **kwargs)

class MyCustomTeam(Team):
    def __init__(self, **kwargs):
        members = [
            CustomAgent1(),
            CustomAgent2()
        ]
        super().__init__(
            members=members,
            name="My Custom Team",
            mode="coordinate", # Ví dụ: chế độ điều phối
            # Cấu hình model và instructions cho Team Leader
            model=OpenAIChat(id="gpt-4-turbo"),
            instructions=["Coordinate the work between Agent 1 and Agent 2."],
            **kwargs
        )
```

### 1.3. Lớp `Workflow` (`agno.workflow.Workflow`)

Lớp `Workflow` đóng vai trò là trình điều phối cấp cao nhất, định nghĩa một chuỗi các bước hoặc logic để hoàn thành một mục tiêu lớn hơn. Một `Workflow` có thể bao gồm việc gọi các `Agent` hoặc `Team`.

**Cấu trúc chính:**

Khác với `Agent` và `Team` chủ yếu dựa vào cấu hình, `Workflow` được thiết kế để kế thừa và **bắt buộc phải triển khai phương thức `run`**. Lớp `Workflow` cơ sở cung cấp các chức năng quản lý vòng đời, trạng thái, bộ nhớ và lưu trữ.

*   `name`, `workflow_id`, `description`: Thông tin định danh cho Workflow.
*   `session_id`, `session_name`, `session_state`: Quản lý trạng thái của một phiên làm việc cụ thể.
*   `memory`: Bộ nhớ cấp Workflow (`WorkflowMemory` hoặc `Memory`).
*   `storage`: Lưu trữ trạng thái Workflow.
*   Các thuộc tính `debug_mode`, `monitoring`, `telemetry`.

**Kế thừa và Triển khai:**

Cách sử dụng chính của `Workflow` là kế thừa từ nó và định nghĩa logic nghiệp vụ trong phương thức `run`:

1.  **Kế thừa:** Tạo một lớp mới kế thừa từ `agno.workflow.Workflow`.
2.  **Khởi tạo:** Trong `__init__`, gọi `super().__init__(...)` và khởi tạo bất kỳ `Agent` hoặc `Team` nào mà Workflow sẽ sử dụng. Các `Agent`/`Team` này có thể được lưu trữ dưới dạng thuộc tính của lớp Workflow.
3.  **Triển khai `run`:** Đây là phương thức cốt lõi nơi bạn định nghĩa luồng công việc. Phương thức `run` nhận các tham số đầu vào cần thiết cho quy trình và phải trả về một đối tượng `RunResponse` hoặc một iterator (`Iterator[RunResponse]`) nếu bạn muốn stream kết quả từng phần.
    *   Bên trong `run`, bạn sẽ gọi phương thức `run` (hoặc `arun`) của các `Agent` hoặc `Team` đã khởi tạo, truyền dữ liệu giữa các bước và xử lý kết quả.

*Ví dụ cơ bản về kế thừa `Workflow`:*

```python
from agno.workflow import Workflow
from agno.run.response import RunResponse
# from .my_agents import MyCustomAgent # Giả sử bạn đã định nghĩa MyCustomAgent
# from .my_teams import MyCustomTeam # Giả sử bạn đã định nghĩa MyCustomTeam

# Giả lập Agent và Team tùy chỉnh
class MyCustomAgent(Agent):
    def __init__(self, **kwargs):
        super().__init__(name="My Custom Agent", **kwargs)
    def run(self, message: str, **kwargs) -> RunResponse:
        # Logic xử lý của Agent
        return RunResponse(content=f"Agent processed: {message}")

class MyCustomTeam(Team):
    def __init__(self, **kwargs):
        super().__init__(name="My Custom Team", members=[MyCustomAgent()], **kwargs)
    def run(self, message: str, **kwargs) -> RunResponse:
        # Logic điều phối của Team
        return RunResponse(content=f"Team processed: {message}")

class MyDataProcessingWorkflow(Workflow):
    def __init__(self, **kwargs):
        super().__init__(name="Data Processing Workflow", **kwargs)
        # Khởi tạo Agent và Team cần thiết
        self.step1_agent = MyCustomAgent()
        self.step2_team = MyCustomTeam()

    def run(self, input_data: str) -> RunResponse:
        # Bước 1: Xử lý bởi Agent
        agent_response = self.step1_agent.run(message=input_data)

        # Bước 2: Xử lý bởi Team (sử dụng kết quả từ bước 1)
        team_response = self.step2_team.run(message=agent_response.content)

        # Trả về kết quả cuối cùng
        final_content = f"Workflow completed. Final result: {team_response.content}"
        return RunResponse(content=final_content)

# Cách sử dụng:
# workflow = MyDataProcessingWorkflow()
# result = workflow.run(input_data="Some initial data")
# print(result.content)
```

Phần tiếp theo sẽ đi sâu vào cách xây dựng Agent tùy chỉnh bằng cách kế thừa từ lớp `Agent`.



## Phần 2: Xây dựng Agent Tùy chỉnh bằng Kế thừa

Việc tạo ra các Agent chuyên biệt cho các nhiệm vụ cụ thể là một phần cốt lõi khi làm việc với Agno. Cách tiếp cận hiệu quả nhất là kế thừa từ lớp `agno.agent.Agent` và tùy chỉnh hành vi thông qua cấu hình và việc cung cấp các công cụ (tools) phù hợp.

### 2.1. Nguyên tắc Kế thừa Lớp `Agent`

Như đã đề cập, `Agent` là một `dataclass`. Khi bạn tạo một lớp con kế thừa từ `Agent`, bạn chủ yếu tập trung vào việc định nghĩa các giá trị mặc định và cấu hình trong phương thức `__init__` của lớp con.

**Các bước chính:**

1.  **Import lớp `Agent`:** Bắt đầu bằng cách import `agno.agent.Agent`.
2.  **Định nghĩa lớp con:** Tạo lớp mới kế thừa từ `Agent`.
3.  **Triển khai `__init__`:**
    *   Gọi `super().__init__(...)` để khởi tạo lớp cha.
    *   Trong lệnh gọi `super().__init__`, cung cấp các giá trị cấu hình mặc định cho Agent của bạn. Các cấu hình quan trọng thường bao gồm:
        *   `model`: Chọn LLM phù hợp (ví dụ: `OpenAIChat`, `AnthropicChat`).
        *   `name`: Đặt tên gợi nhớ cho Agent.
        *   `system_message`: Định nghĩa vai trò, mục tiêu tổng quát của Agent.
        *   `instructions`: Cung cấp các chỉ dẫn chi tiết hơn về cách thực hiện nhiệm vụ hoặc các ràng buộc.
        *   `tools`: Danh sách các hàm hoặc `Toolkit` mà Agent được phép sử dụng. Đây là cách chính để mở rộng khả năng của Agent.
        *   `memory`: Cấu hình bộ nhớ nếu cần các thiết lập khác mặc định.
        *   `knowledge`: Liên kết với cơ sở tri thức nếu Agent cần thực hiện RAG.
    *   Sử dụng `**kwargs` để cho phép người dùng ghi đè các cấu hình mặc định khi tạo một thể hiện của Agent tùy chỉnh.
4.  **(Tùy chọn) Thêm phương thức trợ giúp:** Bạn có thể thêm các phương thức khác vào lớp con để đóng gói logic phức tạp hoặc các tác vụ chuẩn bị dữ liệu.

### 2.2. Ví dụ: Xây dựng một `ResearchAgent`

Hãy xem xét việc xây dựng một Agent chuyên thực hiện nghiên cứu trên web về một chủ đề nhất định.

**Yêu cầu:**

*   Agent nhận một chủ đề đầu vào.
*   Sử dụng công cụ tìm kiếm web để tìm thông tin liên quan.
*   Tổng hợp thông tin tìm được thành một báo cáo ngắn gọn.
*   Sử dụng mô hình GPT-4o.

**Triển khai:**

Trước tiên, chúng ta cần một công cụ tìm kiếm web. Agno có thể tích hợp với các công cụ bên ngoài. Giả sử chúng ta có một hàm `web_search(query: str) -> str` (trong thực tế, bạn có thể dùng thư viện như `requests`, `BeautifulSoup` hoặc các API tìm kiếm chuyên dụng).

```python
import os
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.function import Function # Để định nghĩa tool rõ ràng hơn
from agno.run.response import RunResponse

# --- Giả lập Công cụ Tìm kiếm Web --- 
def perform_web_search(query: str) -> str:
    """Thực hiện tìm kiếm web mô phỏng và trả về kết quả tóm tắt."""
    print(f"[Tool] Searching web for: {query}")
    # Trong thực tế, đây sẽ là nơi gọi API hoặc scraping
    if "Agno framework" in query:
        return "Agno is a framework for building autonomous agents and teams. It provides core classes like Agent, Team, and Workflow."
    elif "large language models" in query:
        return "Large language models (LLMs) are advanced AI models trained on vast amounts of text data, capable of understanding and generating human-like text."
    else:
        return f"Simulated search results for '{query}'. No specific information found in this mock tool."
# -------------------------------------

class ResearchAgent(Agent):
    """Một Agent chuyên thực hiện nghiên cứu web về một chủ đề."""

    def __init__(self, **kwargs):
        # Định nghĩa công cụ tìm kiếm web
        search_tool = Function(
            name="web_search",
            description="Searches the web for information on a given topic.",
            func=perform_web_search,
            parameters=[
                {"name": "query", "type": "string", "description": "The search topic or query.", "required": True}
            ]
        )

        super().__init__(
            model=OpenAIChat(id="gpt-4o"), # Sử dụng GPT-4o
            name="Research Agent",
            system_message=(
                "You are a research assistant. Your goal is to find information "
                "on a given topic using the available web search tool and summarize the findings."
            ),
            instructions=[
                "1. Understand the user's topic.",
                "2. Use the 'web_search' tool to gather information.",
                "3. Synthesize the search results into a concise summary.",
                "4. Present the summary clearly to the user."
            ],
            tools=[search_tool], # Cung cấp công cụ cho Agent
            # Ghi đè các cấu hình mặc định khác nếu cần
            **kwargs
        )

# --- Cách sử dụng ResearchAgent --- 
# research_agent = ResearchAgent(
#     # Bạn có thể ghi đè cấu hình ở đây nếu muốn, ví dụ:
#     # debug_mode=True
# )

# topic = "Tell me about the Agno framework"
# response = research_agent.run(message=topic)

# print("\n--- Research Agent Response ---")
# print(response.content)

# topic_llm = "What are large language models?"
# response_llm = research_agent.run(message=topic_llm)
# print("\n--- Research Agent Response (LLMs) ---")
# print(response_llm.content)
# -----------------------------------
```

**Giải thích:**

1.  **`perform_web_search`**: Hàm Python mô phỏng việc tìm kiếm web. Trong ứng dụng thực tế, hàm này sẽ chứa logic phức tạp hơn.
2.  **`ResearchAgent.__init__`**: Phương thức khởi tạo của lớp con.
3.  **`search_tool = Function(...)`**: Chúng ta định nghĩa công cụ `web_search` bằng cách sử dụng lớp `agno.tools.function.Function`. Điều này cung cấp mô tả rõ ràng và cấu trúc tham số cho LLM biết cách sử dụng công cụ.
4.  **`super().__init__(...)`**: Gọi hàm khởi tạo của lớp `Agent` cha, truyền vào các cấu hình:
    *   `model`: `OpenAIChat(id="gpt-4o")`.
    *   `name`: "Research Agent".
    *   `system_message` và `instructions`: Định hướng cho Agent về vai trò và quy trình làm việc.
    *   `tools=[search_tool]`: Quan trọng nhất, cung cấp công cụ tìm kiếm cho Agent.
5.  **Cách sử dụng**: Tạo một thể hiện của `ResearchAgent` và gọi phương thức `run` với chủ đề cần nghiên cứu.

Agent sẽ tự động hiểu rằng nó cần sử dụng công cụ `web_search` dựa trên `system_message` và `instructions`, gọi hàm `perform_web_search` với truy vấn phù hợp, nhận kết quả trả về từ hàm, và sau đó tổng hợp thành câu trả lời cuối cùng cho người dùng.

### 2.3. Các Phương thức Quan trọng (Thường không cần ghi đè)

Lớp `Agent` cơ sở đã xử lý phần lớn logic phức tạp:

*   **`run(message, ...)` / `arun(message, ...)`**: Điểm vào chính để bắt đầu một lượt tương tác với Agent (đồng bộ và bất đồng bộ).
*   **`_run(...)` / `_arun(...)`**: Logic cốt lõi xử lý việc chuẩn bị thông điệp, gọi mô hình, xử lý tool calls, cập nhật bộ nhớ, lưu trữ.
*   **`get_run_messages(...)`**: Chuẩn bị danh sách các thông điệp (bao gồm system message, lịch sử, thông điệp người dùng, context, references) để gửi đến LLM.
*   **`determine_tools_for_model(...)`**: Xử lý danh sách `tools` và chuẩn bị định dạng phù hợp cho API của LLM.
*   **`_handle_reasoning(...)` / `_handle_reasoning_stream(...)`**: Xử lý logic lập luận từng bước (nếu `reasoning=True`).
*   **`_update_memory(...)` / `_aupdate_memory(...)`**: Cập nhật bộ nhớ sau mỗi lượt chạy.
*   **`write_to_storage(...)` / `read_from_storage(...)`**: Tương tác với lớp `Storage` để lưu và tải trạng thái.

Thông thường, bạn không cần ghi đè các phương thức này khi chỉ muốn tạo Agent với hành vi chuyên biệt thông qua cấu hình và công cụ.

Phần tiếp theo sẽ khám phá cách xây dựng các `Team` tùy chỉnh bằng cách kế thừa.



## Phần 3: Xây dựng Team Tùy chỉnh bằng Kế thừa

Sau khi đã biết cách tạo Agent tùy chỉnh, bước tiếp theo là tổ chức các Agent này thành các nhóm (Teams) để giải quyết các vấn đề phức tạp hơn đòi hỏi sự cộng tác hoặc phân công nhiệm vụ chuyên biệt. Kế thừa từ lớp `agno.team.Team` là cách để tạo ra các cấu trúc nhóm này.

### 3.1. Nguyên tắc Kế thừa Lớp `Team`

Việc kế thừa `Team` cũng tương tự như `Agent`, tập trung vào việc cấu hình trong `__init__`:

1.  **Import lớp `Team` và các `Agent` thành viên:** Import `agno.team.Team` và các lớp `Agent` (hoặc `Team` con) mà bạn muốn đưa vào nhóm.
2.  **Định nghĩa lớp con:** Tạo lớp mới kế thừa từ `Team`.
3.  **Triển khai `__init__`:**
    *   **Khởi tạo thành viên:** Tạo các thể hiện (instances) của các `Agent` hoặc `Team` con sẽ là thành viên của nhóm này. Cung cấp các cấu hình cần thiết cho từng thành viên (ví dụ: `role`).
    *   **Gọi `super().__init__(...)`:** Khởi tạo lớp `Team` cha với các tham số:
        *   `members`: Danh sách các thể hiện thành viên đã tạo.
        *   `mode`: Chọn chế độ hoạt động (`route`, `coordinate`, `collaborate`). Lựa chọn này rất quan trọng vì nó quyết định cách Team leader điều phối công việc.
        *   `model`: (Thường cần thiết cho `route` và `coordinate`) Cấu hình LLM cho Team leader để đưa ra quyết định điều phối.
        *   `name`: Tên cho Team.
        *   `instructions`: Chỉ dẫn cho Team leader về cách điều phối công việc giữa các thành viên.
        *   Các cấu hình khác như `memory`, `storage`, `tools` (cho Team leader) nếu cần.
    *   Sử dụng `**kwargs` để cho phép tùy chỉnh thêm khi tạo thể hiện Team.

### 3.2. Ví dụ: Xây dựng một `ContentGenerationTeam`

Giả sử chúng ta muốn tạo một Team để sản xuất nội dung, bao gồm một Agent viết bài và một Agent kiểm tra ngữ pháp/chỉnh sửa.

**Yêu cầu:**

*   Team nhận một chủ đề.
*   `WritingAgent` soạn thảo nội dung dựa trên chủ đề.
*   `EditingAgent` xem xét và chỉnh sửa bản nháp từ `WritingAgent`.
*   Team hoạt động ở chế độ `coordinate` (điều phối).

**Triển khai:**

```python
from agno.agent import Agent
from agno.team import Team
from agno.models.openai import OpenAIChat
from agno.run.response import RunResponse

# --- Định nghĩa các Agent thành viên --- 

class WritingAgent(Agent):
    """Agent chuyên viết nội dung."""
    def __init__(self, **kwargs):
        super().__init__(
            model=OpenAIChat(id="gpt-4o"),
            name="Writing Agent",
            role="Content Writer", # Vai trò trong team
            system_message="You are a creative writer. Generate content based on the provided topic.",
            instructions=["Write a short paragraph about the topic."],
            **kwargs
        )
    # Giả lập phương thức run cho ví dụ
    def run(self, message: str, **kwargs) -> RunResponse:
        print(f"[WritingAgent] Received topic: {message}")
        # Logic viết bài thực tế sẽ phức tạp hơn
        draft = f"Draft content about {message}: Agno provides powerful tools for agent collaboration..."
        print(f"[WritingAgent] Produced draft: {draft}")
        return RunResponse(content=draft)

class EditingAgent(Agent):
    """Agent chuyên chỉnh sửa nội dung."""
    def __init__(self, **kwargs):
        super().__init__(
            model=OpenAIChat(id="gpt-3.5-turbo"), # Có thể dùng model khác
            name="Editing Agent",
            role="Content Editor", # Vai trò trong team
            system_message="You are a meticulous editor. Review the provided draft for grammar, clarity, and style.",
            instructions=["Improve the draft. Ensure it is error-free."],
            **kwargs
        )
    # Giả lập phương thức run cho ví dụ
    def run(self, message: str, **kwargs) -> RunResponse:
        print(f"[EditingAgent] Received draft: {message}")
        # Logic chỉnh sửa thực tế sẽ phức tạp hơn
        edited_content = message.replace("...", "... ensuring seamless workflow execution.") + " [Edited]"
        print(f"[EditingAgent] Produced edited content: {edited_content}")
        return RunResponse(content=edited_content)

# --- Định nghĩa Team kế thừa --- 

class ContentGenerationTeam(Team):
    """Team điều phối việc viết và chỉnh sửa nội dung."""
    def __init__(self, **kwargs):
        # Khởi tạo các thành viên
        writer = WritingAgent()
        editor = EditingAgent()

        super().__init__(
            members=[writer, editor],
            name="Content Generation Team",
            mode="coordinate", # Leader sẽ điều phối giữa writer và editor
            model=OpenAIChat(id="gpt-4-turbo"), # Model cho Team Leader
            system_message=(
                "You are the team coordinator for content generation. "
                "Your goal is to manage the writing and editing process."
            ),
            instructions=[
                "1. Receive the content topic from the user.",
                "2. Instruct the 'Content Writer' (Writing Agent) to create a draft.",
                "3. Pass the draft from the 'Content Writer' to the 'Content Editor' (Editing Agent) for review.",
                "4. Return the final, edited content to the user."
            ],
            # Cho phép ghi đè cấu hình
            **kwargs
        )

# --- Cách sử dụng ContentGenerationTeam --- 
# content_team = ContentGenerationTeam(
#     # debug_mode=True # Bật debug để xem chi tiết hoạt động
# )

# topic = "the benefits of using Agno Teams"
# final_response = content_team.run(message=topic)

# print("\n--- Content Generation Team Response ---")
# print(final_response.content)
# -------------------------------------------
```

**Giải thích:**

1.  **`WritingAgent`, `EditingAgent`**: Hai lớp Agent tùy chỉnh được định nghĩa, mỗi lớp có vai trò (`role`), mô hình, và chỉ dẫn riêng.
2.  **`ContentGenerationTeam.__init__`**: Phương thức khởi tạo của Team.
3.  **`writer = WritingAgent()`, `editor = EditingAgent()`**: Tạo các thể hiện của Agent thành viên.
4.  **`super().__init__(...)`**: Khởi tạo lớp `Team` cha:
    *   `members=[writer, editor]`: Cung cấp danh sách thành viên.
    *   `mode="coordinate"`: Chọn chế độ điều phối. Team leader (sử dụng `gpt-4-turbo`) sẽ nhận yêu cầu, giao cho `WritingAgent`, nhận kết quả, rồi giao cho `EditingAgent`, và cuối cùng trả kết quả đã chỉnh sửa.
    *   `model`, `system_message`, `instructions`: Cấu hình cho Team leader để nó biết cách điều phối công việc giữa `writer` và `editor` dựa trên vai trò của chúng.
5.  **Cách sử dụng**: Tạo thể hiện của `ContentGenerationTeam` và gọi `run` với chủ đề. Team sẽ tự động quản lý luồng công việc nội bộ.

### 3.3. Lựa chọn Chế độ Hoạt động (`mode`)

Việc chọn `mode` phù hợp là rất quan trọng:

*   **`route`**: Phù hợp khi bạn có nhiều Agent chuyên biệt và muốn chọn *một* Agent tốt nhất cho một nhiệm vụ cụ thể. Team leader đóng vai trò như một bộ định tuyến thông minh.
*   **`coordinate`**: Lý tưởng cho các quy trình gồm nhiều bước, nơi kết quả của một Agent là đầu vào cho Agent khác, hoặc khi cần tổng hợp thông tin từ nhiều Agent. Team leader điều phối luồng công việc.
*   **`collaborate`**: Hữu ích khi các Agent có thể làm việc độc lập (có thể song song) trên cùng một vấn đề và kết quả cuối cùng là sự tổng hợp hoặc lựa chọn từ các kết quả riêng lẻ. Team leader chủ yếu tổng hợp kết quả.

Việc hiểu rõ các chế độ này giúp bạn thiết kế cấu trúc Team hiệu quả nhất cho bài toán của mình.

Phần tiếp theo sẽ tập trung vào cách định nghĩa các `Workflow` tùy chỉnh, nơi bạn kết hợp các Agent và Team đã tạo vào một quy trình lớn hơn.



## Phần 4: Định nghĩa Workflow Tùy chỉnh bằng Kế thừa

Lớp `Workflow` là khung sườn để bạn định nghĩa các quy trình nghiệp vụ phức tạp, điều phối hoạt động của các `Agent` và `Team` đã tạo. Khác với `Agent` và `Team` chủ yếu được tùy chỉnh qua cấu hình, `Workflow` yêu cầu bạn triển khai logic thực thi cụ thể trong phương thức `run`.

### 4.1. Nguyên tắc Kế thừa và Triển khai `Workflow`

1.  **Import lớp `Workflow` và các thành phần cần thiết:** Import `agno.workflow.Workflow`, `agno.run.response.RunResponse`, cùng các lớp `Agent` và `Team` tùy chỉnh mà bạn đã xây dựng.
2.  **Định nghĩa lớp con:** Tạo lớp mới kế thừa từ `Workflow`.
3.  **Triển khai `__init__`:**
    *   Gọi `super().__init__(...)` với các cấu hình cơ bản như `name`, `description`.
    *   **Khởi tạo các Agent/Team:** Tạo và lưu trữ các thể hiện của `Agent` và `Team` mà workflow này sẽ sử dụng. Đây thường là các thuộc tính của lớp Workflow (ví dụ: `self.data_fetcher = DataFetchingAgent()`). Agno sẽ tự động quản lý việc truyền `session_id` cho các Agent/Team thành viên này.
4.  **Triển khai phương thức `run`:** Đây là trái tim của Workflow.
    *   Định nghĩa các tham số đầu vào mà Workflow cần (ví dụ: `def run(self, query: str, output_format: str) -> RunResponse:`).
    *   Viết mã Python để điều phối luồng công việc:
        *   Gọi phương thức `run` (hoặc `arun`) của các `Agent`/`Team` đã khởi tạo.
        *   Truyền dữ liệu và kết quả giữa các bước.
        *   Thực hiện logic xử lý, chuyển đổi dữ liệu nếu cần.
    *   **Trả về kết quả:** Phương thức `run` *phải* trả về một đối tượng `RunResponse` chứa kết quả cuối cùng, hoặc một `Iterator[RunResponse]` nếu bạn muốn stream kết quả.

### 4.2. Ví dụ: Xây dựng một `ReportGenerationWorkflow`

Workflow này sẽ kết hợp `ResearchAgent` (từ Phần 2) và một Agent mới là `FormattingAgent` để tạo báo cáo hoàn chỉnh.

**Yêu cầu:**

*   Workflow nhận một chủ đề nghiên cứu.
*   Sử dụng `ResearchAgent` để thu thập thông tin.
*   Sử dụng `FormattingAgent` để định dạng kết quả nghiên cứu thành Markdown.
*   Trả về báo cáo dưới dạng Markdown.

**Triển khai:**

```python
from agno.workflow import Workflow
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.run.response import RunResponse

# --- Giả lập Công cụ Tìm kiếm Web (từ Phần 2) --- 
def perform_web_search(query: str) -> str:
    """Thực hiện tìm kiếm web mô phỏng và trả về kết quả tóm tắt."""
    print(f"[Tool] Searching web for: {query}")
    if "Agno framework" in query:
        return "Agno is a framework for building autonomous agents and teams. It provides core classes like Agent, Team, and Workflow."
    elif "large language models" in query:
        return "Large language models (LLMs) are advanced AI models trained on vast amounts of text data, capable of understanding and generating human-like text."
    else:
        return f"Simulated search results for 
'{query}
'. No specific information found in this mock tool."
# -------------------------------------

# --- ResearchAgent (từ Phần 2) --- 
from agno.tools.function import Function
class ResearchAgent(Agent):
    """Một Agent chuyên thực hiện nghiên cứu web về một chủ đề."""
    def __init__(self, **kwargs):
        search_tool = Function(
            name="web_search",
            description="Searches the web for information on a given topic.",
            func=perform_web_search,
            parameters=[
                {"name": "query", "type": "string", "description": "The search topic or query.", "required": True}
            ]
        )
        super().__init__(
            model=OpenAIChat(id="gpt-4o"),
            name="Research Agent",
            system_message="You are a research assistant. Find information using web search and summarize findings.",
            instructions=["Use 'web_search' tool.", "Synthesize results concisely."],
            tools=[search_tool],
            **kwargs
        )
    # Giả lập run để trả về RunResponse
    def run(self, message: str, **kwargs) -> RunResponse:
        print(f"[ResearchAgent] Researching topic: {message}")
        # Logic thực tế sẽ gọi model và tool
        search_result = perform_web_search(message)
        summary = f"Research summary for '{message}': {search_result}"
        print(f"[ResearchAgent] Generated summary: {summary}")
        return RunResponse(content=summary)
# -----------------------------------

# --- Định nghĩa FormattingAgent --- 
class FormattingAgent(Agent):
    """Agent chuyên định dạng văn bản thành Markdown."""
    def __init__(self, **kwargs):
        super().__init__(
            model=OpenAIChat(id="gpt-3.5-turbo"),
            name="Formatting Agent",
            system_message="You are a formatting expert. Convert the provided text into well-structured Markdown.",
            instructions=["Use appropriate Markdown syntax (headings, lists, bold text)."],
            # Không cần tool đặc biệt cho việc này
            **kwargs
        )
    # Giả lập run để trả về RunResponse
    def run(self, message: str, **kwargs) -> RunResponse:
        print(f"[FormattingAgent] Formatting text: {message[:50]}...")
        # Logic thực tế sẽ gọi model để định dạng
        markdown_content = f"# Report\n\n## Findings\n\n{message}\n\n_Generated by Formatting Agent_"
        print(f"[FormattingAgent] Generated Markdown: {markdown_content[:70]}...")
        return RunResponse(content=markdown_content)
# -----------------------------------

# --- Định nghĩa Workflow kế thừa --- 

class ReportGenerationWorkflow(Workflow):
    """Workflow điều phối việc nghiên cứu và định dạng báo cáo."""

    def __init__(self, **kwargs):
        super().__init__(
            name="Report Generation Workflow",
            description="Generates a formatted report based on web research.",
            **kwargs
        )
        # Khởi tạo các Agent cần thiết
        self.researcher = ResearchAgent()
        self.formatter = FormattingAgent()

    # Triển khai phương thức run - CỐT LÕI CỦA WORKFLOW
    def run(self, topic: str) -> RunResponse:
        """Thực hiện quy trình: Nghiên cứu -> Định dạng -> Trả về báo cáo Markdown."""
        print(f"\n--- Workflow Step 1: Researching '{topic}' ---")
        # Bước 1: Gọi ResearchAgent để thu thập thông tin
        research_response = self.researcher.run(message=topic)
        research_summary = research_response.content

        print(f"\n--- Workflow Step 2: Formatting Research Summary ---")
        # Bước 2: Gọi FormattingAgent để định dạng kết quả
        formatting_response = self.formatter.run(message=research_summary)
        final_report_markdown = formatting_response.content

        print("\n--- Workflow Finished ---")
        # Bước 3: Trả về kết quả cuối cùng trong đối tượng RunResponse
        # Lưu ý: content của RunResponse cuối cùng này là kết quả của toàn bộ workflow
        return RunResponse(content=final_report_markdown)

# --- Cách sử dụng ReportGenerationWorkflow --- 
# report_workflow = ReportGenerationWorkflow(
#     # debug_mode=True # Bật debug để xem chi tiết
# )

# research_topic = "Agno framework"
# final_report = report_workflow.run(topic=research_topic)

# print("\n========= FINAL WORKFLOW OUTPUT =========")
# print(final_report.content)
# print("=========================================")
# ---------------------------------------------
```

**Giải thích:**

1.  **`ReportGenerationWorkflow.__init__`**: Khởi tạo Workflow và các Agent thành viên (`researcher`, `formatter`).
2.  **`ReportGenerationWorkflow.run(topic: str)`**: Phương thức này định nghĩa luồng công việc:
    *   Nhận `topic` làm đầu vào.
    *   Gọi `self.researcher.run(message=topic)` để thực hiện nghiên cứu.
    *   Lấy kết quả (`research_summary`) từ `research_response.content`.
    *   Gọi `self.formatter.run(message=research_summary)` để định dạng bản tóm tắt.
    *   Lấy kết quả cuối cùng (`final_report_markdown`) từ `formatting_response.content`.
    *   Trả về một `RunResponse` mới chứa báo cáo Markdown cuối cùng.
3.  **Cách sử dụng**: Tạo thể hiện của `ReportGenerationWorkflow` và gọi `run` với chủ đề mong muốn.

Workflow đóng vai trò như một "kịch bản" hoặc "bộ não" điều phối, gọi các Agent/Team chuyên biệt để thực hiện từng phần của công việc lớn.

### 4.3. Streaming trong Workflow

Nếu các Agent hoặc Team bên trong Workflow của bạn hỗ trợ streaming (tức là phương thức `run` của chúng trả về `Iterator[RunResponse]`), bạn có thể thiết kế Workflow của mình để cũng stream kết quả.

Để làm điều này, phương thức `run` của Workflow của bạn cần sử dụng `yield` thay vì `return` ở cuối. Bạn sẽ `yield` các `RunResponse` nhận được từ các Agent/Team bên trong.

```python
# Ví dụ sửa đổi run của Workflow để hỗ trợ streaming (giả định Agent hỗ trợ stream)
# class StreamingReportWorkflow(Workflow):
#     # ... __init__ giống như trên ...
#
#     def run(self, topic: str) -> Iterator[RunResponse]: # Thay đổi kiểu trả về
#         print("\n--- Workflow Step 1: Researching (Streaming) ---")
#         # Giả sử researcher.run trả về Iterator[RunResponse]
#         full_summary = ""
#         for chunk_response in self.researcher.run(message=topic, stream=True):
#             if chunk_response.content:
#                 full_summary += chunk_response.content
#             yield chunk_response # Chuyển tiếp chunk
#
#         print("\n--- Workflow Step 2: Formatting (Streaming) ---")
#         # Giả sử formatter.run trả về Iterator[RunResponse]
#         final_markdown = ""
#         for chunk_response in self.formatter.run(message=full_summary, stream=True):
#             if chunk_response.content:
#                 final_markdown += chunk_response.content
#             yield chunk_response # Chuyển tiếp chunk
#
#         print("\n--- Workflow Finished (Streaming) ---")
#         # Không cần return RunResponse cuối cùng khi dùng yield
```

Khi bạn gọi `run` trên `StreamingReportWorkflow`, nó sẽ trả về một iterator, cho phép bạn xử lý từng phần kết quả khi chúng được tạo ra.

## Phần 5: Ví dụ Workflow Hoàn chỉnh

Phần này sẽ trình bày một ví dụ tổng hợp, kết hợp cả Agent tùy chỉnh, Team tùy chỉnh và Workflow tùy chỉnh để giải quyết một bài toán cụ thể: Lên kế hoạch và viết blog post.

**Bài toán:** Tạo một blog post về một chủ đề do người dùng cung cấp, bao gồm việc lên dàn ý, viết nội dung, và kiểm tra lại.

**Các thành phần:**

1.  **`PlannerAgent` (Agent):** Nhận chủ đề, tạo dàn ý chi tiết cho blog post.
2.  **`WriterAgent` (Agent):** Nhận dàn ý, viết nội dung cho từng phần.
3.  **`ReviewerAgent` (Agent):** Nhận nội dung đã viết, kiểm tra lỗi và đề xuất cải thiện.
4.  **`BlogPostTeam` (Team):** Sử dụng chế độ `coordinate`, điều phối `WriterAgent` và `ReviewerAgent` để tạo ra bản nháp cuối cùng dựa trên dàn ý.
5.  **`BlogWorkflow` (Workflow):** Điều phối toàn bộ quy trình:
    *   Nhận chủ đề từ người dùng.
    *   Gọi `PlannerAgent` để lấy dàn ý.
    *   Gọi `BlogPostTeam` với dàn ý để lấy bản nháp cuối cùng.
    *   Trả về blog post hoàn chỉnh.

**Triển khai (Mã giả lập chi tiết):**

```python
import time
from typing import Iterator
from agno.workflow import Workflow
from agno.team import Team
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.run.response import RunResponse

# --- Các Agent Thành viên --- 

class PlannerAgent(Agent):
    def __init__(self, **kwargs):
        super().__init__(
            name="Planner Agent",
            model=OpenAIChat(id="gpt-4o"),
            system_message="You are a blog post planner. Create a detailed outline for a blog post on the given topic.",
            instructions=["Outline should include introduction, main sections (3-4), and conclusion.", "Output the outline as a Markdown list."],
            **kwargs
        )
    def run(self, message: str, **kwargs) -> RunResponse:
        print(f"[PlannerAgent] Planning blog post for: {message}")
        # Simulate LLM call
        time.sleep(0.5)
        outline = f"## Blog Post Outline: {message}\n\n*   Introduction\n*   Section 1: Key aspect A\n*   Section 2: Key aspect B\n*   Section 3: Key aspect C\n*   Conclusion"
        print("[PlannerAgent] Outline created.")
        return RunResponse(content=outline)

class WriterAgent(Agent):
    def __init__(self, **kwargs):
        super().__init__(
            name="Writer Agent",
            role="Blog Writer",
            model=OpenAIChat(id="gpt-4o"),
            system_message="You are a blog writer. Write content based on the provided outline section.",
            instructions=["Expand the given outline point into a full paragraph."],
            **kwargs
        )
    def run(self, message: str, **kwargs) -> RunResponse:
        # Message ở đây là một phần của dàn ý
        print(f"[WriterAgent] Writing content for section: {message}")
        # Simulate LLM call
        time.sleep(0.7)
        # Giả lập nội dung viết dựa trên dàn ý
        if "Introduction" in message:
            content = f"This blog post discusses {message.split(': ')[-1]}. Let's dive in."
        elif "Section" in message:
            content = f"Content for {message}. Agno helps structure complex agent interactions..."
        elif "Conclusion" in message:
            content = f"In conclusion, {message.split(': ')[-1]} is an important topic..."
        else:
            content = f"Detailed content for {message}."
        print("[WriterAgent] Content written for section.")
        return RunResponse(content=content)

class ReviewerAgent(Agent):
    def __init__(self, **kwargs):
        super().__init__(
            name="Reviewer Agent",
            role="Blog Reviewer",
            model=OpenAIChat(id="gpt-3.5-turbo"),
            system_message="You are a blog post reviewer. Check the provided text for clarity, grammar, and flow.",
            instructions=["Suggest improvements if needed. If okay, approve it."],
            **kwargs
        )
    def run(self, message: str, **kwargs) -> RunResponse:
        print(f"[ReviewerAgent] Reviewing content: {message[:60]}...")
        # Simulate LLM call
        time.sleep(0.4)
        # Giả lập quá trình review
        reviewed_content = message + " [Reviewed & Approved]"
        print("[ReviewerAgent] Content reviewed.")
        return RunResponse(content=reviewed_content)

# --- Team Viết và Review --- 

class BlogPostTeam(Team):
    def __init__(self, **kwargs):
        self.writer = WriterAgent()
        self.reviewer = ReviewerAgent()
        super().__init__(
            name="Blog Post Team",
            members=[self.writer, self.reviewer],
            mode="coordinate", # Leader điều phối writer -> reviewer
            model=OpenAIChat(id="gpt-4-turbo"), # Model cho Leader
            system_message="You coordinate the blog writing process. First, get the writer to draft content based on the outline section provided by the user. Then, pass the draft to the reviewer.",
            instructions=[
                "Receive an outline section.",
                "Instruct 'Blog Writer' to write content for it.",
                "Instruct 'Blog Reviewer' to review the written content.",
                "Return the reviewed content."
            ],
            **kwargs
        )

    # Ghi đè run của Team để xử lý logic cụ thể hơn (ví dụ)
    # Lưu ý: Thông thường, bạn không cần ghi đè run của Team nếu mode='coordinate'
    # và instructions đủ rõ ràng. Đây chỉ là minh họa khả năng tùy chỉnh.
    def run(self, message: str, **kwargs) -> RunResponse:
        # message ở đây là một phần của dàn ý
        print(f"\n  [BlogPostTeam] Coordinating for outline section: {message}")
        # Leader (giả lập) quyết định gọi Writer
        print(f"  [BlogPostTeam] Instructing Writer Agent...")
        write_response = self.writer.run(message=message)
        draft_content = write_response.content

        # Leader (giả lập) quyết định gọi Reviewer
        print(f"  [BlogPostTeam] Instructing Reviewer Agent...")
        review_response = self.reviewer.run(message=draft_content)
        final_section_content = review_response.content

        print(f"  [BlogPostTeam] Coordination complete for section.")
        # Trả về kết quả đã review cho phần dàn ý này
        return RunResponse(content=final_section_content)

# --- Workflow Chính --- 

class BlogWorkflow(Workflow):
    def __init__(self, **kwargs):
        super().__init__(name="Blog Generation Workflow", **kwargs)
        self.planner = PlannerAgent()
        self.blog_team = BlogPostTeam()

    def run(self, topic: str) -> RunResponse:
        print(f"========= Starting Blog Workflow for topic: '{topic}' =========")

        # 1. Lên dàn ý
        print("\n--- Workflow Step 1: Planning Outline ---")
        plan_response = self.planner.run(message=topic)
        outline = plan_response.content
        print(f"Outline Generated:\n{outline}")

        # 2. Viết và Review từng phần của dàn ý
        print("\n--- Workflow Step 2: Writing & Reviewing Sections --- ")
        outline_sections = [sec.strip() for sec in outline.split('\n') if sec.strip().startswith('*')] # Tách các phần dàn ý
        final_blog_content = f"# Blog Post: {topic}\n\n"

        for section_outline in outline_sections:
            section_name = section_outline.lstrip('*').strip()
            print(f"\nProcessing section: {section_name}")
            # Gọi Team để xử lý việc viết và review cho phần này
            section_response = self.blog_team.run(message=section_name)
            final_blog_content += f"## {section_name}\n\n{section_response.content}\n\n"
            time.sleep(0.2) # Nghỉ ngắn giữa các phần

        # 3. Trả về kết quả cuối cùng
        print("========= Blog Workflow Completed =========")
        return RunResponse(content=final_blog_content)

# --- Cách sử dụng Workflow --- 
# blog_workflow = BlogWorkflow(debug_mode=False)
# final_post = blog_workflow.run(topic="The Future of AI Agents with Agno")

# print("\n############### FINAL BLOG POST ###############")
# print(final_post.content)
# print("#############################################")
```

**Điểm chính trong ví dụ:**

*   **Phân tách nhiệm vụ:** Mỗi Agent có một trách nhiệm rõ ràng (lên kế hoạch, viết, review).
*   **Sử dụng Team:** `BlogPostTeam` đóng gói logic cộng tác giữa Writer và Reviewer, giúp Workflow chính đơn giản hơn.
*   **Điều phối Workflow:** `BlogWorkflow` điều phối các bước chính: gọi Planner, sau đó lặp qua dàn ý và gọi Team cho từng phần.
*   **Kế thừa:** Tất cả các Agent, Team, và Workflow đều được tạo bằng cách kế thừa từ các lớp cơ sở của Agno.

Ví dụ này minh họa cách bạn có thể xây dựng các hệ thống phức tạp, có cấu trúc bằng cách kết hợp các lớp `Agent`, `Team`, và `Workflow` của Agno.

## Phần 6: Kết luận và Tài liệu tham khảo

Báo cáo này đã trình bày cách tiếp cận chi tiết để xây dựng các quy trình làm việc (workflows) tùy chỉnh trong Agno bằng cách kế thừa từ các lớp cốt lõi `Agent`, `Team`, và `Workflow`. Bằng cách tuân theo các nguyên tắc và ví dụ được cung cấp, bạn có thể:

1.  **Tạo Agent chuyên biệt:** Xây dựng các Agent với cấu hình, công cụ, và mục tiêu riêng bằng cách kế thừa từ `agno.agent.Agent`.
2.  **Tổ chức Team hiệu quả:** Nhóm các Agent lại với nhau bằng cách kế thừa từ `agno.team.Team`, lựa chọn chế độ hoạt động (`route`, `coordinate`, `collaborate`) phù hợp để quản lý sự cộng tác.
3.  **Định nghĩa Workflow phức tạp:** Điều phối hoạt động của các Agent và Team trong một quy trình nghiệp vụ lớn hơn bằng cách kế thừa từ `agno.workflow.Workflow` và triển khai logic trong phương thức `run`.

Cách tiếp cận dựa trên kế thừa này cho phép bạn tận dụng tối đa kiến trúc mạnh mẽ và linh hoạt của Agno, đồng thời tạo ra các giải pháp tùy chỉnh cao cho các bài toán cụ thể về tự động hóa và trí tuệ nhân tạo.

**Tài liệu tham khảo:**

*   **Kho lưu trữ Agno GitHub:** [https://github.com/agno-agi/agno](https://github.com/agno-agi/agno)
    *   Xem mã nguồn của các lớp `Agent`, `Team`, `Workflow` trong thư mục `libs/agno/agno/`.
    *   Tham khảo các ví dụ thực tế trong thư mục `cookbook/`.

Chúng tôi khuyến khích bạn khám phá thêm mã nguồn và các ví dụ trong kho lưu trữ để hiểu sâu hơn về các khả năng của Agno.

