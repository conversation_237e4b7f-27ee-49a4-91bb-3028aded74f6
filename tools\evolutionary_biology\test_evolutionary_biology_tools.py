#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho các evolutionary biology tools đã đư<PERSON><PERSON> c<PERSON>i tiến.
"""

import sys
import os
import json

# Thêm thư mục gốc vào Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_ncbi_taxonomy_tools():
    """Test NCBI Taxonomy tools."""
    print("=== Testing NCBI Taxonomy Tools ===")
    try:
        from tools.evolutionary_biology.ncbi_taxonomy_tools import NCBITaxonomyTools
        
        tool = NCBITaxonomyTools()
        
        # Test recent taxonomy changes
        print("--- Recent Taxonomy Changes ---")
        result1 = tool.get_recent_taxonomy_changes(3, 30, "species")
        print("Recent taxonomy changes result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test popular lineages
        print("\n--- Popular Lineages ---")
        result2 = tool.get_popular_lineages(3, "vertebrates")
        print("Popular lineages result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        print("✅ NCBI Taxonomy Tools: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing NCBI Taxonomy Tools: {e}")
    print()

def test_phylogenetic_tree_analyzer():
    """Test Phylogenetic Tree Analyzer."""
    print("=== Testing Phylogenetic Tree Analyzer ===")
    try:
        from tools.evolutionary_biology.phylogenetic_tree_analyzer import PhylogeneticTreeAnalyzer
        
        analyzer = PhylogeneticTreeAnalyzer()
        
        # Test phylogenetic tree analysis
        print("--- Phylogenetic Tree Analysis ---")
        result1 = analyzer.analyze_phylogenetic_tree("human,chimp,gorilla,orangutan", "maximum_likelihood", "orangutan")
        print("Tree analysis result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test molecular clock calculation
        print("\n--- Molecular Clock Calculation ---")
        result2 = analyzer.calculate_molecular_clock("nuclear_dna", None, "human_chimp")
        print("Molecular clock result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # Test divergence time estimation
        print("\n--- Divergence Time Estimation ---")
        result3 = analyzer.estimate_divergence_time("Homo sapiens", "Pan troglodytes", 0.012, "nuclear_dna")
        print("Divergence time result:", result3[:200] + "..." if len(result3) > 200 else result3)
        
        # Test tree confidence assessment
        print("\n--- Tree Confidence Assessment ---")
        result4 = analyzer.assess_tree_confidence("maximum_likelihood", 5000, 1000)
        print("Tree confidence result:", result4[:200] + "..." if len(result4) > 200 else result4)
        
        print("✅ Phylogenetic Tree Analyzer: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing Phylogenetic Tree Analyzer: {e}")
    print()

def test_genomic_evolution_tracker():
    """Test Genomic Evolution Tracker."""
    print("=== Testing Genomic Evolution Tracker ===")
    try:
        from tools.evolutionary_biology.genomic_evolution_tracker import GenomicEvolutionTracker
        
        tracker = GenomicEvolutionTracker()
        
        # Test genome evolution tracking
        print("--- Genome Evolution Tracking ---")
        result1 = tracker.track_genome_evolution("Homo sapiens", 3000000000, 100)
        print("Genome evolution result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test gene duplication analysis
        print("\n--- Gene Duplication Analysis ---")
        result2 = tracker.analyze_gene_duplication("immunoglobulin", "segmental", 10)
        print("Gene duplication result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # Test horizontal transfer detection
        print("\n--- Horizontal Transfer Detection ---")
        result3 = tracker.detect_horizontal_transfer("ATCGATCGATCG", "Escherichia coli", "Homo sapiens")
        print("Horizontal transfer result:", result3[:200] + "..." if len(result3) > 200 else result3)
        
        # Test mutation rate calculation
        print("\n--- Mutation Rate Calculation ---")
        result4 = tracker.calculate_mutation_rate("Homo sapiens", 25, "nuclear")
        print("Mutation rate result:", result4[:200] + "..." if len(result4) > 200 else result4)
        
        print("✅ Genomic Evolution Tracker: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing Genomic Evolution Tracker: {e}")
    print()

def test_species_divergence_calculator():
    """Test Species Divergence Calculator."""
    print("=== Testing Species Divergence Calculator ===")
    try:
        from tools.evolutionary_biology.species_divergence_calculator import SpeciesDivergenceCalculator
        
        calculator = SpeciesDivergenceCalculator()
        
        # Test species divergence calculation
        print("--- Species Divergence Calculation ---")
        result1 = calculator.calculate_species_divergence("Homo sapiens", "Pan troglodytes", 0.012, "nuclear_dna")
        print("Species divergence result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test population genetics estimation
        print("\n--- Population Genetics Estimation ---")
        result2 = calculator.estimate_population_genetics("Homo sapiens", 10000, 1e-8)
        print("Population genetics result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # Test speciation events analysis
        print("\n--- Speciation Events Analysis ---")
        result3 = calculator.analyze_speciation_events("Ancestral primate", ["Human", "Chimp", "Gorilla"], "allopatric")
        print("Speciation events result:", result3[:200] + "..." if len(result3) > 200 else result3)
        
        # Test evolutionary trends prediction
        print("\n--- Evolutionary Trends Prediction ---")
        result4 = calculator.predict_evolutionary_trends("Homo sapiens", 1000000, "climate_change")
        print("Evolutionary trends result:", result4[:200] + "..." if len(result4) > 200 else result4)
        
        print("✅ Species Divergence Calculator: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing Species Divergence Calculator: {e}")
    print()

def test_evolutionary_biology_search_toolkit():
    """Test Evolutionary Biology Search Toolkit."""
    print("=== Testing Evolutionary Biology Search Toolkit ===")
    try:
        from tools.evolutionary_biology.evolutionary_biology_search_toolkit import EvolutionaryBiologySearchToolkit
        
        toolkit = EvolutionaryBiologySearchToolkit()
        
        # Test regular keyword generation
        print("--- NCBI Taxonomy Keywords ---")
        result1 = toolkit.generate_ncbi_taxonomy_keywords("Homo sapiens", "species", "Primates")
        print(result1)
        
        print("\n--- BioLib Keywords ---")
        result2 = toolkit.generate_biolib_keywords("Homo.sapiens", "genomic", "comparative")
        print(result2)
        
        print("\n--- Open Tree Keywords ---")
        result3 = toolkit.generate_open_tree_keywords("phylogenetic", "primates", "molecular")
        print(result3)
        
        print("\n--- Wikipedia Evolutionary Keywords ---")
        result4 = toolkit.generate_wikipedia_evolutionary_keywords("natural selection", "evolutionary biology")
        print(result4)
        
        print("\n--- Phylogenetics Keywords ---")
        result5 = toolkit.generate_phylogenetics_keywords("maximum likelihood", "bayesian", "DNA")
        print(result5)
        
        print("\n--- Genomics Keywords ---")
        result6 = toolkit.generate_genomics_keywords("gene duplication", "mammals", "comparative")
        print(result6)
        
        print("\n--- Molecular Evolution Keywords ---")
        result7 = toolkit.generate_molecular_evolution_keywords("protein evolution", "phylogenetic", "sequence")
        print(result7)
        
        # Test recent/trending keyword generation
        print("\n--- NCBI Taxonomy Recent Keywords ---")
        result8 = toolkit.generate_ncbi_taxonomy_recent_keywords("species", 30)
        print(result8)
        
        print("\n--- BioLib Recent Keywords ---")
        result9 = toolkit.generate_biolib_recent_keywords("vertebrates", 30)
        print(result9)
        
        print("\n--- Open Tree Recent Keywords ---")
        result10 = toolkit.generate_open_tree_recent_keywords("maximum_likelihood", 60)
        print(result10)
        
        print("\n--- Wikipedia Evolutionary Recent Keywords ---")
        result11 = toolkit.generate_wikipedia_evolutionary_recent_keywords(30, "en")
        print(result11)
        
        print("\n--- Phylogenetics Recent Keywords ---")
        result12 = toolkit.generate_phylogenetics_recent_keywords("bayesian", 14)
        print(result12)
        
        print("✅ Evolutionary Biology Search Toolkit: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing Search Toolkit: {e}")
    print()

def test_package_import():
    """Test package-level imports."""
    print("=== Testing Package Import ===")
    try:
        # Test core tools
        from tools.evolutionary_biology.ncbi_taxonomy_tools import NCBITaxonomyTools
        from tools.evolutionary_biology.phylogenetic_tree_analyzer import PhylogeneticTreeAnalyzer
        from tools.evolutionary_biology.genomic_evolution_tracker import GenomicEvolutionTracker
        from tools.evolutionary_biology.species_divergence_calculator import SpeciesDivergenceCalculator
        from tools.evolutionary_biology.evolutionary_biology_search_toolkit import EvolutionaryBiologySearchToolkit
        
        print("✅ Core package imports successful")
        
        # Test instantiation
        tools = [
            NCBITaxonomyTools(),
            PhylogeneticTreeAnalyzer(),
            GenomicEvolutionTracker(),
            SpeciesDivergenceCalculator(),
            EvolutionaryBiologySearchToolkit()
        ]
        print("✅ Core tool instantiation successful")
        
    except Exception as e:
        print(f"❌ Package import error: {e}")
    print()

def main():
    """Chạy tất cả các test."""
    print("Testing Evolutionary Biology Tools Functions")
    print("=" * 70)
    print()
    
    # Test các tool đã cải tiến
    test_ncbi_taxonomy_tools()
    test_phylogenetic_tree_analyzer()
    test_genomic_evolution_tracker()
    test_species_divergence_calculator()
    test_evolutionary_biology_search_toolkit()
    test_package_import()
    
    print("=" * 70)
    print("Testing completed!")
    print("\n📊 Summary:")
    print("✅ Working: NCBI Taxonomy Tools (enhanced)")
    print("✅ Working: Phylogenetic Tree Analyzer (new)")
    print("✅ Working: Genomic Evolution Tracker (new)")
    print("✅ Working: Species Divergence Calculator (new)")
    print("✅ Working: Evolutionary Biology Search Toolkit (new)")
    print("\n🌟 Key Features:")
    print("• Recent taxonomy changes and popular lineages")
    print("• Phylogenetic tree analysis and molecular clock calculations")
    print("• Genome evolution tracking and gene duplication analysis")
    print("• Species divergence calculation and population genetics")
    print("• Comprehensive keyword generation for evolutionary research")
    print("• 15+ specialized functions for evolutionary biology analysis")

if __name__ == "__main__":
    main()
