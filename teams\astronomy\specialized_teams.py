"""
Specialized astronomy teams using Agno framework.
These teams coordinate different agent compositions for astronomical research.
"""

from agno.team  import Team
from agno.agent import Agent
from agno.models.ollama import Ollama
from textwrap import dedent
from agents.astronomy.specialized_agents import (
    nasa_agent, nasa_ads_agent, simbad_agent, esa_archives_agent,
    esdc_crawler_agent, wikipedia_astronomy_agent,
    lcot_reasoning_agent, cot_encyclopedia_agent, mcot_agent
)

# --- Qdrant, chunking, reranker tích hợp ---
from utils.chunking import chunk_text
from utils.qdrant_optimization import get_qdrant_optimizer

# Embedding bằng Ollama (nomic-embed-text:latest)
import requests
import numpy as np

def embed_text(text: str) -> list:
    """
    Gọi Ollama embedding model (nomic-embed-text:latest) để lấy vector embedding cho text.
    """
    url = "http://localhost:11434/api/embeddings"
    data = {
        "model": "nomic-embed-text:latest",
        "prompt": text
    }
    try:
        response = requests.post(url, json=data, timeout=30)
        response.raise_for_status()
        result = response.json()
        return result["embedding"]
    except Exception as e:
        print(f"Lỗi embedding với Ollama: {e}")
        return [0.0] * 768

# Qdrant client thực tế
from qdrant_client import QdrantClient
qdrant_client = QdrantClient(host="localhost", port=6333)
QDRANT_COLLECTION = "astronomy_knowledge"
qdrant_optimizer = get_qdrant_optimizer()

# Reranker BAAI/bge-reranker-v2-m3
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import torch

class BGEReRanker:
    def __init__(self, model_name="BAAI/bge-reranker-v2-m3"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForSequenceClassification.from_pretrained(model_name)
        self.model.eval()

    def rerank(self, query: str, docs: list, top_k: int = 5):
        pairs = [(query, doc["text"]) for doc in docs]
        scores = []
        with torch.no_grad():
            for q, d in pairs:
                inputs = self.tokenizer(q, d, return_tensors="pt", truncation=True, max_length=512)
                outputs = self.model(**inputs)
                score = float(outputs.logits[0].item())
                scores.append(score)
        # Gán score vào doc
        for i, doc in enumerate(docs):
            doc["rerank_score"] = scores[i]
        # Sắp xếp theo score giảm dần
        docs_sorted = sorted(docs, key=lambda x: x["rerank_score"], reverse=True)
        return docs_sorted[:top_k]

reranker = BGEReRanker()

# Thêm Writer Agent cho Astronomy (giả sử sẽ được định nghĩa trong specialized_agents.py)
try:
    from agents.astronomy.specialized_agents import writer_agent
except ImportError:
    writer_agent = None  # Nếu chưa có thì để None, sẽ bổ sung sau
from agno.memory.v2.memory import Memory
from agno.storage.agent.sqlite import SqliteAgentStorage
from agno.team import Team as CustomTeam
from agno.team import Team

# Define the model
MODEL = Ollama(id="qwen3:4b")

# Initialize Agno Memory and SQLite storage
memory = Memory(model=MODEL)
agent_storage = SqliteAgentStorage(table_name="agent_sessions", db_file="tmp/persistent_memory.db")
team_storage = SqliteAgentStorage(table_name="team_sessions", db_file="tmp/persistent_memory.db")

# Refactored teams
astronomy_data_router_team = CustomTeam(
    name="Astronomy Data Router",
    mode="route",
    model=MODEL,
    members=[nasa_agent, simbad_agent],
    tools=[],
    instructions=dedent("""
        You are a router for astronomical data queries.
        For object-specific queries, route to SIMBAD Agent.
        For mission data, route to NASA Data Agent.
    """),
    show_tool_calls=True,
    markdown=True,
    memory=memory,  # Attach Agno Memory
    storage=team_storage  # Attach SQLite storage
)

astronomy_analysis_team = CustomTeam(
    name="Astronomy Analysis Team",
    mode="coordinate",
    model=MODEL,
    members=[
        lcot_reasoning_agent, cot_encyclopedia_agent,
        mcot_agent, wikipedia_astronomy_agent
    ],
    tools=[],
    instructions=dedent("""
        Coordinate the analysis workflow in this sequence:
        1. LCoT Agent performs initial analysis of the query.
        2. Encyclopedia Agent expands with comprehensive context.
        3. MCoT Agent explores multiple possibilities and implications.
        4. Wikipedia Agent provides conceptual overviews.
    """),
    show_tool_calls=True,
    markdown=True,
    memory=memory,  # Attach Agno Memory
    storage=team_storage  # Attach SQLite storage
)

astronomy_data_analysis_team = CustomTeam(
    name="Astronomy Data Analysis Team",
    mode="coordinate",
    model=MODEL,
    members=[
        nasa_agent, simbad_agent, lcot_reasoning_agent, cot_encyclopedia_agent
    ],
    tools=[],
    instructions=dedent("""
        Coordinate the workflow in this sequence:
        1. SIMBAD Agent retrieves basic object information.
        2. NASA Agent collects mission data about the object.
        3. LCoT Agent analyzes the combined information.
        4. CoT Encyclopedia Agent provides detailed context.
    """),
    show_tool_calls=True,
    markdown=True,
    memory=memory,  # Attach Agno Memory
    storage=team_storage  # Attach SQLite storage
)

class AstronomyTeam(Team):
    """
    A unified Astronomy Team class that can represent any of the specialized astronomy teams.
    """
    def __init__(self, team_type="router"):
        if team_type == "router":
            team = astronomy_data_router_team
        elif team_type == "analysis":
            team = astronomy_analysis_team
        elif team_type == "data_analysis":
            team = astronomy_data_analysis_team
        elif team_type == "ultimate_researcher":
            team = ultimate_researcher_team
        else:
            raise ValueError("Invalid team_type. Choose from 'router', 'analysis', 'data_analysis', or 'ultimate_researcher'.")

        super().__init__(
            name=team.name,
            mode=team.mode,
            model=team.model,
            members=team.members,
            tools=team.tools,
            instructions=team.instructions,
        )

    def show_responses(self):
        return getattr(self, "get_responses", lambda: None)()

    def show_members_responses(self):
        return getattr(self, "get_members_responses", lambda: None)()

    async def aprint_response(self, query, **kwargs):
        import asyncio
        # Nếu print_response là async
        if asyncio.iscoroutinefunction(super().print_response):
            result = await super().print_response(query)
        else:
            result = super().print_response(query)
        if result is None or (isinstance(result, str) and result.strip() == ""):
            print("⚠️ Không có nội dung trả lời từ AI (None hoặc rỗng).")
        return result

    async def apredict_response(self, query, **kwargs):
        return await self.aprint_response(query, **kwargs)

    async def save_content_to_qdrant(self, content: str, query: str, writer_type: str = "team_specialized", chunk_strategy: str = "auto") -> None:
        # Kiểm tra response/model trả về None hoặc rỗng
        if content is None or (isinstance(content, str) and not content.strip()):
            print("Không có nội dung trả lời từ AI (None hoặc rỗng). Bỏ qua chunking và upsert Qdrant.")
            return
        # Chunk content mới và lưu lên Qdrant
        new_chunks = chunk_text(content, strategy=chunk_strategy)
        points = []
        for idx, chunk in enumerate(new_chunks):
            points.append({
                "id": None,
                "vector": embed_text(chunk),
                "payload": {
                    "text": chunk,
                    "source": "team_specialized",
                    "writer_type": writer_type,
                    "query": query,
                    "chunk_index": idx
                }
            })
        # Kiểm tra points trước khi upsert
        if not points:
            print("Không có dữ liệu để lưu lên Qdrant (points rỗng). Bỏ qua upsert.")
            return
        try:
            qdrant_client.upsert(
                collection_name=QDRANT_COLLECTION,
                points=points
            )
        except Exception as e:
            print(f"Lỗi upsert Qdrant: {e}")

# Ultimate Researcher Team cho Astronomy
from textwrap import dedent

class UltimateResearcherTeam(CustomTeam):
    def __init__(self, writer_agent=None):
        super().__init__(
            name="Astronomy Ultimate Researcher Team",
            mode="coordinate",
            model=MODEL,
            members=[
                lcot_reasoning_agent,  # Phân tích, chia nhỏ câu hỏi
                cot_encyclopedia_agent,  # Mở rộng kiến thức
                nasa_agent, nasa_ads_agent, simbad_agent, esa_archives_agent, esdc_crawler_agent, wikipedia_astronomy_agent,  # Các agent research
                mcot_agent,  # Reflect đa hướng
                writer_agent if writer_agent else lcot_reasoning_agent  # Writer Agent tổng hợp, fallback nếu chưa có
            ],
            tools=[],
            instructions=dedent("""
                Workflow:
                1. LCoT Reasoning Agent phân tích và chia nhỏ câu hỏi thành các sub-question.
                2. CoT Encyclopedia Agent tăng cường kiến thức cho từng sub-question.
                3. Các agent research (NASA, SIMBAD, ESA, ESDC, Wikipedia) tìm kiếm thông tin cho từng sub-question.
                4. MCoT Agent phản biện, tổng hợp đa hướng.
                5. Writer Agent reasoning lại toàn bộ và tổng hợp thành câu trả lời cuối cùng.
                Đảm bảo trả lời chi tiết, logic, có dẫn nguồn, ưu tiên tiếng Việt cho user.
            """),
            show_tool_calls=True,
            markdown=True,
            memory=memory,
            storage=team_storage
        )

    async def save_content_to_qdrant(self, content: str, query: str, writer_type: str = "ultimate_researcher", chunk_strategy: str = "auto"):
        # Chunk content mới và lưu lên Qdrant
        new_chunks = chunk_text(content, strategy=chunk_strategy)
        points = []
        for idx, chunk in enumerate(new_chunks):
            points.append({
                "id": None,  # Qdrant sẽ tự sinh id nếu None
                "vector": embed_text(chunk),
                "payload": {
                    "text": chunk,
                    "source": "ultimate_researcher",
                    "writer_type": writer_type,
                    "query": query,
                    "chunk_index": idx
                }
            })
        await qdrant_optimizer.optimized_upsert(
            client=qdrant_client,
            collection_name=QDRANT_COLLECTION,
            points=points
        )

ultimate_researcher_team = UltimateResearcherTeam(writer_agent if writer_agent else lcot_reasoning_agent)

# Export teams
__all__ = [
    "astronomy_data_router_team",
    "astronomy_analysis_team",
    "astronomy_data_analysis_team",
    "ultimate_researcher_team",
    "AstronomyTeam",
    "UltimateResearcherTeam"
]
