import json
import logging
from typing import List, Dict
from agno.tools import Toolkit
from agno.agent import Agent
from agno.reasoning.step import ReasoningStep, NextAction
from FlagEmbedding import FlagReranker

logger = logging.getLogger(__name__)

class BGERerankerTools(Toolkit):
    """Agno-compatible tool for reranking document chunks using bge-reranker-v2-m3."""

    def __init__(self):
        super().__init__(name="bge_reranker")
        self.description = "Reranks retrieved chunks based on semantic relevance to query."
        self.model = FlagReranker("BAAI/bge-reranker-v2-m3", use_fp16=True)
        self.register(self.rerank_documents)

    async def rerank_documents(self, query: str, documents: List[Dict[str, str]], agent: Agent) -> str:
        """Rerank documents based on relevance to the query."""
        try:
            logger.info(f"Running reranker for query: {query} with {len(documents)} documents.")

            if not query or not documents:
                return json.dumps({"error": "Query and documents must be provided."})

            # Format input for reranker
            pairs = [{"query": query, "text": doc["text"]} for doc in documents]
            scores = self.model.compute_score(pairs)

            # Attach scores and sort
            for i, doc in enumerate(documents):
                doc["score"] = scores[i]
            ranked = sorted(documents, key=lambda x: x["score"], reverse=True)

            output = "\n\n".join([f"## Rank {i+1}\nScore: {doc['score']:.3f}\n{doc['text']}" for i, doc in enumerate(ranked)])

            reasoning_step = ReasoningStep(
                title="BGE Reranking",
                reasoning=output,
                action="Reranking completed",
                next_action=NextAction.CONTINUE,
                confidence=max(scores)
            )
            self._save_reasoning_step(agent, reasoning_step)

            return json.dumps({
                "tool_call": {
                    "tool": "rerank_documents",
                    "parameters": {
                        "query": query,
                        "doc_count": len(documents)
                    }
                },
                "output": output,
                "top_doc": ranked[0]
            }, indent=2)

        except Exception as e:
            logger.error(f"BGE reranker error: {e}")
            return json.dumps({"error": str(e)})

    def _save_reasoning_step(self, agent: Agent, step: ReasoningStep):
        if agent.session_state is None:
            agent.session_state = {}
        agent.session_state.setdefault("reasoning_steps", {}).setdefault(agent.run_id, []).append(
            step.model_dump_json()
        )
