from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class WikisourcePhilosophyTool(Toolkit):
    """
    Wikisource Philosophy Tool for searching original philosophical works and translations from Wikisource.
    """

    def __init__(self):
        super().__init__(
            name="Wikisource Philosophy Search Tool",
            description="Tool for searching original philosophical works and translations from Wikisource.",
            tools=[self.search_wikisource_philosophy]
        )

    async def search_wikisource_philosophy(self, query: str, language: str = "en", limit: int = 5) -> Dict[str, Any]:
        """
        Search Wikisource for original philosophical works and translations.

        Parameters:
        - query: Name of philosophical work, author, or concept (e.g., 'Thus Spoke Zarathustra', 'Meditations Marcus <PERSON>', 'Plato Republic')
        - language: Wikisource language code (default: 'en')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with search results including title, author, snippet, and Wikisource URLs
        """
        logger.info(f"Searching Wikisource ({language}) for philosophy: {query}")

        try:
            # Wikisource uses the MediaWiki API for search
            api_url = f"https://{language}.wikisource.org/w/api.php"
            params = {
                "action": "query",
                "list": "search",
                "srsearch": query,
                "format": "json",
                "srlimit": limit
            }
            response = requests.get(api_url, params=params)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikisource",
                    "message": f"Wikisource API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            search_results = data.get("query", {}).get("search", [])
            results = []
            for item in search_results:
                title = item.get("title")
                snippet = item.get("snippet")
                page_url = f"https://{language}.wikisource.org/wiki/{title.replace(' ', '_')}"
                results.append({
                    "title": title,
                    "snippet": snippet,
                    "wikisource_url": page_url
                })

            return {
                "status": "success",
                "source": "Wikisource",
                "query": query,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching Wikisource Philosophy: {str(e)}")
            return {
                "status": "error",
                "source": "Wikisource",
                "message": str(e),
                "query": query
            }
