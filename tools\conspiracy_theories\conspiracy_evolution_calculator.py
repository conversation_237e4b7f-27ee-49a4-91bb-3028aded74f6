# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math
from datetime import datetime

class ConspiracyEvolutionCalculator(Toolkit):
    """
    Conspiracy Evolution Calculator cho tính toán conspiracy theory evolution, belief divergence và misinformation spread.
    """

    def __init__(self, enable_calculations: bool = True, **kwargs):
        super().__init__(
            name="conspiracy_evolution_calculator",
            **kwargs
        )
        
        # Conspiracy theory lifecycle stages
        self.conspiracy_stages = {
            "emergence": {"duration_ratio": 0.1, "believers": 0.01, "credibility": 0.2},
            "growth": {"duration_ratio": 0.3, "believers": 0.05, "credibility": 0.4},
            "peak": {"duration_ratio": 0.2, "believers": 0.15, "credibility": 0.6},
            "decline": {"duration_ratio": 0.3, "believers": 0.08, "credibility": 0.3},
            "persistence": {"duration_ratio": 0.1, "believers": 0.03, "credibility": 0.1}
        }
        
        # Spread mechanisms and rates
        self.spread_mechanisms = {
            "word_of_mouth": {"rate": 0.1, "reach": 10, "persistence": 0.8},
            "print_media": {"rate": 0.3, "reach": 1000, "persistence": 0.9},
            "radio_tv": {"rate": 0.5, "reach": 100000, "persistence": 0.7},
            "internet_forums": {"rate": 0.7, "reach": 10000, "persistence": 0.6},
            "social_media": {"rate": 0.9, "reach": 1000000, "persistence": 0.4},
            "video_platforms": {"rate": 0.8, "reach": 500000, "persistence": 0.5}
        }
        
        # Psychological factors
        self.psychological_factors = {
            "confirmation_bias": 0.8,
            "pattern_seeking": 0.7,
            "distrust_authority": 0.6,
            "need_for_control": 0.5,
            "social_identity": 0.9,
            "cognitive_dissonance": 0.4
        }
        
        if enable_calculations:
            self.register(self.calculate_conspiracy_divergence)
            self.register(self.estimate_belief_evolution)
            self.register(self.analyze_misinformation_spread)
            self.register(self.predict_conspiracy_trends)

    def calculate_conspiracy_divergence(self, theory1: str, theory2: str, 
                                      separation_years: int = None, 
                                      divergence_factor: str = "narrative") -> str:
        """
        Tính toán conspiracy theory divergence và belief system evolution.
        
        Args:
            theory1: Conspiracy theory thứ nhất
            theory2: Conspiracy theory thứ hai  
            separation_years: Thời gian tách biệt (years)
            divergence_factor: Yếu tố divergence ('narrative', 'evidence', 'community')
            
        Returns:
            Chuỗi JSON chứa tính toán conspiracy divergence
        """
        log_debug(f"Calculating conspiracy divergence between {theory1} and {theory2}")
        
        try:
            if separation_years is None:
                separation_years = 10  # Default separation
            
            # Calculate divergence metrics
            divergence_rates = {
                "narrative": 0.2,    # Stories evolve quickly
                "evidence": 0.1,     # Evidence claims evolve slower
                "community": 0.3,    # Communities fragment rapidly
                "methodology": 0.15  # Research methods evolve moderately
            }
            
            evolution_rate = divergence_rates.get(divergence_factor, 0.2)
            divergence_index = 1 - math.exp(-evolution_rate * separation_years)
            
            # Belief system analysis
            belief_overlap = max(0, 1 - (separation_years * 0.05))
            narrative_similarity = max(0, 1 - divergence_index)
            
            # Community fragmentation
            community_split = min(1.0, separation_years * 0.08)
            echo_chamber_effect = separation_years * 0.1
            
            # Mutation mechanisms
            mutation_factors = {
                "reinterpretation": round(divergence_index * 0.7, 3),
                "evidence_selection": round(divergence_index * 0.8, 3),
                "narrative_drift": round(divergence_index * 0.9, 3),
                "community_pressure": round(community_split, 3)
            }
            
            result = {
                "conspiracy_comparison": {
                    "theory1": theory1,
                    "theory2": theory2,
                    "separation_years": separation_years,
                    "divergence_factor": divergence_factor
                },
                "divergence_analysis": {
                    "divergence_index": round(divergence_index, 3),
                    "belief_overlap": round(belief_overlap, 3),
                    "narrative_similarity": round(narrative_similarity, 3),
                    "evolution_rate": evolution_rate
                },
                "community_dynamics": {
                    "community_split": round(community_split, 3),
                    "echo_chamber_effect": round(echo_chamber_effect, 3),
                    "cross_pollination": max(0, 0.5 - echo_chamber_effect)
                },
                "mutation_mechanisms": mutation_factors,
                "psychological_drivers": self._analyze_psychological_drivers(theory1, theory2),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error calculating conspiracy divergence: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate conspiracy divergence: {str(e)}"
            }, indent=4)

    def estimate_belief_evolution(self, conspiracy_theory: str, evolution_years: int = 20, 
                                media_environment: str = "social_media") -> str:
        """
        Ước tính belief evolution và conspiracy theory development.
        """
        log_debug(f"Estimating belief evolution for {conspiracy_theory} over {evolution_years} years")
        
        try:
            # Get media environment parameters
            media_params = self.spread_mechanisms.get(media_environment, self.spread_mechanisms["social_media"])
            
            # Calculate evolution trajectory
            belief_growth_rate = media_params["rate"] * 0.1
            complexity_evolution = (1 + belief_growth_rate) ** evolution_years
            
            # Lifecycle analysis
            lifecycle_phases = []
            for stage_name, stage_data in self.conspiracy_stages.items():
                phase_duration = int(evolution_years * stage_data["duration_ratio"])
                phase_believers = stage_data["believers"] * media_params["reach"]
                
                lifecycle_phases.append({
                    "stage": stage_name.title(),
                    "duration_years": phase_duration,
                    "believer_percentage": round(stage_data["believers"] * 100, 2),
                    "estimated_believers": int(phase_believers),
                    "credibility_score": stage_data["credibility"],
                    "characteristics": self._get_stage_characteristics(stage_name)
                })
            
            # Narrative evolution
            narrative_changes = {
                "core_claims_stability": round(1 - (evolution_years * 0.02), 2),
                "detail_elaboration": round(evolution_years * 0.1, 1),
                "evidence_accumulation": round(evolution_years * 0.05, 1),
                "counter_narrative_development": "High" if evolution_years > 10 else "Low"
            }
            
            result = {
                "evolution_analysis": {
                    "conspiracy_theory": conspiracy_theory,
                    "evolution_years": evolution_years,
                    "media_environment": media_environment,
                    "complexity_growth": round(complexity_evolution, 2)
                },
                "lifecycle_phases": lifecycle_phases,
                "narrative_evolution": narrative_changes,
                "spread_analysis": self._analyze_spread_patterns(media_params, evolution_years),
                "resistance_factors": self._identify_resistance_factors(conspiracy_theory),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error estimating belief evolution: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to estimate belief evolution: {str(e)}"
            }, indent=4)

    def analyze_misinformation_spread(self, misinformation_type: str, 
                                    spread_duration_days: int = 30,
                                    platform: str = "social_media") -> str:
        """
        Phân tích misinformation spread patterns và viral dynamics.
        """
        log_debug(f"Analyzing misinformation spread for {misinformation_type}")
        
        try:
            platform_params = self.spread_mechanisms.get(platform, self.spread_mechanisms["social_media"])
            
            # Viral spread modeling
            initial_exposure = 100
            viral_coefficient = platform_params["rate"] * 2
            peak_reach = initial_exposure * (viral_coefficient ** (spread_duration_days / 7))
            
            # Spread phases
            spread_phases = {
                "seeding": {
                    "duration_days": spread_duration_days * 0.1,
                    "reach": initial_exposure,
                    "characteristics": ["Initial posts", "Early adopters", "Limited verification"]
                },
                "amplification": {
                    "duration_days": spread_duration_days * 0.3,
                    "reach": peak_reach * 0.3,
                    "characteristics": ["Rapid sharing", "Emotional responses", "Algorithm boost"]
                },
                "peak_viral": {
                    "duration_days": spread_duration_days * 0.2,
                    "reach": peak_reach,
                    "characteristics": ["Maximum exposure", "Media attention", "Fact-checking response"]
                },
                "decline": {
                    "duration_days": spread_duration_days * 0.4,
                    "reach": peak_reach * 0.1,
                    "characteristics": ["Reduced sharing", "Counter-narratives", "Platform intervention"]
                }
            }
            
            result = {
                "spread_analysis": {
                    "misinformation_type": misinformation_type,
                    "spread_duration_days": spread_duration_days,
                    "platform": platform,
                    "peak_reach": int(peak_reach)
                },
                "viral_dynamics": {
                    "viral_coefficient": round(viral_coefficient, 2),
                    "growth_rate": round((peak_reach / initial_exposure) ** (1/spread_duration_days), 3),
                    "persistence_score": platform_params["persistence"]
                },
                "spread_phases": spread_phases,
                "mitigation_effectiveness": self._assess_mitigation_effectiveness(platform, spread_duration_days),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error analyzing misinformation spread: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze misinformation spread: {str(e)}"
            }, indent=4)

    def predict_conspiracy_trends(self, current_theories: List[str], 
                                prediction_years: int = 10,
                                social_context: str = "digital_age") -> str:
        """
        Dự đoán conspiracy theory trends và future developments.
        """
        log_debug(f"Predicting conspiracy trends over {prediction_years} years")
        
        try:
            # Trend prediction factors
            trend_drivers = {
                "technological_change": 0.8,
                "social_uncertainty": 0.7,
                "political_polarization": 0.9,
                "economic_instability": 0.6,
                "information_overload": 0.8
            }
            
            trend_strength = sum(trend_drivers.values()) / len(trend_drivers)
            
            # Predicted conspiracy categories
            emerging_categories = {
                "ai_surveillance": {
                    "probability": 0.9,
                    "timeline": f"{prediction_years // 3} years",
                    "themes": ["AI control", "Digital surveillance", "Algorithm manipulation"]
                },
                "climate_manipulation": {
                    "probability": 0.7,
                    "timeline": f"{prediction_years // 2} years", 
                    "themes": ["Weather control", "Climate hoax", "Green agenda"]
                },
                "biotech_control": {
                    "probability": 0.8,
                    "timeline": f"{prediction_years // 4} years",
                    "themes": ["Genetic manipulation", "Bioweapons", "Population control"]
                }
            }
            
            result = {
                "prediction_parameters": {
                    "current_theories": current_theories,
                    "prediction_years": prediction_years,
                    "social_context": social_context,
                    "trend_strength": round(trend_strength, 2)
                },
                "trend_drivers": trend_drivers,
                "emerging_categories": emerging_categories,
                "evolution_patterns": self._predict_evolution_patterns(prediction_years),
                "mitigation_strategies": self._suggest_mitigation_strategies(),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error predicting conspiracy trends: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to predict conspiracy trends: {str(e)}"
            }, indent=4)

    # Helper methods
    def _analyze_psychological_drivers(self, theory1: str, theory2: str) -> Dict[str, float]:
        """Analyze psychological drivers for conspiracy divergence."""
        return {
            "confirmation_bias": 0.8,
            "pattern_seeking": 0.7,
            "social_identity": 0.9,
            "distrust_authority": 0.6
        }

    def _get_stage_characteristics(self, stage: str) -> List[str]:
        """Get characteristics for conspiracy lifecycle stage."""
        characteristics_map = {
            "emergence": ["Small community", "Basic narrative", "Limited evidence"],
            "growth": ["Expanding audience", "Narrative development", "Evidence gathering"],
            "peak": ["Maximum believers", "Complex narrative", "Mainstream attention"],
            "decline": ["Reduced interest", "Counter-evidence", "Community fragmentation"],
            "persistence": ["Core believers", "Simplified narrative", "Underground status"]
        }
        return characteristics_map.get(stage, ["General characteristics"])

    def _analyze_spread_patterns(self, media_params: Dict, years: int) -> Dict[str, Any]:
        """Analyze spread patterns based on media environment."""
        return {
            "reach_potential": media_params["reach"],
            "spread_velocity": media_params["rate"],
            "persistence_factor": media_params["persistence"],
            "network_effects": "High" if media_params["reach"] > 100000 else "Medium"
        }

    def _identify_resistance_factors(self, theory: str) -> List[str]:
        """Identify factors that resist conspiracy theory spread."""
        return [
            "Critical thinking education",
            "Fact-checking initiatives", 
            "Scientific literacy",
            "Media literacy programs",
            "Transparent institutions"
        ]

    def _assess_mitigation_effectiveness(self, platform: str, duration: int) -> Dict[str, str]:
        """Assess effectiveness of mitigation strategies."""
        return {
            "fact_checking": "Moderate" if duration > 14 else "High",
            "content_removal": "High" if platform == "social_media" else "Low",
            "algorithm_adjustment": "Moderate",
            "user_education": "Low" if duration < 7 else "Moderate"
        }

    def _predict_evolution_patterns(self, years: int) -> Dict[str, str]:
        """Predict conspiracy theory evolution patterns."""
        return {
            "complexity_trend": "Increasing" if years > 5 else "Stable",
            "fragmentation_pattern": "High" if years > 8 else "Moderate",
            "mainstream_integration": "Possible" if years > 10 else "Unlikely",
            "counter_narrative_strength": "Strong" if years > 7 else "Developing"
        }

    def _suggest_mitigation_strategies(self) -> List[str]:
        """Suggest strategies for mitigating conspiracy theory spread."""
        return [
            "Improve media literacy education",
            "Enhance fact-checking capabilities",
            "Promote critical thinking skills",
            "Increase institutional transparency",
            "Support quality journalism",
            "Develop better detection algorithms"
        ]
