from agno.tools.mcp import MCPTools, SSEClientParams
from agno.models.ollama import Ollama
from agno.agent import Agent
import asyncio

async def main():
    async with MCPTools(
        transport="sse",
        server_params=SSEClientParams(
            url="http://localhost:8000/sse"
        )
    ) as tools:
        agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="MCP Agent",
            tools=[tools], 
            )
        # Sửa lỗi: chỉ truyền 1 argument cho arun
        result = await agent.arun("browse_url https://example.com")
        print(result)

if __name__ == "__main__":
    asyncio.run(main())
