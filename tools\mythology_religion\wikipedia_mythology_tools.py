from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class WikipediaMythologyTool(Toolkit):
    """
    Wikipedia Mythology Tool for searching mythological concepts, deities, pantheons, and motifs on Wikipedia.
    """

    def __init__(self):
        super().__init__(
            name="Wikipedia Mythology Tools",
            tools=[
                self.search_wikipedia_mythology,
                self.get_mythology_article,
                self.search_by_mythology,
                self.get_mythological_figures,
                self.compare_mythologies
            ]
        )

    async def search_wikipedia_mythology(self, query: str, language: str = "en") -> Dict[str, Any]:
        """
        Search Wikipedia for mythological concepts, deities, pantheons, or motifs.

        Parameters:
        - query: Deity, pantheon, motif, or mythology (e.g., 'Thor', 'Aztec gods', 'trickster figure', 'Japanese mythology')
        - language: Wikipedia language code (default: 'en')

        Returns:
        - JSON with summary, page URL, thumbnail, and related topics
        """
        logger.info(f"Searching Wikipedia ({language}) for: {query}")

        try:
            # Wikipedia API endpoint
            api_url = f"https://{language}.wikipedia.org/api/rest_v1/page/summary/{query.replace(' ', '_')}"
            response = requests.get(api_url)

            if response.status_code == 404:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": "No article found for query",
                    "query": query
                }
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": f"Wikipedia API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            summary = data.get("extract")
            page_url = data.get("content_urls", {}).get("desktop", {}).get("page")
            title = data.get("title")
            thumbnail = data.get("thumbnail", {}).get("source")

            # Optionally, get related topics (using search API)
            related = []
            try:
                search_url = f"https://{language}.wikipedia.org/w/api.php"
                # Tối ưu keyword: kết hợp tên thần, motif, pantheon, mythology, list, figure
                search_params = {
                    "action": "query",
                    "list": "search",
                    "srsearch": f"{query} mythology OR deity OR pantheon OR motif OR list OR figure",
                    "format": "json",
                    "srlimit": 5
                }
                search_resp = requests.get(search_url, params=search_params)
                if search_resp.status_code == 200:
                    search_data = search_resp.json()
                    for item in search_data.get("query", {}).get("search", []):
                        if item.get("title") != title:
                            related.append(item.get("title"))
            except Exception as rel_err:
                log_debug(f"Error fetching related Wikipedia topics: {str(rel_err)}")

            return {
                "status": "success",
                "source": "Wikipedia",
                "query": query,
                "title": title,
                "summary": summary,
                "page_url": page_url,
                "thumbnail": thumbnail,
                "related_topics": related
            }

        except Exception as e:
            log_debug(f"Error searching Wikipedia: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "query": query
            }
            
    async def get_mythology_article(self, title: str, language: str = "en") -> Dict[str, Any]:
        """
        Lấy nội dung đầy đủ của một bài viết thần thoại trên Wikipedia.

        Parameters:
        - title: Tiêu đề chính xác của bài viết (ví dụ: 'Thor', 'Thần thoại Hy Lạp')
        - language: Mã ngôn ngữ Wikipedia (mặc định: 'en')

        Returns:
        - JSON chứa nội dung đầy đủ, mục lục, hình ảnh và liên kết
        """
        logger.info(f"Đang lấy nội dung bài viết Wikipedia: {title}")

        try:
            # Lấy thông tin cơ bản
            api_url = f"https://{language}.wikipedia.org/api/rest_v1/page/summary/{title.replace(' ', '_')}"
            response = requests.get(api_url, timeout=10)

            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": f"Không tìm thấy bài viết. Mã lỗi: {response.status_code}",
                    "title": title
                }

            data = response.json()
            
            # Lấy nội dung đầy đủ
            content_url = f"https://{language}.wikipedia.org/w/api.php"
            params = {
                "action": "parse",
                "page": title,
                "prop": "text|sections|images",
                "format": "json"
            }
            content_resp = requests.get(content_url, params=params, timeout=15)
            content_data = content_resp.json()
            
            # Xử lý nội dung
            html_content = content_data.get("parse", {}).get("text", {}).get("*", "")
            sections = content_data.get("parse", {}).get("sections", [])
            images = content_data.get("parse", {}).get("images", [])
            
            # Lấy liên kết ngoài
            extlinks_params = {
                "action": "query",
                "titles": title,
                "prop": "extlinks",
                "format": "json"
            }
            extlinks_resp = requests.get(content_url, params=extlinks_params, timeout=10)
            extlinks = [link["*"] for link in extlinks_resp.json().get("query", {}).get("pages", {}).get("1", {}).get("extlinks", [])]
            
            return {
                "status": "success",
                "source": "Wikipedia",
                "title": data.get("title"),
                "description": data.get("description"),
                "extract": data.get("extract"),
                "page_url": data.get("content_urls", {}).get("desktop", {}).get("page"),
                "thumbnail": data.get("thumbnail", {}).get("source"),
                "sections": [s["line"] for s in sections],
                "images": images[:10],  # Giới hạn số lượng ảnh
                "external_links": extlinks[:10]  # Giới hạn số lượng liên kết
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy nội dung bài viết: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "title": title
            }
            
    async def search_by_mythology(self, mythology: str, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm các bài viết liên quan đến một nền thần thoại cụ thể.

        Parameters:
        - mythology: Tên nền thần thoại (ví dụ: 'Greek', 'Norse', 'Hindu')
        - limit: Số lượng kết quả tối đa (mặc định: 5)

        Returns:
        - JSON chứa danh sách các bài viết liên quan
        """
        logger.info(f"Tìm kiếm bài viết về thần thoại: {mythology}")

        try:
            search_url = "https://en.wikipedia.org/w/api.php"
            params = {
                "action": "query",
                "list": "search",
                "srsearch": f"{mythology} mythology gods goddesses deities",
                "format": "json",
                "srlimit": limit
            }
            
            response = requests.get(search_url, params=params, timeout=10)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": f"Lỗi tìm kiếm. Mã lỗi: {response.status_code}",
                    "mythology": mythology
                }
                
            data = response.json()
            results = []
            for item in data.get("query", {}).get("search", [])[:limit]:
                results.append({
                    "title": item.get("title"),
                    "snippet": item.get("snippet"),
                    "pageid": item.get("pageid")
                })
                
            return {
                "status": "success",
                "source": "Wikipedia",
                "mythology": mythology,
                "results_count": len(results),
                "results": results,
                "search_url": f"https://en.wikipedia.org/wiki/Category:{mythology}_mythology"
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm thần thoại: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "mythology": mythology
            }
            
    async def get_mythological_figures(self, mythology: str, limit: int = 10) -> Dict[str, Any]:
        """
        Lấy danh sách các nhân vật thần thoại từ một nền văn hóa cụ thể.

        Parameters:
        - mythology: Tên nền thần thoại (ví dụ: 'Greek', 'Norse', 'Hindu')
        - limit: Số lượng kết quả tối đa (mặc định: 10)

        Returns:
        - JSON chứa danh sách các nhân vật thần thoại
        """
        logger.info(f"Lấy danh sách nhân vật thần thoại: {mythology}")
        
        try:
            # Sử dụng Wikipedia API để lấy danh sách thể loại con
            category_url = "https://en.wikipedia.org/w/api.php"
            params = {
                "action": "query",
                "list": "categorymembers",
                "cmtitle": f"Category:{mythology}_deities",
                "cmlimit": limit,
                "format": "json"
            }
            
            response = requests.get(category_url, params=params, timeout=10)
            if response.status_code != 200:
                # Thử tìm kiếm thay thế nếu không tìm thấy thể loại chính xác
                return await self.search_by_mythology(mythology, limit)
                
            data = response.json()
            figures = []
            
            for member in data.get("query", {}).get("categorymembers", [])[:limit]:
                figures.append({
                    "title": member.get("title"),
                    "pageid": member.get("pageid"),
                    "url": f"https://en.wikipedia.org/?curid={member.get('pageid')}"
                })
                
            return {
                "status": "success",
                "source": "Wikipedia",
                "mythology": mythology,
                "figures_count": len(figures),
                "figures": figures,
                "category_url": f"https://en.wikipedia.org/wiki/Category:{mythology}_deities"
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy danh sách nhân vật thần thoại: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "mythology": mythology
            }
            
    async def compare_mythologies(self, myth1: str, myth2: str) -> Dict[str, Any]:
        """
        So sánh hai nền thần thoại khác nhau.

        Parameters:
        - myth1: Tên nền thần thoại thứ nhất
        - myth2: Tên nền thần thoại thứ hai

        Returns:
        - JSON chứa thông tin so sánh
        """
        logger.info(f"So sánh hai nền thần thoại: {myth1} và {myth2}")
        
        try:
            # Lấy thông tin cơ bản về hai nền thần thoại
            myth1_info = await self.search_wikipedia_mythology(f"{myth1} mythology")
            myth2_info = await self.search_wikipedia_mythology(f"{myth2} mythology")
            
            # Lấy danh sách các vị thần chính
            myth1_figures = await self.get_mythological_figures(myth1, 5)
            myth2_figures = await self.get_mythological_figures(myth2, 5)
            
            # Tìm các chủ đề tương đồng
            common_themes = []
            if myth1_info.get("status") == "success" and myth2_info.get("status") == "success":
                # Đơn giản hóa: tìm các từ khóa chung trong mô tả
                desc1 = myth1_info.get("summary", "").lower().split()
                desc2 = myth2_info.get("summary", "").lower().split()
                common = set(desc1) & set(desc2)
                # Lọc bỏ các từ thông dụng
                common_themes = [word for word in common if len(word) > 4 and word not in ["mythology", "deities", "gods", "goddesses"]]
            
            return {
                "status": "success",
                "source": "Wikipedia",
                "comparison": {
                    myth1: {
                        "title": myth1_info.get("title"),
                        "summary": myth1_info.get("summary"),
                        "main_figures": [f["title"] for f in myth1_figures.get("figures", [])]
                    },
                    myth2: {
                        "title": myth2_info.get("title"),
                        "summary": myth2_info.get("summary"),
                        "main_figures": [f["title"] for f in myth2_figures.get("figures", [])]
                    },
                    "common_themes": common_themes[:5],  # Giới hạn số lượng chủ đề
                    "comparison_urls": [
                        myth1_info.get("page_url"),
                        myth2_info.get("page_url")
                    ]
                }
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi so sánh thần thoại: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "mythologies": [myth1, myth2]
            }
