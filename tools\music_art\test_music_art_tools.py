#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho Music Art Tools
Kiểm tra functionality của tất cả tools trong music_art directory
"""

import sys
import os
import json
import random
import traceback
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_spotify_tools():
    """Test Spotify Tools functionality"""
    print("🎵 Testing Spotify Tools...")
    try:
        from music_art.spotify_tools import SpotifyTool
        
        spotify = SpotifyTool()
        
        # Test search_spotify
        print("  - Testing search_spotify...")
        result = spotify.search_spotify("Taylor Swift", "track", 3)
        data = json.loads(result)
        assert data["status"] == "success"
        assert len(data["results"]) == 3
        print("    ✅ search_spotify works")
        
        # Test get_top_new
        print("  - Testing get_top_new...")
        result = spotify.get_top_new("tracks", 5, "week", "pop")
        data = json.loads(result)
        assert data["status"] == "success"
        assert len(data["results"]) == 5
        print("    ✅ get_top_new works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Spotify Tools failed: {str(e)}")
        traceback.print_exc()
        return False

def test_pitchfork_tools():
    """Test Pitchfork Tools functionality"""
    print("🎤 Testing Pitchfork Tools...")
    try:
        from music_art.pitchfork_tools import PitchforkTool
        
        pitchfork = PitchforkTool()
        
        # Test search_pitchfork
        print("  - Testing search_pitchfork...")
        result = pitchfork.search_pitchfork("Radiohead", "reviews", 3)
        data = json.loads(result)
        assert data["status"] == "success"
        assert len(data["results"]) <= 3
        print("    ✅ search_pitchfork works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Pitchfork Tools failed: {str(e)}")
        traceback.print_exc()
        return False

def test_music_art_search_toolkit():
    """Test Music Art Search Toolkit functionality"""
    print("🔍 Testing Music Art Search Toolkit...")
    try:
        from music_art.music_art_search_toolkit import MusicArtSearchToolkit
        
        toolkit = MusicArtSearchToolkit()
        
        # Test search_music_content
        print("  - Testing search_music_content...")
        result = toolkit.search_music_content("jazz fusion", "all", "jazz", "contemporary")
        data = json.loads(result)
        assert "search_parameters" in data
        assert data["search_parameters"]["music_query"] == "jazz fusion"
        print("    ✅ search_music_content works")
        
        # Test search_art_content
        print("  - Testing search_art_content...")
        result = toolkit.search_art_content("abstract painting", "painting", "abstract", "contemporary")
        data = json.loads(result)
        assert "search_parameters" in data
        assert data["search_parameters"]["art_query"] == "abstract painting"
        print("    ✅ search_art_content works")
        
        # Test search_artists_creators
        print("  - Testing search_artists_creators...")
        result = toolkit.search_artists_creators("Banksy", "visual_artist", "established", "street_art")
        data = json.loads(result)
        assert "search_parameters" in data
        assert data["search_parameters"]["creator_name"] == "Banksy"
        print("    ✅ search_artists_creators works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Music Art Search Toolkit failed: {str(e)}")
        traceback.print_exc()
        return False

def test_music_art_analysis_calculator():
    """Test Music Art Analysis Calculator functionality"""
    print("🧮 Testing Music Art Analysis Calculator...")
    try:
        from music_art.music_art_analysis_calculator import MusicArtAnalysisCalculator
        
        calculator = MusicArtAnalysisCalculator()
        
        # Test analyze_music_metrics
        print("  - Testing analyze_music_metrics...")
        music_data = {"track_name": "Test Song", "artist": "Test Artist"}
        audio_features = {"tempo": 128.0, "energy": 0.8, "valence": 0.7}
        result = calculator.analyze_music_metrics(music_data, "comprehensive", audio_features, "electronic")
        data = json.loads(result)
        assert "analysis_parameters" in data
        assert data["analysis_parameters"]["track_name"] == "Test Song"
        print("    ✅ analyze_music_metrics works")
        
        # Test analyze_art_composition
        print("  - Testing analyze_art_composition...")
        artwork_data = {"title": "Test Artwork", "medium": "Oil on canvas"}
        composition_elements = {"balance": 8.0, "contrast": 7.5, "emphasis": 8.5}
        result = calculator.analyze_art_composition(artwork_data, composition_elements, "contemporary")
        data = json.loads(result)
        assert "analysis_parameters" in data
        assert data["analysis_parameters"]["artwork_title"] == "Test Artwork"
        print("    ✅ analyze_art_composition works")
        
        # Test assess_cultural_impact
        print("  - Testing assess_cultural_impact...")
        content_data = {"title": "Test Content", "type": "music"}
        impact_metrics = {"views": 5000000, "shares": 100000, "comments": 50000}
        result = calculator.assess_cultural_impact(content_data, impact_metrics, "current", "global")
        data = json.loads(result)
        assert "assessment_parameters" in data
        assert data["assessment_parameters"]["content_title"] == "Test Content"
        print("    ✅ assess_cultural_impact works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Music Art Analysis Calculator failed: {str(e)}")
        traceback.print_exc()
        return False

def test_music_art_analyzer():
    """Test Music Art Analyzer functionality"""
    print("📊 Testing Music Art Analyzer...")
    try:
        from music_art.music_art_analyzer import MusicArtAnalyzer
        
        analyzer = MusicArtAnalyzer()
        
        # Test analyze_music_trends
        print("  - Testing analyze_music_trends...")
        result = analyzer.analyze_music_trends("electronic", "year", "global", "popularity")
        data = json.loads(result)
        assert "analysis_parameters" in data
        assert data["analysis_parameters"]["music_genre"] == "electronic"
        print("    ✅ analyze_music_trends works")
        
        # Test analyze_art_movements
        print("  - Testing analyze_art_movements...")
        result = analyzer.analyze_art_movements("digital", "contemporary", "global", "comprehensive")
        data = json.loads(result)
        assert "analysis_parameters" in data
        assert data["analysis_parameters"]["art_medium"] == "digital"
        print("    ✅ analyze_art_movements works")
        
        # Test analyze_cultural_patterns
        print("  - Testing analyze_cultural_patterns...")
        result = analyzer.analyze_cultural_patterns("cross_domain", "global", "current", "innovation")
        data = json.loads(result)
        assert "analysis_parameters" in data
        assert data["analysis_parameters"]["pattern_type"] == "cross_domain"
        print("    ✅ analyze_cultural_patterns works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Music Art Analyzer failed: {str(e)}")
        traceback.print_exc()
        return False

def run_random_tests():
    """Run random tests on various tools"""
    print("\n🎲 Running Random Tests...")
    
    test_cases = [
        ("Spotify random search", lambda: test_random_spotify_search()),
        ("Search toolkit random query", lambda: test_random_search_query()),
        ("Calculator random metrics", lambda: test_random_calculator_metrics()),
        ("Analyzer random analysis", lambda: test_random_analyzer_analysis())
    ]
    
    # Randomly select and run 2-3 test cases
    selected_tests = random.sample(test_cases, min(3, len(test_cases)))
    
    for test_name, test_func in selected_tests:
        print(f"  🎯 {test_name}...")
        try:
            test_func()
            print(f"    ✅ {test_name} passed")
        except Exception as e:
            print(f"    ❌ {test_name} failed: {str(e)}")

def test_random_spotify_search():
    """Random Spotify search test"""
    from music_art.spotify_tools import SpotifyTool
    
    queries = ["Beatles", "Mozart", "Billie Eilish", "Drake", "Adele"]
    types = ["track", "artist", "album", "playlist"]
    
    spotify = SpotifyTool()
    query = random.choice(queries)
    search_type = random.choice(types)
    
    result = spotify.search_spotify(query, search_type, random.randint(1, 5))
    data = json.loads(result)
    assert data["status"] == "success"

def test_random_search_query():
    """Random search toolkit test"""
    from music_art.music_art_search_toolkit import MusicArtSearchToolkit
    
    music_queries = ["rock music", "classical symphony", "hip hop beats", "jazz improvisation"]
    art_queries = ["modern sculpture", "digital art", "oil painting", "street art"]
    
    toolkit = MusicArtSearchToolkit()
    
    if random.choice([True, False]):
        query = random.choice(music_queries)
        result = toolkit.search_music_content(query, "all", "", "contemporary")
    else:
        query = random.choice(art_queries)
        result = toolkit.search_art_content(query, "all", "", "contemporary")
    
    data = json.loads(result)
    assert "search_parameters" in data

def test_random_calculator_metrics():
    """Random calculator test"""
    from music_art.music_art_analysis_calculator import MusicArtAnalysisCalculator
    
    calculator = MusicArtAnalysisCalculator()
    
    if random.choice([True, False]):
        # Test music metrics
        music_data = {"track_name": f"Random Song {random.randint(1, 100)}", "artist": "Random Artist"}
        result = calculator.analyze_music_metrics(music_data, "comprehensive", None, "pop")
    else:
        # Test art composition
        artwork_data = {"title": f"Random Art {random.randint(1, 100)}", "medium": "Mixed media"}
        result = calculator.analyze_art_composition(artwork_data, None, "contemporary")
    
    data = json.loads(result)
    assert "analysis_parameters" in data

def test_random_analyzer_analysis():
    """Random analyzer test"""
    from music_art.music_art_analyzer import MusicArtAnalyzer
    
    analyzer = MusicArtAnalyzer()
    
    genres = ["pop", "rock", "electronic", "jazz", "classical"]
    mediums = ["painting", "sculpture", "digital", "photography"]
    
    if random.choice([True, False]):
        genre = random.choice(genres)
        result = analyzer.analyze_music_trends(genre, "year", "global", "popularity")
    else:
        medium = random.choice(mediums)
        result = analyzer.analyze_art_movements(medium, "contemporary", "global", "comprehensive")
    
    data = json.loads(result)
    assert "analysis_parameters" in data

def main():
    """Main test function"""
    print("🎨🎵 MUSIC ART TOOLS TEST SUITE")
    print("=" * 50)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_results = []
    
    # Test individual tools
    test_functions = [
        ("Spotify Tools", test_spotify_tools),
        ("Pitchfork Tools", test_pitchfork_tools),
        ("Music Art Search Toolkit", test_music_art_search_toolkit),
        ("Music Art Analysis Calculator", test_music_art_analysis_calculator),
        ("Music Art Analyzer", test_music_art_analyzer)
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()
    
    # Run random tests
    run_random_tests()
    
    # Summary
    print("\n" + "="*50)
    print("📋 TEST SUMMARY")
    print("="*50)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Music Art Tools are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
