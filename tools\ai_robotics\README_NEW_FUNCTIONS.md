# AI Robotics Tools - New Architecture & "Get Top New" Functions

Tài liệu này mô tả việc chia nhỏ AI Robotics tools và các hàm mới được thêm vào để lấy nội dung trending/mới nhất.

## Tổng quan

Đã chia nhỏ từ 1 file lớn thành 6 tool riêng biệt:

1. **Papers With Code Tools** - `papers_with_code_tools.py`
2. **Hugging Face Tools** - `huggingface_tools.py`
3. **arXiv Tools** - `arxiv_tools.py`
4. **OpenML Tools** - `openml_tools.py`
5. **Wikipedia AI Tools** - `wikipedia_ai_tools.py`
6. **AI Robotics Search Toolkit** - `ai_robotics_search_toolkit.py` (mới)

## Chi tiết các tool và hàm mới

### 1. Papers With Code Tools

#### Hàm hiện có:
- `search_papers_with_code(query, limit=10)`

#### Hàm mới:
- `get_trending_papers(limit=10, time_period="week")` - Lấy papers trending
- `get_top_new_papers(limit=10, days_back=30, category=None)` - Lấy papers mới nhất

**Ví dụ sử dụng:**
```python
from tools.ai_robotics.papers_with_code_tools import PapersWithCodeTools

tool = PapersWithCodeTools()
trending = tool.get_trending_papers(5, "week")
new_papers = tool.get_top_new_papers(5, 30, "computer-vision")
```

### 2. Hugging Face Tools

#### Hàm hiện có:
- `search_huggingface_hub(query, model_type=None, limit=10)`

#### Hàm mới:
- `get_trending_models(limit=10, task=None, time_period="week")` - Lấy models trending
- `get_recent_models(limit=10, days_back=30, task=None)` - Lấy models mới

**Ví dụ sử dụng:**
```python
from tools.ai_robotics.huggingface_tools import HuggingFaceTools

tool = HuggingFaceTools()
trending = tool.get_trending_models(5, "text-classification")
recent = tool.get_recent_models(5, 30, "image-classification")
```

### 3. arXiv Tools

#### Hàm hiện có:
- `search_arxiv(query, max_results=10, categories=None)`

#### Hàm mới:
- `get_recent_papers(limit=10, days_back=7, categories=None)` - Lấy papers mới nhất
- `get_top_cited_papers(limit=10, category="cs.AI", time_period="year")` - Lấy papers được cite nhiều

**Ví dụ sử dụng:**
```python
from tools.ai_robotics.arxiv_tools import ArxivTools

tool = ArxivTools()
recent = tool.get_recent_papers(5, 7, ["cs.AI", "cs.LG"])
top_cited = tool.get_top_cited_papers(5, "cs.AI")
```

### 4. OpenML Tools

#### Hàm hiện có:
- `search_openml(query, query_type="data", limit=10)`

#### Hàm mới:
- `get_recent_datasets(limit=10, days_back=30)` - Lấy datasets mới
- `get_popular_datasets(limit=10, category=None)` - Lấy datasets phổ biến

**Ví dụ sử dụng:**
```python
from tools.ai_robotics.openml_tools import OpenMLTools

tool = OpenMLTools()
recent = tool.get_recent_datasets(5, 30)
popular = tool.get_popular_datasets(5, "classification")
```

### 5. Wikipedia AI Tools

#### Hàm hiện có:
- `search_wikipedia_ai(query, limit=5)`

#### Hàm mới:
- `get_recent_ai_articles(limit=10, days_back=30, language="en")` - Lấy bài viết AI mới

**Ví dụ sử dụng:**
```python
from tools.ai_robotics.wikipedia_ai_tools import WikipediaAITools

tool = WikipediaAITools()
recent = tool.get_recent_ai_articles(5, 30, "en")
```

### 6. AI Robotics Search Toolkit (Mới)

Tool chia sub question tương tự astronomy search toolkit.

#### Hàm tạo từ khóa thông thường:
- `generate_papers_with_code_keywords(task, model_type=None)`
- `generate_huggingface_keywords(model_type, query=None)`
- `generate_arxiv_keywords(categories, topic=None)`
- `generate_openml_keywords(query_type, category=None)`
- `generate_wikipedia_ai_keywords(concept)`

#### Hàm tạo từ khóa cho trending/recent:
- `generate_papers_with_code_trending_keywords(category=None, time_period="week")`
- `generate_huggingface_trending_keywords(task=None, time_period="week")`
- `generate_arxiv_recent_keywords(categories=None, days_back=7)`
- `generate_openml_recent_keywords(category=None, days_back=30)`
- `generate_wikipedia_ai_recent_keywords(days_back=30, language="en")`

**Ví dụ sử dụng:**
```python
from tools.ai_robotics.ai_robotics_search_toolkit import AIRoboticsSearchToolkit

toolkit = AIRoboticsSearchToolkit()
keywords = toolkit.generate_papers_with_code_trending_keywords("nlp", "week")
```

## Tính năng chung

### Fallback Data
Tất cả các hàm đều có cơ chế fallback data khi API không khả dụng:
- Papers With Code: Dữ liệu mẫu với thông tin trending/recent
- Hugging Face: Models mẫu với metadata phù hợp
- arXiv: Papers mẫu với categories và thời gian
- OpenML: Datasets mẫu với popularity scores
- Wikipedia AI: Bài viết AI mẫu với relevance scores

### Caching
Các tool đều sử dụng cache để tránh gọi API quá nhiều lần với TTL khác nhau:
- Regular search: Cache lâu hơn
- Trending/Recent: Cache ngắn hơn (1 giờ)

### Error Handling
- Comprehensive retry mechanism với exponential backoff
- Detailed logging cho debugging
- Graceful degradation với fallback data

### Authentication
- Secure token management
- Environment variable support
- Fallback tokens cho testing

## Cấu trúc thư mục mới

```
tools/ai_robotics/
├── __init__.py (mới)
├── ai_robotics_tools.py (giữ lại cho backward compatibility)
├── ai_robotics_search_toolkit.py (mới)
├── papers_with_code_tools.py (mới)
├── huggingface_tools.py (mới)
├── arxiv_tools.py (mới)
├── openml_tools.py (mới)
├── wikipedia_ai_tools.py (mới)
├── test_new_functions.py (mới)
└── README_NEW_FUNCTIONS.md (mới)
```

## Test

Chạy file test để kiểm tra tất cả các hàm:
```bash
cd tools/ai_robotics
python test_new_functions.py
```

## Backward Compatibility

File `ai_robotics_tools.py` gốc vẫn được giữ lại để đảm bảo backward compatibility. Tuy nhiên, khuyến khích sử dụng các tool mới để có hiệu suất và tính năng tốt hơn.

## Migration Guide

### Từ tool cũ sang tool mới:

**Cũ:**
```python
from tools.ai_robotics.ai_robotics_tools import AIRoboticsTools
tool = AIRoboticsTools()
result = tool.search_papers_with_code("transformer")
```

**Mới:**
```python
from tools.ai_robotics.papers_with_code_tools import PapersWithCodeTools
tool = PapersWithCodeTools()
result = tool.search_papers_with_code("transformer")
# Thêm trending/recent
trending = tool.get_trending_papers(5, "week")
```

## Lợi ích của kiến trúc mới

1. **Modularity**: Mỗi tool độc lập, dễ maintain
2. **Scalability**: Dễ thêm tool mới hoặc mở rộng existing tools
3. **Performance**: Cache riêng biệt cho từng tool
4. **Testing**: Test riêng biệt cho từng component
5. **New Features**: Trending/recent content cho tất cả tools
6. **Search Toolkit**: Intelligent keyword generation cho complex queries

Kiến trúc mới này cung cấp foundation mạnh mẽ cho AI research tools với khả năng mở rộng cao và user experience tốt hơn.
