#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive Test Script cho tất cả Enhanced Tools
Test từ game_esports đến music_art để đảm bả<PERSON> không có regression
"""

import sys
import os
import json
import random
import traceback
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_game_esports_tools():
    """Test Game Esports Tools"""
    print("🎮 Testing Game Esports Tools...")
    try:
        # Test a few key tools from gameesports
        from tools.gameesports.gameesports_search_toolkit import GameEsportsSearchToolkit

        toolkit = GameEsportsSearchToolkit()

        # Test basic functionality
        print("  - Testing GameEsports Search Toolkit...")
        result = toolkit.comprehensive_esports_search("Counter-Strike", "CS2", "all")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ GameEsports Search Toolkit works")

        return True

    except Exception as e:
        print(f"    ❌ Game Esports Tools failed: {str(e)}")
        return False

def test_game_lore_tools():
    """Test Game Lore Tools"""
    print("📚 Testing Game Lore Tools...")
    try:
        # Test game lore tools
        from tools.game_lore.game_lore_search_toolkit import GameLoreSearchToolkit

        toolkit = GameLoreSearchToolkit()

        print("  - Testing Game Lore Search Toolkit...")
        result = toolkit.comprehensive_lore_search("Final Fantasy", "Final Fantasy", "all")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Game Lore Search Toolkit works")

        return True

    except Exception as e:
        print(f"    ❌ Game Lore Tools failed: {str(e)}")
        return False

def test_modern_agriculture_tools():
    """Test Modern Agriculture Tools"""
    print("🌾 Testing Modern Agriculture Tools...")
    try:
        # Test agriculture tools
        from tools.modern_agriculture.agriculture_search_toolkit import AgricultureSearchToolkit

        toolkit = AgricultureSearchToolkit()

        print("  - Testing Agriculture Search Toolkit...")
        result = toolkit.search_crop_information("rice", "all", "asia", "all")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Agriculture Search Toolkit works")

        return True

    except Exception as e:
        print(f"    ❌ Modern Agriculture Tools failed: {str(e)}")
        return False

def test_music_art_tools():
    """Test Music Art Tools"""
    print("🎨 Testing Music Art Tools...")
    try:
        # Test music art tools
        from tools.music_art.spotify_tools import SpotifyTool
        from tools.music_art.bandcamp_tools import BandcampTool

        spotify = SpotifyTool()
        bandcamp = BandcampTool()

        print("  - Testing Spotify Tools...")
        result = spotify.search_spotify("Beatles", "track", 3)
        data = json.loads(result)
        assert data["status"] == "success"
        print("    ✅ Spotify Tools work")

        print("  - Testing Bandcamp Tools...")
        result = bandcamp.search_bandcamp("indie", "music", 3)
        data = json.loads(result)
        assert data["status"] == "success"
        print("    ✅ Bandcamp Tools work")

        # Test get_top_new functionality
        print("  - Testing get_top_new functionality...")
        result = spotify.get_top_new("tracks", 5, "week", "pop")
        data = json.loads(result)
        assert data["status"] == "success"
        print("    ✅ get_top_new works")

        result = bandcamp.get_top_new("music", 5, "week", "indie")
        data = json.loads(result)
        assert data["status"] == "success"
        print("    ✅ Bandcamp get_top_new works")

        return True

    except Exception as e:
        print(f"    ❌ Music Art Tools failed: {str(e)}")
        traceback.print_exc()
        return False

def test_enhanced_toolkits():
    """Test Enhanced Toolkits (Search, Calculator, Analyzer)"""
    print("🔧 Testing Enhanced Toolkits...")
    try:
        # Test various enhanced toolkits
        from tools.music_art.music_art_search_toolkit import MusicArtSearchToolkit
        from tools.music_art.music_art_analysis_calculator import MusicArtAnalysisCalculator

        search_toolkit = MusicArtSearchToolkit()
        calculator = MusicArtAnalysisCalculator()

        print("  - Testing Music Art Search Toolkit...")
        result = search_toolkit.search_music_content("jazz", "all", "jazz", "contemporary")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Music Art Search Toolkit works")

        print("  - Testing Music Art Analysis Calculator...")
        music_data = {"track_name": "Test Song", "artist": "Test Artist"}
        result = calculator.analyze_music_metrics(music_data, "comprehensive", None, "pop")
        data = json.loads(result)
        assert "analysis_parameters" in data
        print("    ✅ Music Art Analysis Calculator works")

        return True

    except Exception as e:
        print(f"    ❌ Enhanced Toolkits failed: {str(e)}")
        traceback.print_exc()
        return False

def run_random_cross_channel_tests():
    """Run random tests across different channels"""
    print("\n🎲 Running Random Cross-Channel Tests...")

    test_cases = [
        ("Game Esports random test", lambda: test_random_game_esports()),
        ("Agriculture random test", lambda: test_random_agriculture()),
        ("Music Art random test", lambda: test_random_music_art()),
        ("Cross-toolkit test", lambda: test_cross_toolkit_functionality())
    ]

    # Randomly select and run 2-3 test cases
    selected_tests = random.sample(test_cases, min(3, len(test_cases)))

    for test_name, test_func in selected_tests:
        print(f"  🎯 {test_name}...")
        try:
            test_func()
            print(f"    ✅ {test_name} passed")
        except Exception as e:
            print(f"    ❌ {test_name} failed: {str(e)}")

def test_random_game_esports():
    """Random game esports test"""
    from tools.gameesports.gameesports_search_toolkit import GameEsportsSearchToolkit

    games = ["Dota 2", "CS2", "Valorant", "League of Legends"]
    content_types = ["tournaments", "teams", "players", "matches"]

    toolkit = GameEsportsSearchToolkit()
    game = random.choice(games)
    content_type = random.choice(content_types)

    result = toolkit.comprehensive_esports_search(game, game, "all")
    data = json.loads(result)
    assert "search_parameters" in data

def test_random_agriculture():
    """Random agriculture test"""
    from tools.modern_agriculture.agriculture_search_toolkit import AgricultureSearchToolkit

    crops = ["wheat", "corn", "rice", "soybeans"]
    regions = ["global", "asia", "africa", "americas"]

    toolkit = AgricultureSearchToolkit()
    crop = random.choice(crops)
    region = random.choice(regions)

    result = toolkit.search_crop_information(crop, "all", region, "all")
    data = json.loads(result)
    assert "search_parameters" in data

def test_random_music_art():
    """Random music art test"""
    from tools.music_art.spotify_tools import SpotifyTool

    queries = ["rock", "jazz", "electronic", "classical"]
    types = ["track", "artist", "album"]

    spotify = SpotifyTool()
    query = random.choice(queries)
    search_type = random.choice(types)

    result = spotify.search_spotify(query, search_type, random.randint(1, 5))
    data = json.loads(result)
    assert data["status"] == "success"

def test_cross_toolkit_functionality():
    """Test functionality across different toolkits"""
    from tools.music_art.music_art_search_toolkit import MusicArtSearchToolkit

    toolkit = MusicArtSearchToolkit()

    # Test different search methods
    result1 = toolkit.search_music_content("classical", "all", "classical", "contemporary")
    result2 = toolkit.search_art_content("abstract", "painting", "abstract", "contemporary")

    data1 = json.loads(result1)
    data2 = json.loads(result2)

    assert "search_parameters" in data1
    assert "search_parameters" in data2

def main():
    """Main test function"""
    print("🚀 COMPREHENSIVE ENHANCED TOOLS TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing from game_esports to music_art to ensure no regression...")
    print()

    test_results = []

    # Test all channels in order
    test_functions = [
        ("Game Esports Tools", test_game_esports_tools),
        ("Game Lore Tools", test_game_lore_tools),
        ("Modern Agriculture Tools", test_modern_agriculture_tools),
        ("Music Art Tools", test_music_art_tools),
        ("Enhanced Toolkits", test_enhanced_toolkits)
    ]

    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()

    # Run random cross-channel tests
    run_random_cross_channel_tests()

    # Summary
    print("\n" + "="*60)
    print("📋 COMPREHENSIVE TEST SUMMARY")
    print("="*60)

    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")

    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 All enhanced tools are working correctly!")
        print("✨ No regression detected across all channels!")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")

    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
