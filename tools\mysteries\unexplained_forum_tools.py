from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
import time
from urllib.parse import quote_plus, urljoin

class UnexplainedForumTools(Toolkit):
    """
    Công cụ tìm kiếm và tương tác với diễn đàn Unexplained Mysteries.
    
    Công cụ này cung cấp khả năng tìm kiếm và đọc các chủ đề thảo luận 
    về các hiện tượ<PERSON> b<PERSON>, chưa giải thích được trên diễn đàn Unexplained Mysteries.
    
    Các từ khóa tìm kiếm phổ biến:
    - "UFO sightings"
    - "Ghost encounters"
    - "Paranormal experiences"
    - "Conspiracy theories"
    - "Cryptid sightings"
    """
    
    def __init__(self):
        super().__init__(
            name="unexplained_forum_tools",
            description="<PERSON>ông cụ tìm kiếm và tương tác với diễn đàn Unexplained Mysteries.",
            tools=[
                self.search_forum,
                self.get_popular_threads,
                self.get_forum_categories
            ]
        )
        self.base_url = "https://www.unexplained-mysteries.com/forum/"
        self.api_url = "https://www.unexplained-mysteries.com/api/v1/"
        
        # Tạo session để duy trì kết nối
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'MysteryResearchTool/1.0',
            'Accept': 'application/json'
        })

    def search_forum(self, query: str, limit: int = 5, category: str = None) -> str:
        """
        Tìm kiếm các chủ đề thảo luận trên diễn đàn Unexplained Mysteries.
        
        Args:
            query (str): Từ khóa tìm kiếm
            limit (int, optional): Số lượng kết quả trả về. Mặc định là 5.
            category (str, optional): Danh mục cụ thể để tìm kiếm (ví dụ: "ufos", "ghosts").
            
        Returns:
            str: Kết quả tìm kiếm dưới dạng JSON
            
        Example:
            >>> tool = UnexplainedForumTools()
            >>> result = tool.search_forum("UFO sighting", limit=3)
            >>> print(json.loads(result)['results'][0]['title'])
            'My UFO sighting experience last night'
        """
        logger.info(f"Searching Unexplained Mysteries Forum for: {query}")
        
        try:
            params = {
                'q': query,
                'limit': min(limit, 20),  # Giới hạn tối đa 20 kết quả
                'category': category if category else ''
            }
            
            # Thêm thời gian chờ giữa các yêu cầu để tránh bị chặn
            time.sleep(1)
            
            # Gửi yêu cầu tìm kiếm
            response = self.session.get(
                urljoin(self.api_url, 'search'),
                params=params,
                timeout=15
            )
            response.raise_for_status()
            
            # Xử lý kết quả
            data = response.json()
            results = []
            
            for thread in data.get('threads', [])[:limit]:
                results.append({
                    'title': thread.get('title', 'No title'),
                    'url': urljoin(self.base_url, f"topic/{thread.get('id', '')}/"),
                    'author': thread.get('author', {}).get('name', 'Anonymous'),
                    'post_count': thread.get('post_count', 0),
                    'last_post_date': thread.get('last_post_date', ''),
                    'category': thread.get('category', {}).get('name', 'Uncategorized')
                })
            
            return json.dumps({
                'status': 'success',
                'query': query,
                'total_results': len(results),
                'results': results,
                'timestamp': datetime.now().isoformat()
            }, indent=2, ensure_ascii=False)
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Lỗi kết nối đến diễn đàn: {str(e)}"
            logger.error(error_msg)
            return json.dumps({
                'status': 'error',
                'message': error_msg,
                'query': query,
                'results': []
            }, indent=2)
            
        except Exception as e:
            error_msg = f"Lỗi khi xử lý kết quả tìm kiếm: {str(e)}"
            logger.error(error_msg)
            return json.dumps({
                'status': 'error',
                'message': error_msg,
                'query': query,
                'results': []
            }, indent=2)
    
    def get_popular_threads(self, days: int = 7, limit: int = 5) -> str:
        """
        Lấy danh sách các chủ đề phổ biến trong một khoảng thời gian.
        
        Args:
            days (int, optional): Số ngày gần đây. Mặc định là 7.
            limit (int, optional): Số lượng kết quả trả về. Mặc định là 5.
            
        Returns:
            str: Danh sách chủ đề phổ biến dưới dạng JSON
        """
        try:
            response = self.session.get(
                urljoin(self.api_url, 'popular-threads'),
                params={
                    'days': max(1, min(days, 30)),  # Giới hạn từ 1-30 ngày
                    'limit': min(limit, 20)  # Giới hạn tối đa 20 kết quả
                },
                timeout=10
            )
            response.raise_for_status()
            
            data = response.json()
            return json.dumps({
                'status': 'success',
                'days': days,
                'results': data.get('threads', [])[:limit],
                'timestamp': datetime.now().isoformat()
            }, indent=2, ensure_ascii=False)
            
        except Exception as e:
            error_msg = f"Không thể lấy danh sách chủ đề phổ biến: {str(e)}"
            logger.error(error_msg)
            return json.dumps({
                'status': 'error',
                'message': error_msg,
                'results': []
            }, indent=2)
    
    def get_forum_categories(self) -> str:
        """
        Lấy danh sách các danh mục có sẵn trên diễn đàn.
        
        Returns:
            str: Danh sách danh mục dưới dạng JSON
        """
        try:
            response = self.session.get(
                urljoin(self.api_url, 'categories'),
                timeout=10
            )
            response.raise_for_status()
            
            data = response.json()
            return json.dumps({
                'status': 'success',
                'categories': data.get('categories', []),
                'timestamp': datetime.now().isoformat()
            }, indent=2, ensure_ascii=False)
            
        except Exception as e:
            error_msg = f"Không thể lấy danh sách danh mục: {str(e)}"
            logger.error(error_msg)
            return json.dumps({
                'status': 'error',
                'message': error_msg,
                'categories': []
            }, indent=2)
