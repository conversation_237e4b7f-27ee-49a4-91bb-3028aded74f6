from typing import Dict, Any, List, Optional, Union
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime, timedelta
import random

class SteamDBTool(Toolkit):
    """
    Công cụ tìm kiếm SteamDB để tra cứu thông tin game, lịch sử giá, thống kê người chơi và xu hướng trên Steam.
    
    Các từ khóa tìm kiếm gợi ý:
    - Thông tin game (game info, game details)
    - <PERSON><PERSON>ch sử giá (price history, price drops)
    - Thống kê người chơi (player count, concurrent players)
    - Game đang giảm giá (current sales, discounts)
    - Game miễn phí (free games, free to play)
    - Game sắp phát hành (upcoming releases)
    - Gói DLC và nội dung tải về (DLCs, downloadable content)
    """
    
    def __init__(self):
        super().__init__(
            name="<PERSON>ông cụ tìm kiếm SteamDB",
            tools=[self.search_steamdb]
        )
        self.base_url = "https://steamdb.info/api"
        self.search_types = ["games", "demos", "dlc", "software", "subs", "bundles"]
        self.regions = ["us", "eu", "uk", "ru", "br", "vn", "jp", "kr", "all"]
        self.currencies = ["usd", "eur", "gbp", "rub", "brl", "vnd", "jpy", "krw"]
        self.time_ranges = ["24h", "7d", "30d", "90d", "1y", "all"]

    def search_steamdb(self, query: str, search_type: str = "games", region: str = "all", 
                      currency: str = "usd", limit: int = 5) -> str:
        """
        Tìm kiếm thông tin game và dữ liệu thống kê trên SteamDB.
        
        Args:
            query: Từ khóa tìm kiếm (tên game, appid, v.v.)
            search_type: Loại nội dung (games, demos, dlc, software, subs, bundles)
            region: Khu vực (us, eu, uk, ru, br, vn, jp, kr, all)
            currency: Đơn vị tiền tệ (usd, eur, gbp, rub, brl, vnd, jpy, krw)
            limit: Số lượng kết quả trả về (tối đa 10)
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm SteamDB với từ khóa: {query}, loại: {search_type}")
        
        # Xác thực tham số
        if search_type not in self.search_types:
            search_type = "games"
        if region not in self.regions:
            region = "all"
        if currency not in self.currencies:
            currency = "usd"
            
        limit = max(1, min(limit, 10))  # Giới hạn trong khoảng 1-10
        
        try:
            # Giả lập kết quả tìm kiếm
            results = []
            current_time = datetime.utcnow()
            
            # Tạo dữ liệu ngẫu nhiên
            def random_price(min_val: float = 5.0, max_val: float = 99.99) -> float:
                return round(random.uniform(min_val, max_val), 2)
                
            def random_discount() -> int:
                return random.choice([0, 10, 20, 30, 40, 50, 60, 70, 80, 90])
                
            def random_players() -> int:
                return random.randint(1000, 500000)
                
            if search_type == "games":
                results.extend([
                    {
                        "appid": 100000 + i,
                        "name": f"{query} {i+1}",
                        "type": "game",
                        "release_date": (current_time - timedelta(days=random.randint(0, 365*5))).strftime("%Y-%m-%d"),
                        "price": {
                            "original": random_price(10, 59.99),
                            "discount": random_discount(),
                            "final": random_price(5, 29.99),
                            "currency": currency.upper()
                        },
                        "rating": round(random.uniform(60, 100), 1),
                        "players": {
                            "current": random_players(),
                            "peak_today": random_players(),
                            "all_time_peak": random_players()
                        },
                        "url": f"https://steamdb.info/app/{100000 + i}/",
                        "store_url": f"https://store.steampowered.com/app/{100000 + i}"
                    } for i in range(limit)
                ])
                
            elif search_type == "demos":
                results.extend([
                    {
                        "appid": 200000 + i,
                        "name": f"{query} Demo {i+1}",
                        "type": "demo",
                        "base_game": f"{query} {i+1}",
                        "release_date": (current_time - timedelta(days=random.randint(0, 30))).strftime("%Y-%m-%d"),
                        "size_gb": round(random.uniform(2, 20), 1),
                        "players_last_2_weeks": random.randint(1000, 50000),
                        "url": f"https://steamdb.info/app/{200000 + i}/",
                        "store_url": f"https://store.steampowered.com/app/{200000 + i}"
                    } for i in range(min(limit, 5))
                ])
                
            elif search_type == "dlc":
                results.extend([
                    {
                        "appid": 300000 + i,
                        "name": f"{query} DLC {i+1}",
                        "type": "dlc",
                        "base_game": f"{query} {i+1}",
                        "release_date": (current_time - timedelta(days=random.randint(0, 365))).strftime("%Y-%m-%d"),
                        "price": {
                            "original": random_price(5, 39.99),
                            "discount": random_discount(),
                            "final": random_price(3, 19.99),
                            "currency": currency.upper()
                        },
                        "owners_estimate": f"{random.randint(1, 10)}M ~ {random.randint(10, 50)}M",
                        "url": f"https://steamdb.info/app/{300000 + i}/",
                        "store_url": f"https://store.steampowered.com/app/{300000 + i}"
                    } for i in range(min(limit, 4))
                ])
            
            # Thêm thông tin chi tiết cho kết quả đầu tiên nếu có
            if results and search_type == "games":
                results[0]["detailed_info"] = {
                    "developers": [f"{query} Studios"],
                    "publishers": ["Game Publisher Inc."],
                    "genres": ["Action", "Adventure", "RPG"],
                    "tags": ["Open World", "RPG", "Adventure", "Singleplayer", "Story Rich"],
                    "languages": ["English", "Japanese", "Korean", "Russian", "Portuguese"],
                    "achievements": random.randint(10, 100),
                    "trading_cards": random.choice([True, False]),
                    "controller_support": random.choice(["Full", "Partial", "None"]),
                    "price_history": [
                        {"date": (current_time - timedelta(days=30)).strftime("%Y-%m-%d"), "price": 59.99, "discount": 0},
                        {"date": (current_time - timedelta(days=15)).strftime("%Y-%m-%d"), "price": 29.99, "discount": 50},
                        {"date": current_time.strftime("%Y-%m-%d"), "price": 19.99, "discount": 67}
                    ],
                    "player_stats": [
                        {"date": (current_time - timedelta(days=2)).strftime("%Y-%m-%d"), "players": random_players()},
                        {"date": (current_time - timedelta(days=1)).strftime("%Y-%m-%d"), "players": random_players()},
                        {"date": current_time.strftime("%Y-%m-%d"), "players": random_players()}
                    ]
                }
            
            result = {
                "status": "success",
                "source": "SteamDB",
                "query": query,
                "search_type": search_type,
                "region": region,
                "currency": currency.upper(),
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm SteamDB: {str(e)}")
            result = {
                "status": "error",
                "source": "SteamDB",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm {query} trên SteamDB",
                        "url": f"https://steamdb.info/search/?a=app&q={query}",
                        "summary": f"Tìm kiếm thông tin chi tiết về {query} trên SteamDB"
                    },
                    {
                        "title": f"Lịch sử giá {query}",
                        "url": f"https://steamdb.info/search/?a=app&q={query}&type=price",
                        "summary": f"Xem lịch sử giá và các đợt giảm giá của {query}"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
