from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class SIPRITool(Toolkit):
    """
    SIPRI Tool cho tìm kiếm dữ liệu chuyển giao vũ khí, ngân sách quốc phòng, và các báo cáo từ SIPRI.
    """

    def __init__(self):
        super().__init__(
            name="SIPRI Arms Transfers & Defense Data Tool",
            description="Tool cho tìm kiếm dữ liệu chuyển giao vũ khí, ngân sách quốc phòng, và báo cáo từ SIPRI.",
            tools=[self.search_sipri]
        )

    async def search_sipri(self, query: str, country: Optional[str] = None, year_range: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm dữ liệu SIPRI về chuyển giao vũ khí, ngân sách quốc <PERSON>hò<PERSON>, và các báo cáo.

        Parameters:
        - query: Từ khóa tìm kiếm (ví dụ: 'arms exports', 'defense budget', 'arms imports')
        - country: Tên quốc gia (ví dụ: 'Russia', 'Vietnam')
        - year_range: Khoảng năm (ví dụ: '2018-2023')
        - limit: Số lượng kết quả tối đa (default: 10)

        Returns:
        - JSON với dữ liệu chuyển giao vũ khí, ngân sách quốc phòng, và liên kết đến báo cáo SIPRI
        """
        logger.info(f"Tìm kiếm SIPRI: query={query}, country={country}, year_range={year_range}")

        try:
            # SIPRI không có public API chính thức, sử dụng endpoint dữ liệu mở hoặc web scraping đơn giản
            # Ví dụ: https://sipri.org/databases/armstransfers
            # Sử dụng dữ liệu CSV mở của SIPRI (nếu có)
            base_url = "https://sipri.org/sites/default/files/armstransfers/trends"
            results = []

            # Gợi ý: Nếu country hoặc year_range được cung cấp, tạo URL hoặc filter phù hợp
            # Ở đây chỉ mô phỏng truy vấn, thực tế cần tải file CSV về và phân tích (hoặc crawl HTML)
            # Để minh họa, trả về cấu trúc mẫu

            # Tối ưu hóa keyword: 
            # - arms exports/imports <country> <year_range>
            # - defense budget <country> <year_range>
            # - arms transfers <country> <year_range>
            # - arms production <country>
            # - military expenditure <country> <year_range>
            # - arms register <country> <year_range>

            # Mô phỏng kết quả
            for i in range(min(limit, 5)):
                results.append({
                    "type": "arms_transfer" if "arms" in query.lower() else "defense_budget",
                    "country": country or "Global",
                    "year_range": year_range or "latest",
                    "summary": f"Sample data for {query} in {country or 'all countries'} ({year_range or 'latest'})",
                    "sipri_report_url": "https://sipri.org/databases/armstransfers"
                })

            return {
                "status": "success",
                "source": "SIPRI",
                "query": query,
                "country": country,
                "year_range": year_range,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "arms exports <country> <year_range>",
                    "arms imports <country> <year_range>",
                    "defense budget <country> <year_range>",
                    "military expenditure <country> <year_range>",
                    "arms production <country>",
                    "arms register <country> <year_range>"
                ],
                "official_data_url": "https://sipri.org/databases/armstransfers"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm SIPRI: {str(e)}")
            # Trả về mẫu nếu lỗi
            return {
                "status": "error",
                "source": "SIPRI",
                "message": str(e),
                "query": query,
                "results": [
                    {"title": "Arms Exports 2023", "url": "https://sipri.org/databases/armstransfers", "summary": "SIPRI data on global arms exports in 2023."},
                    {"title": "Defense Budget Vietnam 2022", "url": "https://sipri.org/databases/milex", "summary": "Vietnam's defense budget data from SIPRI."}
                ]
            }
