#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive Test Script cho 3 kênh còn lại:
- Literature
- Military History
- Medical Science
"""

import sys
import os
import json
import random
import traceback
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_literature_tools():
    """Test Literature Tools"""
    print("📚 Testing Literature Tools...")
    try:
        # Test literature search toolkit
        from tools.literature.literature_search_toolkit import LiteratureSearchToolkit

        toolkit = LiteratureSearchToolkit()

        print("  - Testing Literature Search Toolkit...")
        result = toolkit.search_literary_works("Shakespeare", "all", "drama", "comprehensive")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Literature Search Toolkit works")

        # Test literature analysis calculator
        from tools.literature.literature_analysis_calculator import LiteratureAnalysisCalculator

        calculator = LiteratureAnalysisCalculator()

        print("  - Testing Literature Analysis Calculator...")
        text_data = {"title": "Hamlet", "author": "Shakespeare", "text": "To be or not to be..."}
        result = calculator.analyze_literary_style(text_data, "comprehensive", None, "drama")
        data = json.loads(result)
        assert "analysis_parameters" in data
        print("    ✅ Literature Analysis Calculator works")

        # Test literature analyzer
        from tools.literature.literature_analyzer import LiteratureAnalyzer

        analyzer = LiteratureAnalyzer()

        print("  - Testing Literature Analyzer...")
        result = analyzer.analyze_literary_movements("Romanticism", "19th century", "poetry", "comprehensive")
        data = json.loads(result)
        assert "analysis_parameters" in data
        print("    ✅ Literature Analyzer works")

        return True

    except Exception as e:
        print(f"    ❌ Literature Tools failed: {str(e)}")
        traceback.print_exc()
        return False

def test_military_history_tools():
    """Test Military History Tools"""
    print("⚔️ Testing Military History Tools...")
    try:
        # Test military search toolkit
        from tools.military_history.military_search_toolkit import MilitarySearchToolkit

        toolkit = MilitarySearchToolkit()

        print("  - Testing Military Search Toolkit...")
        result = toolkit.search_military_conflicts("World War II", "all", "global", "comprehensive")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Military Search Toolkit works")

        # Test military analysis calculator
        from tools.military_history.military_analysis_calculator import MilitaryAnalysisCalculator

        calculator = MilitaryAnalysisCalculator()

        print("  - Testing Military Analysis Calculator...")
        conflict_data = {"name": "Battle of Stalingrad", "year": 1942, "battle_type": "siege"}
        result = calculator.calculate_battle_metrics(conflict_data, 100000, 80000, 1.2)
        data = json.loads(result)
        assert "calculation_parameters" in data
        print("    ✅ Military Analysis Calculator works")

        # Test military analyzer
        from tools.military_history.military_analyzer import MilitaryAnalyzer

        analyzer = MilitaryAnalyzer()

        print("  - Testing Military Analyzer...")
        result = analyzer.analyze_military_technology("tanks", "WWII", "armor", "comprehensive")
        data = json.loads(result)
        assert "analysis_parameters" in data
        print("    ✅ Military Analyzer works")

        return True

    except Exception as e:
        print(f"    ❌ Military History Tools failed: {str(e)}")
        traceback.print_exc()
        return False

def test_medical_science_tools():
    """Test Medical Science Tools"""
    print("🏥 Testing Medical Science Tools...")
    try:
        # Test medical search toolkit
        from tools.medical_science.medical_search_toolkit import MedicalSearchToolkit

        toolkit = MedicalSearchToolkit()

        print("  - Testing Medical Search Toolkit...")
        result = toolkit.search_medical_literature("diabetes", "all", "5years", "comprehensive")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Medical Search Toolkit works")

        # Test medical analysis calculator
        from tools.medical_science.medical_analysis_calculator import MedicalAnalysisCalculator

        calculator = MedicalAnalysisCalculator()

        print("  - Testing Medical Analysis Calculator...")
        study_data = {"title": "Diabetes Treatment Study", "study_type": "clinical_trial", "participants": 1000}
        result = calculator.assess_clinical_significance(study_data, 0.6, 0.03, 500)
        data = json.loads(result)
        assert "assessment_parameters" in data
        print("    ✅ Medical Analysis Calculator works")

        # Test medical analyzer
        from tools.medical_science.medical_analyzer import MedicalAnalyzer

        analyzer = MedicalAnalyzer()

        print("  - Testing Medical Analyzer...")
        result = analyzer.analyze_disease_trends("diabetes", "global", "chronic", "comprehensive")
        data = json.loads(result)
        assert "analysis_parameters" in data
        print("    ✅ Medical Analyzer works")

        return True

    except Exception as e:
        print(f"    ❌ Medical Science Tools failed: {str(e)}")
        traceback.print_exc()
        return False

def test_individual_tools():
    """Test individual tools from each channel"""
    print("\n🔧 Testing Individual Tools...")

    try:
        # Test Project Gutenberg from Literature
        from tools.literature.project_gutenberg_lit_tools import ProjectGutenbergLitTool

        gutenberg = ProjectGutenbergLitTool()
        print("  - Testing Project Gutenberg Tools...")
        # Note: search_gutenberg_lit is async, so we'll test get_top_new instead
        result = gutenberg.get_top_new("popular", 3, "month", "fiction")
        # get_top_new returns string, so we need to evaluate it
        data = eval(result)  # Using eval since it returns string representation of dict
        assert data["status"] == "success"
        print("    ✅ Project Gutenberg Tools work")

        # Test PubMed from Medical Science
        from tools.medical_science.pubmed_tools import PubMedTool

        pubmed = PubMedTool()
        print("  - Testing PubMed Tools...")
        result = pubmed.search_pubmed("cancer treatment", "all", 3)
        data = json.loads(result)
        assert data["status"] == "success"
        print("    ✅ PubMed Tools work")

        return True

    except Exception as e:
        print(f"    ❌ Individual Tools failed: {str(e)}")
        traceback.print_exc()
        return False

def run_random_cross_channel_tests():
    """Run random tests across the three channels"""
    print("\n🎲 Running Random Cross-Channel Tests...")

    test_cases = [
        ("Literature random test", lambda: test_random_literature()),
        ("Military random test", lambda: test_random_military()),
        ("Medical random test", lambda: test_random_medical()),
        ("Cross-toolkit test", lambda: test_cross_toolkit_functionality())
    ]

    # Randomly select and run 3 test cases
    selected_tests = random.sample(test_cases, min(3, len(test_cases)))

    for test_name, test_func in selected_tests:
        print(f"  🎯 {test_name}...")
        try:
            test_func()
            print(f"    ✅ {test_name} passed")
        except Exception as e:
            print(f"    ❌ {test_name} failed: {str(e)}")

def test_random_literature():
    """Random literature test"""
    from tools.literature.literature_search_toolkit import LiteratureSearchToolkit

    authors = ["Shakespeare", "Dickens", "Austen", "Hemingway"]
    genres = ["drama", "novel", "poetry", "fiction"]

    toolkit = LiteratureSearchToolkit()
    author = random.choice(authors)
    genre = random.choice(genres)

    result = toolkit.search_literary_works(author, "all", genre, "standard")
    data = json.loads(result)
    assert "search_parameters" in data

def test_random_military():
    """Random military test"""
    from tools.military_history.military_search_toolkit import MilitarySearchToolkit

    conflicts = ["World War I", "World War II", "Vietnam War", "Korean War"]
    scopes = ["global", "regional", "local", "tactical"]

    toolkit = MilitarySearchToolkit()
    conflict = random.choice(conflicts)
    scope = random.choice(scopes)

    result = toolkit.search_military_conflicts(conflict, "all", scope, "standard")
    data = json.loads(result)
    assert "search_parameters" in data

def test_random_medical():
    """Random medical test"""
    from tools.medical_science.medical_search_toolkit import MedicalSearchToolkit

    topics = ["diabetes", "cancer", "heart disease", "alzheimer"]
    time_periods = ["1year", "5years", "10years", "all"]

    toolkit = MedicalSearchToolkit()
    topic = random.choice(topics)
    time_period = random.choice(time_periods)

    result = toolkit.search_medical_literature(topic, "all", time_period, "standard")
    data = json.loads(result)
    assert "search_parameters" in data

def test_cross_toolkit_functionality():
    """Test functionality across different toolkits"""
    from tools.literature.literature_search_toolkit import LiteratureSearchToolkit
    from tools.medical_science.medical_search_toolkit import MedicalSearchToolkit

    lit_toolkit = LiteratureSearchToolkit()
    med_toolkit = MedicalSearchToolkit()

    # Test different search methods
    result1 = lit_toolkit.search_literary_works("medical literature", "", "non-fiction", "standard")
    result2 = med_toolkit.search_medical_literature("literature review", "all", "5years", "standard")

    data1 = json.loads(result1)
    data2 = json.loads(result2)

    assert "search_parameters" in data1
    assert "search_parameters" in data2

def main():
    """Main test function"""
    print("🚀 REMAINING THREE CHANNELS TEST SUITE")
    print("=" * 70)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing Literature, Military History, and Medical Science channels...")
    print()

    test_results = []

    # Test all three channels
    test_functions = [
        ("Literature Tools", test_literature_tools),
        ("Military History Tools", test_military_history_tools),
        ("Medical Science Tools", test_medical_science_tools),
        ("Individual Tools", test_individual_tools)
    ]

    for test_name, test_func in test_functions:
        print(f"\n{'='*25} {test_name} {'='*25}")
        result = test_func()
        test_results.append((test_name, result))
        print()

    # Run random cross-channel tests
    run_random_cross_channel_tests()

    # Summary
    print("\n" + "="*70)
    print("📋 THREE CHANNELS TEST SUMMARY")
    print("="*70)

    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")

    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 All three remaining channels are working correctly!")
        print("✨ Literature, Military History, and Medical Science tools verified!")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")

    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
