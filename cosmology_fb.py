from typing import List, Optional, Dict, Any

from agno.agent import Agent
from agno.knowledge.agent import AgentKnowledge
from agno.memory.v2.memory import Memory
from agno.run.team import RunResponse
from agno.storage.agent.sqlite import SqliteAgentStorage
from agno.team.team import Team
from agno.utils.pprint import pprint_run_response
from agno.workflow import Workflow
from tools.cosmology.arxiv_tools import ArXivTools
from tools.cosmology.cern_opendata_tools import CERNOpenDataTools
from tools.cosmology.cosmology_search_toolkit import CosmologySearchToolkit
from tools.cosmology.inspirehep_tools import InspireHEPTools
from tools.cosmology.nasa_ads_physics_tools import NASAADSPhysicsTools
from tools.cosmology.wikipedia_physics_tools import WikipediaPhysicsTools
from tools.cosmology.cosmic_evolution_calculator import CosmicEvolutionCalculator
from tools.writer.writer_fb import WriterFbTools
from agno.models.ollama import Ollama

from agno.utils.log import logger
from typing import Iterator
import logging

class CosmologyFbWorkflow(Workflow):
    description: str = ("A workflow for the cosmology research and Facebook post creation workflow.")

    def __init__(self):
        super().__init__()
        self.knowledge = AgentKnowledge(
            knowledge_base="tmp/cosmology_research_agent_storage.db",
            table_name="cosmology_research_agent_storage",
        )
        self.memory = Memory(model=Ollama(id="qwen3:4b"), delete_memories=True, clear_memories=True)

        self.sub_question_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Cosmology Sub Question Agent",
            description="An agent that can generate cosmology-related sub-questions based on the user's query.",
            tools=[CosmologySearchToolkit()],
            instructions=("""
                You are a cosmology sub-question agent that can generate sub-questions based on the user's query.
                Use the cosmology search tools to find relevant information and generate sub-questions.
                Focus on cosmological concepts like dark matter, dark energy, cosmic microwave background,
                Big Bang theory, cosmic inflation, galaxy formation, and universe evolution.
                If you find a relevant sub-question, return it.
                If you do not find a relevant sub-question, say "No relevant sub-questions found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.arxiv_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="ArXiv Cosmology Agent",
            description="An agent that can search ArXiv for cosmology and astrophysics papers.",
            tools=[ArXivTools()],
            instructions=("""
                You are an ArXiv agent specialized in cosmology and astrophysics research.
                Use the tools provided to find relevant papers and summarize their findings.
                Focus on recent developments in cosmology, dark matter research, dark energy studies,
                cosmic microwave background analysis, and theoretical cosmology.
                If you find relevant papers, summarize their key findings and implications.
                If you do not find relevant papers, say "No relevant papers found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.cern_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="CERN Open Data Agent",
            description="An agent that can search CERN Open Data for particle physics and cosmology data.",
            tools=[CERNOpenDataTools()],
            instructions=("""
                You are a CERN Open Data agent that can search for particle physics and cosmology data.
                Use the tools provided to find relevant datasets and experimental results.
                Focus on data related to fundamental particles, high-energy physics experiments,
                and their implications for cosmology and our understanding of the universe.
                If you find relevant data, summarize its significance and implications.
                If you do not find relevant data, say "No relevant data found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.inspirehep_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="INSPIRE-HEP Agent",
            description="An agent that can search INSPIRE-HEP for high-energy physics and cosmology literature.",
            tools=[InspireHEPTools()],
            instructions=("""
                You are an INSPIRE-HEP agent that can search for high-energy physics and cosmology literature.
                Use the tools provided to find relevant papers, conferences, and research developments.
                Focus on theoretical cosmology, particle cosmology, and experimental results
                that impact our understanding of the universe.
                If you find relevant literature, summarize the key findings and their significance.
                If you do not find relevant literature, say "No relevant literature found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.nasa_ads_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="NASA ADS Physics Agent",
            description="An agent that can search NASA ADS for physics and cosmology papers.",
            tools=[NASAADSPhysicsTools()],
            instructions=("""
                You are a NASA ADS physics agent that can search for cosmology and astrophysics papers.
                Use the tools provided to find relevant papers and summarize their findings.
                Focus on observational cosmology, theoretical models, and experimental results
                that advance our understanding of the universe's structure and evolution.
                If you find relevant papers, summarize their key contributions to cosmology.
                If you do not find relevant papers, say "No relevant papers found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.wikipedia_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Wikipedia Physics Agent",
            description="An agent that can search Wikipedia for physics and cosmology information.",
            tools=[WikipediaPhysicsTools()],
            instructions=("""
                You are a Wikipedia physics agent that can search for cosmology and physics information.
                Use the tools provided to find relevant information and summarize it in an accessible way.
                Focus on explaining complex cosmological concepts in simple terms that general audiences can understand.
                If you find relevant information, provide clear explanations and context.
                If you do not find relevant information, say "No relevant information found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.calculator_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Cosmic Evolution Calculator Agent",
            description="An agent that can perform cosmological calculations and modeling.",
            tools=[CosmicEvolutionCalculator()],
            instructions=("""
                You are a cosmic evolution calculator agent that can perform cosmological calculations.
                Use the tools provided to calculate cosmic parameters, distances, ages, and evolution models.
                Focus on providing quantitative insights that support the research findings from other agents.
                If calculations are relevant to the topic, provide clear explanations of the results.
                If calculations are not applicable, say "No relevant calculations needed."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.writer_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Cosmology Writer Agent",
            description="An agent that can write engaging Facebook posts about cosmology based on research findings.",
            tools=[WriterFbTools()],
            instructions=("""
                You are a cosmology writer agent that creates engaging Facebook posts based on research findings.
                Use the tools provided to write comprehensive, accessible, and engaging Facebook posts about cosmology.
                Your posts should:
                - Make complex cosmological concepts accessible to general audiences
                - Include fascinating facts and recent discoveries
                - Use engaging language that sparks curiosity about the universe
                - Include relevant hashtags and call-to-action elements
                - Balance scientific accuracy with readability
                If you need more information, ask the relevant research agents.
                Always aim to inspire wonder about the cosmos while maintaining scientific integrity.
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.cosmology_research_team = Team(
            name="Cosmology Research Team",
            model=Ollama(id="qwen3:4b"),
            mode="collaborative",
            storage=SqliteAgentStorage(
                db_file="tmp/cosmology_research_team_storage.db",
                table_name="cosmology_research_team_storage",
            ),
            members=[
                self.sub_question_agent,
                self.arxiv_agent,
                self.cern_agent,
                self.inspirehep_agent,
                self.nasa_ads_agent,
                self.wikipedia_agent,
                self.calculator_agent,
                self.writer_agent
            ],
            instructions=("""
                You are a team of cosmology research and writing agents.
                Your task is to collaboratively research and write engaging Facebook posts about cosmology.
                Each member has a specific role:
                - Cosmology Sub Question Agent: Generates cosmology-related sub-questions
                - ArXiv Cosmology Agent: Searches ArXiv for recent cosmology papers
                - CERN Open Data Agent: Searches for particle physics data relevant to cosmology
                - INSPIRE-HEP Agent: Searches high-energy physics literature
                - NASA ADS Physics Agent: Searches NASA ADS for cosmology papers
                - Wikipedia Physics Agent: Provides accessible explanations of concepts
                - Cosmic Evolution Calculator Agent: Performs cosmological calculations
                - Cosmology Writer Agent: Creates engaging Facebook posts

                Work together to create content that is both scientifically accurate and engaging for general audiences.
                Focus on making the wonders of cosmology accessible and inspiring.
            """),
            memory=self.memory,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
            success_criteria=(""" The team successfully creates an engaging and scientifically accurate Facebook post about cosmology """)
        )

    def run_workflow(self, message):
        logging.info(f"Running cosmology research workflow with message: {message}")
        result = self.cosmology_research_team.run(message)

        if hasattr(result, '__iter__') and not isinstance(result, str):
            yield from result
        else:
            yield result

def chat_loop():
    workflow = CosmologyFbWorkflow()
    try:
        while True:
            user_input = input("User: ")
            if user_input.lower() == "exit":
                break
            pprint_run_response(workflow.run_workflow(user_input), markdown=True, show_time=True)
    except Exception as e:
        logging.error(f"Error occurred in chat loop: {e}")

if __name__ == "__main__":
    chat_loop()
