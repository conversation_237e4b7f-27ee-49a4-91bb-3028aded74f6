from typing import List, Optional, Dict, Any, Iterator

from agno.agent import Agent, RunResponse
from agno.knowledge.agent import AgentKnowledge
from agno.memory.v2.memory import Memory
from agno.run.team import TeamRunResponse
from agno.storage.agent.sqlite import SqliteAgentStorage
from agno.team.team import Team
from agno.utils.pprint import pprint_run_response
from agno.workflow import Workflow
from tools.cosmology.arxiv_tools import ArXivTools
from tools.cosmology.cern_opendata_tools import CERNOpenDataTools
from tools.cosmology.cosmology_search_toolkit import CosmologySearchToolkit
from tools.cosmology.inspirehep_tools import InspireHEPTools
from tools.cosmology.nasa_ads_physics_tools import NASAADSPhysicsTools
from tools.cosmology.wikipedia_physics_tools import WikipediaPhysicsTools
from tools.cosmology.cosmic_evolution_calculator import CosmicEvolutionCalculator
from tools.writer.writer_fb import WriterFbTools
from agno.models.ollama import Ollama

from agno.utils.log import logger
import logging
import time

# Import optimization utilities
from utils.text_cleaning import get_text_cleaner, clean_qwen_output, clean_for_qdrant
from utils.qdrant_optimization import get_qdrant_optimizer
from utils.chunking import chunk_text, auto_chunking
from utils.reranker import rerank_chunks

class CosmologyFbWorkflow(Workflow):
    description: str = ("A workflow for the cosmology research and Facebook post creation workflow.")

    def __init__(self):
        super().__init__()
        self.knowledge = AgentKnowledge(
            knowledge_base="tmp/cosmology_research_agent_storage.db",
            table_name="cosmology_research_agent_storage",
        )
        self.memory = Memory(model=Ollama(id="qwen3:4b"), delete_memories=True, clear_memories=True)

        # Initialize optimization utilities
        self.text_cleaner = get_text_cleaner()
        self.qdrant_optimizer = get_qdrant_optimizer()

        # Chunking and reranking settings
        self.chunk_size = 1000
        self.chunk_overlap = 200
        self.rerank_top_k = 5

        # Qdrant settings (optional - only if user wants to use Qdrant)
        self.use_qdrant = True  # Set to True to enable Qdrant
        self.qdrant_collection = "cosmology_posts"
        self.vector_db = None

        self.sub_question_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Cosmology Sub Question Agent",
            description="An agent that can generate cosmology-related sub-questions based on the user's query.",
            tools=[CosmologySearchToolkit()],
            instructions=("""
                You are a cosmology sub-question agent that can generate sub-questions based on the user's query.
                Use the cosmology search tools to find relevant information and generate sub-questions.
                Focus on cosmological concepts like dark matter, dark energy, cosmic microwave background,
                Big Bang theory, cosmic inflation, galaxy formation, and universe evolution.
                If you find a relevant sub-question, return it.
                If you do not find a relevant sub-question, say "No relevant sub-questions found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.arxiv_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="ArXiv Cosmology Agent",
            description="An agent that can search ArXiv for cosmology and astrophysics papers.",
            tools=[ArXivTools()],
            instructions=("""
                You are an ArXiv agent specialized in cosmology and astrophysics research.
                Use the tools provided to find relevant papers and summarize their findings.
                Focus on recent developments in cosmology, dark matter research, dark energy studies,
                cosmic microwave background analysis, and theoretical cosmology.
                If you find relevant papers, summarize their key findings and implications.
                If you do not find relevant papers, say "No relevant papers found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.cern_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="CERN Open Data Agent",
            description="An agent that can search CERN Open Data for particle physics and cosmology data.",
            tools=[CERNOpenDataTools()],
            instructions=("""
                You are a CERN Open Data agent that can search for particle physics and cosmology data.
                Use the tools provided to find relevant datasets and experimental results.
                Focus on data related to fundamental particles, high-energy physics experiments,
                and their implications for cosmology and our understanding of the universe.
                If you find relevant data, summarize its significance and implications.
                If you do not find relevant data, say "No relevant data found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.inspirehep_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="INSPIRE-HEP Agent",
            description="An agent that can search INSPIRE-HEP for high-energy physics and cosmology literature.",
            tools=[InspireHEPTools()],
            instructions=("""
                You are an INSPIRE-HEP agent that can search for high-energy physics and cosmology literature.
                Use the tools provided to find relevant papers, conferences, and research developments.
                Focus on theoretical cosmology, particle cosmology, and experimental results
                that impact our understanding of the universe.
                If you find relevant literature, summarize the key findings and their significance.
                If you do not find relevant literature, say "No relevant literature found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.nasa_ads_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="NASA ADS Physics Agent",
            description="An agent that can search NASA ADS for physics and cosmology papers.",
            tools=[NASAADSPhysicsTools()],
            instructions=("""
                You are a NASA ADS physics agent that can search for cosmology and astrophysics papers.
                Use the tools provided to find relevant papers and summarize their findings.
                Focus on observational cosmology, theoretical models, and experimental results
                that advance our understanding of the universe's structure and evolution.
                If you find relevant papers, summarize their key contributions to cosmology.
                If you do not find relevant papers, say "No relevant papers found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.wikipedia_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Wikipedia Physics Agent",
            description="An agent that can search Wikipedia for physics and cosmology information.",
            tools=[WikipediaPhysicsTools()],
            instructions=("""
                You are a Wikipedia physics agent that can search for cosmology and physics information.
                Use the tools provided to find relevant information and summarize it in an accessible way.
                Focus on explaining complex cosmological concepts in simple terms that general audiences can understand.
                If you find relevant information, provide clear explanations and context.
                If you do not find relevant information, say "No relevant information found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.calculator_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Cosmic Evolution Calculator Agent",
            description="An agent that can perform cosmological calculations and modeling.",
            tools=[CosmicEvolutionCalculator()],
            instructions=("""
                You are a cosmic evolution calculator agent that can perform cosmological calculations.
                Use the tools provided to calculate cosmic parameters, distances, ages, and evolution models.
                Focus on providing quantitative insights that support the research findings from other agents.
                If calculations are relevant to the topic, provide clear explanations of the results.
                If calculations are not applicable, say "No relevant calculations needed."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.writer_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Cosmology Writer Agent",
            description="An agent that can write engaging Facebook posts about cosmology based on research findings.",
            tools=[WriterFbTools()],
            instructions=("""
                You are a cosmology writer agent that creates engaging Facebook posts based on research findings.
                Use the tools provided to write comprehensive, accessible, and engaging Facebook posts about cosmology.
                Your posts should:
                - Make complex cosmological concepts accessible to general audiences
                - Include fascinating facts and recent discoveries
                - Use engaging language that sparks curiosity about the universe
                - Include relevant hashtags and call-to-action elements
                - Balance scientific accuracy with readability
                If you need more information, ask the relevant research agents.
                Always aim to inspire wonder about the cosmos while maintaining scientific integrity.
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )

        self.cosmology_research_team = Team(
            name="Cosmology Research Team",
            model=Ollama(id="qwen3:4b"),
            mode="collaborative",
            storage=SqliteAgentStorage(
                db_file="tmp/cosmology_research_team_storage.db",
                table_name="cosmology_research_team_storage",
            ),
            members=[
                self.sub_question_agent,
                self.arxiv_agent,
                self.cern_agent,
                self.inspirehep_agent,
                self.nasa_ads_agent,
                self.wikipedia_agent,
                self.calculator_agent
            ],
            instructions=("""
                You are a team of cosmology research and writing agents.
                Your task is to collaboratively research and write engaging Facebook posts about cosmology.
                Each member has a specific role:
                - Cosmology Sub Question Agent: Generates cosmology-related sub-questions
                - ArXiv Cosmology Agent: Searches ArXiv for recent cosmology papers
                - CERN Open Data Agent: Searches for particle physics data relevant to cosmology
                - INSPIRE-HEP Agent: Searches high-energy physics literature
                - NASA ADS Physics Agent: Searches NASA ADS for cosmology papers
                - Wikipedia Physics Agent: Provides accessible explanations of concepts
                - Cosmic Evolution Calculator Agent: Performs cosmological calculations

                Work together to gather comprehensive, scientifically accurate information about the given topic.
                Focus on providing detailed research that covers multiple aspects of the cosmology question.
                Your research will be used by a writer to create engaging content for general audiences.
            """),
            memory=self.memory,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
            success_criteria=(""" The team successfully gathers comprehensive research information about the cosmology topic """)
        )

    def process_cosmology_query(self, query: str = "What is dark matter?") -> str:
        """
        Process a cosmology query and return a Facebook post.

        Args:
            query: The cosmology question or topic to research

        Returns:
            The final Facebook post content as string
        """
        logger.info(f"Starting cosmology research workflow for: {query}")

        try:
            # Step 1: Team research
            logger.info("Step 1: Running team research...")
            team_result = self.cosmology_research_team.run(
                f"Research and analyze: {query}. Provide comprehensive information from multiple scientific sources."
            )

            if not team_result or not hasattr(team_result, 'content') or not team_result.content:
                return "Sorry, I couldn't gather enough information to create a post about this topic."

            logger.info("Team research completed successfully")

            # Step 2: Create Facebook post
            logger.info("Step 2: Creating Facebook post...")
            writer_result = self.writer_agent.run(
                f"Based on this research: {team_result.content}\n\n"
                f"Create an engaging Facebook post about: {query}"
            )

            # Handle writer result and apply final optimizations
            if hasattr(writer_result, 'content') and writer_result.content:
                # Apply final optimizations to the result
                final_result = self._apply_final_optimizations(writer_result.content, query)

                logger.info(f"Final optimizations applied: {len(writer_result.content)} -> {len(final_result)} chars")

                return final_result
            else:
                return "Sorry, I couldn't create a Facebook post based on the research."

        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")
            return f"Sorry, there was an error processing your request: {str(e)}"

    def _apply_final_optimizations(self, final_content: str, query: str) -> str:
        """
        Apply final optimizations to the completed result (text cleaning, chunking if needed, Qdrant prep)

        Args:
            final_content: Final content from writer agent
            query: Original query for context

        Returns:
            Optimized final content
        """
        try:
            # Step 1: Clean Qwen output (remove <think> tags, etc.)
            cleaned_content = clean_qwen_output(final_content)

            # Step 2: If content is very long, apply chunking and reranking for better quality
            if len(cleaned_content) > self.chunk_size * 2:  # Only if significantly long
                chunks = auto_chunking(
                    cleaned_content,
                    max_chunk_size=self.chunk_size,
                    min_chunk_size=self.chunk_size // 4
                )

                if len(chunks) > 1:
                    # Convert to reranker format
                    chunk_dicts = []
                    for i, chunk in enumerate(chunks):
                        chunk_dicts.append({
                            "id": f"final_chunk_{i}",
                            "text": chunk,
                            "metadata": {"source": "final_content"}
                        })

                    # Rerank to get best parts
                    reranked_chunks = rerank_chunks(
                        chunk_dicts,
                        query,
                        strategy="relevance",  # Use simpler strategy for final content
                        top_k=min(3, len(chunk_dicts))  # Keep top 3 chunks max
                    )

                    # Combine best chunks
                    cleaned_content = "\n\n".join([chunk["text"] for chunk in reranked_chunks])
                    logger.info(f"Final content optimized: {len(chunks)} chunks -> {len(reranked_chunks)} chunks")

            # Step 3: Prepare for Qdrant storage (but don't store yet)
            qdrant_ready_content = clean_for_qdrant(cleaned_content)

            # Step 4: Store in knowledge base and Qdrant for future reference
            self._store_content_in_knowledge(query, qdrant_ready_content)

            # Step 5: Store in Qdrant if enabled
            if self.use_qdrant:
                self.store_in_qdrant(qdrant_ready_content, query)

            return cleaned_content

        except Exception as e:
            logger.warning(f"Failed to apply final optimizations: {e}")
            # Fallback to basic cleaning
            return clean_qwen_output(final_content)

    def _store_content_in_knowledge(self, query: str, content: str):
        """
        Store cleaned content in knowledge base for future reference

        Args:
            query: The original query
            content: Cleaned content ready for storage
        """
        try:
            # Store in knowledge base using correct method
            # AgentKnowledge uses different method name
            if hasattr(self.knowledge, 'add'):
                self.knowledge.add(content)
            elif hasattr(self.knowledge, 'save'):
                self.knowledge.save(content)
            else:
                # Fallback: just log the content
                logger.info(f"Knowledge base method not found, content logged for query: {query[:50]}...")
                return

            logger.info(f"Content stored in knowledge base for query: {query[:50]}...")
        except Exception as e:
            logger.warning(f"Failed to store content in knowledge base: {e}")

    def setup_qdrant(self, collection_name: str = "cosmology_posts", vector_size: int = 384):
        """
        Setup Qdrant vector database for storing optimized content

        Args:
            collection_name: Name of the Qdrant collection
            vector_size: Size of embedding vectors (384 for all-MiniLM-L6-v2)
        """
        try:
            from qdrant_client import QdrantClient
            from qdrant_client.models import Distance, VectorParams

            # Initialize Qdrant client (local)
            self.vector_db = QdrantClient(host="localhost", port=6333)
            self.qdrant_collection = collection_name

            # Create collection if it doesn't exist
            try:
                self.vector_db.get_collection(collection_name)
                logger.info(f"Qdrant collection '{collection_name}' already exists")
            except:
                self.vector_db.create_collection(
                    collection_name=collection_name,
                    vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE)
                )
                logger.info(f"Created Qdrant collection '{collection_name}'")

            self.use_qdrant = True
            logger.info("Qdrant setup completed successfully")

        except ImportError:
            logger.warning("Qdrant client not installed. Install with: pip install qdrant-client")
        except Exception as e:
            logger.warning(f"Failed to setup Qdrant: {e}")

    def store_in_qdrant(self, content: str, query: str, metadata: dict = None):
        """
        Store content in Qdrant vector database

        Args:
            content: Content to store
            query: Original query
            metadata: Additional metadata
        """
        if not self.use_qdrant or not self.vector_db:
            return

        try:
            from sentence_transformers import SentenceTransformer
            import uuid

            # Generate embedding
            model = SentenceTransformer('all-MiniLM-L6-v2')
            embedding = model.encode(content).tolist()

            # Prepare metadata
            point_metadata = {
                "query": query,
                "content": content[:500] + "..." if len(content) > 500 else content,
                "timestamp": time.time(),
                "type": "facebook_post",
                "source": "cosmology_team"
            }
            if metadata:
                point_metadata.update(metadata)

            # Store in Qdrant
            point_id = str(uuid.uuid4())
            self.vector_db.upsert(
                collection_name=self.qdrant_collection,
                points=[{
                    "id": point_id,
                    "vector": embedding,
                    "payload": point_metadata
                }]
            )

            logger.info(f"Content stored in Qdrant with ID: {point_id}")

        except Exception as e:
            logger.warning(f"Failed to store in Qdrant: {e}")

    def enable_qdrant(self, collection_name: str = "cosmology_posts"):
        """
        Enable Qdrant storage for the workflow

        Args:
            collection_name: Name of the Qdrant collection
        """
        self.setup_qdrant(collection_name)

    def run_workflow(self, message: str = "What is dark matter?") -> str:
        """Legacy method for backward compatibility - returns string instead of Iterator"""
        result = self.process_cosmology_query(message)
        return result

def chat_loop():
    workflow = CosmologyFbWorkflow()
    try:
        while True:
            user_input = input("User: ")
            if user_input.lower() == "exit":
                break
            pprint_run_response(workflow.run_workflow(user_input), markdown=True, show_time=True)
    except Exception as e:
        logging.error(f"Error occurred in chat loop: {e}")

if __name__ == "__main__":
    chat_loop()
