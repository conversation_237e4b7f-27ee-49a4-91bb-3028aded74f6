#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NOAA Ocean Tools - Công cụ tìm kiếm dữ liệu đại dương từ NOAA
"""

from typing import Dict, Any
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json


class NOAAOceanTool(Toolkit):
    """
    NOAA Ocean Tool for searching ocean data, marine research, and oceanographic information.
    """

    def __init__(self):
        super().__init__(
            name="NOAA Ocean Search Tool",
            tools=[self.search_ocean_data, self.get_top_new]
        )

    async def search_ocean_data(self, query: str, data_type: str = "all", limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm dữ liệu đại dương từ NOAA.
        
        Parameters:
        - query: Từ khóa tìm kiếm
        - data_type: <PERSON><PERSON><PERSON> dữ liệ<PERSON> (temperature, currents, marine_life, weather, bathymetry)
        - limit: <PERSON><PERSON> lư<PERSON><PERSON> kết quả
        
        Returns:
        - Dict ch<PERSON><PERSON> kết quả tìm kiếm từ NOAA
        """
        logger.info(f"Searching NOAA ocean data for: {query}")
        
        try:
            # Simulate ocean data search results
            results = []
            for i in range(limit):
                result = {
                    "id": f"noaa_ocean_{1000+i:04d}",
                    "title": f"Ocean {data_type.title()} Data: {query} Study {chr(65+i)}",
                    "description": f"Comprehensive {data_type} analysis of {query} from NOAA oceanographic research. This dataset includes measurements, observations, and analysis from multiple research expeditions.",
                    "data_type": data_type,
                    "location": ["Pacific Ocean", "Atlantic Ocean", "Indian Ocean", "Arctic Ocean", "Southern Ocean"][i % 5],
                    "depth_range": f"{100 + (i * 500)}-{1000 + (i * 1000)}m",
                    "collection_date": f"2024-{1+i%12:02d}-{15+i:02d}",
                    "research_vessel": f"NOAA Ship {['Okeanos Explorer', 'Ronald H. Brown', 'Thomas Jefferson', 'Fairweather'][i % 4]}",
                    "coordinates": {
                        "latitude": round(-60 + (i * 20), 4),
                        "longitude": round(-180 + (i * 40), 4)
                    },
                    "data_quality": ["Excellent", "Good", "Fair"][i % 3],
                    "access_level": ["Public", "Restricted", "Embargoed"][i % 3],
                    "file_format": ["NetCDF", "CSV", "JSON", "XML"][i % 4],
                    "file_size": f"{10 + (i * 5)}MB",
                    "noaa_url": f"https://www.noaa.gov/ocean-data/{query.lower()}-{chr(97+i)}",
                    "doi": f"10.25921/noaa-ocean-{1000+i:04d}"
                }
                results.append(result)
            
            return {
                "status": "success",
                "source": "NOAA Ocean Database",
                "query": query,
                "data_type": data_type,
                "total_results": len(results),
                "results": results,
                "search_metadata": {
                    "search_time": "2024-01-15T10:30:00Z",
                    "database_version": "NOAA Ocean DB v2.1",
                    "coverage": "Global ocean data"
                }
            }
            
        except Exception as e:
            logger.error(f"Error searching NOAA ocean data: {str(e)}")
            return {
                "status": "error",
                "source": "NOAA Ocean Database",
                "message": str(e),
                "query": query
            }

    async def get_top_new(self, content_type: str = "discoveries", limit: int = 10, 
                         time_period: str = "month", category: str = "") -> Dict[str, Any]:
        """
        Lấy nội dung mới nhất và nổi bật từ NOAA Ocean.
        
        Parameters:
        - content_type: Loại nội dung (discoveries, expeditions, research, species, phenomena)
        - limit: Số lượng kết quả (tối đa 20)
        - time_period: Khoảng thời gian (week, month, year, all_time)
        - category: Danh mục cụ thể (deep_sea, coral_reefs, marine_life, etc.)
        
        Returns:
        - Dict với nội dung ocean mới nhất từ NOAA
        """
        logger.info(f"Lấy top {content_type} mới nhất từ NOAA Ocean trong {time_period}")
        
        limit = max(1, min(limit, 20))
        
        try:
            results = []
            
            if content_type == "discoveries":
                # Khám phá mới nhất về đại dương
                results = [
                    {
                        "name": f"🌊 {category or 'Ocean'} Discovery #{i+1}",
                        "discovery_id": f"noaa_discovery_{2024}_{1000+i:04d}",
                        "title": f"{category or 'Deep Sea'} {['Species', 'Formation', 'Phenomenon', 'Ecosystem'][i % 4]} Discovery {chr(65+i)}",
                        "discovery_type": ["New Species", "Geological Formation", "Ocean Phenomenon", "Ecosystem"][i % 4],
                        "location": {
                            "ocean": ["Pacific", "Atlantic", "Indian", "Arctic", "Southern"][i % 5],
                            "region": f"{['North', 'South', 'East', 'West'][i % 4]} {['Pacific', 'Atlantic', 'Indian', 'Arctic'][i % 4]}",
                            "coordinates": {
                                "latitude": round(-60 + (i * 20), 4),
                                "longitude": round(-180 + (i * 40), 4)
                            },
                            "depth": f"{1000 + (i * 500)}m"
                        },
                        "discovery_date": f"2024-{1+i%12:02d}-{15+i:02d}",
                        "research_team": f"NOAA {['Deep Ocean Exploration', 'Marine Biology', 'Oceanography', 'Climate Research'][i % 4]} Team",
                        "expedition": f"Expedition {chr(65+i)}{chr(97+i)}",
                        "description": f"Remarkable discovery of {category or 'deep sea'} {['species with unique bioluminescence', 'geological formation with unusual properties', 'ocean current pattern', 'ecosystem with extreme conditions'][i % 4]}. This finding provides new insights into {['marine biodiversity', 'ocean geology', 'climate patterns', 'deep sea ecology'][i % 4]} and has significant implications for {['conservation efforts', 'scientific understanding', 'climate research', 'marine protection'][i % 4]}.",
                        "significance": ["High", "Critical", "Moderate", "Revolutionary"][i % 4],
                        "research_vessel": f"NOAA Ship {['Okeanos Explorer', 'Ronald H. Brown', 'Thomas Jefferson'][i % 3]}",
                        "equipment_used": ["ROV Deep Discoverer", "CTD Rosette", "Multibeam Sonar", "Sediment Corer"][i % 4],
                        "samples_collected": 10 + (i * 5),
                        "images_captured": 100 + (i * 50),
                        "video_footage": f"{2 + (i % 8)} hours",
                        "follow_up_research": i % 2 == 0,
                        "publication_status": ["Published", "Under Review", "In Preparation", "Submitted"][i % 4],
                        "noaa_url": f"https://oceanexplorer.noaa.gov/discoveries/{category or 'ocean'}-{chr(97+i)}",
                        "media_coverage": ["National Geographic", "Science Magazine", "Nature", "Ocean Today"][i % 4]
                    } for i in range(limit)
                ]
                
            elif content_type == "expeditions":
                # Chuyến thám hiểm mới nhất
                results = [
                    {
                        "name": f"🚢 {category or 'Ocean'} Expedition #{i+1}",
                        "expedition_id": f"noaa_expedition_{2024}_{2000+i:04d}",
                        "expedition_name": f"{category or 'Deep Sea'} {['Explorer', 'Discoverer', 'Pioneer', 'Navigator'][i % 4]} Mission {chr(65+i)}",
                        "mission_type": ["Exploration", "Research", "Mapping", "Conservation"][i % 4],
                        "start_date": f"2024-{1+i%12:02d}-{1+i:02d}",
                        "end_date": f"2024-{2+i%12:02d}-{15+i:02d}",
                        "duration": f"{14 + (i * 7)} days",
                        "research_vessel": f"NOAA Ship {['Okeanos Explorer', 'Ronald H. Brown', 'Thomas Jefferson', 'Fairweather'][i % 4]}",
                        "target_area": {
                            "ocean": ["Pacific", "Atlantic", "Indian", "Arctic"][i % 4],
                            "specific_region": f"{['Mariana Trench', 'Mid-Atlantic Ridge', 'Coral Triangle', 'Arctic Basin'][i % 4]}",
                            "coordinates": f"{round(-60 + (i * 20), 2)}°, {round(-180 + (i * 40), 2)}°",
                            "depth_range": f"{500 + (i * 1000)}-{3000 + (i * 2000)}m"
                        },
                        "objectives": [f"Objective {j+1}" for j in range(4)],
                        "research_team": {
                            "chief_scientist": f"Dr. {chr(65+i)} {chr(75+i)}",
                            "team_size": 15 + (i * 3),
                            "specializations": ["Marine Biology", "Geology", "Oceanography", "Archaeology"][i % 4],
                            "institutions": [f"Institution {chr(65+j)}" for j in range(3)]
                        },
                        "equipment": ["ROV", "AUV", "CTD", "Multibeam Sonar", "Sediment Corer"][:(i % 4) + 2],
                        "expected_discoveries": [f"Expected Discovery {j+1}" for j in range(3)],
                        "live_streaming": i % 2 == 0,
                        "public_engagement": ["Live broadcasts", "Educational programs", "Social media updates"][:(i % 3) + 1],
                        "budget": f"${2 + (i * 0.5):.1f}M",
                        "sponsors": ["NOAA", "NSF", "Private Foundation"][:(i % 3) + 1],
                        "expedition_status": ["Active", "Completed", "Planned", "In Progress"][i % 4],
                        "noaa_url": f"https://oceanexplorer.noaa.gov/expeditions/{category or 'ocean'}-{chr(97+i)}",
                        "tracking_url": f"https://oceanexplorer.noaa.gov/live/{category or 'ocean'}-{chr(97+i)}"
                    } for i in range(limit)
                ]
                
            elif content_type == "research":
                # Nghiên cứu mới nhất
                results = [
                    {
                        "name": f"🔬 {category or 'Ocean'} Research #{i+1}",
                        "research_id": f"noaa_research_{2024}_{3000+i:04d}",
                        "title": f"{category or 'Marine'} {['Ecosystem', 'Climate', 'Biodiversity', 'Geology'][i % 4]} Research Study {chr(65+i)}",
                        "research_type": ["Field Study", "Laboratory Analysis", "Modeling Study", "Long-term Monitoring"][i % 4],
                        "principal_investigator": f"Dr. {chr(65+i)} {chr(75+i)}",
                        "institution": ["NOAA PMEL", "NOAA NMFS", "NOAA OAR", "NOAA NOS"][i % 4],
                        "publication_date": f"2024-{1+i%12:02d}-{20+i:02d}",
                        "journal": ["Nature", "Science", "Ocean Science", "Marine Biology"][i % 4],
                        "study_area": {
                            "ocean": ["Pacific", "Atlantic", "Indian", "Arctic"][i % 4],
                            "ecosystem": category or ["Coral Reefs", "Deep Sea", "Coastal", "Open Ocean"][i % 4],
                            "depth_focus": f"{100 + (i * 500)}-{1000 + (i * 1000)}m"
                        },
                        "research_duration": f"{1 + (i % 5)} years",
                        "sample_size": 100 + (i * 50),
                        "methodology": ["Field sampling", "Remote sensing", "Laboratory analysis", "Computer modeling"][i % 4],
                        "key_findings": [f"Finding {j+1}" for j in range(4)],
                        "implications": [f"Implication {j+1}" for j in range(3)],
                        "climate_relevance": ["High", "Medium", "Low", "Critical"][i % 4],
                        "conservation_impact": ["Significant", "Moderate", "Limited", "Major"][i % 4],
                        "funding_source": ["NOAA", "NSF", "NASA", "Private"][i % 4],
                        "funding_amount": f"${500 + (i * 200)}K",
                        "collaborators": [f"Collaborator {chr(65+j)}" for j in range(2)],
                        "data_availability": ["Open Access", "Restricted", "Upon Request"][i % 3],
                        "doi": f"10.1029/noaa-research-{3000+i:04d}",
                        "noaa_url": f"https://research.noaa.gov/article/{category or 'ocean'}-{chr(97+i)}",
                        "citation_count": 5 + (i * 3)
                    } for i in range(limit)
                ]
                
            elif content_type == "species":
                # Loài sinh vật biển mới
                results = [
                    {
                        "name": f"🐠 {category or 'Marine'} Species #{i+1}",
                        "species_id": f"noaa_species_{2024}_{4000+i:04d}",
                        "scientific_name": f"{category or 'Oceanus'} {chr(97+i)}{chr(97+i)}ensis {chr(65+i)}{chr(97+i)}i",
                        "common_name": f"{category or 'Deep Sea'} {['Fish', 'Coral', 'Jellyfish', 'Crab', 'Squid'][i % 5]} {chr(65+i)}",
                        "discovery_date": f"2024-{1+i%12:02d}-{10+i:02d}",
                        "discovery_location": {
                            "ocean": ["Pacific", "Atlantic", "Indian", "Arctic"][i % 4],
                            "region": f"{['Mariana Trench', 'Mid-Atlantic Ridge', 'Coral Triangle', 'Arctic Basin'][i % 4]}",
                            "depth": f"{1000 + (i * 500)}m",
                            "coordinates": f"{round(-60 + (i * 20), 2)}°, {round(-180 + (i * 40), 2)}°"
                        },
                        "taxonomic_classification": {
                            "kingdom": "Animalia",
                            "phylum": ["Chordata", "Cnidaria", "Arthropoda", "Mollusca"][i % 4],
                            "class": category or ["Actinopterygii", "Anthozoa", "Malacostraca", "Cephalopoda"][i % 4],
                            "order": f"{category or 'Order'}{chr(65+i)}",
                            "family": f"{category or 'Family'}{chr(65+i)}idae"
                        },
                        "physical_characteristics": {
                            "size": f"{5 + (i * 2)}cm",
                            "weight": f"{10 + (i * 5)}g",
                            "color": ["Bioluminescent blue", "Transparent", "Deep red", "Iridescent"][i % 4],
                            "unique_features": [f"Feature {j+1}" for j in range(3)]
                        },
                        "habitat": {
                            "depth_range": f"{500 + (i * 500)}-{2000 + (i * 1000)}m",
                            "temperature_range": f"{2 + (i % 8)}-{10 + (i % 15)}°C",
                            "pressure": f"{50 + (i * 50)} atm",
                            "substrate": ["Rocky", "Sandy", "Muddy", "Coral"][i % 4]
                        },
                        "behavior": {
                            "feeding": ["Filter feeder", "Predator", "Scavenger", "Symbiotic"][i % 4],
                            "reproduction": ["Broadcast spawning", "Brooding", "Asexual", "Complex lifecycle"][i % 4],
                            "social_structure": ["Solitary", "Schooling", "Colonial", "Territorial"][i % 4]
                        },
                        "conservation_status": ["Data Deficient", "Least Concern", "Near Threatened", "Vulnerable"][i % 4],
                        "threats": [f"Threat {j+1}" for j in range(2)],
                        "research_priority": ["High", "Medium", "Low"][i % 3],
                        "specimens_collected": 1 + (i % 5),
                        "genetic_analysis": i % 2 == 0,
                        "noaa_url": f"https://www.fishbase.noaa.gov/species/{category or 'marine'}-{chr(97+i)}",
                        "images_available": 5 + (i * 2)
                    } for i in range(limit)
                ]
                
            elif content_type == "phenomena":
                # Hiện tượng đại dương mới
                results = [
                    {
                        "name": f"🌀 {category or 'Ocean'} Phenomenon #{i+1}",
                        "phenomenon_id": f"noaa_phenomenon_{2024}_{5000+i:04d}",
                        "phenomenon_name": f"{category or 'Marine'} {['Vortex', 'Current', 'Upwelling', 'Bloom'][i % 4]} Event {chr(65+i)}",
                        "phenomenon_type": ["Physical", "Biological", "Chemical", "Geological"][i % 4],
                        "observation_date": f"2024-{1+i%12:02d}-{5+i:02d}",
                        "location": {
                            "ocean": ["Pacific", "Atlantic", "Indian", "Arctic"][i % 4],
                            "region": f"{['North Pacific Gyre', 'Gulf Stream', 'Kuroshio Current', 'Antarctic Circumpolar'][i % 4]}",
                            "coordinates": f"{round(-60 + (i * 20), 2)}°, {round(-180 + (i * 40), 2)}°",
                            "affected_area": f"{100 + (i * 100)} km²"
                        },
                        "description": f"Unusual {category or 'ocean'} phenomenon characterized by {['rapid temperature changes', 'abnormal current patterns', 'massive algae blooms', 'underwater acoustic anomalies'][i % 4]}. This event has been observed using {['satellite imagery', 'underwater sensors', 'research vessels', 'autonomous vehicles'][i % 4]} and shows {['unprecedented scale', 'unique characteristics', 'potential climate links', 'ecological impacts'][i % 4]}.",
                        "intensity": ["Low", "Moderate", "High", "Extreme"][i % 4],
                        "duration": f"{1 + (i % 30)} days",
                        "frequency": ["Rare", "Occasional", "Seasonal", "Unprecedented"][i % 4],
                        "causes": [f"Cause {j+1}" for j in range(3)],
                        "effects": [f"Effect {j+1}" for j in range(4)],
                        "monitoring_methods": ["Satellite", "Buoys", "Research vessels", "Autonomous vehicles"][i % 4],
                        "data_collected": f"{50 + (i * 25)}GB",
                        "climate_connection": ["Strong", "Moderate", "Weak", "Unknown"][i % 4],
                        "ecological_impact": ["Significant", "Moderate", "Minor", "Unknown"][i % 4],
                        "prediction_models": i % 2 == 0,
                        "similar_events": 2 + (i % 8),
                        "research_teams": [f"Team {chr(65+j)}" for j in range(2)],
                        "publication_status": ["Published", "Under Review", "In Preparation"][i % 3],
                        "noaa_url": f"https://oceanservice.noaa.gov/phenomena/{category or 'ocean'}-{chr(97+i)}",
                        "real_time_data": f"https://oceanservice.noaa.gov/live/{category or 'ocean'}-{chr(97+i)}"
                    } for i in range(limit)
                ]
            
            result = {
                "status": "success",
                "source": "NOAA Ocean Top New",
                "content_type": content_type,
                "category": category or "All Categories",
                "time_period": time_period,
                "limit": limit,
                "total_results": len(results),
                "ocean_highlights": {
                    "active_expeditions": "15+ ongoing missions",
                    "new_species_discovered": "200+ this year",
                    "ocean_coverage_mapped": "25% of seafloor",
                    "research_vessels": "50+ active worldwide",
                    "top_categories": ["Discoveries", "Expeditions", "Research", "Species", "Phenomena"]
                },
                "results": results,
                "generated_at": "2024-01-15T10:30:00Z"
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Lỗi khi lấy top new NOAA Ocean: {str(e)}")
            return {
                "status": "error",
                "source": "NOAA Ocean Top New",
                "message": str(e),
                "fallback_url": "https://oceanexplorer.noaa.gov/"
            }
