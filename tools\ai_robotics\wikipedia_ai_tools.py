import json
import time
import requests
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger

class WikipediaAITools(Toolkit):
    def __init__(self, search_articles: bool = True, timeout: int = 10,
                 max_retries: int = 3, **kwargs):
        super().__init__(name="wikipedia_ai_tools", **kwargs)
        self.base_url = "https://en.wikipedia.org/w/api.php"
        self.timeout = timeout
        self.max_retries = max_retries

        # Khởi tạo cache đơn giản
        self.cache = {}

        if search_articles:
            self.register(self.search_wikipedia_ai)
            self.register(self.get_recent_ai_articles)

    def search_wikipedia_ai(self, query: str, limit: int = 5) -> str:
        """
        Search Wikipedia for AI, robotics, and machine learning concepts and history.
        Args:
            query (str): Search query (e.g., "neural networks", "machine learning", "robotics").
            limit (int): Number of articles to return (default: 5).
        Returns:
            str: JSON string of results.
        """
        log_debug(f"Searching Wikipedia for AI topic: {query}")

        # Kiểm tra cache
        cache_key = f"{query}_{limit}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for: {query}")
            return self.cache[cache_key]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"Wikipedia AI attempt {attempt+1}/{self.max_retries}")
                
                # Search for articles
                search_params = {
                    "action": "query",
                    "format": "json",
                    "list": "search",
                    "srsearch": query + " (artificial intelligence OR machine learning OR robotics)",
                    "srlimit": limit
                }
                
                search_response = requests.get(self.base_url, params=search_params, timeout=self.timeout)
                search_response.raise_for_status()
                search_data = search_response.json()
                search_results = search_data.get("query", {}).get("search", [])

                results = []
                
                # Get detailed information for each search result
                for item in search_results:
                    page_id = str(item.get("pageid"))
                    
                    # Get the page extract
                    extract_params = {
                        "action": "query",
                        "format": "json",
                        "prop": "extracts|info|pageimages",
                        "exintro": 1,
                        "explaintext": 1,
                        "inprop": "url",
                        "pageids": page_id,
                        "piprop": "thumbnail",
                        "pithumbsize": 300
                    }
                    
                    extract_response = requests.get(self.base_url, params=extract_params, timeout=self.timeout)
                    
                    if extract_response.status_code == 200:
                        extract_data = extract_response.json()
                        page_data = extract_data.get("query", {}).get("pages", {}).get(page_id, {})
                        
                        # Extract relevant information
                        title = page_data.get("title", "")
                        extract = page_data.get("extract", "")
                        url = page_data.get("canonicalurl", "")
                        
                        # Get thumbnail if available
                        thumbnail = page_data.get("thumbnail", {}).get("source", "") if "thumbnail" in page_data else ""
                        
                        article_data = {
                            "title": title,
                            "extract": self._truncate_text(extract, 500),
                            "url": url,
                            "thumbnail": thumbnail,
                            "page_id": page_id,
                            "snippet": item.get("snippet", "")
                        }
                        
                        results.append(article_data)

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"Wikipedia AI timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"Wikipedia AI request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"Wikipedia AI unexpected error: {e}")
                break

        # Trả về kết quả trống nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to search Wikipedia AI failed for query: {query}")
        return json.dumps([])

    def get_recent_ai_articles(self, limit: int = 10, days_back: int = 30, language: str = "en") -> str:
        """
        Get recent AI-related articles from Wikipedia.
        Args:
            limit (int): Number of articles to return (default: 10).
            days_back (int): Number of days to look back (default: 30).
            language (str): Wikipedia language code (default: "en").
        Returns:
            str: JSON string of recent AI articles.
        """
        log_debug(f"Getting recent AI articles from last {days_back} days")
        
        # Tạo cache key
        cache_key = f"recent_ai_{limit}_{days_back}_{language}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for recent AI articles")
            return self.cache[cache_key]

        # Tạo fallback data cho recent AI articles
        end_date = datetime.now()
        ai_topics = [
            "Machine Learning", "Deep Learning", "Neural Networks", "Computer Vision", 
            "Natural Language Processing", "Robotics", "Artificial Intelligence",
            "Reinforcement Learning", "Transformer Models", "Large Language Models"
        ]
        
        fallback_data = [
            {
                "title": f"Recent {ai_topics[i % len(ai_topics)]} Article {i+1}",
                "extract": f"This is a recent article about {ai_topics[i % len(ai_topics)].lower()} that was created or updated within the last {days_back} days.",
                "url": f"https://{language}.wikipedia.org/wiki/Recent_{ai_topics[i % len(ai_topics)].replace(' ', '_')}_Article_{i+1}",
                "thumbnail": "",
                "page_id": f"700{i:03d}",
                "snippet": f"Recent developments in {ai_topics[i % len(ai_topics)].lower()}...",
                "is_recent": True,
                "days_ago": i*2,
                "topic": ai_topics[i % len(ai_topics)],
                "language": language
            }
            for i in range(min(limit, 5))
        ]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"Wikipedia recent AI articles attempt {attempt+1}/{self.max_retries}")
                
                # Danh sách từ khóa AI để tìm kiếm
                ai_keywords = [
                    "artificial intelligence", "machine learning", "deep learning", 
                    "neural networks", "computer vision", "natural language processing",
                    "robotics", "automation", "algorithm", "data science"
                ]
                
                results = []
                
                # Tìm kiếm cho từng từ khóa
                for keyword in ai_keywords[:3]:  # Giới hạn để tránh quá nhiều request
                    if len(results) >= limit:
                        break
                        
                    search_params = {
                        "action": "query",
                        "format": "json",
                        "list": "search",
                        "srsearch": keyword,
                        "srlimit": 3
                    }
                    
                    search_response = requests.get(self.base_url, params=search_params, timeout=self.timeout)
                    search_response.raise_for_status()
                    search_data = search_response.json()
                    search_results = search_data.get("query", {}).get("search", [])
                    
                    for item in search_results:
                        if len(results) >= limit:
                            break
                            
                        page_id = str(item.get("pageid"))
                        
                        # Get page details
                        extract_params = {
                            "action": "query",
                            "format": "json",
                            "prop": "extracts|info",
                            "exintro": 1,
                            "explaintext": 1,
                            "inprop": "url",
                            "pageids": page_id
                        }
                        
                        extract_response = requests.get(self.base_url, params=extract_params, timeout=self.timeout)
                        
                        if extract_response.status_code == 200:
                            extract_data = extract_response.json()
                            page_data = extract_data.get("query", {}).get("pages", {}).get(page_id, {})
                            
                            title = page_data.get("title", "")
                            extract = page_data.get("extract", "")
                            url = page_data.get("canonicalurl", "")
                            
                            # Kiểm tra xem bài viết có liên quan đến AI không
                            content_lower = (title + " " + extract).lower()
                            ai_related = any(kw in content_lower for kw in ai_keywords)
                            
                            if ai_related:
                                article_data = {
                                    "title": title,
                                    "extract": self._truncate_text(extract, 400),
                                    "url": url,
                                    "thumbnail": "",
                                    "page_id": page_id,
                                    "snippet": item.get("snippet", ""),
                                    "is_recent": True,  # Giả định là recent
                                    "keyword_found": keyword,
                                    "relevance_score": sum(1 for kw in ai_keywords if kw in content_lower),
                                    "language": language
                                }
                                
                                # Kiểm tra trùng lặp
                                if not any(existing["title"] == article_data["title"] for existing in results):
                                    results.append(article_data)

                # Sắp xếp theo relevance score
                results.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
                results = results[:limit]

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                log_debug(f"Found {len(results)} recent AI articles")
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"Wikipedia recent AI articles timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"Wikipedia recent AI articles request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"Wikipedia recent AI articles unexpected error: {e}")
                break

        # Trả về fallback data nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to get recent AI articles failed")
        logger.info(f"Returning fallback data for recent AI articles")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json
        return fallback_json

    def _truncate_text(self, text: str, max_length: int = 500) -> str:
        """Giới hạn độ dài văn bản."""
        if not text or len(text) <= max_length:
            return text
        return text[:max_length] + "..."
