#!/usr/bin/env python3
"""
Test script cho Cosmology Facebook Team
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.append('.')

def test_cosmology_team_import():
    """Test import của Cosmology team"""
    print("=== Testing Cosmology Team Import ===")

    try:
        from cosmology_fb import CosmologyFbWorkflow
        print("✅ CosmologyFbWorkflow import successful!")

        # Test tạo workflow instance
        workflow = CosmologyFbWorkflow()
        print("✅ CosmologyFbWorkflow instance created successfully!")

        # Test các agents
        print(f"✅ Sub Question Agent: {workflow.sub_question_agent.name}")
        print(f"✅ ArXiv Agent: {workflow.arxiv_agent.name}")
        print(f"✅ CERN Agent: {workflow.cern_agent.name}")
        print(f"✅ INSPIRE-HEP Agent: {workflow.inspirehep_agent.name}")
        print(f"✅ NASA ADS Agent: {workflow.nasa_ads_agent.name}")
        print(f"✅ Wikipedia Agent: {workflow.wikipedia_agent.name}")
        print(f"✅ Calculator Agent: {workflow.calculator_agent.name}")
        print(f"✅ Writer Agent: {workflow.writer_agent.name}")

        # Test team
        print(f"✅ Research Team: {workflow.cosmology_research_team.name}")
        print(f"✅ Team Members: {len(workflow.cosmology_research_team.members)} agents")

        return True

    except Exception as e:
        print(f"❌ Cosmology team import error: {e}")
        return False

def test_cosmology_tools_import():
    """Test import của các cosmology tools"""
    print("\n=== Testing Cosmology Tools Import ===")

    tools_to_test = [
        ("tools.cosmology.arxiv_tools", "ArXivTools"),
        ("tools.cosmology.cern_opendata_tools", "CERNOpenDataTools"),
        ("tools.cosmology.cosmology_search_toolkit", "CosmologySearchToolkit"),
        ("tools.cosmology.inspirehep_tools", "InspireHEPTools"),
        ("tools.cosmology.nasa_ads_physics_tools", "NASAADSPhysicsTools"),
        ("tools.cosmology.wikipedia_physics_tools", "WikipediaPhysicsTools"),
        ("tools.cosmology.cosmic_evolution_calculator", "CosmicEvolutionCalculator"),
        ("tools.writer.writer_fb", "WriterFbTools")
    ]

    success_count = 0
    for module_name, class_name in tools_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            tool_class = getattr(module, class_name)
            print(f"✅ {class_name} import successful!")
            success_count += 1
        except Exception as e:
            print(f"❌ {class_name} import error: {e}")

    print(f"\n📊 Tools Import Summary: {success_count}/{len(tools_to_test)} successful")
    return success_count == len(tools_to_test)

def test_workflow_basic_functionality():
    """Test basic functionality của workflow"""
    print("\n=== Testing Workflow Basic Functionality ===")

    try:
        from cosmology_fb import CosmologyFbWorkflow
        workflow = CosmologyFbWorkflow()

        # Test memory
        print(f"✅ Memory initialized: {workflow.memory is not None}")

        # Test knowledge base
        print(f"✅ Knowledge base initialized: {workflow.knowledge is not None}")

        # Test agents có tools
        agents_with_tools = [
            workflow.sub_question_agent,
            workflow.arxiv_agent,
            workflow.cern_agent,
            workflow.inspirehep_agent,
            workflow.nasa_ads_agent,
            workflow.wikipedia_agent,
            workflow.calculator_agent,
            workflow.writer_agent
        ]

        for agent in agents_with_tools:
            if hasattr(agent, 'tools') and agent.tools:
                print(f"✅ {agent.name} has {len(agent.tools)} tool(s)")
            else:
                print(f"⚠️ {agent.name} has no tools")

        return True

    except Exception as e:
        print(f"❌ Workflow functionality test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Cosmology Facebook Team Tests\n")

    test_results = []

    # Test 1: Import
    test_results.append(test_cosmology_team_import())

    # Test 2: Tools Import
    test_results.append(test_cosmology_tools_import())

    # Test 3: Basic Functionality
    test_results.append(test_workflow_basic_functionality())

    # Summary
    print("\n" + "="*50)
    print("📋 TEST SUMMARY")
    print("="*50)

    passed = sum(test_results)
    total = len(test_results)

    print(f"✅ Tests Passed: {passed}/{total}")

    if passed == total:
        print("🎉 All tests passed! Cosmology Facebook Team is ready to use!")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
