"""
Công cụ tìm kiếm Wikipedia chuyên biệt cho vũ trụ học và lịch sử vật lý.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from agno.tools import Toolkit
from agno.tools.wikipedia import WikipediaTools
from agno.utils.log import log_debug, logger

class WikipediaPhysicsTools(Toolkit):
    """Công cụ tìm kiếm Wikipedia chuyên biệt cho vũ trụ học và lịch sử vật lý."""

    def __init__(self, search_wikipedia: bool = True, **kwargs):
        """
        Khởi tạo công cụ tìm kiếm Wikipedia cho vũ trụ học và lịch sử vật lý.

        Args:
            search_wikipedia: <PERSON><PERSON> đăng ký phương thức tìm kiếm hay không
            **kwargs: <PERSON><PERSON><PERSON> tham số khác
        """
        super().__init__(name="wikipedia_physics_tools", **kwargs)

        # Khởi tạo công cụ Wikipedia cơ bản
        self.base_wikipedia_tools = WikipediaTools()

        # <PERSON>h sách từ khóa vật lý phổ biến
        self.physics_keywords = [
            "physics", "cosmology", "quantum", "relativity", "particle",
            "string theory", "dark matter", "dark energy", "black hole", "gravity",
            "spacetime", "universe", "big bang", "higgs boson", "standard model",
            "quantum mechanics", "quantum field theory", "general relativity", "special relativity",
            "einstein", "feynman", "hawking", "planck", "bohr", "heisenberg",
            "cern", "lhc", "particle accelerator", "quantum gravity", "supersymmetry"
        ]

        if search_wikipedia:
            self.register(self.search_physics_wikipedia)

    def search_physics_wikipedia(self, query: str) -> str:
        """
        Tìm kiếm thông tin vũ trụ học và lịch sử vật lý trên Wikipedia.

        Args:
            query: Từ khóa tìm kiếm

        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        log_debug(f"Searching physics Wikipedia for: {query}")

        try:
            # Tối ưu hóa truy vấn với từ khóa vật lý
            optimized_query = self._optimize_physics_query(query)
            log_debug(f"Optimized query: {optimized_query}")

            # Tìm kiếm Wikipedia
            results = self.base_wikipedia_tools.search_wikipedia(query=optimized_query)

            # Phân tích kết quả JSON
            try:
                results_dict = json.loads(results)
            except Exception as e:
                logger.error(f"Failed to parse Wikipedia results as JSON: {e}")
                return json.dumps({"error": "WikipediaTools did not return valid JSON"})

            # Đảm bảo kết quả là list hoặc chuẩn hóa nếu là dict
            if isinstance(results_dict, dict):
                # Nếu là dict, chuyển thành list và chuẩn hóa trường
                if "content" in results_dict:
                    results_dict = [{
                        "summary": results_dict.get("content", ""),
                        "title": results_dict.get("name", ""),
                        **results_dict
                    }]
                else:
                    results_dict = [results_dict]
            elif not isinstance(results_dict, list):
                logger.error(f"Unexpected results structure: {type(results_dict)}: {results_dict}")
                return json.dumps({"error": "Unexpected results structure", "data": results_dict})

            # Kiểm tra từng phần tử là dict và có trường 'summary'
            for i, result in enumerate(results_dict):
                if not isinstance(result, dict):
                    logger.error(f"Result at index {i} is not a dict: {result}")
                    continue
                summary = result.get("summary", "")
                result["physics_relevance"] = self._calculate_physics_relevance(summary)
                result["key_concepts"] = self._extract_physics_concepts(summary)
                result["historical_context"] = self._extract_historical_context(summary)

            # Sắp xếp lại theo độ liên quan đến vật lý
            results_dict = sorted(results_dict, key=lambda x: x.get("physics_relevance", 0), reverse=True)

            return json.dumps(results_dict, indent=4, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error searching physics Wikipedia: {e}")
            return json.dumps({"error": str(e)})

    def _optimize_physics_query(self, query: str) -> str:
        """
        Tối ưu hóa truy vấn với từ khóa vật lý.

        Args:
            query: Truy vấn gốc

        Returns:
            Truy vấn đã tối ưu hóa
        """
        # Kiểm tra xem truy vấn đã có từ khóa vật lý chưa
        has_physics_keyword = any(keyword.lower() in query.lower() for keyword in self.physics_keywords)

        # Nếu chưa có, thêm từ khóa "physics" hoặc "cosmology"
        if not has_physics_keyword:
            if "history" in query.lower():
                return f"{query} physics history"
            elif "what is" in query.lower() or "definition" in query.lower():
                return f"{query} physics"
            else:
                return f"{query} in physics"

        return query

    def _calculate_physics_relevance(self, text: str) -> float:
        """
        Tính toán độ liên quan đến vật lý của văn bản.

        Args:
            text: Văn bản cần tính toán

        Returns:
            Điểm liên quan (0-1)
        """
        if not text:
            return 0.0

        text_lower = text.lower()
        keyword_count = sum(1 for keyword in self.physics_keywords if keyword.lower() in text_lower)

        # Tính điểm dựa trên số lượng từ khóa và độ dài văn bản
        relevance = min(1.0, keyword_count / 5)  # Tối đa 1.0

        return relevance

    def _extract_physics_concepts(self, text: str) -> List[str]:
        """
        Trích xuất các khái niệm vật lý từ văn bản.

        Args:
            text: Văn bản cần trích xuất

        Returns:
            Danh sách các khái niệm
        """
        if not text:
            return []

        text_lower = text.lower()
        concepts = [keyword for keyword in self.physics_keywords if keyword.lower() in text_lower]

        return concepts[:5]  # Giới hạn 5 khái niệm

    def _extract_historical_context(self, text: str) -> str:
        """
        Trích xuất bối cảnh lịch sử từ văn bản.

        Args:
            text: Văn bản cần trích xuất

        Returns:
            Bối cảnh lịch sử
        """
        if not text:
            return ""

        # Danh sách các nhà vật lý nổi tiếng
        physicists = ["Einstein", "Feynman", "Hawking", "Planck", "Bohr", "Heisenberg",
                      "Newton", "Maxwell", "Dirac", "Schrödinger", "Pauli", "Fermi"]

        # Danh sách các năm quan trọng trong lịch sử vật lý
        years = ["1905", "1915", "1925", "1927", "1932", "1935", "1947", "1964", "1967", "1974", "2012"]

        # Tìm kiếm các nhà vật lý và năm trong văn bản
        found_physicists = [p for p in physicists if p in text]
        found_years = [y for y in years if y in text]

        if found_physicists and found_years:
            return f"Related to {', '.join(found_physicists)} and years {', '.join(found_years)}"
        elif found_physicists:
            return f"Related to {', '.join(found_physicists)}"
        elif found_years:
            return f"From around {', '.join(found_years)}"
        else:
            return ""


if __name__ == "__main__":
    # Test công cụ
    tools = WikipediaPhysicsTools()
    result = tools.search_physics_wikipedia("quantum mechanics")
    print(result)