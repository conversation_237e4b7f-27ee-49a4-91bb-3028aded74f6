# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math
from datetime import datetime, timedelta

class ClimateTrendAnalyzer(Toolkit):
    """
    Climate Trend Analyzer cho phân tích xu hướng khí hậu và dự đoán các thay đổi môi trường.
    """

    def __init__(self, enable_analysis: bool = True, **kwargs):
        super().__init__(
            name="climate_trend_analyzer",
            **kwargs
        )
        
        # Historical climate data (simplified for demonstration)
        self.historical_data = {
            "global_temperature": {
                "1990": 14.4, "2000": 14.7, "2010": 14.9, "2020": 15.2, "2023": 15.4
            },
            "sea_level": {
                "1990": 0, "2000": 3.2, "2010": 6.8, "2020": 10.5, "2023": 12.1  # cm above 1990 baseline
            },
            "co2_concentration": {
                "1990": 354, "2000": 369, "2010": 389, "2020": 414, "2023": 421  # ppm
            },
            "arctic_ice": {
                "1990": 100, "2000": 95, "2010": 85, "2020": 75, "2023": 70  # % of 1990 extent
            }
        }
        
        # Regional climate factors
        self.regional_factors = {
            "tropical": {"temp_sensitivity": 1.2, "precipitation_change": 1.1},
            "temperate": {"temp_sensitivity": 1.0, "precipitation_change": 1.0},
            "arctic": {"temp_sensitivity": 2.0, "precipitation_change": 1.3},
            "desert": {"temp_sensitivity": 1.5, "precipitation_change": 0.8},
            "coastal": {"temp_sensitivity": 0.8, "precipitation_change": 1.2}
        }
        
        if enable_analysis:
            self.register(self.analyze_temperature_trends)
            self.register(self.predict_sea_level_rise)
            self.register(self.assess_drought_risk)
            self.register(self.evaluate_extreme_weather_probability)

    def analyze_temperature_trends(self, location: str, years: int = 30, 
                                 region_type: str = "temperate") -> str:
        """
        Phân tích xu hướng nhiệt độ cho một khu vực.
        
        Args:
            location: Tên địa điểm
            years: Số năm để phân tích (default: 30)
            region_type: Loại khu vực ('tropical', 'temperate', 'arctic', 'desert', 'coastal')
            
        Returns:
            Chuỗi JSON chứa phân tích xu hướng nhiệt độ
        """
        log_debug(f"Analyzing temperature trends for {location} over {years} years")
        
        try:
            # Lấy regional factor
            factor = self.regional_factors.get(region_type, {"temp_sensitivity": 1.0})
            
            # Tính toán temperature trends
            global_warming_rate = 0.18  # °C per decade (IPCC estimate)
            regional_rate = global_warming_rate * factor["temp_sensitivity"]
            
            # Historical analysis
            temp_increase_total = (years / 10) * regional_rate
            current_baseline = 15.0  # Global average baseline
            
            # Future projections
            projections = {}
            for year_ahead in [10, 20, 50]:
                future_increase = (year_ahead / 10) * regional_rate
                projections[f"{year_ahead}_years"] = {
                    "temperature_increase": round(future_increase, 2),
                    "probability_extreme_heat": min(95, 20 + (future_increase * 15)),
                    "growing_season_change": round(future_increase * 7, 0)  # days
                }
            
            # Risk assessment
            risk_level = "Low"
            if temp_increase_total > 2.0:
                risk_level = "High"
            elif temp_increase_total > 1.0:
                risk_level = "Moderate"
            
            result = {
                "location": location,
                "region_type": region_type,
                "analysis_period_years": years,
                "historical_trends": {
                    "temperature_increase_total": round(temp_increase_total, 2),
                    "warming_rate_per_decade": round(regional_rate, 2),
                    "baseline_temperature": current_baseline
                },
                "future_projections": projections,
                "risk_assessment": {
                    "overall_risk": risk_level,
                    "heat_stress_risk": "High" if temp_increase_total > 1.5 else "Moderate",
                    "ecosystem_impact": "Significant" if temp_increase_total > 2.0 else "Moderate"
                },
                "impacts": {
                    "agriculture": self._assess_agricultural_impact(temp_increase_total),
                    "water_resources": self._assess_water_impact(temp_increase_total),
                    "human_health": self._assess_health_impact(temp_increase_total)
                },
                "adaptation_strategies": self._get_adaptation_strategies(region_type, temp_increase_total),
                "data_sources": ["IPCC AR6", "NASA GISS", "NOAA Climate Data"],
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error analyzing temperature trends: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze temperature trends: {str(e)}"
            }, indent=4)

    def predict_sea_level_rise(self, location: str, timeframe: int = 50, 
                             coastal_type: str = "open_coast") -> str:
        """
        Dự đoán mức tăng của mực nước biển.
        
        Args:
            location: Tên địa điểm ven biển
            timeframe: Khung thời gian dự đoán (năm)
            coastal_type: Loại bờ biển ('open_coast', 'bay', 'delta', 'island')
            
        Returns:
            Chuỗi JSON chứa dự đoán mực nước biển
        """
        log_debug(f"Predicting sea level rise for {location} over {timeframe} years")
        
        try:
            # Sea level rise rates (mm/year)
            base_rate = 3.3  # Global average
            
            # Coastal type multipliers
            coastal_multipliers = {
                "open_coast": 1.0,
                "bay": 1.2,
                "delta": 1.5,
                "island": 1.3
            }
            
            multiplier = coastal_multipliers.get(coastal_type, 1.0)
            local_rate = base_rate * multiplier
            
            # Calculate projections
            total_rise_mm = local_rate * timeframe
            total_rise_cm = total_rise_mm / 10
            
            # Risk scenarios
            scenarios = {
                "conservative": total_rise_cm * 0.8,
                "moderate": total_rise_cm,
                "aggressive": total_rise_cm * 1.5
            }
            
            # Impact assessment
            flood_risk = "Low"
            if total_rise_cm > 30:
                flood_risk = "High"
            elif total_rise_cm > 15:
                flood_risk = "Moderate"
            
            result = {
                "location": location,
                "coastal_type": coastal_type,
                "timeframe_years": timeframe,
                "predictions": {
                    "total_rise_cm": round(total_rise_cm, 1),
                    "annual_rate_mm": round(local_rate, 1),
                    "scenarios": {k: round(v, 1) for k, v in scenarios.items()}
                },
                "risk_assessment": {
                    "flood_risk": flood_risk,
                    "erosion_risk": "High" if total_rise_cm > 20 else "Moderate",
                    "infrastructure_risk": "Critical" if total_rise_cm > 25 else "Moderate"
                },
                "impacts": {
                    "coastal_flooding": f"Increased frequency by {min(500, int(total_rise_cm * 10))}%",
                    "saltwater_intrusion": "Significant" if total_rise_cm > 15 else "Moderate",
                    "ecosystem_loss": f"{min(50, int(total_rise_cm * 2))}% of coastal wetlands at risk"
                },
                "adaptation_measures": self._get_coastal_adaptation_measures(total_rise_cm),
                "confidence_level": "Medium" if timeframe <= 30 else "Low",
                "data_sources": ["IPCC Sea Level Projections", "Satellite Altimetry", "Tide Gauge Data"],
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error predicting sea level rise: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to predict sea level rise: {str(e)}"
            }, indent=4)

    def assess_drought_risk(self, region: str, season: str = "summer", 
                          climate_zone: str = "temperate") -> str:
        """
        Đánh giá nguy cơ hạn hán.
        
        Args:
            region: Tên khu vực
            season: Mùa ('spring', 'summer', 'autumn', 'winter')
            climate_zone: Vùng khí hậu
            
        Returns:
            Chuỗi JSON chứa đánh giá nguy cơ hạn hán
        """
        log_debug(f"Assessing drought risk for {region} in {season}")
        
        try:
            # Seasonal risk factors
            seasonal_risk = {
                "spring": 0.3, "summer": 0.8, "autumn": 0.4, "winter": 0.2
            }
            
            # Climate zone factors
            zone_risk = {
                "arid": 0.9, "semi_arid": 0.7, "temperate": 0.4, 
                "tropical": 0.3, "polar": 0.1
            }
            
            base_risk = seasonal_risk.get(season, 0.5) * zone_risk.get(climate_zone, 0.5)
            
            # Calculate drought probability
            drought_probability = min(95, base_risk * 100)
            
            # Severity assessment
            if drought_probability > 70:
                severity = "High"
            elif drought_probability > 40:
                severity = "Moderate"
            else:
                severity = "Low"
            
            result = {
                "region": region,
                "season": season,
                "climate_zone": climate_zone,
                "drought_assessment": {
                    "probability_percent": round(drought_probability, 1),
                    "severity_level": severity,
                    "duration_estimate": f"{2 + int(base_risk * 10)}-{6 + int(base_risk * 20)} months",
                    "confidence": "Medium"
                },
                "contributing_factors": [
                    "Rising temperatures",
                    "Changing precipitation patterns",
                    "Increased evaporation rates",
                    "Soil moisture depletion"
                ],
                "potential_impacts": {
                    "agriculture": f"{int(base_risk * 50)}% crop yield reduction risk",
                    "water_supply": "Moderate to severe stress" if base_risk > 0.6 else "Mild stress",
                    "ecosystems": "Vegetation stress and fire risk" if base_risk > 0.5 else "Minimal impact",
                    "economic": f"${int(base_risk * 1000)}M potential losses"
                },
                "mitigation_strategies": self._get_drought_mitigation_strategies(severity),
                "monitoring_indicators": [
                    "Soil moisture levels",
                    "Precipitation patterns",
                    "Temperature anomalies",
                    "Vegetation health indices"
                ],
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error assessing drought risk: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to assess drought risk: {str(e)}"
            }, indent=4)

    def evaluate_extreme_weather_probability(self, location: str, event_type: str, 
                                           timeframe: int = 10) -> str:
        """
        Đánh giá xác suất xảy ra thời tiết cực đoan.
        
        Args:
            location: Địa điểm
            event_type: Loại sự kiện ('hurricane', 'heatwave', 'flood', 'wildfire', 'tornado')
            timeframe: Khung thời gian (năm)
            
        Returns:
            Chuỗi JSON chứa đánh giá xác suất thời tiết cực đoan
        """
        log_debug(f"Evaluating {event_type} probability for {location} over {timeframe} years")
        
        try:
            # Base probabilities (annual) for different events
            base_probabilities = {
                "hurricane": 0.15,
                "heatwave": 0.25,
                "flood": 0.20,
                "wildfire": 0.18,
                "tornado": 0.12,
                "blizzard": 0.10
            }
            
            annual_prob = base_probabilities.get(event_type, 0.15)
            
            # Climate change multiplier (events becoming more frequent)
            climate_multiplier = 1.5  # 50% increase due to climate change
            adjusted_prob = min(0.95, annual_prob * climate_multiplier)
            
            # Calculate probability over timeframe
            cumulative_prob = 1 - (1 - adjusted_prob) ** timeframe
            
            # Severity assessment
            severity_factors = {
                "hurricane": {"intensity": "Category 3-4", "damage": "Severe"},
                "heatwave": {"intensity": "40°C+", "damage": "Health risks"},
                "flood": {"intensity": "100-year flood", "damage": "Infrastructure"},
                "wildfire": {"intensity": "Large scale", "damage": "Ecosystem loss"},
                "tornado": {"intensity": "EF2-EF3", "damage": "Localized severe"}
            }
            
            event_details = severity_factors.get(event_type, {"intensity": "Moderate", "damage": "Variable"})
            
            result = {
                "location": location,
                "event_type": event_type,
                "timeframe_years": timeframe,
                "probability_assessment": {
                    "annual_probability": round(adjusted_prob * 100, 1),
                    "cumulative_probability": round(cumulative_prob * 100, 1),
                    "return_period_years": round(1 / adjusted_prob, 1),
                    "climate_change_factor": climate_multiplier
                },
                "event_characteristics": {
                    "expected_intensity": event_details["intensity"],
                    "potential_damage": event_details["damage"],
                    "typical_duration": self._get_event_duration(event_type),
                    "affected_area": self._get_affected_area(event_type)
                },
                "risk_factors": self._get_risk_factors(event_type),
                "preparedness_measures": self._get_preparedness_measures(event_type),
                "early_warning_systems": self._get_warning_systems(event_type),
                "confidence_level": "Medium",
                "data_sources": ["Historical weather records", "Climate models", "IPCC projections"],
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error evaluating extreme weather probability: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to evaluate extreme weather probability: {str(e)}"
            }, indent=4)

    # Helper methods
    def _assess_agricultural_impact(self, temp_increase: float) -> str:
        if temp_increase > 2.0:
            return "Severe crop yield reductions, shifting growing zones"
        elif temp_increase > 1.0:
            return "Moderate impacts on crop productivity"
        else:
            return "Minor changes in growing conditions"

    def _assess_water_impact(self, temp_increase: float) -> str:
        if temp_increase > 2.0:
            return "Significant water stress, increased demand"
        elif temp_increase > 1.0:
            return "Moderate water resource pressure"
        else:
            return "Minimal impact on water availability"

    def _assess_health_impact(self, temp_increase: float) -> str:
        if temp_increase > 2.0:
            return "High heat stress, increased mortality risk"
        elif temp_increase > 1.0:
            return "Moderate health risks from heat"
        else:
            return "Low additional health risks"

    def _get_adaptation_strategies(self, region_type: str, temp_increase: float) -> List[str]:
        strategies = ["Improve building insulation", "Develop heat action plans", "Enhance water conservation"]
        if region_type == "coastal":
            strategies.append("Coastal protection measures")
        if temp_increase > 1.5:
            strategies.extend(["Relocate vulnerable populations", "Develop drought-resistant crops"])
        return strategies

    def _get_coastal_adaptation_measures(self, rise_cm: float) -> List[str]:
        measures = ["Beach nourishment", "Improved drainage systems"]
        if rise_cm > 15:
            measures.extend(["Sea walls", "Managed retreat"])
        if rise_cm > 25:
            measures.extend(["Floating infrastructure", "Relocation planning"])
        return measures

    def _get_drought_mitigation_strategies(self, severity: str) -> List[str]:
        strategies = ["Water conservation", "Drought-resistant crops", "Improved irrigation"]
        if severity == "High":
            strategies.extend(["Emergency water supplies", "Crop insurance", "Alternative livelihoods"])
        return strategies

    def _get_event_duration(self, event_type: str) -> str:
        durations = {
            "hurricane": "1-3 days", "heatwave": "3-7 days", "flood": "1-5 days",
            "wildfire": "1-4 weeks", "tornado": "Minutes", "blizzard": "1-3 days"
        }
        return durations.get(event_type, "Variable")

    def _get_affected_area(self, event_type: str) -> str:
        areas = {
            "hurricane": "100-500 km radius", "heatwave": "Regional", "flood": "Watershed",
            "wildfire": "10-1000 km²", "tornado": "1-50 km path", "blizzard": "Regional"
        }
        return areas.get(event_type, "Variable")

    def _get_risk_factors(self, event_type: str) -> List[str]:
        return ["Climate change", "Geographic location", "Seasonal patterns", "Local topography"]

    def _get_preparedness_measures(self, event_type: str) -> List[str]:
        return ["Emergency planning", "Early warning systems", "Infrastructure hardening", "Community education"]

    def _get_warning_systems(self, event_type: str) -> List[str]:
        return ["Meteorological monitoring", "Satellite surveillance", "Ground-based sensors", "Predictive modeling"]
