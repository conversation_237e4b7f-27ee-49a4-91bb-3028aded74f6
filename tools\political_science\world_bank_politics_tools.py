from typing import Dict, Any, Optional, List, Union
from agno.tools import Toolkit
from agno.utils.log import logger
import requests
import json
from datetime import datetime, timedelta
from urllib.parse import urlencode, quote

# <PERSON><PERSON>c hằng số
WB_API_BASE = "http://api.worldbank.org/v2"
WB_DATA_PORTAL = "https://data.worldbank.org"
CACHE_EXPIRY_DAYS = 7

class WorldBankPoliticsTool(Toolkit):
    """
    Công cụ tìm kiếm dữ liệu chính trị, quản trị và phát triển từ World Bank
    """

    def __init__(self):
        super().__init__(
            name="Công cụ dữ liệu Ngân hàng Thế giới",
            tools=[
                self.search_world_bank_politics,
                self.get_governance_indicators,
                self.get_country_data,
                self.search_indicators
            ]
        )
        self.base_url = WB_API_BASE
        self.data_portal = WB_DATA_PORTAL
        self.timeout = 15
        self.cache = {}
        self._load_cache()

    def _load_cache(self) -> None:
        """Tải dữ liệu cache từ file"""
        try:
            with open(".wb_cache.json", 'r', encoding='utf-8') as f:
                self.cache = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            self.cache = {}

    def _save_cache(self) -> None:
        """Lưu dữ liệu cache vào file"""
        with open(".wb_cache.json", 'w', encoding='utf-8') as f:
            json.dump(self.cache, f, ensure_ascii=False, indent=2)

    def _get_cache(self, key: str) -> Any:
        """Lấy dữ liệu từ cache"""
        cached = self.cache.get(key)
        if cached and datetime.now().timestamp() < cached.get('expires', 0):
            return cached['data']
        return None

    def _set_cache(self, key: str, data: Any, ttl: int = 86400) -> None:
        """Lưu dữ liệu vào cache"""
        self.cache[key] = {
            'data': data,
            'expires': datetime.now().timestamp() + ttl
        }
        self._save_cache()

    async def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Any:
        """Gửi yêu cầu đến World Bank API"""
        try:
            url = f"{self.base_url}/{endpoint}"
            cache_key = f"{endpoint}?{urlencode(params or {})}"
            
            # Kiểm tra cache
            cached = self._get_cache(cache_key)
            if cached is not None:
                return cached
            
            # Gửi yêu cầu mới
            response = requests.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            data = response.json()
            
            # Lưu vào cache (1 tuần cho dữ liệu tĩnh, 1 ngày cho dữ liệu động)
            ttl = 86400 * 7 if 'indicator' in endpoint else 86400
            self._set_cache(cache_key, data, ttl)
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Lỗi kết nối World Bank API: {str(e)}")
            raise

    async def search_indicators(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm các chỉ số phù hợp với từ khóa
        
        Parameters:
        - query: Từ khóa tìm kiếm (ví dụ: 'governance', 'GDP')
        - limit: Số lượng kết quả tối đa
        
        Returns:
        - Danh sách các chỉ số phù hợp
        """
        try:
            data = await self._make_request("indicator", {"format": "json", "per_page": limit, "q": query})
            indicators = []
            
            if isinstance(data, list) and len(data) > 1:
                for item in data[1]:
                    indicators.append({
                        "id": item.get("id"),
                        "name": item.get("name"),
                        "source": item.get("source", {}).get("value"),
                        "description": item.get("sourceNote"),
                        "url": f"{self.data_portal}/indicator/{item.get('id')}"
                    })
            
            return {
                "status": "success",
                "query": query,
                "results": indicators,
                "results_count": len(indicators)
            }
            
        except Exception as e:
            logger.error(f"Lỗi khi tìm kiếm chỉ số: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "query": query
            }
            
    async def search_world_bank_politics(
        self,
        indicator: str,
        country: Optional[str] = None,
        date: Optional[str] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        Tìm kiếm dữ liệu từ World Bank về các chỉ số chính trị, quản trị và phát triển.
        
        Parameters:
        - indicator: Mã chỉ số hoặc từ khóa tìm kiếm (ví dụ: 'GE.EST', 'governance')
        - country: Mã quốc gia (ví dụ: 'VN', 'USA') hoặc 'all' cho tất cả
        - date: Năm hoặc khoảng năm (ví dụ: '2020' hoặc '2010:2020')
        - limit: Số lượng kết quả tối đa
        
        Returns:
        - Dict chứa kết quả tìm kiếm
        """
        logger.info(f"Tìm kiếm dữ liệu World Bank: {indicator}, quốc gia={country}, năm={date}")
        
        try:
            # Kiểm tra xem indicator có phải là mã hợp lệ không
            if not indicator.isupper() or "." not in indicator:
                # Nếu không phải mã, tìm kiếm chỉ số phù hợp
                search_result = await self.search_indicators(indicator, 1)
                if search_result["results_count"] > 0:
                    indicator = search_result["results"][0]["id"]
            
            # Lấy dữ liệu chỉ số
            country_code = country or "all"
            endpoint = f"country/{country_code}/indicator/{indicator}"
            params = {"format": "json", "per_page": limit}
            if date:
                params["date"] = date
                
            data = await self._make_request(endpoint, params)
            
            # Xử lý kết quả
            results = []
            if isinstance(data, list) and len(data) > 1:
                for item in data[1]:
                    results.append({
                        "country": item.get("country", {}).get("value"),
                        "country_code": item.get("country", {}).get("id"),
                        "year": item.get("date"),
                        "value": item.get("value"),
                        "unit": item.get("unit"),
                        "indicator": item.get("indicator", {}).get("value"),
                        "indicator_code": item.get("indicator", {}).get("id"),
                        "url": f"{self.data_portal}/indicator/{indicator}"
                    })
            
            # Lấy thông tin chi tiết về chỉ số
            indicator_info = await self._get_indicator_info(indicator)
            
            return {
                "status": "success",
                "indicator": indicator_info.get("name") if indicator_info else indicator,
                "indicator_code": indicator,
                "description": indicator_info.get("sourceNote") if indicator_info else "",
                "source": indicator_info.get("source", {}).get("value") if indicator_info else "World Bank",
                "results_count": len(results),
                "results": results,
                "search_metadata": {
                    "query_type": "indicator_search",
                    "country": country_code,
                    "date_range": date or "all",
                    "suggested_searches": self._get_search_guides()
                },
                "official_data_url": f"{self.data_portal}/indicator/{indicator}"
            }
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Lỗi kết nối đến World Bank: {str(e)}"
            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg,
                "suggested_actions": [
                    "Kiểm tra kết nối mạng",
                    "Thử lại sau ít phút",
                    "Kiểm tra lại mã chỉ số hoặc từ khóa"
                ]
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi tìm kiếm dữ liệu: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "indicator": indicator,
                "country": country,
                "date": date
            }
    
    async def _get_indicator_info(self, indicator_code: str) -> Optional[Dict[str, Any]]:
        """Lấy thông tin chi tiết về một chỉ số"""
        try:
            data = await self._make_request(f"indicator/{indicator_code}", {"format": "json"})
            if isinstance(data, list) and len(data) > 1:
                return data[1][0]
            return None
        except Exception:
            return None
    
    def _get_search_guides(self) -> List[Dict[str, str]]:
        """Trả về danh sách hướng dẫn tìm kiếm"""
        return [
            {"query": "GDP per capita", "description": "Tổng sản phẩm quốc nội bình quân đầu người"},
            {"query": "GE.EST", "description": "Chỉ số hiệu quả chính phủ"},
            {"query": "NY.GDP.MKTP.KD.ZG", "description": "Tăng trưởng GDP hàng năm"},
            {"query": "SE.XPD.TOTL.GD.ZS", "description": "Chi tiêu giáo dục (% GDP)"},
            {"query": "SH.DYN.MORT", "description": "Tỷ lệ tử vong trẻ em"},
            {"query": "SP.POP.TOTL", "description": "Dân số tổng"},
            {"query": "search_indicators('governance')", "description": "Tìm kiếm các chỉ số về quản trị"}
        ]
    
    async def get_governance_indicators(self, country: str = "all", year: Optional[str] = None) -> Dict[str, Any]:
        """
        Lấy các chỉ số quản trị toàn cầu
        
        Parameters:
        - country: Mã quốc gia hoặc 'all' cho tất cả
        - year: Năm cụ thể (tùy chọn)
        """
        governance_indicators = {
            "CC.EST": "Kiểm soát tham nhũng",
            "GE.EST": "Hiệu quả chính phủ",
            "PV.EST": "Ổn định chính trị",
            "RL.EST": "Pháp quyền",
            "RQ.EST": "Chất lượng quản lý",
            "VA.EST": "Tiếng nói và trách nhiệm giải trình"
        }
        
        results = {}
        for code, name in governance_indicators.items():
            try:
                endpoint = f"country/{country}/indicator/{code}"
                params = {"format": "json", "per_page": 1}
                if year:
                    params["date"] = year
                
                data = await self._make_request(endpoint, params)
                if isinstance(data, list) and len(data) > 1 and data[1]:
                    item = data[1][0]
                    results[code] = {
                        "name": name,
                        "value": item.get("value"),
                        "year": item.get("date"),
                        "unit": item.get("unit"),
                        "url": f"{self.data_portal}/indicator/{code}"
                    }
            except Exception as e:
                logger.warning(f"Không thể lấy chỉ số {code}: {str(e)}")
        
        return {
            "status": "success",
            "country": country,
            "year": year or "latest",
            "indicators": results,
            "results_count": len(results)
        }
    
    async def get_country_data(self, country_code: str, indicators: List[str] = None) -> Dict[str, Any]:
        """
        Lấy dữ liệu nhiều chỉ số cho một quốc gia
        
        Parameters:
        - country_code: Mã quốc gia (ví dụ: 'VN', 'US')
        - indicators: Danh sách các mã chỉ số cần lấy
        """
        if not indicators:
            indicators = ["NY.GDP.MKTP.CD", "SP.POP.TOTL", "NY.GDP.PCAP.CD"]
        
        results = {}
        for indicator in indicators:
            try:
                data = await self.search_world_bank_politics(indicator, country_code, limit=1)
                if data["status"] == "success" and data["results"]:
                    results[indicator] = data["results"][0]
            except Exception as e:
                logger.warning(f"Không thể lấy dữ liệu {indicator}: {str(e)}")
        
        return {
            "status": "success",
            "country": country_code,
            "results": results,
            "results_count": len(results)
        }
