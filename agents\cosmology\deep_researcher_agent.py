"""
Deep Researcher Agent for Cosmology.
"""

from agno.agent import Agent
from agno.models.ollama import Ollama
from agno.tools.toolkit import Toolkit
from textwrap import dedent

def create_cosmology_deep_researcher_agent(model_id: str) -> Agent:
    """
    Create a deep researcher agent for cosmology.

    Args:
        model_id (str): The model ID to use for the agent.

    Returns:
        Agent: An initialized deep researcher agent.
    """
    model = Ollama(id=model_id)
    tools = []  # Add specific tools for deep research if needed

    return Agent(
        name="Cosmology Deep Researcher Agent",
        model=model,
        instructions=dedent("""
            You are a deep researcher specializing in cosmology.
            Analyze complex queries and provide detailed insights.
            Always ensure scientific accuracy and clarity in your responses.
        """),
        tools=tools,
        show_tool_calls=True,
        markdown=True
    )
