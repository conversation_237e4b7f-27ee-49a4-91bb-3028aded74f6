import asyncio
from typing import Iterator

from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.astronomy.nasa_ads_tools import NasaAdsTools
from tools.writer.writer_research import WriterResearchTools
from agno.team.team import Team
from agno.memory.v2.memory import Memory
from agno.storage.agent.sqlite import SqliteAgentStorage
from agno.knowledge.agent import AgentKnowledge
from agno.utils.log import logger
from agno.workflow import Workflow
from agno.run.team import RunResponse
from agno.utils.pprint import pprint_run_response
import logging



class ResearchWorkflow(Workflow):
    description: str = ("A workflow for the research process.")

    def __init__(self):
        super().__init__()
        self.knowledge = AgentKnowledge(
            knowledge_base="tmp/research_agent_storage.db",
            table_name="research_agent_storage",
        )
        self.memory = Memory(model=Ollama(id="qwen3:4b"))
        self.research_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Research Agent",
            description="A research agent that can search by NASA ADS and write a research report based on the findings like a professional.",
            storage=SqliteAgentStorage(
                db_file="tmp/research_agent_storage.db",
                table_name="research_agent_storage",
            ),
            tools=[NasaAdsTools()],
            instructions=("""
                You are a research agent that can search for academic papers and summarize them.
                Use the tools provided to find relevant papers and summarize their findings.
                If you find a paper that is relevant to the user's query, summarize its findings.
                If you do not find a relevant paper, say "No relevant papers found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )
        self.writer_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Writer Agent",
            knowledge=self.knowledge,
            storage=SqliteAgentStorage(
                db_file="tmp/writer_agent_storage.db",
                table_name="writer_agent_storage",
            ),
            tools=[WriterResearchTools()],
            instructions=("""
                You are a writing agent that can assist with generating high-quality research reports.
                Your knowledge base contains in research_agent_storage.db.
                Use the knowledge base to find relevant information and examples.
                Use the WriterResearchTools to enhance the report writing process.
            """),
            memory=self.memory,
            markdown=True,
            show_tool_calls=True,
            add_datetime_to_instructions=True,
        )
        self.research_team = Team(
            name="Research Team",
            model=Ollama(id="qwen3:4b"),
            mode="collaborative",
            storage=SqliteAgentStorage(
                db_file="tmp/research_team_storage.db",
                table_name="research_team_storage",
            ),
            members=[self.research_agent, self.writer_agent],
            instructions=("""
                You are a team of research and writing agents.
                First, the research agent will search for relevant academic papers using the NasaAdsTools and summarize their findings.
                Then, the writing agent will assist in generating a high-quality research report based on the findings.
                The research agent will use the WriterResearchTools to enhance the report writing process.
                The team will also ensure that the report meets quality standards and is well-structured.
                The team will coordinate and ensure that the research and writing processes are efficient and effective.
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_members_responses=True,
        )

    def run_workflow(self, message):
        logging.info("Running research workflow...")
        result = self.research_team.run(message)
        # If result is an iterator/generator, yield from it; else yield as single
        if hasattr(result, '__iter__') and not isinstance(result, str):
            yield from result
        else:
            yield result


def chat_loop():
    workflow = ResearchWorkflow()
    try:
        while True:
            user_input = input("User: ")
            if user_input.lower() in ["exit", "quit"]:
                print("Exiting chat loop.")
                break
            pprint_run_response(workflow.run_workflow(user_input), markdown=True, show_time=True)
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    chat_loop()
