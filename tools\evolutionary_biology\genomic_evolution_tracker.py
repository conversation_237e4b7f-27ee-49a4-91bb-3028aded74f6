# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math
from datetime import datetime

class GenomicEvolutionTracker(Toolkit):
    """
    Genomic Evolution Tracker cho theo dõi evolution ở mức genome, gene duplication và horizontal transfer.
    """

    def __init__(self, enable_tracking: bool = True, **kwargs):
        super().__init__(
            name="genomic_evolution_tracker",
            **kwargs
        )
        
        # Genome size data (base pairs)
        self.genome_sizes = {
            "virus": {"min": 1000, "max": 2500000, "avg": 50000},
            "bacteria": {"min": 160000, "max": 15000000, "avg": 4000000},
            "archaea": {"min": 490000, "max": 5700000, "avg": 2500000},
            "fungi": {"min": 2900000, "max": 177000000, "avg": 30000000},
            "plants": {"min": 12600000, "max": 150000000000, "avg": 500000000},
            "animals": {"min": 37000000, "max": 670000000000, "avg": 3000000000},
            "protists": {"min": 2900000, "max": 670000000000, "avg": 50000000}
        }
        
        # Mutation rates (per base pair per generation)
        self.mutation_rates = {
            "dna_virus": 1e-6,
            "rna_virus": 1e-4,
            "bacteria": 1e-10,
            "yeast": 3e-10,
            "drosophila": 3e-9,
            "mouse": 5e-10,
            "human": 1e-8
        }
        
        # Gene duplication frequencies
        self.duplication_rates = {
            "whole_genome": 0.01,  # per million years
            "segmental": 0.1,      # per million years
            "tandem": 1.0,         # per million years
            "transposon": 0.5      # per million years
        }
        
        if enable_tracking:
            self.register(self.track_genome_evolution)
            self.register(self.analyze_gene_duplication)
            self.register(self.detect_horizontal_transfer)
            self.register(self.calculate_mutation_rate)

    def track_genome_evolution(self, organism: str, genome_size: int = None, 
                             timeframe_mya: float = 100) -> str:
        """
        Theo dõi evolution của genome qua thời gian.
        
        Args:
            organism: Tên sinh vật
            genome_size: Kích thước genome (base pairs)
            timeframe_mya: Khung thời gian (million years ago)
            
        Returns:
            Chuỗi JSON chứa tracking genome evolution
        """
        log_debug(f"Tracking genome evolution for {organism} over {timeframe_mya} MYA")
        
        try:
            # Determine organism category
            organism_category = self._categorize_organism(organism)
            
            # Use provided genome size or estimate
            if genome_size is None:
                genome_size = self.genome_sizes[organism_category]["avg"]
            
            # Calculate evolutionary changes
            mutation_rate = self.mutation_rates.get(organism_category, 1e-9)
            total_mutations = int(genome_size * mutation_rate * timeframe_mya * 1e6)
            
            # Genome size evolution
            size_change_factor = 1 + (timeframe_mya / 1000) * 0.1  # 10% per billion years
            ancestral_size = int(genome_size / size_change_factor)
            
            # Gene content evolution
            current_genes = int(genome_size / 1000)  # Rough estimate: 1 gene per kb
            ancestral_genes = int(ancestral_size / 1000)
            gene_gain = current_genes - ancestral_genes
            gene_loss = int(ancestral_genes * 0.1)  # 10% gene loss
            net_gene_change = gene_gain - gene_loss
            
            # Chromosomal rearrangements
            rearrangements = int(timeframe_mya * 0.1)  # 0.1 per MYA
            
            # Repetitive element expansion
            repeat_content = min(50, timeframe_mya * 0.2)  # Max 50%
            
            result = {
                "organism": organism,
                "organism_category": organism_category,
                "timeframe_mya": timeframe_mya,
                "genome_evolution": {
                    "current_size_bp": genome_size,
                    "ancestral_size_bp": ancestral_size,
                    "size_change_percent": round(((genome_size - ancestral_size) / ancestral_size) * 100, 1),
                    "size_evolution_trend": "expansion" if genome_size > ancestral_size else "contraction"
                },
                "mutational_load": {
                    "total_mutations": total_mutations,
                    "mutation_rate": mutation_rate,
                    "mutations_per_generation": int(genome_size * mutation_rate),
                    "fixation_rate": "Variable depending on selection"
                },
                "gene_content_evolution": {
                    "current_genes": current_genes,
                    "ancestral_genes": ancestral_genes,
                    "gene_gain": gene_gain,
                    "gene_loss": gene_loss,
                    "net_change": net_gene_change,
                    "gene_family_expansion": "Detected" if gene_gain > 0 else "Minimal"
                },
                "structural_evolution": {
                    "chromosomal_rearrangements": rearrangements,
                    "repeat_content_percent": round(repeat_content, 1),
                    "genome_organization": "Increasingly complex" if timeframe_mya > 50 else "Stable"
                },
                "evolutionary_forces": [
                    "Natural selection",
                    "Genetic drift",
                    "Gene flow",
                    "Mutation pressure"
                ],
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error tracking genome evolution: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to track genome evolution: {str(e)}"
            }, indent=4)

    def analyze_gene_duplication(self, gene_family: str, duplication_type: str = "segmental", 
                               species_count: int = 10) -> str:
        """
        Phân tích gene duplication events và evolution.
        
        Args:
            gene_family: Tên gene family
            duplication_type: Loại duplication ('whole_genome', 'segmental', 'tandem')
            species_count: Số loài để so sánh
            
        Returns:
            Chuỗi JSON chứa phân tích gene duplication
        """
        log_debug(f"Analyzing gene duplication for {gene_family} family")
        
        try:
            # Duplication rate
            dup_rate = self.duplication_rates.get(duplication_type, 0.1)
            
            # Simulate gene family evolution
            ancestral_copies = 1
            current_copies = max(1, int(ancestral_copies * (1 + dup_rate) ** 10))  # 10 MYA evolution
            
            # Copy number variation across species
            copy_numbers = []
            for i in range(species_count):
                variation = 1 + (i * 0.2) - 1  # -100% to +100% variation
                copies = max(1, int(current_copies * (1 + variation * 0.3)))
                copy_numbers.append(copies)
            
            # Functional divergence
            functional_categories = {
                "conserved": int(current_copies * 0.4),
                "subfunctionalized": int(current_copies * 0.3),
                "neofunctionalized": int(current_copies * 0.2),
                "pseudogenized": int(current_copies * 0.1)
            }
            
            # Selection analysis
            selection_pressure = {
                "purifying_selection": 0.7,
                "positive_selection": 0.1,
                "neutral_evolution": 0.2
            }
            
            # Duplication mechanisms
            mechanisms = {
                "unequal_crossing_over": 0.4,
                "retrotransposition": 0.3,
                "transposon_mediated": 0.2,
                "segmental_duplication": 0.1
            }
            
            result = {
                "gene_family": gene_family,
                "duplication_analysis": {
                    "duplication_type": duplication_type,
                    "duplication_rate": dup_rate,
                    "ancestral_copies": ancestral_copies,
                    "current_copies": current_copies,
                    "expansion_factor": current_copies / ancestral_copies
                },
                "copy_number_variation": {
                    "species_analyzed": species_count,
                    "copy_numbers": copy_numbers,
                    "min_copies": min(copy_numbers),
                    "max_copies": max(copy_numbers),
                    "average_copies": round(sum(copy_numbers) / len(copy_numbers), 1)
                },
                "functional_divergence": functional_categories,
                "selection_analysis": selection_pressure,
                "duplication_mechanisms": mechanisms,
                "evolutionary_consequences": [
                    "Increased gene dosage",
                    "Functional diversification",
                    "Evolutionary innovation",
                    "Genetic robustness"
                ],
                "clinical_relevance": self._get_clinical_relevance(gene_family),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error analyzing gene duplication: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze gene duplication: {str(e)}"
            }, indent=4)

    def detect_horizontal_transfer(self, gene_sequence: str, source_organism: str, 
                                 target_organism: str) -> str:
        """
        Phát hiện horizontal gene transfer events.
        
        Args:
            gene_sequence: Sequence của gene (hoặc mô tả)
            source_organism: Sinh vật nguồn
            target_organism: Sinh vật đích
            
        Returns:
            Chuỗi JSON chứa phân tích horizontal transfer
        """
        log_debug(f"Detecting horizontal transfer from {source_organism} to {target_organism}")
        
        try:
            # Analyze sequence characteristics
            sequence_length = len(gene_sequence) if gene_sequence else 1000
            gc_content = 0.5 + (hash(gene_sequence) % 20 - 10) / 100  # Simulate GC content
            
            # Transfer likelihood analysis
            source_category = self._categorize_organism(source_organism)
            target_category = self._categorize_organism(target_organism)
            
            # Calculate transfer probability
            transfer_probability = self._calculate_transfer_probability(source_category, target_category)
            
            # Phylogenetic incongruence
            incongruence_score = 0.8 if transfer_probability > 0.5 else 0.3
            
            # Compositional analysis
            compositional_bias = abs(gc_content - 0.5) * 2  # 0-1 scale
            
            # Codon usage analysis
            codon_usage_bias = 0.6 if source_category != target_category else 0.2
            
            # Evidence strength
            evidence_scores = {
                "phylogenetic_incongruence": incongruence_score,
                "compositional_bias": compositional_bias,
                "codon_usage_bias": codon_usage_bias,
                "sequence_similarity": 0.85,
                "genomic_context": 0.4
            }
            
            overall_evidence = sum(evidence_scores.values()) / len(evidence_scores)
            
            # Transfer classification
            if overall_evidence > 0.7:
                classification = "Strong evidence for HGT"
            elif overall_evidence > 0.5:
                classification = "Moderate evidence for HGT"
            else:
                classification = "Weak evidence for HGT"
            
            result = {
                "transfer_analysis": {
                    "source_organism": source_organism,
                    "target_organism": target_organism,
                    "source_category": source_category,
                    "target_category": target_category,
                    "transfer_probability": round(transfer_probability, 2)
                },
                "sequence_analysis": {
                    "sequence_length": sequence_length,
                    "gc_content": round(gc_content, 3),
                    "compositional_bias": round(compositional_bias, 3),
                    "codon_usage_bias": round(codon_usage_bias, 3)
                },
                "evidence_assessment": evidence_scores,
                "overall_evidence_score": round(overall_evidence, 2),
                "classification": classification,
                "transfer_mechanisms": [
                    "Conjugation",
                    "Transformation",
                    "Transduction",
                    "Vesicle-mediated transfer"
                ],
                "evolutionary_impact": self._assess_transfer_impact(overall_evidence),
                "validation_methods": [
                    "Phylogenetic analysis",
                    "Compositional analysis",
                    "Synteny analysis",
                    "Experimental validation"
                ],
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error detecting horizontal transfer: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to detect horizontal transfer: {str(e)}"
            }, indent=4)

    def calculate_mutation_rate(self, organism: str, generation_time: float = None, 
                              sequence_type: str = "nuclear") -> str:
        """
        Tính toán mutation rate cho sinh vật.
        
        Args:
            organism: Tên sinh vật
            generation_time: Thời gian thế hệ (years)
            sequence_type: Loại sequence ('nuclear', 'mitochondrial', 'chloroplast')
            
        Returns:
            Chuỗi JSON chứa tính toán mutation rate
        """
        log_debug(f"Calculating mutation rate for {organism}")
        
        try:
            # Determine organism category and base mutation rate
            organism_category = self._categorize_organism(organism)
            base_rate = self.mutation_rates.get(organism_category, 1e-9)
            
            # Adjust for sequence type
            sequence_multipliers = {
                "nuclear": 1.0,
                "mitochondrial": 10.0,  # Faster evolution
                "chloroplast": 0.5      # Slower evolution
            }
            
            adjusted_rate = base_rate * sequence_multipliers.get(sequence_type, 1.0)
            
            # Generation time effects
            if generation_time is None:
                generation_times = {
                    "bacteria": 0.01,  # ~4 days
                    "yeast": 0.01,     # ~4 days
                    "drosophila": 0.05, # ~18 days
                    "mouse": 0.25,      # ~3 months
                    "human": 25.0       # ~25 years
                }
                generation_time = generation_times.get(organism_category, 1.0)
            
            # Calculate rates in different units
            per_generation = adjusted_rate
            per_year = adjusted_rate / generation_time
            per_site_per_myr = per_year * 1e6
            
            # Mutation spectrum
            mutation_spectrum = {
                "transitions": 0.65,    # A<->G, C<->T
                "transversions": 0.35,  # A<->C, A<->T, G<->C, G<->T
                "indels": 0.1,          # Insertions/deletions
                "structural": 0.01      # Large rearrangements
            }
            
            # Factors affecting mutation rate
            affecting_factors = [
                "DNA repair efficiency",
                "Replication fidelity",
                "Environmental mutagens",
                "Metabolic rate",
                "Generation time"
            ]
            
            result = {
                "organism": organism,
                "organism_category": organism_category,
                "mutation_rate_analysis": {
                    "base_rate": base_rate,
                    "sequence_type": sequence_type,
                    "adjusted_rate": adjusted_rate,
                    "generation_time_years": generation_time
                },
                "mutation_rates": {
                    "per_base_per_generation": adjusted_rate,
                    "per_base_per_year": per_year,
                    "per_site_per_million_years": per_site_per_myr,
                    "genome_wide_per_generation": int(adjusted_rate * 3e9)  # Assuming 3Gb genome
                },
                "mutation_spectrum": mutation_spectrum,
                "affecting_factors": affecting_factors,
                "evolutionary_implications": [
                    "Rate of adaptive evolution",
                    "Genetic load accumulation",
                    "Population genetics dynamics",
                    "Molecular clock calibration"
                ],
                "comparison_with_model_organisms": self._compare_mutation_rates(organism_category),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error calculating mutation rate: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate mutation rate: {str(e)}"
            }, indent=4)

    # Helper methods
    def _categorize_organism(self, organism: str) -> str:
        """Categorize organism based on name."""
        organism_lower = organism.lower()
        if any(word in organism_lower for word in ["human", "mouse", "rat", "mammal"]):
            return "animals"
        elif any(word in organism_lower for word in ["bacteria", "e.coli", "bacillus"]):
            return "bacteria"
        elif any(word in organism_lower for word in ["yeast", "fungi", "saccharomyces"]):
            return "fungi"
        elif any(word in organism_lower for word in ["plant", "arabidopsis", "rice"]):
            return "plants"
        elif any(word in organism_lower for word in ["virus", "phage"]):
            return "virus"
        elif any(word in organism_lower for word in ["archaea", "archaeal"]):
            return "archaea"
        else:
            return "animals"  # Default

    def _calculate_transfer_probability(self, source: str, target: str) -> float:
        """Calculate probability of horizontal gene transfer."""
        # Higher probability within same category
        if source == target:
            return 0.8
        # Moderate probability between related categories
        elif (source == "bacteria" and target == "archaea") or (source == "archaea" and target == "bacteria"):
            return 0.6
        # Lower probability between distant categories
        else:
            return 0.3

    def _get_clinical_relevance(self, gene_family: str) -> str:
        """Get clinical relevance of gene family."""
        clinical_families = ["immunoglobulin", "hla", "cytochrome", "globin"]
        if any(family in gene_family.lower() for family in clinical_families):
            return "High clinical relevance - disease associations known"
        else:
            return "Potential clinical relevance - further investigation needed"

    def _assess_transfer_impact(self, evidence_score: float) -> str:
        """Assess evolutionary impact of horizontal transfer."""
        if evidence_score > 0.7:
            return "Major evolutionary impact - likely functional acquisition"
        elif evidence_score > 0.5:
            return "Moderate impact - possible functional significance"
        else:
            return "Minor impact - likely neutral or deleterious"

    def _compare_mutation_rates(self, organism_category: str) -> Dict[str, float]:
        """Compare mutation rates with model organisms."""
        return {
            "relative_to_human": self.mutation_rates.get(organism_category, 1e-9) / self.mutation_rates["human"],
            "relative_to_yeast": self.mutation_rates.get(organism_category, 1e-9) / self.mutation_rates["yeast"],
            "relative_to_bacteria": self.mutation_rates.get(organism_category, 1e-9) / self.mutation_rates["bacteria"]
        }
