import asyncio
from typing import Iterator

from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.astronomy.nasa_ads_tools import NasaAdsTools
from tools.writer.writer_research import WriterResearchTools
from agno.memory.v2.memory import Memory
from agno.storage.agent.sqlite import SqliteAgentStorage
from agno.knowledge.agent import AgentKnowledge
from agno.utils.log import logger
import logging


knowledge = AgentKnowledge(
    knowledge_base="tmp/research_combo_storage.db",
    table_name="research_combo_storage",
)

memory = Memory(model=Ollama(id="qwen3:4b"))


research_agent = Agent(
    model=Ollama(id="qwen3:4b"),
    name="Research Agent",
    storage=SqliteAgentStorage(
        db_file="tmp/research_combo_storage.db",
        table_name="research_combo_storage",
    ),
    tools=[NasaAdsTools(), WriterResearchTools()],
    knowledge=knowledge,
    instructions=("""
        You are a research agent that can search for academic papers and summarize them.
        First, use the NasaAdsTools to find relevant papers based on the user's query.
        Second, use the WriterResearchTools to assist with generating high-quality research reports.
        Your knowledge base contains in research_combo_storage.db.
        If you find a paper that is relevant to the user's query, summarize its findings.
        If you do not find a relevant paper, say "No relevant papers found."
    """),
    memory=memory,
    markdown=True,
    add_datetime_to_instructions=True,
    show_tool_calls=True,
    stream=True,
)

def run_agent(agent, user_input):
    try:
        # Use print_response for synchronous CLI output (per Agno context7 docs)
        agent.print_response(user_input)
    except Exception as e:
        logging.error(f"Error running agent: {e}")
        print(f"An error occurred while running the agent: {e}")

def chat_loop():
    try:
        while True:
            user_input = input("User: ")
            if user_input.lower() in ["exit", "quit"]:
                print("Exiting chat loop.")
                break
            agent = research_agent  # create a new Agent instance for each input
            run_agent(agent, user_input)
            print("User input sent to research agent.")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    chat_loop()
