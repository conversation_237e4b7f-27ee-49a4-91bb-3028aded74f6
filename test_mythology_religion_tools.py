#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script cho Mythology & Religion Tools
"""

import sys
import os
import json
import random
import asyncio
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_mythopedia_tools():
    """Test Mythopedia Tools"""
    print("🏛️ Testing Mythopedia Tools...")
    try:
        from tools.mythology_religion.mythopedia_tools import MythopediaTool

        mythopedia_tool = MythopediaTool()

        print("  - Testing Mythopedia instantiation...")
        print("    ✅ Mythopedia Tools instantiated")

        # Test get_top_new (sync version for testing)
        print("  - Testing Mythopedia get_top_new...")
        # Since it's async, we'll test basic structure
        assert hasattr(mythopedia_tool, 'get_top_new')
        print("    ✅ Mythopedia get_top_new method exists")

        return True

    except Exception as e:
        print(f"    ❌ Mythopedia Tools failed: {str(e)}")
        return False

def test_mythology_search_toolkit():
    """Test Mythology Religion Search Toolkit"""
    print("🔍 Testing Mythology Religion Search Toolkit...")
    try:
        from tools.mythology_religion.mythology_religion_search_toolkit import MythologyReligionSearchToolkit

        toolkit = MythologyReligionSearchToolkit()

        print("  - Testing deity search...")
        result = toolkit.search_deities_pantheons("Zeus", "Greek", "Ancient Greece", "sky")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Deity search works")

        print("  - Testing creature search...")
        result = toolkit.search_mythological_creatures("dragon", "Norse", "mountains", "high")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Creature search works")

        print("  - Testing sacred texts search...")
        result = toolkit.search_sacred_texts("Christianity", "scripture", "Greek", "ancient")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Sacred texts search works")

        return True

    except Exception as e:
        print(f"    ❌ Mythology Search Toolkit failed: {str(e)}")
        return False

def test_other_mythology_tools():
    """Test other mythology tools"""
    print("📚 Testing Other Mythology Tools...")
    try:
        # Test Sacred Texts Tools
        from tools.mythology_religion.sacred_texts_tools import SacredTextsTool

        sacred_tool = SacredTextsTool()

        print("  - Testing Sacred Texts Tools...")
        print("    ✅ Sacred Texts Tools instantiated")

        # Test Wikipedia Mythology Tools
        from tools.mythology_religion.wikipedia_mythology_tools import WikipediaMythologyTool

        wiki_tool = WikipediaMythologyTool()

        print("  - Testing Wikipedia Mythology Tools...")
        print("    ✅ Wikipedia Mythology Tools instantiated")

        return True

    except Exception as e:
        print(f"    ❌ Other Mythology Tools failed: {str(e)}")
        return False

def test_random_mythology_functionality():
    """Test random mythology functionality"""
    print("\n🎲 Testing Random Mythology Functionality...")

    try:
        # Random deity search test
        from tools.mythology_religion.mythology_religion_search_toolkit import MythologyReligionSearchToolkit
        toolkit = MythologyReligionSearchToolkit()

        deities = ["Zeus", "Odin", "Ra", "Shiva", "Amaterasu"]
        deity = random.choice(deities)
        result = toolkit.search_deities_pantheons(deity, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random deity {deity} search test passed")

        # Random creature search test
        creatures = ["dragon", "phoenix", "kraken", "sphinx", "griffin"]
        creature = random.choice(creatures)
        result = toolkit.search_mythological_creatures(creature, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random creature {creature} search test passed")

        # Random religious tradition test
        traditions = ["Buddhism", "Christianity", "Islam", "Hinduism", "Judaism"]
        tradition = random.choice(traditions)
        result = toolkit.search_religious_traditions(tradition, "all", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random tradition {tradition} test passed")

        return True

    except Exception as e:
        print(f"    ❌ Random Mythology Functionality failed: {str(e)}")
        return False

def test_mythology_search_variations():
    """Test various mythology search variations"""
    print("\n🏺 Testing Mythology Search Variations...")

    try:
        from tools.mythology_religion.mythology_religion_search_toolkit import MythologyReligionSearchToolkit
        toolkit = MythologyReligionSearchToolkit()

        # Test comprehensive mythology search
        print("  - Testing comprehensive mythology search...")
        result = toolkit.comprehensive_mythology_search("creation myths", "all", "global", "ancient")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive mythology search works")

        # Test religious traditions with different parameters
        print("  - Testing religious traditions variations...")
        result = toolkit.search_religious_traditions("Zen Buddhism", "ritual", "Asia", "medieval")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Religious traditions variations work")

        return True

    except Exception as e:
        print(f"    ❌ Mythology Search Variations failed: {str(e)}")
        return False

async def test_async_mythopedia():
    """Test async Mythopedia functionality"""
    print("\n⚡ Testing Async Mythopedia...")

    try:
        from tools.mythology_religion.mythopedia_tools import MythopediaTool

        mythopedia_tool = MythopediaTool()

        # Test async get_top_new
        print("  - Testing async get_top_new...")
        result = await mythopedia_tool.get_top_new("deities", 5, "Greek", "month")
        assert result["status"] == "success"
        print("    ✅ Async get_top_new works")

        return True

    except Exception as e:
        print(f"    ❌ Async Mythopedia failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 MYTHOLOGY & RELIGION TOOLS TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing Mythology & Religion channel tools...")
    print()

    test_results = []

    # Test all mythology tools
    test_functions = [
        ("Mythopedia Tools", test_mythopedia_tools),
        ("Mythology Search Toolkit", test_mythology_search_toolkit),
        ("Other Mythology Tools", test_other_mythology_tools),
        ("Random Mythology Functionality", test_random_mythology_functionality),
        ("Mythology Search Variations", test_mythology_search_variations)
    ]

    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()

    # Test async functionality
    print(f"\n{'='*20} Async Tests {'='*20}")
    try:
        async_result = asyncio.run(test_async_mythopedia())
        test_results.append(("Async Mythopedia", async_result))
    except Exception as e:
        print(f"❌ Async tests failed: {str(e)}")
        test_results.append(("Async Mythopedia", False))

    # Summary
    print("\n" + "="*60)
    print("📋 MYTHOLOGY & RELIGION TOOLS TEST SUMMARY")
    print("="*60)

    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")

    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 All mythology & religion tools are working correctly!")
        print("✨ Mythology & Religion channel fully functional!")
    elif passed >= total * 0.8:
        print("✅ Excellent performance - most functionality working!")
    elif passed >= total * 0.6:
        print("✅ Good performance - majority working!")
    else:
        print("⚠️  Some issues detected. Please check the error messages above.")

    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
