from typing import List, Optional, Dict, Any

from agno.agent import Agent
from agno.knowledge.agent import AgentKnowledge
from agno.memory.v2.memory import Memory
from agno.run.team import RunResponse
from agno.storage.agent.sqlite import SqliteAgentStorage
from agno.team.team import Team
from agno.utils.pprint import pprint_run_response
from agno.workflow import Workflow
from tools.astronomy.nasa_ads_tools import NasaAdsTools  
from tools.writer.writer_fb import WriterFbTools
from agno.models.ollama import Ollama
from tools.astronomy.wikipedia_astronomy_tools import WikipediaAstronomyTools
from tools.astronomy.astronomy_search_toolkit import AstronomySearchToolkits
from tools.astronomy.simbad_tools import SimbadTools
from tools.astronomy.esa_archives_tools import EsaArchivesTools
from tools.astronomy.esdc_crawler_tools import EsdcCrawlerTools


from agno.utils.log import logger
from typing import Iterator
import logging

class AstronomyFbWorkflow(Workflow):
    description: str = ("A workflow for the astronomy research workflow. ")

    def __init__(self):
        super().__init__()
        self.knowledge = AgentKnowledge(
            knowledge_base="tmp/research_deep_agent_storage.db",
            table_name="research_deep_agent_storage",
        )
        self.memory = Memory(model=Ollama(id="qwen3:4b"), delete_memories=True, clear_memories=True)
               
        self.sub_question_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Sub Question Agent",
            description="An agent that can generate sub-questions based on the user's query.",
            tools=[AstronomySearchToolkits()],
            instructions=("""
                You are a sub-question agent that can generate sub-questions based on the user's query.
                Use the tools provided to find relevant information and generate sub-questions.
                If you find a relevant sub-question, return it.
                If you do not find a relevant sub-question, say "No relevant sub-questions found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )
        self.simbad_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Simbad Astronomy Agent",
            description="An agent that can search Simbad for astronomy-related information.",
            tools=[SimbadTools()],
            instructions=("""
                You are a Simbad agent that can search for astronomy-related information.
                Use the tools provided to find relevant information and summarize it.
                If you find relevant information, return it.
                If you do not find relevant information, say "No relevant information found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )
        self.esa_archives_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="ESA Archives Agent",
            description="An agent that can search ESA archives for astronomy-related information.",
            tools=[EsaArchivesTools()],
            instructions=("""
                You are a ESA archives agent that can search for astronomy-related information.
                Use the tools provided to find relevant information and summarize it.
                If you find relevant information, return it.
                If you do not find relevant information, say "No relevant information found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )
        self.esdc_crawler_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="ESDC Crawler Agent",
            description="An agent that can search ESDC archives for astronomy-related information.",
            tools=[EsdcCrawlerTools()],
            instructions=("""
                You are a ESDC crawler agent that can search for astronomy-related information.
                Use the tools provided to find relevant information and summarize it.
                If you find relevant information, return it.
                If you do not find relevant information, say "No relevant information found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )
        self.nasa_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="NASA Research Agent",
            description="A research agent that can search by NASA ADS and write a research report based on the findings like a professional.",
            tools=[NasaAdsTools()],
            instructions=("""
                You are a research agent that can search from NASA ADS.
                Use the tools provided to find relevant papers and summarize their findings.
                If you find a paper that is relevant to the user's query, summarize its findings.
                If you do not find a relevant paper, say "No relevant papers found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )
        self.wikipedia_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Wikipedia Astronomy Agent",
            description="An agent that can search Wikipedia for astronomy-related information.",
            tools=[WikipediaAstronomyTools()],
            instructions=("""
                You are a Wikipedia agent that can search for astronomy-related information.
                Use the tools provided to find relevant information and summarize it.
                If you find relevant information, return it.
                If you do not find relevant information, say "No relevant information found."
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )
        self.writer_agent = Agent(
            model=Ollama(id="qwen3:4b"),
            name="Writer Agent",
            description="An agent that can write facebook posts based on the findings from other agents.",
            tools=[WriterFbTools()],
            instructions=("""
                You are a writer agent that can write facebook posts based on the findings from other agents.
                Use the tools provided to write a comprehensive facebook post.
                If you need more information, ask the relevant agents.
            """),
            memory=self.memory,
            markdown=True,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
        )
        self.deep_research_team = Team(
            name="Deep Research Team",
            model=Ollama(id="qwen3:4b"),
            mode="collaborative",
            storage=SqliteAgentStorage(
                db_file="tmp/deep_research_team_storage.db",
                table_name="deep_research_team_storage",
            ),
            members=[self.sub_question_agent, self.nasa_agent, self.wikipedia_agent, self.writer_agent, self.simbad_agent, self.esa_archives_agent, self.esdc_crawler_agent],
            instructions=("""
                You are a team of research and writing agents.
                Your task is to collaboratively research and write a comprehensive facebook post based on the user's query.
                Each member of the team has a specific role:
                - Sub Question Agent: Generates sub-questions based on the user's query.
                - NASA Research Agent: Searches NASA ADS for relevant papers and summarizes their findings.
                - Wikipedia Astronomy Agent: Searches Wikipedia for astronomy-related information.
                - Simbad Astronomy Agent: Searches Simbad for astronomy-related information.
                - ESA Archives Agent: Searches ESA archives for astronomy-related information.
                - ESDC Crawler Agent: Searches ESDC archives for astronomy-related information.
                - Writer Agent: Writes a comprehensive facebook post based on the findings from other agents.
                Each member should use the tools provided to find relevant information and summarize it.
                If you find relevant information, return it.
            """),
            memory=self.memory,
            add_datetime_to_instructions=True,
            show_tool_calls=True,
            success_criteria=(""" The team successfully completes the task """)
        )
        
    def run_workflow(self, message):
        logging.info(f"Running deep research workflow with message: {message}")
        result = self.deep_research_team.run(message)

        if hasattr(result, '__iter__') and not isinstance(result, str):
            yield from result
        else:
            yield result    

def chat_loop():
    workflow = AstronomyFbWorkflow()
    try:
        while True:
            user_input = input("User: ")
            if user_input.lower() == "exit":
                break
            # Correct usage: pass run_response as first argument
            pprint_run_response(workflow.run_workflow(user_input), markdown=True, show_time=True)
    except Exception as e:
        logging.error(f"Error occurred in chat loop: {e}")

if __name__ == "__main__":
    chat_loop()
