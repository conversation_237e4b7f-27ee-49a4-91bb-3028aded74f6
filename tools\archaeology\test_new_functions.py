#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho c<PERSON><PERSON> hàm get_recent/popular mới đ<PERSON><PERSON><PERSON> thêm vào archaeology tools.
"""

import sys
import os
import json

# Thêm thư mục gốc vào Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_archnet():
    """Test Archnet tools."""
    print("=== Testing Archnet Tools ===")
    try:
        from tools.archaeology.archnet_tools import ArchnetTools

        tool = ArchnetTools()

        # Test regular search
        print("--- Regular Search ---")
        result1 = tool.search_archnet("Mesopotamia", 3)
        print("Search result:", result1[:200] + "..." if len(result1) > 200 else result1)

        # Test recent discoveries
        print("\n--- Recent Discoveries ---")
        result2 = tool.get_recent_discoveries(3, 30, "Middle East")
        print("Recent discoveries result:", result2[:200] + "..." if len(result2) > 200 else result2)

        # Test trending sites
        print("\n--- Trending Sites ---")
        result3 = tool.get_trending_sites(3, "Mediterranean")
        print("Trending sites result:", result3[:200] + "..." if len(result3) > 200 else result3)

    except Exception as e:
        print(f"Error testing Archnet: {e}")
    print()

def test_british_museum():
    """Test British Museum tools."""
    print("=== Testing British Museum Tools ===")
    try:
        from tools.archaeology.british_museum_tools import BritishMuseumTools

        tool = BritishMuseumTools()

        # Test regular search
        print("--- Regular Search ---")
        result1 = tool.search_british_museum("Egyptian", 3)
        print("Search result:", result1[:200] + "..." if len(result1) > 200 else result1)

        # Test recent acquisitions
        print("\n--- Recent Acquisitions ---")
        result2 = tool.get_recent_acquisitions(3, 90, "Egyptian Antiquities")
        print("Recent acquisitions result:", result2[:200] + "..." if len(result2) > 200 else result2)

        # Test popular artifacts
        print("\n--- Popular Artifacts ---")
        result3 = tool.get_popular_artifacts(3, "Greek and Roman Antiquities")
        print("Popular artifacts result:", result3[:200] + "..." if len(result3) > 200 else result3)

    except Exception as e:
        print(f"Error testing British Museum: {e}")
    print()

def test_search_toolkit():
    """Test Archaeology Search Toolkit."""
    print("=== Testing Archaeology Search Toolkit ===")
    try:
        from tools.archaeology.archaeology_search_toolkit import ArchaeologySearchToolkit

        toolkit = ArchaeologySearchToolkit()

        # Test regular keyword generation
        print("--- Archnet Keywords ---")
        result1 = toolkit.generate_archnet_keywords("Mesopotamia", "Ur", "Early Dynastic")
        print(result1)

        print("\n--- British Museum Keywords ---")
        result2 = toolkit.generate_british_museum_keywords("Egyptian", "statue", "New Kingdom")
        print(result2)

        print("\n--- Met Museum Keywords ---")
        result3 = toolkit.generate_met_museum_keywords("Greek", "sculpture")
        print(result3)

        print("\n--- Internet Archive Keywords ---")
        result4 = toolkit.generate_internet_archive_keywords("mesopotamian archaeology", "texts")
        print(result4)

        print("\n--- Wikipedia Archaeology Keywords ---")
        result5 = toolkit.generate_wikipedia_archaeology_keywords("Maya archaeology")
        print(result5)

        # Test recent/popular keyword generation
        print("\n--- Archnet Recent Keywords ---")
        result6 = toolkit.generate_archnet_recent_keywords("Middle East", 30)
        print(result6)

        print("\n--- Museum Recent Keywords ---")
        result7 = toolkit.generate_museum_recent_keywords("british", "Egyptian Antiquities")
        print(result7)

        print("\n--- Archive Recent Keywords ---")
        result8 = toolkit.generate_archive_recent_keywords("archaeology", 30)
        print(result8)

        print("\n--- Wikipedia Archaeology Recent Keywords ---")
        result9 = toolkit.generate_wikipedia_archaeology_recent_keywords(30, "en")
        print(result9)

    except Exception as e:
        print(f"Error testing Search Toolkit: {e}")
    print()

def test_other_tools():
    """Test other archaeology tools briefly."""
    print("=== Testing Other Archaeology Tools ===")

    # Test Met Museum (if available)
    try:
        print("--- Met Museum Tools ---")
        from tools.archaeology.met_museum_tools import MetMuseumTool
        tool = MetMuseumTool()
        # Assume it has similar structure
        print("Met Museum tools loaded successfully")
    except Exception as e:
        print(f"Met Museum tools error: {e}")

    # Test Internet Archive (if available)
    try:
        print("--- Internet Archive Tools ---")
        from tools.archaeology.internet_archive_archaeo_tools import InternetArchiveArchaeoTool
        tool = InternetArchiveArchaeoTool()
        print("Internet Archive tools loaded successfully")
    except Exception as e:
        print(f"Internet Archive tools error: {e}")

    # Test Wikipedia Archaeology (if available)
    try:
        print("--- Wikipedia Archaeology Tools ---")
        from tools.archaeology.wikipedia_archaeology_tools import WikipediaArchaeologyTool
        tool = WikipediaArchaeologyTool()
        print("Wikipedia Archaeology tools loaded successfully")
    except Exception as e:
        print(f"Wikipedia Archaeology tools error: {e}")

    print()

def main():
    """Chạy tất cả các test."""
    print("Testing New Archaeology Tools Functions")
    print("=" * 50)
    print()

    # Test các tool đã cải tiến
    test_archnet()
    test_british_museum()
    test_search_toolkit()
    test_other_tools()

    print("=" * 50)
    print("Testing completed!")

if __name__ == "__main__":
    main()
