from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class MythopediaTool(Toolkit):
    """
    Mythopedia Tool cho tìm kiếm thần thoại, nhân vật thần tho<PERSON>, sinh vật và truyền thuyết từ Mythopedia.
    """

    def __init__(self):
        super().__init__(
            name="Mythopedia Tools",
            tools=[
                self.search_mythopedia,
                self.get_deity_details,
                self.list_mythologies,
                self.search_creatures,
                self.get_mythology_comparison
            ]
        )

    async def search_mythopedia(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm Mythopedia cho thần thoại, nh<PERSON> vật, sinh vật, truy<PERSON>n thuyết.

        Parameters:
        - query: T<PERSON><PERSON> thần, sinh vật, truy<PERSON>n thuyết hoặc nền văn hóa (ví dụ: 'Thor', 'Japanese mythology', 'dragon', 'Greek gods')
        - limit: <PERSON><PERSON> lượng kết quả tối đa (default: 5)

        Returns:
        - J<PERSON><PERSON> với thông tin tên, mô tả, loại, nguồn gốc, hình ảnh, liên kết Mythopedia
        """
        logger.info(f"Tìm kiếm Mythopedia: {query}")

        try:
            # Mythopedia không có API chính thức, sử dụng endpoint tìm kiếm web (scraping nhẹ)
            search_url = f"https://mythopedia.com/api/search"
            params = {
                "q": query,
                "limit": limit
            }
            response = requests.get(search_url, params=params, timeout=10)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Mythopedia",
                    "message": f"Mythopedia search API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for item in data.get("results", [])[:limit]:
                mytho_url = f"https://mythopedia.com/topics/{item.get('slug')}" if item.get("slug") else None
                results.append({
                    "name": item.get("title"),
                    "type": item.get("type"),
                    "origin": item.get("origin"),
                    "description": item.get("description"),
                    "image_url": item.get("image"),
                    "mythopedia_url": mytho_url
                })

            return {
                "status": "success",
                "source": "Mythopedia",
                "query": query,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Mythopedia: {str(e)}")
            return {
                "status": "error",
                "source": "Mythopedia",
                "message": str(e),
                "query": query
            }

    async def get_deity_details(self, deity_name: str) -> Dict[str, Any]:
        """
        Lấy thông tin chi tiết về một vị thần hoặc nhân vật thần thoại từ Mythopedia.

        Parameters:
        - deity_name: Tên vị thần hoặc nhân vật thần thoại (ví dụ: 'Zeus', 'Odin', 'Amaterasu')

        Returns:
        - JSON với thông tin chi tiết về vị thần/nhân vật
        """
        logger.info(f"Lấy thông tin chi tiết về vị thần: {deity_name}")

        try:
            # Đầu tiên tìm slug của vị thần
            search_url = "https://mythopedia.com/api/search"
            params = {"q": deity_name, "limit": 1}
            search_response = requests.get(search_url, params=params, timeout=10)
            
            if search_response.status_code != 200 or not search_response.json().get("results"):
                return {
                    "status": "error",
                    "source": "Mythopedia",
                    "message": "Không tìm thấy thông tin về vị thần/nhân vật này",
                    "deity_name": deity_name
                }
            
            # Lấy slug từ kết quả tìm kiếm
            slug = search_response.json()["results"][0].get("slug")
            if not slug:
                return {
                    "status": "error",
                    "source": "Mythopedia",
                    "message": "Không tìm thấy đường dẫn chi tiết",
                    "deity_name": deity_name
                }
            
            # Lấy thông tin chi tiết
            detail_url = f"https://mythopedia.com/api/v1/topics/{slug}"
            detail_response = requests.get(detail_url, timeout=10)
            
            if detail_response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Mythopedia",
                    "message": f"Không thể lấy thông tin chi tiết. Mã lỗi: {detail_response.status_code}",
                    "deity_name": deity_name
                }
            
            data = detail_response.json()
            
            # Trích xuất thông tin cơ bản
            result = {
                "name": data.get("title"),
                "type": data.get("type"),
                "origin": data.get("origin"),
                "description": data.get("description"),
                "image_url": data.get("image"),
                "mythopedia_url": f"https://mythopedia.com/topics/{slug}",
                "characteristics": {},
                "relations": [],
                "stories": []
            }
            
            # Trích xuất đặc điểm
            for field in data.get("fields", []):
                if field.get("value"):
                    result["characteristics"][field.get("name")] = field.get("value")
            
            # Trích xuất mối quan hệ
            for rel in data.get("relations", []):
                if rel.get("title") and rel.get("slug"):
                    result["relations"].append({
                        "name": rel.get("title"),
                        "type": rel.get("type"),
                        "relationship": rel.get("relationship"),
                        "url": f"https://mythopedia.com/topics/{rel.get('slug')}"
                    })
            
            # Trích xuất câu chuyện liên quan
            for story in data.get("stories", []):
                if story.get("title") and story.get("slug"):
                    result["stories"].append({
                        "title": story.get("title"),
                        "excerpt": story.get("excerpt"),
                        "url": f"https://mythopedia.com/stories/{story.get('slug')}"
                    })
            
            return {
                "status": "success",
                "source": "Mythopedia",
                "deity_name": deity_name,
                "data": result
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy thông tin vị thần: {str(e)}")
            return {
                "status": "error",
                "source": "Mythopedia",
                "message": str(e),
                "deity_name": deity_name
            }

    async def list_mythologies(self, limit: int = 10) -> Dict[str, Any]:
        """
        Lấy danh sách các nền thần thoại khác nhau từ Mythopedia.

        Parameters:
        - limit: Số lượng kết quả tối đa (mặc định: 10)

        Returns:
        - JSON với danh sách các nền thần thoại
        """
        logger.info(f"Lấy danh sách các nền thần thoại (giới hạn: {limit})")

        try:
            # Sử dụng API tìm kiếm với từ khóa chung
            search_url = "https://mythopedia.com/api/search"
            params = {
                "q": "mythology",
                "limit": limit,
                "type": "origin"  # Lọc theo nguồn gốc/thần thoại
            }
            
            response = requests.get(search_url, params=params, timeout=10)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Mythopedia",
                    "message": f"Lỗi API: {response.status_code}"
                }
            
            data = response.json()
            mythologies = []
            
            for item in data.get("results", []):
                mytho_url = f"https://mythopedia.com/origins/{item.get('slug')}" if item.get("slug") else None
                mythologies.append({
                    "name": item.get("title"),
                    "description": item.get("description"),
                    "image_url": item.get("image"),
                    "mythopedia_url": mytho_url,
                    "deities_count": item.get("deities_count", 0)
                })
            
            return {
                "status": "success",
                "source": "Mythopedia",
                "mythologies_count": len(mythologies),
                "mythologies": mythologies,
                "keyword_guide": [
                    "Greek mythology",
                    "Norse mythology",
                    "Egyptian mythology",
                    "Japanese mythology",
                    "Hindu mythology",
                    "Celtic mythology",
                    "Aztec mythology",
                    "Mayan mythology",
                    "Chinese mythology",
                    "Slavic mythology"
                ]
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy danh sách thần thoại: {str(e)}")
            return {
                "status": "error",
                "source": "Mythopedia",
                "message": str(e)
            }

    async def search_creatures(self, creature_type: str = None, origin: str = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm các sinh vật thần thoại trên Mythopedia.

        Parameters:
        - creature_type: Loại sinh vật (ví dụ: 'dragon', 'giant', 'spirit')
        - origin: Nguồn gốc thần thoại (ví dụ: 'Greek', 'Norse', 'Japanese')
        - limit: Số lượng kết quả tối đa (mặc định: 5)

        Returns:
        - JSON với danh sách các sinh vật thần thoại phù hợp
        """
        logger.info(f"Tìm kiếm sinh vật thần thoại - Loại: {creature_type}, Nguồn gốc: {origin}")

        try:
            search_url = "https://mythopedia.com/api/search"
            
            # Xây dựng query tìm kiếm
            query_parts = []
            if creature_type:
                query_parts.append(creature_type)
            if origin:
                query_parts.append(origin)
            
            query = " ".join(query_parts) if query_parts else "mythical creatures"
            
            params = {
                "q": query,
                "limit": limit,
                "type": "figure"  # Lọc theo nhân vật/sinh vật
            }
            
            response = requests.get(search_url, params=params, timeout=10)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Mythopedia",
                    "message": f"Lỗi API: {response.status_code}",
                    "creature_type": creature_type,
                    "origin": origin
                }
            
            data = response.json()
            creatures = []
            
            for item in data.get("results", []):
                mytho_url = f"https://mythopedia.com/topics/{item.get('slug')}" if item.get("slug") else None
                creatures.append({
                    "name": item.get("title"),
                    "type": item.get("type"),
                    "origin": item.get("origin"),
                    "description": item.get("description"),
                    "image_url": item.get("image"),
                    "mythopedia_url": mytho_url
                })
            
            return {
                "status": "success",
                "source": "Mythopedia",
                "creature_type": creature_type,
                "origin": origin,
                "results_count": len(creatures),
                "creatures": creatures,
                "search_tips": [
                    "Tìm kiếm theo loại: 'dragon', 'giant', 'spirit', 'demon', 'angel'...",
                    "Tìm kiếm theo nguồn gốc: 'Greek', 'Norse', 'Japanese', 'Egyptian'...",
                    "Kết hợp: 'Japanese dragon', 'Norse giant', 'Greek spirit'..."
                ]
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm sinh vật thần thoại: {str(e)}")
            return {
                "status": "error",
                "source": "Mythopedia",
                "message": str(e),
                "creature_type": creature_type,
                "origin": origin
            }

    async def get_mythology_comparison(self, mythology1: str, mythology2: str) -> Dict[str, Any]:
        """
        So sánh giữa hai nền thần thoại khác nhau.

        Parameters:
        - mythology1: Tên nền thần thoại thứ nhất (ví dụ: 'Greek', 'Norse')
        - mythology2: Tên nền thần thoại thứ hai (ví dụ: 'Roman', 'Egyptian')

        Returns:
        - JSON với thông tin so sánh giữa hai nền thần thoại
        """
        logger.info(f"So sánh thần thoại: {mythology1} và {mythology2}")

        try:
            # Lấy thông tin về từng nền thần thoại
            myth1_info = await self.search_mythopedia(mythology1 + " mythology", limit=1)
            myth2_info = await self.search_mythopedia(mythology2 + " mythology", limit=1)
            
            if myth1_info.get("status") != "success" or myth2_info.get("status") != "success":
                return {
                    "status": "error",
                    "source": "Mythopedia",
                    "message": "Không thể tìm thấy thông tin về một hoặc cả hai nền thần thoại"
                }
            
            # Lấy danh sách các vị thần chính
            gods1 = await self.search_mythopedia(f"{mythology1} gods", limit=5)
            gods2 = await self.search_mythopedia(f"{mythology2} gods", limit=5)
            
            # Lấy danh sách các sinh vật thần thoại
            creatures1 = await self.search_creatures(origin=mythology1, limit=5)
            creatures2 = await self.search_creatures(origin=mythology2, limit=5)
            
            return {
                "status": "success",
                "source": "Mythopedia",
                "comparison": {
                    "mythology1": {
                        "name": mythology1,
                        "info": myth1_info.get("results", [{}])[0] if myth1_info.get("results") else {},
                        "major_gods": [g.get("name") for g in gods1.get("results", [])[:3]] if gods1.get("results") else [],
                        "creatures": [c.get("name") for c in creatures1.get("creatures", [])[:3]] if creatures1.get("creatures") else []
                    },
                    "mythology2": {
                        "name": mythology2,
                        "info": myth2_info.get("results", [{}])[0] if myth2_info.get("results") else {},
                        "major_gods": [g.get("name") for g in gods2.get("results", [])[:3]] if gods2.get("results") else [],
                        "creatures": [c.get("name") for c in creatures2.get("creatures", [])[:3]] if creatures2.get("creatures") else []
                    },
                    "similarities": [
                        f"Cả hai đều là hệ thống thần thoại đa thần",
                        f"Đều có các vị thần đại diện cho các hiện tượng tự nhiên",
                        f"Có các câu chuyện về sự sáng tạo và tận thế"
                    ],
                    "differences": [
                        f"{mythology1} có hệ thống thần thoại {myth1_info.get('results', [{}])[0].get('origin', '').lower() if myth1_info.get('results') else ''} trong khi {mythology2} có nguồn gốc {myth2_info.get('results', [{}])[0].get('origin', '').lower() if myth2_info.get('results') else ''}",
                        f"Các vị thần chính khác biệt rõ ràng giữa hai hệ thống"
                    ]
                },
                "suggested_searches": [
                    f"So sánh giữa thần Zeus ({mythology1}) và thần Odin ({mythology2})",
                    f"Sự tương đồng giữa {mythology1} và {mythology2} creation myths",
                    f"Sự khác biệt giữa {mythology1} và {mythology2} pantheons"
                ]
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi so sánh thần thoại: {str(e)}")
            return {
                "status": "error",
                "source": "Mythopedia",
                "message": str(e),
                "mythology1": mythology1,
                "mythology2": mythology2
            }
