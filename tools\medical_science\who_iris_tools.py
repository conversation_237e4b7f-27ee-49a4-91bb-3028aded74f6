from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class WHOIrisTool(Toolkit):
    """
    WHO IRIS Tool cho tìm kiếm tài liệu y tế công cộng, b<PERSON><PERSON> c<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> cứu từ WHO IRIS (Institutional Repository for Information Sharing).
    """

    def __init__(self):
        super().__init__(
            name="WHO IRIS Search Tool",
            tools=[self.search_who_iris]
        )

    async def search_who_iris(self, query: str, language: str = "en", limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm WHO IRIS cho tài liệu y tế công cộng, báo c<PERSON>o, nghiên cứu.

        Parameters:
        - query: Từ khóa tìm kiếm (ví dụ: 'malaria treatment', 'COVID-19 vaccine', 'public health policy')
        - language: Mã ngôn ngữ (ví dụ: 'en', 'fr', 'es', 'ru', 'zh', 'ar')
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với tiêu đề, tác giả, năm, mô tả, link tài liệu WHO IRIS
        """
        logger.info(f"Tìm kiếm WHO IRIS: query={query}, language={language}, limit={limit}")

        try:
            # WHO IRIS sử dụng DSpace REST API (hoặc OpenSearch)
            # Sử dụng endpoint OpenSearch (RSS/JSON)
            search_url = "https://apps.who.int/iris/discover/search"
            params = {
                "query": query,
                "scope": "",
                "filtertype_0": "language",
                "filter_relational_operator_0": "equals",
                "filter_0": language,
                "sort_by": "score",
                "order": "desc",
                "rpp": limit,
                "format": "json"
            }
            response = requests.get(search_url, params=params, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "WHO IRIS",
                    "message": f"WHO IRIS API trả về mã lỗi {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for item in data.get("results", []):
                title = item.get("title")
                authors = item.get("author")
                year = item.get("dateIssued")
                description = item.get("description")
                iris_url = item.get("handleUrl") or item.get("uri")
                subjects = item.get("subject")
                results.append({
                    "title": title,
                    "authors": authors,
                    "year": year,
                    "description": description,
                    "subjects": subjects,
                    "iris_url": iris_url
                })

            return {
                "status": "success",
                "source": "WHO IRIS",
                "query": query,
                "language": language,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "malaria treatment",
                    "COVID-19 vaccine",
                    "public health policy",
                    "tuberculosis report",
                    "maternal health",
                    "noncommunicable diseases",
                    "health systems strengthening"
                ],
                "official_data_url": "https://apps.who.int/iris/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm WHO IRIS: {str(e)}")
            return {
                "status": "error",
                "source": "WHO IRIS",
                "message": str(e),
                "query": query
            }
