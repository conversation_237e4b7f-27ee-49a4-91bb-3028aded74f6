from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger, log_warning
import aiohttp
import asyncio
import re
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import json

# Cache kết quả tìm kiếm trong 1 giờ
SEARCH_CACHE = {}
CACHE_EXPIRY = 3600  # 1 giờ

class CacheEntry:
    def __init__(self, data: Any):
        self.data = data
        self.timestamp = datetime.now()
    
    def is_expired(self) -> bool:
        return (datetime.now() - self.timestamp).total_seconds() > CACHE_EXPIRY

def cache_key(query: str, topic: Optional[str], year: Optional[str], limit: int) -> str:
    """Tạo khóa cache duy nhất cho mỗi yêu cầu tìm kiếm"""
    return f"fao:{query}:{topic or 'all'}:{year or 'all'}:{limit}"

class FAOAgriTools(Toolkit):
    """
    FAO Agriculture Tool cho tìm kiếm dữ liệu, b<PERSON><PERSON> c<PERSON>, công nghệ nông nghiệp từ FAO.
    Hỗ trợ caching, retry tự động và xử lý bất đồng bộ để tối ưu hiệu suất.
    """

    def __init__(self):
        super().__init__(
            name="FAO Agriculture Search Tool",
            tools=[self.search_fao_agri]
        )
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=20, connect=8)
        self.retry_attempts = 3
        self.retry_delay = 2  # giây
        self.base_url = "https://www.fao.org"
        self.search_url = f"{self.base_url}/search/en/"

    async def _get_session(self) -> aiohttp.ClientSession:
        """Tạo hoặc trả về session hiện có"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                timeout=self.timeout,
                headers={
                    "User-Agent": "Mozilla/5.0 (compatible; FAOAgriSearchBot/1.0; +https://github.com/your-repo)",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                }
            )
        return self.session

    async def _make_request(self, url: str, params: Dict[str, Any], attempt: int = 1) -> str:
        """Thực hiện HTTP request với cơ chế retry"""
        session = await self._get_session()
        
        try:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.text()
                elif response.status in [429, 502, 503, 504] and attempt <= self.retry_attempts:
                    retry_after = int(response.headers.get('Retry-After', self.retry_delay * attempt))
                    await asyncio.sleep(retry_after)
                    return await self._make_request(url, params, attempt + 1)
                else:
                    raise Exception(f"HTTP {response.status}: {response.reason}")
        except asyncio.TimeoutError:
            if attempt <= self.retry_attempts:
                await asyncio.sleep(self.retry_delay * attempt)
                return await self._make_request(url, params, attempt + 1)
            raise Exception("Request timeout after multiple retries")
        except Exception as e:
            if attempt <= self.retry_attempts:
                await asyncio.sleep(self.retry_delay * attempt)
                return await self._make_request(url, params, attempt + 1)
            raise e

    async def search_fao_agri(self, query: str, topic: Optional[str] = None, year: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm dữ liệu, báo cáo, công nghệ nông nghiệp từ FAO với hiệu suất cao.

        Parameters:
        - query: Từ khóa tìm kiếm (ví dụ: 'rice production', 'precision agriculture')
        - topic: Chủ đề cụ thể (ví dụ: 'technology', 'market', 'statistics')
        - year: Năm hoặc khoảng năm (ví dụ: '2022', '2015-2022')
        - limit: Số lượng kết quả tối đa (default: 5, tối đa 20)

        Returns:
        - Dict chứa kết quả tìm kiếm hoặc thông báo lỗi
        """
        # Kiểm tra cache trước
        cache_key_str = cache_key(query, topic, year, limit)
        if cache_key_str in SEARCH_CACHE and not SEARCH_CACHE[cache_key_str].is_expired():
            logger.info(f"Lấy kết quả từ cache cho: {query}")
            return SEARCH_CACHE[cache_key_str].data

        logger.info(f"Tìm kiếm FAO: query={query}, topic={topic}, year={year}, limit={limit}")

        try:
            # Xây dựng tham số tìm kiếm
            params = {
                "q": query,
                "sort": "date",
                "rows": min(limit, 20)  # Giới hạn tối đa 20 kết quả
            }
            
            if topic:
                params["fq"] = f"topic:{topic}"
            if year:
                params["fq"] = f"year:{year}"
            
            # Thực hiện request bất đồng bộ
            html_content = await self._make_request(self.search_url, params)
            
            # Phân tích HTML với BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Lấy các kết quả tìm kiếm
            results = []
            result_items = soup.select('.result-item')
            
            for item in result_items[:limit]:
                try:
                    # Trích xuất thông tin
                    title_elem = item.select_one('h3 a')
                    title = title_elem.get_text(strip=True) if title_elem else "No title"
                    
                    url = title_elem['href'] if title_elem and 'href' in title_elem.attrs else None
                    if url and url.startswith('/'):
                        url = f"{self.base_url}{url}"
                    
                    desc_elem = item.select_one('.result-description')
                    description = desc_elem.get_text(strip=True) if desc_elem else None
                    
                    # Trích xuất metadata
                    meta_elems = item.select('.result-meta span')
                    meta_data = {}
                    for meta in meta_elems:
                        text = meta.get_text(strip=True)
                        if ':' in text:
                            key, value = text.split(':', 1)
                            meta_data[key.strip().lower()] = value.strip()
                    
                    # Lấy ảnh đại diện (nếu có)
                    img_elem = item.select_one('.result-image img')
                    image_url = img_elem['src'] if img_elem and 'src' in img_elem.attrs else None
                    if image_url and image_url.startswith('/'):
                        image_url = f"{self.base_url}{image_url}"
                    
                    results.append({
                        "title": title,
                        "description": description[:500] + ("..." if description and len(description) > 500 else ""),
                        "url": url,
                        "image_url": image_url,
                        "source": "FAO",
                        "metadata": meta_data,
                        "year": meta_data.get('year', year)
                    })
                except Exception as e:
                    log_warning(f"Lỗi khi xử lý kết quả: {str(e)}")
                    continue

            # Tạo kết quả và lưu vào cache
            result = {
                "status": "success",
                "source": "FAO",
                "query": query,
                "topic": topic,
                "year": year,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "rice production",
                    "precision agriculture",
                    "climate smart agriculture",
                    "food security",
                    "market report",
                    "agricultural statistics",
                    "sustainable farming",
                    "digital agriculture",
                    "policy brief"
                ],
                "official_data_url": "https://www.fao.org/search/en/",
                "cached_at": datetime.now().isoformat(),
                "cache_ttl_seconds": CACHE_EXPIRY
            }
            
            # Lưu vào cache nếu có kết quả
            if results:
                SEARCH_CACHE[cache_key_str] = CacheEntry(result)
                
            return result

        except Exception as e:
            log_warning(f"Lỗi khi tìm kiếm FAO: {str(e)}")
            return {
                "status": "error",
                "source": "FAO",
                "message": f"Lỗi: {str(e)}",
                "query": query,
                "suggestion": "Vui lòng thử lại với từ khóa khác hoặc kiểm tra kết nối mạng."
            }
            
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session and not self.session.closed:
            await self.session.close()
            
    def __del__(self):
        if hasattr(self, 'session') and self.session and not self.session.closed:
            if not self.session._loop.is_closed():
                asyncio.create_task(self.session.close())
