from typing import Dict, Any, List, Optional
from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.astronomy.nasa_ads_tools import NasaAdsTools
from ..config import MODEL_CONFIG, NASA_ADS_CONFIG
import logging
import json

logger = logging.getLogger(__name__)

class NasaAdsAgent(Agent):
    """Agent chuyên tìm kiếm tài liệu từ NASA ADS."""
    
    def __init__(self, **kwargs):
        super().__init__(
            model=Ollama(
                id=MODEL_CONFIG["base_model"],
            ),
            name="NASA ADS Agent",
            description="""
            Bạn là một chuyên gia tìm kiếm tài liệu thiên văn học từ cơ sở dữ liệu NASA ADS.
            Nhiệm vụ của bạn là tìm kiếm và trả về các bài báo khoa học liên quan đến chủ đề được yêu cầu.
            """,
            **kwargs
        )
        
        # Khởi tạo công cụ NASA ADS
        self.nasa_ads_tools = NasaAdsTools(
            search_ads_papers=True,
            token=NASA_ADS_CONFIG["token"],
            timeout=NASA_ADS_CONFIG["timeout"],
            max_retries=NASA_ADS_CONFIG["max_retries"]
        )
        self.tools = [self.nasa_ads_tools]
        
        logger.info("Khởi tạo NASA ADS Agent thành công")
    
    async def arun(self, message: Any, **kwargs) -> dict:
        """Tìm kiếm bài báo khoa học dựa trên danh sách từ khóa."""
        # message có thể là dict hoặc list hoặc str
        if isinstance(message, dict) and "keywords" in message:
            keywords = message["keywords"]
        elif isinstance(message, list):
            keywords = message
        elif isinstance(message, str):
            keywords = [message]
        else:
            keywords = []
        if not keywords:
            return {"status": "error", "message": "Không có từ khóa tìm kiếm", "papers": [], "total_results": 0}
        max_papers = kwargs.get("max_papers", NASA_ADS_CONFIG["max_papers"])
        query = " OR ".join([f"{kw}" for kw in keywords])
        logger.info(f"Đang tìm kiếm với query: {query}")
        try:
            search_result = await self.nasa_ads_tools.search_nasa_ads_papers(
                query=query,
                rows=max_papers
            )
            papers = json.loads(search_result)
            formatted_results = []
            for paper in papers:
                formatted_results.append({
                    "title": paper.get("title", "Không có tiêu đề"),
                    "authors": ", ".join(paper.get("authors", ["Không rõ tác giả"])[:3]) + \
                              (" và cộng sự" if len(paper.get("authors", [])) > 3 else ""),
                    "year": paper.get("published", "")[:4] if paper.get("published") else "N/A",
                    "abstract": paper.get("abstract", "Không có tóm tắt"),
                    "doi": paper.get("doi", "Không có DOI"),
                    "url": paper.get("ads_url", "#")
                })
            logger.info(f"Tìm thấy {len(formatted_results)} bài báo")
            return {
                "status": "success",
                "query": query,
                "papers": formatted_results,
                "total_results": len(formatted_results)
            }
        except Exception as e:
            logger.error(f"Lỗi khi tìm kiếm bài báo: {str(e)}")
            return {
                "status": "error",
                "message": f"Lỗi khi tìm kiếm bài báo: {str(e)}",
                "papers": [],
                "total_results": 0
            }

    async def run(self, message: Any, **kwargs) -> dict:
        return await self.arun(message, **kwargs)
