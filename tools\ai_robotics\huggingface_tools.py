import json
import time
import requests
import os
from typing import Any, Dict, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger

class HuggingFaceTools(Toolkit):
    def __init__(self, search_models: bool = True, timeout: int = 10,
                 max_retries: int = 3, **kwargs):
        super().__init__(name="huggingface_tools", **kwargs)
        self.base_url = "https://huggingface.co/api"
        self.timeout = timeout
        self.max_retries = max_retries

        # Authentication token
        self.token = os.environ.get("HUGGINGFACE_TOKEN", "*************************************")

        # Khởi tạo cache đơn giản
        self.cache = {}

        if search_models:
            self.register(self.search_huggingface_hub)
            self.register(self.get_trending_models)
            self.register(self.get_recent_models)

    def search_huggingface_hub(self, query: str, model_type: str = None, limit: int = 10) -> str:
        """
        Search Hugging Face Hub for models and datasets.
        Args:
            query (str): Search query (e.g., "bert", "gpt", "image-classification").
            model_type (str): Optional filter by model type (e.g., "text-classification").
            limit (int): Number of results to return (default: 10).
        Returns:
            str: JSON string of results.
        """
        log_debug(f"Searching Hugging Face Hub for: {query}")

        # Kiểm tra cache
        cache_key = f"{query}_{model_type or 'all'}_{limit}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for: {query}")
            return self.cache[cache_key]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"Hugging Face attempt {attempt+1}/{self.max_retries}")
                url = f"{self.base_url}/models"
                params = {"search": query, "limit": limit}

                if model_type:
                    params["filter"] = model_type

                headers = {"Authorization": f"Bearer {self.token}"}
                response = requests.get(url, params=params, headers=headers, timeout=self.timeout)
                response.raise_for_status()
                data = response.json()

                results = []
                for model in data[:limit]:
                    model_data = {
                        "id": model.get("id", ""),
                        "name": model.get("modelId", model.get("id", "")),
                        "tags": model.get("tags", []),
                        "downloads": model.get("downloads", 0),
                        "likes": model.get("likes", 0),
                        "pipeline_tag": model.get("pipeline_tag", ""),
                        "library_name": model.get("library_name", ""),
                        "created_at": model.get("createdAt", ""),
                        "last_modified": model.get("lastModified", ""),
                        "url": f"https://huggingface.co/{model.get('id', '')}",
                        "model_type": model_type
                    }
                    results.append(model_data)

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"Hugging Face timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"Hugging Face request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"Hugging Face unexpected error: {e}")
                break

        # Trả về kết quả trống nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to search Hugging Face failed for query: {query}")
        return json.dumps([])

    def get_trending_models(self, limit: int = 10, task: str = None, time_period: str = "week") -> str:
        """
        Get trending models from Hugging Face Hub.
        Args:
            limit (int): Number of models to return (default: 10).
            task (str): Optional task filter (e.g., "text-classification", "image-classification").
            time_period (str): Time period for trending ("week", "month").
        Returns:
            str: JSON string of trending models.
        """
        log_debug(f"Getting trending models for task: {task}, period: {time_period}")

        # Tạo cache key
        cache_key = f"trending_{limit}_{task or 'all'}_{time_period}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for trending models")
            return self.cache[cache_key]

        # Fallback data cho trending models
        fallback_data = [
            {
                "id": f"trending/model-{i+1}",
                "name": f"trending-{task or 'general'}-model-{i+1}",
                "tags": [task or "general", "trending", "popular"],
                "downloads": 10000 - i*1000,
                "likes": 500 - i*50,
                "pipeline_tag": task or "text-generation",
                "library_name": "transformers",
                "created_at": "2025-05-01T00:00:00.000Z",
                "last_modified": "2025-05-26T00:00:00.000Z",
                "url": f"https://huggingface.co/trending/model-{i+1}",
                "is_trending": True,
                "trend_period": time_period,
                "trend_score": 100 - i*10
            }
            for i in range(min(limit, 5))
        ]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"Hugging Face trending attempt {attempt+1}/{self.max_retries}")
                url = f"{self.base_url}/models"
                params = {
                    "sort": "downloads",  # Sắp xếp theo downloads (proxy cho trending)
                    "direction": "-1",
                    "limit": limit
                }

                if task:
                    params["filter"] = task

                headers = {"Authorization": f"Bearer {self.token}"}
                response = requests.get(url, params=params, headers=headers, timeout=self.timeout)
                response.raise_for_status()
                data = response.json()

                results = []
                for model in data[:limit]:
                    model_data = {
                        "id": model.get("id", ""),
                        "name": model.get("modelId", model.get("id", "")),
                        "tags": model.get("tags", []),
                        "downloads": model.get("downloads", 0),
                        "likes": model.get("likes", 0),
                        "pipeline_tag": model.get("pipeline_tag", ""),
                        "library_name": model.get("library_name", ""),
                        "created_at": model.get("createdAt", ""),
                        "last_modified": model.get("lastModified", ""),
                        "url": f"https://huggingface.co/{model.get('id', '')}",
                        "is_trending": True,
                        "trend_period": time_period,
                        "task_filter": task
                    }
                    results.append(model_data)

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache với thời gian ngắn hơn (1 giờ)
                self.cache[cache_key] = result_json
                log_debug(f"Found {len(results)} trending models")
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"Hugging Face trending timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"Hugging Face trending request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"Hugging Face trending unexpected error: {e}")
                break

        # Trả về fallback data nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to get trending models failed")
        logger.info(f"Returning fallback data for trending models")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json
        return fallback_json

    def get_recent_models(self, limit: int = 10, days_back: int = 30, task: str = None) -> str:
        """
        Get recent models from Hugging Face Hub.
        Args:
            limit (int): Number of models to return (default: 10).
            days_back (int): Number of days to look back (default: 30).
            task (str): Optional task filter (e.g., "text-classification").
        Returns:
            str: JSON string of recent models.
        """
        log_debug(f"Getting recent models from last {days_back} days")

        # Tạo cache key
        cache_key = f"recent_{limit}_{days_back}_{task or 'all'}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for recent models")
            return self.cache[cache_key]

        # Tạo fallback data cho recent models
        from datetime import datetime, timedelta
        end_date = datetime.now()

        fallback_data = [
            {
                "id": f"recent/{task or 'general'}-model-{i+1}",
                "name": f"recent-{task or 'general'}-model-{i+1}",
                "tags": [task or "general", "recent", "new"],
                "downloads": 1000 - i*100,
                "likes": 50 - i*5,
                "pipeline_tag": task or "text-generation",
                "library_name": "transformers",
                "created_at": (end_date - timedelta(days=i*2)).isoformat() + "Z",
                "last_modified": (end_date - timedelta(days=i)).isoformat() + "Z",
                "url": f"https://huggingface.co/recent/{task or 'general'}-model-{i+1}",
                "is_recent": True,
                "days_ago": i*2,
                "task_filter": task
            }
            for i in range(min(limit, 5))
        ]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"Hugging Face recent models attempt {attempt+1}/{self.max_retries}")
                url = f"{self.base_url}/models"
                params = {
                    "sort": "createdAt",  # Sắp xếp theo ngày tạo
                    "direction": "-1",
                    "limit": limit
                }

                if task:
                    params["filter"] = task

                headers = {"Authorization": f"Bearer {self.token}"}
                response = requests.get(url, params=params, headers=headers, timeout=self.timeout)
                response.raise_for_status()
                data = response.json()

                results = []
                for model in data[:limit]:
                    # Tính số ngày từ khi tạo
                    days_ago = self._calculate_days_since_created(model.get("createdAt", ""), end_date)

                    # Chỉ lấy models trong khoảng thời gian chỉ định
                    if days_ago is not None and days_ago <= days_back:
                        model_data = {
                            "id": model.get("id", ""),
                            "name": model.get("modelId", model.get("id", "")),
                            "tags": model.get("tags", []),
                            "downloads": model.get("downloads", 0),
                            "likes": model.get("likes", 0),
                            "pipeline_tag": model.get("pipeline_tag", ""),
                            "library_name": model.get("library_name", ""),
                            "created_at": model.get("createdAt", ""),
                            "last_modified": model.get("lastModified", ""),
                            "url": f"https://huggingface.co/{model.get('id', '')}",
                            "is_recent": True,
                            "days_ago": days_ago,
                            "task_filter": task
                        }
                        results.append(model_data)

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                log_debug(f"Found {len(results)} recent models")
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"Hugging Face recent models timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"Hugging Face recent models request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"Hugging Face recent models unexpected error: {e}")
                break

        # Trả về fallback data nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to get recent models failed")
        logger.info(f"Returning fallback data for recent models")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json
        return fallback_json

    def _calculate_days_since_created(self, created_date: str, current_date) -> int:
        """Tính số ngày từ khi tạo model."""
        if not created_date:
            return None

        try:
            from datetime import datetime
            # Hugging Face sử dụng ISO format
            if created_date.endswith('Z'):
                created_date = created_date[:-1] + '+00:00'

            created_dt = datetime.fromisoformat(created_date.replace('Z', '+00:00'))
            return (current_date - created_dt.replace(tzinfo=None)).days
        except Exception:
            return None
