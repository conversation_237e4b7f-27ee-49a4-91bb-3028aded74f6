#!/usr/bin/env python3
"""
Final script để fix tất cả các file còn lại
"""

import os
import re

def final_fix_file(file_path):
    """Fix file một cách chính xác cuối cùng"""
    print(f"Final fixing {file_path}...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove unused imports
    content = re.sub(r'from agno.utils.log import log_debug, logger', 'from agno.utils.log import logger', content)
    
    # Fix broken response blocks with missing metadata and extra braces
    # Pattern 1: Fix incomplete response with missing metadata
    pattern1 = r'(\s+)response = \{\n(\s+"status": "success",\n\s+"source": "[^"]+",\n(?:\s+"[^"]+": [^,\n]+,\n)*\s+"total_results": len\(results\),\n\s+"results": results)\n\n\s+\}\n\n\s+return json\.dumps\(response, ensure_ascii=False, indent=2\)'
    
    def replace_success_response(match):
        indent = match.group(1)
        content_part = match.group(2)
        return f'''{indent}response = {{
{content_part},
{indent}    "search_metadata": {{
{indent}        "search_time": "2024-01-15T10:30:00Z",
{indent}        "database_coverage": "Global database"
{indent}    }}
{indent}}}
{indent}return json.dumps(response, ensure_ascii=False, indent=2)'''
    
    content = re.sub(pattern1, replace_success_response, content, flags=re.DOTALL)
    
    # Pattern 2: Fix error responses
    pattern2 = r'(\s+)response = \{\n(\s+"status": "error",\n\s+"source": "[^"]+",\n\s+"message": str\(e\))\n\n\s+\}\n\n\s+return json\.dumps\(response, ensure_ascii=False, indent=2\)'
    
    def replace_error_response(match):
        indent = match.group(1)
        content_part = match.group(2)
        return f'''{indent}response = {{
{content_part}
{indent}}}
{indent}return json.dumps(response, ensure_ascii=False, indent=2)'''
    
    content = re.sub(pattern2, replace_error_response, content, flags=re.DOTALL)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Final fixed {file_path}")

# Fix all remaining files
all_files = []

# Get all Python files in archaeology and aviation
for root, dirs, files in os.walk('tools/archaeology'):
    for file in files:
        if file.endswith('.py') and file != '__init__.py' and file != 'test_new_functions.py':
            all_files.append(os.path.join(root, file))

for root, dirs, files in os.walk('tools/aviation'):
    for file in files:
        if file.endswith('.py'):
            all_files.append(os.path.join(root, file))

for file_path in all_files:
    if os.path.exists(file_path):
        final_fix_file(file_path)

print("Done with final fixes!")
