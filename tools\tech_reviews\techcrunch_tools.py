import requests
import json
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
from datetime import datetime
from bs4 import BeautifulSoup
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class TechCrunchTools(Toolkit):
    """
    Công cụ tìm kiếm và truy xuất tin tức khởi nghiệp và công nghệ từ TechCrunch.
    
    Cung cấp quyền truy cập vào các bà<PERSON> b<PERSON>o, tin tức khởi nghiệp, vòng gọi vốn,
    và xu hướng công nghệ mới nhất.
    
    Keyword gợi ý: "khởi nghiệp công nghệ", "vòng gọi vốn series A", "startup công nghệ",
    "tin tức blockchain", "công nghệ tài chính", "AI trong doanh nghiệp"
    """
    
    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(name="techcrunch_tools", **kwargs)
        self.base_url = "https://techcrunch.com"
        self.api_url = "https://techcrunch.com/wp-json/tc/v1/magazine"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        if enable_search:
            self.register(self.search_articles)
            self.register(self.get_latest_news)
    
    def search_articles(self, query: str, max_results: int = 5) -> str:
        """
        Tìm kiếm bài viết trên TechCrunch.
        
        Args:
            query (str): Từ khóa tìm kiếm (ví dụ: "khởi nghiệp công nghệ", "startup AI")
            max_results (int, optional): Số lượng kết quả tối đa. Mặc định: 5.
            
        Returns:
            str: Chuỗi JSON chứa kết quả tìm kiếm
            
        Ví dụ:
            search_articles("khởi nghiệp công nghệ Việt Nam", 3)
        """
        log_debug(f"Tìm kiếm trên TechCrunch: {query}")
        
        try:
            search_url = f"{self.base_url}/search"
            params = {
                "s": query,
                "sortby": "recent"
            }
            
            response = requests.get(
                search_url,
                params=params,
                headers=self.headers,
                timeout=15
            )
            response.raise_for_status()
            
            # Phân tích kết quả HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # Lấy các kết quả tìm kiếm
            search_results = soup.select('div.content article')
            
            for result in search_results[:max_results]:
                title_elem = result.select_one('h2 a')
                if not title_elem:
                    continue
                    
                title = title_elem.get_text(strip=True)
                url = title_elem.get('href', '')
                
                # Lấy mô tả nếu có
                desc_elem = result.select_one('.excerpt p')
                description = desc_elem.get_text(strip=True) if desc_elem else ""
                
                # Lấy ngày đăng
                date_elem = result.select_one('time')
                date = date_elem.get('datetime') if date_elem else ""
                
                # Lấy tác giả
                author_elem = result.select_one('.author-name')
                author = author_elem.get_text(strip=True) if author_elem else ""
                
                results.append({
                    "title": title,
                    "url": url,
                    "summary": description,
                    "date": date,
                    "author": author,
                    "source": "TechCrunch"
                })
            
            # Nếu không có kết quả, trả về kết quả mặc định
            if not results:
                return self._get_default_results(query)
            
            return json.dumps({
                "status": "success",
                "source": "TechCrunch",
                "query": query,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)
            
        except requests.RequestException as e:
            logger.error(f"Lỗi khi truy vấn TechCrunch: {e}")
            return self._get_error_response(query, str(e))
    
    def get_latest_news(self, category: str = "startups", limit: int = 5) -> str:
        """
        Lấy tin tức mới nhất từ TechCrunch theo danh mục.
        
        Args:
            category (str): Danh mục tin tức (startups, ai, apps, security, etc.)
            limit (int): Số lượng kết quả tối đa
            
        Returns:
            str: Chuỗi JSON chứa danh sách tin tức
        """
        log_debug(f"Lấy tin tức mới nhất từ TechCrunch - Danh mục: {category}")
        
        try:
            category_map = {
                "startups": "startups",
                "ai": "artificial-intelligence",
                "apps": "apps",
                "security": "security",
                "cryptocurrency": "cryptocurrency",
                "fintech": "fintech"
            }
            
            category_path = category_map.get(category.lower(), "startups")
            url = f"{self.base_url}/category/{category_path}/"
            
            response = requests.get(url, headers=self.headers, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            articles = soup.select('div.content article')
            for article in articles[:limit]:
                title_elem = article.select_one('h2 a')
                if not title_elem:
                    continue
                    
                title = title_elem.get_text(strip=True)
                url = title_elem.get('href', '')
                
                desc_elem = article.select_one('.excerpt p')
                description = desc_elem.get_text(strip=True) if desc_elem else ""
                
                date_elem = article.select_one('time')
                date = date_elem.get('datetime') if date_elem else ""
                
                author_elem = article.select_one('.author-name')
                author = author_elem.get_text(strip=True) if author_elem else ""
                
                results.append({
                    "title": title,
                    "url": url,
                    "summary": description,
                    "date": date,
                    "author": author,
                    "category": category,
                    "source": "TechCrunch"
                })
            
            return json.dumps({
                "status": "success",
                "source": "TechCrunch",
                "category": category,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"Lỗi khi lấy tin tức từ TechCrunch: {e}")
            return json.dumps({
                "status": "error",
                "source": "TechCrunch",
                "category": category,
                "message": str(e),
                "results": []
            }, indent=2, ensure_ascii=False)
    
    def _get_default_results(self, query: str) -> str:
        """Trả về kết quả mặc định khi không tìm thấy kết quả."""
        default_results = [
            {
                "title": "Top 10 Startup đáng chú ý 2024",
                "url": "https://techcrunch.com/startups-2024",
                "summary": "Danh sách các startup công nghệ đáng chú ý nhất năm 2024.",
                "source": "TechCrunch"
            },
            {
                "title": "Xu hướng AI trong doanh nghiệp",
                "url": "https://techcrunch.com/ai-business-trends",
                "summary": "Cách các doanh nghiệp đang áp dụng AI để cải thiện hiệu suất.",
                "source": "TechCrunch"
            },
            {
                "title": "Hướng dẫn gọi vốn Series A",
                "url": "https://techcrunch.com/guide-series-a-funding",
                "summary": "Những điều cần biết khi gọi vốn vòng Series A cho startup công nghệ.",
                "source": "TechCrunch"
            }
        ]
        
        return json.dumps({
            "status": "success",
            "source": "TechCrunch",
            "query": query,
            "message": "Không tìm thấy kết quả phù hợp. Dưới đây là một số gợi ý.",
            "results": default_results,
            "result_count": len(default_results)
        }, indent=2, ensure_ascii=False)
    
    def _get_error_response(self, query: str, error_msg: str) -> str:
        """Trả về phản hồi lỗi có cấu trúc."""
        return json.dumps({
            "status": "error",
            "source": "TechCrunch",
            "query": query,
            "message": f"Không thể truy xuất kết quả: {error_msg}",
            "results": []
        }, indent=2, ensure_ascii=False)
