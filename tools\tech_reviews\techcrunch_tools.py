import requests
import json
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
from datetime import datetime
from bs4 import BeautifulSoup
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class TechCrunchTools(Toolkit):
    """
    Công cụ tìm kiếm và truy xuất tin tức khởi nghiệp và công nghệ từ TechCrunch.

    Cung cấp quyền truy cập vào các bà<PERSON> b<PERSON>, tin tức khởi nghiệp, vòng gọi vốn,
    và xu hướng công nghệ mới nhất.

    Keyword gợi ý: "khởi nghiệp công nghệ", "vòng gọi vốn series A", "startup công nghệ",
    "tin tức blockchain", "công nghệ tài chính", "AI trong doanh nghiệp"
    """

    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(name="techcrunch_tools", **kwargs)
        self.base_url = "https://techcrunch.com"
        self.api_url = "https://techcrunch.com/wp-json/tc/v1/magazine"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        if enable_search:
            self.register(self.search_articles)
            self.register(self.get_latest_news)
            self.register(self.get_top_new)

    def search_articles(self, query: str, max_results: int = 5) -> str:
        """
        Tìm kiếm bài viết trên TechCrunch.

        Args:
            query (str): Từ khóa tìm kiếm (ví dụ: "khởi nghiệp công nghệ", "startup AI")
            max_results (int, optional): Số lượng kết quả tối đa. Mặc định: 5.

        Returns:
            str: Chuỗi JSON chứa kết quả tìm kiếm

        Ví dụ:
            search_articles("khởi nghiệp công nghệ Việt Nam", 3)
        """
        log_debug(f"Tìm kiếm trên TechCrunch: {query}")

        try:
            search_url = f"{self.base_url}/search"
            params = {
                "s": query,
                "sortby": "recent"
            }

            response = requests.get(
                search_url,
                params=params,
                headers=self.headers,
                timeout=15
            )
            response.raise_for_status()

            # Phân tích kết quả HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []

            # Lấy các kết quả tìm kiếm
            search_results = soup.select('div.content article')

            for result in search_results[:max_results]:
                title_elem = result.select_one('h2 a')
                if not title_elem:
                    continue

                title = title_elem.get_text(strip=True)
                url = title_elem.get('href', '')

                # Lấy mô tả nếu có
                desc_elem = result.select_one('.excerpt p')
                description = desc_elem.get_text(strip=True) if desc_elem else ""

                # Lấy ngày đăng
                date_elem = result.select_one('time')
                date = date_elem.get('datetime') if date_elem else ""

                # Lấy tác giả
                author_elem = result.select_one('.author-name')
                author = author_elem.get_text(strip=True) if author_elem else ""

                results.append({
                    "title": title,
                    "url": url,
                    "summary": description,
                    "date": date,
                    "author": author,
                    "source": "TechCrunch"
                })

            # Nếu không có kết quả, trả về kết quả mặc định
            if not results:
                return self._get_default_results(query)

            return json.dumps({
                "status": "success",
                "source": "TechCrunch",
                "query": query,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)

        except requests.RequestException as e:
            logger.error(f"Lỗi khi truy vấn TechCrunch: {e}")
            return self._get_error_response(query, str(e))

    def get_latest_news(self, category: str = "startups", limit: int = 5) -> str:
        """
        Lấy tin tức mới nhất từ TechCrunch theo danh mục.

        Args:
            category (str): Danh mục tin tức (startups, ai, apps, security, etc.)
            limit (int): Số lượng kết quả tối đa

        Returns:
            str: Chuỗi JSON chứa danh sách tin tức
        """
        log_debug(f"Lấy tin tức mới nhất từ TechCrunch - Danh mục: {category}")

        try:
            category_map = {
                "startups": "startups",
                "ai": "artificial-intelligence",
                "apps": "apps",
                "security": "security",
                "cryptocurrency": "cryptocurrency",
                "fintech": "fintech"
            }

            category_path = category_map.get(category.lower(), "startups")
            url = f"{self.base_url}/category/{category_path}/"

            response = requests.get(url, headers=self.headers, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')
            results = []

            articles = soup.select('div.content article')
            for article in articles[:limit]:
                title_elem = article.select_one('h2 a')
                if not title_elem:
                    continue

                title = title_elem.get_text(strip=True)
                url = title_elem.get('href', '')

                desc_elem = article.select_one('.excerpt p')
                description = desc_elem.get_text(strip=True) if desc_elem else ""

                date_elem = article.select_one('time')
                date = date_elem.get('datetime') if date_elem else ""

                author_elem = article.select_one('.author-name')
                author = author_elem.get_text(strip=True) if author_elem else ""

                results.append({
                    "title": title,
                    "url": url,
                    "summary": description,
                    "date": date,
                    "author": author,
                    "category": category,
                    "source": "TechCrunch"
                })

            return json.dumps({
                "status": "success",
                "source": "TechCrunch",
                "category": category,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Lỗi khi lấy tin tức từ TechCrunch: {e}")
            return json.dumps({
                "status": "error",
                "source": "TechCrunch",
                "category": category,
                "message": str(e),
                "results": []
            }, indent=2, ensure_ascii=False)

    def _get_default_results(self, query: str) -> str:
        """Trả về kết quả mặc định khi không tìm thấy kết quả."""
        default_results = [
            {
                "title": "Top 10 Startup đáng chú ý 2024",
                "url": "https://techcrunch.com/startups-2024",
                "summary": "Danh sách các startup công nghệ đáng chú ý nhất năm 2024.",
                "source": "TechCrunch"
            },
            {
                "title": "Xu hướng AI trong doanh nghiệp",
                "url": "https://techcrunch.com/ai-business-trends",
                "summary": "Cách các doanh nghiệp đang áp dụng AI để cải thiện hiệu suất.",
                "source": "TechCrunch"
            },
            {
                "title": "Hướng dẫn gọi vốn Series A",
                "url": "https://techcrunch.com/guide-series-a-funding",
                "summary": "Những điều cần biết khi gọi vốn vòng Series A cho startup công nghệ.",
                "source": "TechCrunch"
            }
        ]

        return json.dumps({
            "status": "success",
            "source": "TechCrunch",
            "query": query,
            "message": "Không tìm thấy kết quả phù hợp. Dưới đây là một số gợi ý.",
            "results": default_results,
            "result_count": len(default_results)
        }, indent=2, ensure_ascii=False)

    def _get_error_response(self, query: str, error_msg: str) -> str:
        """Trả về phản hồi lỗi có cấu trúc."""
        return json.dumps({
            "status": "error",
            "source": "TechCrunch",
            "query": query,
            "message": f"Không thể truy xuất kết quả: {error_msg}",
            "results": []
        }, indent=2, ensure_ascii=False)

    def get_top_new(self, content_type: str = "articles", limit: int = 10,
                   time_period: str = "week", category: str = "") -> str:
        """
        Lấy nội dung mới nhất và nổi bật từ TechCrunch.

        Parameters:
        - content_type: Loại nội dung (articles, startups, funding, reviews, events)
        - limit: Số lượng kết quả (tối đa 20)
        - time_period: Khoảng thời gian (day, week, month, year)
        - category: Danh mục cụ thể (ai, blockchain, fintech, security, etc.)

        Returns:
        - Chuỗi JSON với nội dung tech mới nhất
        """
        logger.info(f"Lấy top {content_type} mới nhất từ TechCrunch trong {time_period}")

        limit = max(1, min(limit, 20))

        try:
            results = []

            if content_type == "articles":
                # Bài viết công nghệ mới nhất
                results = [
                    {
                        "name": f"📰 {category or 'Tech'} Article #{i+1}",
                        "article_id": f"tc_article_{2024}_{1000+i:04d}",
                        "title": f"{category or 'Breaking'}: {['AI Revolution', 'Startup Success', 'Tech Innovation', 'Digital Transformation'][i % 4]} {chr(65+i)}",
                        "url": f"https://techcrunch.com/{category or 'tech'}-{['ai-revolution', 'startup-success', 'tech-innovation', 'digital-transformation'][i % 4]}-{chr(97+i)}",
                        "author": f"{chr(65+i)}{chr(97+i)}son {chr(75+i)}{chr(97+i)}nter",
                        "category": category or ["AI", "Startups", "Apps", "Security", "Fintech"][i % 5],
                        "subcategory": f"Sub-{category or 'category'} {chr(65+i)}",
                        "summary": f"Latest developments in {category or 'technology'} sector showing {['breakthrough innovations', 'market disruption', 'user adoption', 'investment growth'][i % 4]}. This article covers {['emerging trends', 'industry analysis', 'expert insights', 'market predictions'][i % 4]} and their impact on {['businesses', 'consumers', 'developers', 'investors'][i % 4]}.",
                        "publication_date": f"2024-{1+i%12:02d}-{15+i:02d}",
                        "reading_time": f"{3 + (i % 8)} minutes",
                        "tags": [f"Tag {j+1}" for j in range(3)],
                        "engagement": {
                            "views": 10000 + (i * 1500),
                            "shares": 500 + (i * 50),
                            "comments": 50 + (i * 10)
                        },
                        "trending_score": 85 + (i % 15),
                        "related_articles": [f"Related Article {j+1}" for j in range(2)],
                        "techcrunch_url": f"https://techcrunch.com/{category or 'tech'}-{chr(97+i)}",
                        "social_media": f"@techcrunch/status/{1000000+i}"
                    } for i in range(limit)
                ]

            elif content_type == "startups":
                # Startup mới nhất được đưa tin
                results = [
                    {
                        "name": f"🚀 {category or 'Tech'} Startup #{i+1}",
                        "startup_id": f"tc_startup_{2024}_{2000+i:04d}",
                        "company_name": f"{category or 'Tech'}{chr(65+i)}{chr(97+i)} {['Labs', 'Systems', 'Solutions', 'Technologies'][i % 4]}",
                        "url": f"https://techcrunch.com/startup/{category or 'tech'}-{chr(97+i)}-{['labs', 'systems', 'solutions', 'technologies'][i % 4]}",
                        "industry": category or ["AI/ML", "Fintech", "Healthtech", "Edtech", "Cleantech"][i % 5],
                        "stage": ["Pre-seed", "Seed", "Series A", "Series B", "Series C"][i % 5],
                        "description": f"Innovative {category or 'technology'} startup developing {['AI-powered solutions', 'blockchain platforms', 'mobile applications', 'enterprise software'][i % 4]} for {['businesses', 'consumers', 'developers', 'enterprises'][i % 4]}. The company focuses on {['automation', 'optimization', 'personalization', 'scalability'][i % 4]} and has shown {['rapid growth', 'strong traction', 'market validation', 'user engagement'][i % 4]}.",
                        "founded_date": f"202{2+i%3}-{1+i%12:02d}-01",
                        "headquarters": ["San Francisco", "New York", "London", "Berlin", "Singapore"][i % 5],
                        "team_size": 10 + (i * 5),
                        "funding_info": {
                            "total_raised": f"${1 + (i * 0.5):.1f}M",
                            "last_round": f"${0.5 + (i * 0.2):.1f}M",
                            "investors": [f"Investor {chr(65+j)}" for j in range(2)]
                        },
                        "key_metrics": {
                            "users": f"{10 + (i * 5)}K",
                            "revenue_growth": f"{100 + (i * 20)}%",
                            "market_size": f"${10 + (i * 5)}B"
                        },
                        "founders": [f"Founder {chr(65+j)} {chr(75+j)}" for j in range(2)],
                        "techcrunch_url": f"https://techcrunch.com/startup/{category or 'tech'}-{chr(97+i)}",
                        "company_website": f"https://{category or 'tech'}{chr(97+i)}.com"
                    } for i in range(limit)
                ]

            elif content_type == "funding":
                # Tin tức gọi vốn mới nhất
                results = [
                    {
                        "name": f"💰 {category or 'Tech'} Funding #{i+1}",
                        "funding_id": f"tc_funding_{2024}_{3000+i:04d}",
                        "company_name": f"{category or 'Tech'}{chr(65+i)} {['Ventures', 'Capital', 'Holdings', 'Group'][i % 4]}",
                        "url": f"https://techcrunch.com/funding/{category or 'tech'}-{chr(97+i)}-raises-funding",
                        "round_type": ["Seed", "Series A", "Series B", "Series C", "IPO"][i % 5],
                        "amount_raised": f"${5 + (i * 2)}M",
                        "valuation": f"${50 + (i * 20)}M",
                        "announcement_date": f"2024-{1+i%12:02d}-{10+i:02d}",
                        "lead_investor": f"{['Sequoia', 'Andreessen Horowitz', 'Kleiner Perkins', 'Accel'][i % 4]} Capital",
                        "participating_investors": [f"Investor {chr(65+j)}" for j in range(3)],
                        "industry_sector": category or ["AI/ML", "Fintech", "Biotech", "Cleantech", "SaaS"][i % 5],
                        "use_of_funds": [
                            "Product development",
                            "Market expansion",
                            "Team growth",
                            "Technology advancement"
                        ][:(i % 4) + 1],
                        "company_description": f"Leading {category or 'technology'} company specializing in {['artificial intelligence', 'financial services', 'healthcare solutions', 'sustainable technology'][i % 4]}. The company has demonstrated {['strong market traction', 'rapid user growth', 'revenue scalability', 'technological innovation'][i % 4]}.",
                        "previous_funding": f"${1 + (i * 0.5):.1f}M total raised",
                        "employee_count": 50 + (i * 25),
                        "market_opportunity": f"${100 + (i * 50)}B addressable market",
                        "techcrunch_url": f"https://techcrunch.com/funding/{category or 'tech'}-{chr(97+i)}",
                        "press_release": f"https://techcrunch.com/press/{category or 'tech'}-{chr(97+i)}-funding"
                    } for i in range(limit)
                ]

            elif content_type == "reviews":
                # Reviews sản phẩm công nghệ mới nhất
                results = [
                    {
                        "name": f"⭐ {category or 'Tech'} Review #{i+1}",
                        "review_id": f"tc_review_{2024}_{4000+i:04d}",
                        "product_name": f"{category or 'Tech'} {['Pro', 'Max', 'Ultra', 'Plus'][i % 4]} {chr(65+i)}",
                        "url": f"https://techcrunch.com/review/{category or 'tech'}-{['pro', 'max', 'ultra', 'plus'][i % 4]}-{chr(97+i)}",
                        "product_category": category or ["Smartphones", "Laptops", "Tablets", "Wearables", "Smart Home"][i % 5],
                        "manufacturer": ["Apple", "Samsung", "Google", "Microsoft", "Amazon"][i % 5],
                        "review_score": round(7.5 + (i * 0.3), 1),
                        "price_range": f"${500 + (i * 200)}-{800 + (i * 300)}",
                        "review_date": f"2024-{1+i%12:02d}-{20+i:02d}",
                        "reviewer": f"Tech Reviewer {chr(65+i)} {chr(75+i)}",
                        "key_features": [f"Feature {j+1}" for j in range(4)],
                        "pros": [f"Pro {j+1}" for j in range(3)],
                        "cons": [f"Con {j+1}" for j in range(2)],
                        "performance_rating": round(8.0 + (i * 0.2), 1),
                        "design_rating": round(8.5 + (i * 0.1), 1),
                        "value_rating": round(7.0 + (i * 0.4), 1),
                        "summary": f"Comprehensive review of the latest {category or 'technology'} product featuring {['cutting-edge design', 'advanced performance', 'innovative features', 'excellent value'][i % 4]}. The device excels in {['user experience', 'build quality', 'performance', 'battery life'][i % 4]} while offering {['competitive pricing', 'premium features', 'reliable performance', 'innovative design'][i % 4]}.",
                        "comparison_products": [f"Competitor {j+1}" for j in range(2)],
                        "techcrunch_url": f"https://techcrunch.com/review/{category or 'tech'}-{chr(97+i)}",
                        "video_review": f"https://techcrunch.com/video/review-{category or 'tech'}-{chr(97+i)}"
                    } for i in range(limit)
                ]

            elif content_type == "events":
                # Sự kiện công nghệ mới nhất
                results = [
                    {
                        "name": f"🎯 {category or 'Tech'} Event #{i+1}",
                        "event_id": f"tc_event_{2024}_{5000+i:04d}",
                        "event_name": f"{category or 'Tech'} {['Summit', 'Conference', 'Expo', 'Forum'][i % 4]} {chr(65+i)}",
                        "url": f"https://techcrunch.com/events/{category or 'tech'}-{['summit', 'conference', 'expo', 'forum'][i % 4]}-{chr(97+i)}",
                        "event_type": ["Conference", "Summit", "Expo", "Workshop", "Meetup"][i % 5],
                        "date": f"2024-{2+i%12:02d}-{15+i:02d}",
                        "duration": f"{1 + (i % 3)} days",
                        "location": ["San Francisco", "New York", "Austin", "Las Vegas", "Virtual"][i % 5],
                        "format": ["In-Person", "Virtual", "Hybrid", "Online"][i % 4],
                        "focus_area": category or ["AI/ML", "Startups", "Fintech", "Cybersecurity", "Web3"][i % 5],
                        "description": f"Premier {category or 'technology'} event bringing together {['industry leaders', 'startup founders', 'investors', 'developers'][i % 4]} to discuss {['emerging trends', 'innovation strategies', 'market opportunities', 'technological advances'][i % 4]}. The event features {['keynote speeches', 'panel discussions', 'networking sessions', 'product demos'][i % 4]} and {['startup pitches', 'investor meetings', 'tech showcases', 'expert workshops'][i % 4]}.",
                        "expected_attendees": 1000 + (i * 500),
                        "keynote_speakers": [f"Speaker {chr(65+j)} {chr(75+j)}" for j in range(3)],
                        "agenda_highlights": [f"Session {j+1}" for j in range(4)],
                        "sponsors": [f"Sponsor {chr(65+j)}" for j in range(3)],
                        "registration_fee": f"${200 + (i * 100)}" if i % 2 == 0 else "Free",
                        "registration_deadline": f"2024-{2+i%12:02d}-{10+i:02d}",
                        "networking_opportunities": ["Startup Showcase", "Investor Meetups", "Tech Demos", "Panel Q&A"][i % 4],
                        "techcrunch_url": f"https://techcrunch.com/events/{category or 'tech'}-{chr(97+i)}",
                        "registration_url": f"https://techcrunch.com/events/{category or 'tech'}-{chr(97+i)}/register"
                    } for i in range(limit)
                ]

            result = {
                "status": "success",
                "source": "TechCrunch Top New",
                "content_type": content_type,
                "category": category or "All Categories",
                "time_period": time_period,
                "limit": limit,
                "total_results": len(results),
                "tech_highlights": {
                    "trending_topics": ["AI Revolution", "Startup Funding", "Tech Reviews", "Innovation"],
                    "hot_categories": ["AI/ML", "Fintech", "Cybersecurity", "Web3", "Healthtech"],
                    "funding_volume": "$2.5B this month",
                    "new_startups": "150+ featured",
                    "top_content_types": ["Articles", "Startups", "Funding", "Reviews", "Events"]
                },
                "results": results,
                "generated_at": "2024-01-15T10:30:00Z"
            }

            return json.dumps(result, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Lỗi khi lấy top new TechCrunch: {str(e)}")
            result = {
                "status": "error",
                "source": "TechCrunch Top New",
                "message": str(e),
                "fallback_url": "https://techcrunch.com/"
            }
            return json.dumps(result, indent=2, ensure_ascii=False)
