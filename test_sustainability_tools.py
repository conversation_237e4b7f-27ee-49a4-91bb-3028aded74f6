#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script cho Sustainability Tools
"""

import sys
import os
import json
import random
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_climate_data_tools():
    """Test Climate Data Tools"""
    print("🌡️ Testing Climate Data Tools...")
    try:
        from tools.sustainability.climate_data_tools import ClimateDataTool
        
        climate_tool = ClimateDataTool()
        
        print("  - Testing Climate Data instantiation...")
        print("    ✅ Climate Data Tools instantiated")
        
        # Test get_top_new
        print("  - Testing Climate Data get_top_new...")
        assert hasattr(climate_tool, 'get_top_new')
        print("    ✅ Climate Data get_top_new method exists")
        
        # Test basic functionality
        print("  - Testing basic climate data functionality...")
        result = climate_tool.get_climate_data("temperature", "global", "2020-01-01", "2023-12-31", "nasa")
        data = json.loads(result)
        assert data["status"] == "success"
        print("    ✅ Basic climate data functionality works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Climate Data Tools failed: {str(e)}")
        return False

def test_sustainability_search_toolkit():
    """Test Sustainability Search Toolkit"""
    print("🔍 Testing Sustainability Search Toolkit...")
    try:
        from tools.sustainability.sustainability_search_toolkit import SustainabilitySearchToolkit
        
        toolkit = SustainabilitySearchToolkit()
        
        print("  - Testing climate data search...")
        result = toolkit.search_climate_data("temperature", "global", "current", "anomaly")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Climate data search works")
        
        print("  - Testing sustainability initiatives search...")
        result = toolkit.search_sustainability_initiatives("corporate", "energy", "global", "carbon_reduction")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Sustainability initiatives search works")
        
        print("  - Testing environmental policies search...")
        result = toolkit.search_environmental_policies("regulation", "national", "climate", "implemented")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Environmental policies search works")
        
        print("  - Testing comprehensive sustainability search...")
        result = toolkit.comprehensive_sustainability_search("renewable energy", "all", "environmental", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive sustainability search works")
        
        print("  - Testing green technologies search...")
        result = toolkit.search_green_technologies("renewable_energy", "energy", "commercial", "high")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Green technologies search works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Sustainability Search Toolkit failed: {str(e)}")
        return False

def test_other_sustainability_tools():
    """Test other sustainability tools"""
    print("🌱 Testing Other Sustainability Tools...")
    try:
        # Test Global Footprint Tools
        from tools.sustainability.global_footprint_tools import GlobalFootprintTool
        
        footprint_tool = GlobalFootprintTool()
        
        print("  - Testing Global Footprint Tools...")
        print("    ✅ Global Footprint Tools instantiated")
        
        # Test UNEP Green Tools
        from tools.sustainability.unep_green_tools import UNEPGreenTool
        
        unep_tool = UNEPGreenTool()
        
        print("  - Testing UNEP Green Tools...")
        print("    ✅ UNEP Green Tools instantiated")
        
        # Test Sustainable Products Tools
        from tools.sustainability.sustainable_products_tools import SustainableProductsTool
        
        products_tool = SustainableProductsTool()
        
        print("  - Testing Sustainable Products Tools...")
        print("    ✅ Sustainable Products Tools instantiated")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Other Sustainability Tools failed: {str(e)}")
        return False

def test_random_sustainability_functionality():
    """Test random sustainability functionality"""
    print("\n🎲 Testing Random Sustainability Functionality...")
    
    try:
        # Random climate data test
        from tools.sustainability.sustainability_search_toolkit import SustainabilitySearchToolkit
        toolkit = SustainabilitySearchToolkit()
        
        data_types = ["temperature", "co2", "sea_level", "precipitation", "extreme_events"]
        data_type = random.choice(data_types)
        result = toolkit.search_climate_data(data_type, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random climate data {data_type} search test passed")
        
        # Random sustainability initiatives test
        initiative_types = ["corporate", "government", "ngo", "community", "international"]
        initiative_type = random.choice(initiative_types)
        result = toolkit.search_sustainability_initiatives(initiative_type, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random sustainability {initiative_type} initiatives test passed")
        
        # Random green technologies test
        tech_types = ["renewable_energy", "efficiency", "storage", "capture", "recycling"]
        tech_type = random.choice(tech_types)
        result = toolkit.search_green_technologies(tech_type, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random green technology {tech_type} test passed")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Random Sustainability Functionality failed: {str(e)}")
        return False

def test_sustainability_search_variations():
    """Test various sustainability search variations"""
    print("\n🌍 Testing Sustainability Search Variations...")
    
    try:
        from tools.sustainability.sustainability_search_toolkit import SustainabilitySearchToolkit
        toolkit = SustainabilitySearchToolkit()
        
        # Test comprehensive search with different parameters
        print("  - Testing comprehensive search variations...")
        result = toolkit.comprehensive_sustainability_search("climate change", "climate", "environmental", "advanced")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive search variations work")
        
        # Test environmental policies with different parameters
        print("  - Testing environmental policies variations...")
        result = toolkit.search_environmental_policies("", "international", "biodiversity", "proposed")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Environmental policies variations work")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Sustainability Search Variations failed: {str(e)}")
        return False

def test_climate_data_get_top_new():
    """Test Climate Data get_top_new functionality"""
    print("\n⚡ Testing Climate Data get_top_new...")
    
    try:
        from tools.sustainability.climate_data_tools import ClimateDataTool
        
        climate_tool = ClimateDataTool()
        
        # Test get_top_new with different content types
        content_types = ["climate_data", "reports", "alerts", "research", "events"]
        
        for content_type in content_types:
            print(f"  - Testing get_top_new for {content_type}...")
            result = climate_tool.get_top_new(content_type, 5, "month", "temperature")
            data = json.loads(result)
            assert data["status"] == "success"
            print(f"    ✅ get_top_new for {content_type} works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Climate Data get_top_new failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 SUSTAINABILITY TOOLS TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing Sustainability channel tools...")
    print()
    
    test_results = []
    
    # Test all sustainability tools
    test_functions = [
        ("Climate Data Tools", test_climate_data_tools),
        ("Sustainability Search Toolkit", test_sustainability_search_toolkit),
        ("Other Sustainability Tools", test_other_sustainability_tools),
        ("Random Sustainability Functionality", test_random_sustainability_functionality),
        ("Sustainability Search Variations", test_sustainability_search_variations),
        ("Climate Data get_top_new", test_climate_data_get_top_new)
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()
    
    # Summary
    print("\n" + "="*60)
    print("📋 SUSTAINABILITY TOOLS TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All sustainability tools are working correctly!")
        print("✨ Sustainability channel fully functional!")
        print("🌱 Ready for environmental analysis and climate action!")
    elif passed >= total * 0.8:
        print("✅ Excellent performance - most functionality working!")
    elif passed >= total * 0.6:
        print("✅ Good performance - majority working!")
    else:
        print("⚠️  Some issues detected. Please check the error messages above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
