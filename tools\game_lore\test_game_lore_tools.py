#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho tất cả Game Lore Tools
"""

import sys
import json
from datetime import datetime

def test_tool_import_and_basic_function(tool_name, tool_class, test_function, test_args):
    """Test import và basic function của một tool."""
    print(f"\n🧪 Testing {tool_name}...")
    
    try:
        # Test import
        print(f"  ✓ Import {tool_class.__name__} successful")
        
        # Test initialization
        tool = tool_class()
        print(f"  ✓ Initialize {tool_name} successful")
        
        # Test basic function
        if hasattr(tool, test_function):
            func = getattr(tool, test_function)
            result = func(**test_args)
            
            # Check if result is valid JSON
            if isinstance(result, str):
                try:
                    parsed = json.loads(result)
                    print(f"  ✓ Function {test_function} returns valid JSON")
                    print(f"  ✓ Result keys: {list(parsed.keys())}")
                    return True
                except json.JSONDecodeError:
                    print(f"  ❌ Function {test_function} returns invalid JSON")
                    return False
            else:
                print(f"  ❌ Function {test_function} doesn't return string")
                return False
        else:
            print(f"  ❌ Function {test_function} not found")
            return False
            
    except Exception as e:
        print(f"  ❌ Error testing {tool_name}: {str(e)}")
        return False

def main():
    """Main test function."""
    print("🚀 Starting Game Lore Tools Test Suite")
    print("=" * 50)
    
    test_results = {}
    
    # Test 1: AO3 Fanfic Tools
    try:
        from ao3_fanfic_tools import AO3FanficTools
        test_results["AO3FanficTools"] = test_tool_import_and_basic_function(
            "AO3 Fanfic Tools",
            AO3FanficTools,
            "search_ao3_fanfic",
            {"query": "test", "limit": 2}
        )
    except Exception as e:
        print(f"❌ Failed to test AO3FanficTools: {e}")
        test_results["AO3FanficTools"] = False

    # Test 2: Fandom Gamepedia Tools
    try:
        from fandom_gamepedia_tools import FandomGamepediaTools
        test_results["FandomGamepediaTools"] = test_tool_import_and_basic_function(
            "Fandom Gamepedia Tools",
            FandomGamepediaTools,
            "search_fandom_gamepedia",
            {"query": "test", "limit": 2}
        )
    except Exception as e:
        print(f"❌ Failed to test FandomGamepediaTools: {e}")
        test_results["FandomGamepediaTools"] = False

    # Test 3: TVTropes Tools
    try:
        from tvtropes_tools import TVTropesTools
        test_results["TVTropesTools"] = test_tool_import_and_basic_function(
            "TVTropes Tools",
            TVTropesTools,
            "search_tvtropes",
            {"query": "test", "limit": 2}
        )
    except Exception as e:
        print(f"❌ Failed to test TVTropesTools: {e}")
        test_results["TVTropesTools"] = False

    # Test 4: Wikia API Tools
    try:
        from wikia_api_tools import WikiaApiTools
        test_results["WikiaApiTools"] = test_tool_import_and_basic_function(
            "Wikia API Tools",
            WikiaApiTools,
            "search_wikia_api",
            {"query": "test", "wiki": "elderscrolls", "limit": 2}
        )
    except Exception as e:
        print(f"❌ Failed to test WikiaApiTools: {e}")
        test_results["WikiaApiTools"] = False

    # Test 5: Wikipedia Game Tools
    try:
        from wikipedia_game_tools import WikipediaGameTools
        test_results["WikipediaGameTools"] = test_tool_import_and_basic_function(
            "Wikipedia Game Tools",
            WikipediaGameTools,
            "search_wikipedia_game",
            {"query": "Final Fantasy"}
        )
    except Exception as e:
        print(f"❌ Failed to test WikipediaGameTools: {e}")
        test_results["WikipediaGameTools"] = False

    # Test 6: Narrative Evolution Calculator
    try:
        from narrative_evolution_calculator import NarrativeEvolutionCalculator
        test_results["NarrativeEvolutionCalculator"] = test_tool_import_and_basic_function(
            "Narrative Evolution Calculator",
            NarrativeEvolutionCalculator,
            "calculate_narrative_divergence",
            {"story1": "Story A", "story2": "Story B", "divergence_installments": 3}
        )
    except Exception as e:
        print(f"❌ Failed to test NarrativeEvolutionCalculator: {e}")
        test_results["NarrativeEvolutionCalculator"] = False

    # Test 7: Game Lore Analyzer
    try:
        from game_lore_analyzer import GameLoreAnalyzer
        test_results["GameLoreAnalyzer"] = test_tool_import_and_basic_function(
            "Game Lore Analyzer",
            GameLoreAnalyzer,
            "analyze_character_relationships",
            {"characters": ["Character A", "Character B"], "relationship_context": "main_story"}
        )
    except Exception as e:
        print(f"❌ Failed to test GameLoreAnalyzer: {e}")
        test_results["GameLoreAnalyzer"] = False

    # Test 8: Game Lore Search Toolkit
    try:
        from game_lore_search_toolkit import GameLoreSearchToolkit
        test_results["GameLoreSearchToolkit"] = test_tool_import_and_basic_function(
            "Game Lore Search Toolkit",
            GameLoreSearchToolkit,
            "search_character_lore",
            {"character_name": "Test Character", "game_franchise": "Test Game"}
        )
    except Exception as e:
        print(f"❌ Failed to test GameLoreSearchToolkit: {e}")
        test_results["GameLoreSearchToolkit"] = False

    # Test Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in test_results.values() if result)
    total = len(test_results)
    
    for tool_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {tool_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tools passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Game Lore Tools are ready!")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
