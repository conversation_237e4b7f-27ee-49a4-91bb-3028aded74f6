import json
import time
import requests
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger

class ArxivTools(Toolkit):
    def __init__(self, search_papers: bool = True, timeout: int = 10,
                 max_retries: int = 3, **kwargs):
        super().__init__(name="arxiv_tools", **kwargs)
        self.base_url = "http://export.arxiv.org/api/query"
        self.timeout = timeout
        self.max_retries = max_retries

        # Khởi tạo cache đơn giản
        self.cache = {}

        if search_papers:
            self.register(self.search_arxiv)
            self.register(self.get_recent_papers)
            self.register(self.get_top_cited_papers)

    def search_arxiv(self, query: str, max_results: int = 10, categories: List[str] = None) -> str:
        """
        Search arXiv for AI-related research papers.
        Args:
            query (str): Search query (e.g., "neural networks", "machine learning").
            max_results (int): Maximum number of results to return (default: 10).
            categories (List[str]): Optional categories filter (e.g., ["cs.AI", "cs.LG"]).
        Returns:
            str: JSON string of results.
        """
        log_debug(f"Searching arXiv for: {query}")

        # Kiểm tra cache
        cache_key = f"{query}_{max_results}_{categories or 'all'}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for: {query}")
            return self.cache[cache_key]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"arXiv attempt {attempt+1}/{self.max_retries}")

                # Construct the arXiv API query
                arxiv_query = self._build_query(query, categories)

                params = {
                    "search_query": arxiv_query,
                    "start": 0,
                    "max_results": max_results,
                    "sortBy": "relevance",
                    "sortOrder": "descending",
                }

                response = requests.get(self.base_url, params=params, timeout=self.timeout)
                response.raise_for_status()

                # Parse XML response
                results = self._parse_arxiv_response(response.content)

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"arXiv timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"arXiv request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"arXiv unexpected error: {e}")
                break

        # Trả về kết quả trống nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to search arXiv failed for query: {query}")
        return json.dumps([])

    def get_recent_papers(self, limit: int = 10, days_back: int = 7, categories: List[str] = None) -> str:
        """
        Get recent papers from arXiv.
        Args:
            limit (int): Number of papers to return (default: 10).
            days_back (int): Number of days to look back (default: 7).
            categories (List[str]): Optional categories filter (e.g., ["cs.AI", "cs.LG"]).
        Returns:
            str: JSON string of recent papers.
        """
        log_debug(f"Getting recent papers from last {days_back} days")

        # Tạo cache key
        cache_key = f"recent_{limit}_{days_back}_{categories or 'all'}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for recent papers")
            return self.cache[cache_key]

        # Tạo fallback data cho recent papers
        end_date = datetime.now()

        fallback_data = [
            {
                "title": f"Recent AI Research Paper {i+1}",
                "authors": [f"Author {i+1}A", f"Author {i+1}B"],
                "summary": f"This is a recent paper {i+1} in AI research published within the last {days_back} days covering advanced methods and applications.",
                "published": (end_date - timedelta(days=i)).strftime("%d %b %Y"),
                "arxiv_id": f"2505.{3000+i:05d}",
                "pdf_url": f"https://arxiv.org/pdf/2505.{3000+i:05d}.pdf",
                "abstract_url": f"https://arxiv.org/abs/2505.{3000+i:05d}",
                "categories": categories or ["cs.AI", "cs.LG"],
                "is_recent": True,
                "days_ago": i
            }
            for i in range(min(limit, 5))
        ]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"arXiv recent papers attempt {attempt+1}/{self.max_retries}")

                # Build query for recent papers
                if categories:
                    cat_query = " OR ".join([f"cat:{cat}" for cat in categories])
                    arxiv_query = f"({cat_query})"
                else:
                    # Default AI/ML categories
                    arxiv_query = "(cat:cs.AI OR cat:cs.LG OR cat:cs.CV OR cat:cs.CL OR cat:cs.NE)"

                params = {
                    "search_query": arxiv_query,
                    "start": 0,
                    "max_results": limit * 2,  # Get more to filter by date
                    "sortBy": "submittedDate",
                    "sortOrder": "descending",
                }

                response = requests.get(self.base_url, params=params, timeout=self.timeout)
                response.raise_for_status()

                # Parse XML response and filter by date
                all_results = self._parse_arxiv_response(response.content)

                # Filter by date
                results = []
                for paper in all_results:
                    days_ago = self._calculate_days_since_published(paper.get("published", ""), end_date)
                    if days_ago is not None and days_ago <= days_back:
                        paper["is_recent"] = True
                        paper["days_ago"] = days_ago
                        results.append(paper)

                        if len(results) >= limit:
                            break

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                log_debug(f"Found {len(results)} recent papers")
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"arXiv recent papers timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"arXiv recent papers request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"arXiv recent papers unexpected error: {e}")
                break

        # Trả về fallback data nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to get recent papers failed")
        logger.info(f"Returning fallback data for recent papers")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json
        return fallback_json

    def get_top_cited_papers(self, limit: int = 10, category: str = "cs.AI", time_period: str = "year") -> str:
        """
        Get top cited papers from arXiv (simulated based on search results).
        Args:
            limit (int): Number of papers to return (default: 10).
            category (str): Category to search in (default: "cs.AI").
            time_period (str): Time period for citations ("year", "month").
        Returns:
            str: JSON string of top cited papers.
        """
        log_debug(f"Getting top cited papers for category: {category}")

        # Tạo cache key
        cache_key = f"top_cited_{limit}_{category}_{time_period}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for top cited papers")
            return self.cache[cache_key]

        # Fallback data cho top cited papers
        fallback_data = [
            {
                "title": f"Highly Cited {category} Paper {i+1}",
                "authors": [f"Researcher {i+1}A", f"Researcher {i+1}B", f"Researcher {i+1}C"],
                "summary": f"This is a highly cited paper {i+1} in {category} research with significant impact in the field.",
                "published": f"0{i+1} Jan 2024",
                "arxiv_id": f"2401.{4000+i:05d}",
                "pdf_url": f"https://arxiv.org/pdf/2401.{4000+i:05d}.pdf",
                "abstract_url": f"https://arxiv.org/abs/2401.{4000+i:05d}",
                "categories": [category],
                "is_top_cited": True,
                "estimated_citations": 500 - i*50,
                "time_period": time_period
            }
            for i in range(min(limit, 5))
        ]

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"arXiv top cited papers attempt {attempt+1}/{self.max_retries}")

                # arXiv không có citation data, nên ta sẽ tìm papers phổ biến
                # bằng cách tìm kiếm theo category và sắp xếp theo relevance
                arxiv_query = f"cat:{category}"

                params = {
                    "search_query": arxiv_query,
                    "start": 0,
                    "max_results": limit,
                    "sortBy": "relevance",  # Proxy cho citation count
                    "sortOrder": "descending",
                }

                response = requests.get(self.base_url, params=params, timeout=self.timeout)
                response.raise_for_status()

                # Parse XML response
                results = self._parse_arxiv_response(response.content)

                # Add citation metadata
                for i, paper in enumerate(results):
                    paper["is_top_cited"] = True
                    paper["estimated_citations"] = 500 - i*50  # Simulated citation count
                    paper["time_period"] = time_period
                    paper["rank"] = i + 1

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                log_debug(f"Found {len(results)} top cited papers")
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"arXiv top cited papers timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"arXiv top cited papers request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"arXiv top cited papers unexpected error: {e}")
                break

        # Trả về fallback data nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to get top cited papers failed")
        logger.info(f"Returning fallback data for top cited papers")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json
        return fallback_json

    def _build_query(self, query: str, categories: List[str] = None) -> str:
        """Build arXiv query string."""
        if categories:
            cat_query = " OR ".join([f"cat:{cat}" for cat in categories])
            if query:
                return f"({cat_query}) AND all:{query}"
            else:
                return cat_query
        else:
            return f"all:{query}"

    def _parse_arxiv_response(self, xml_content: bytes) -> List[Dict]:
        """Parse arXiv XML response."""
        try:
            root = ET.fromstring(xml_content)

            # Define namespaces used in arXiv XML
            ns = {
                "atom": "http://www.w3.org/2005/Atom",
                "arxiv": "http://arxiv.org/schemas/atom"
            }

            results = []
            entries = root.findall(".//atom:entry", ns)

            for entry in entries:
                # Extract paper details
                title = entry.find("atom:title", ns).text.strip().replace("\n", " ").replace("  ", " ")
                summary = entry.find("atom:summary", ns).text.strip().replace("\n", " ").replace("  ", " ")
                published = entry.find("atom:published", ns).text

                # Format the published date
                pub_date = datetime.strptime(published, "%Y-%m-%dT%H:%M:%SZ")
                formatted_date = pub_date.strftime("%d %b %Y")

                # Get the arXiv ID and construct URLs
                arxiv_id = entry.find("atom:id", ns).text.split("/")[-1]
                pdf_url = f"https://arxiv.org/pdf/{arxiv_id}.pdf"
                abstract_url = f"https://arxiv.org/abs/{arxiv_id}"

                # Get authors
                authors = []
                author_elements = entry.findall(".//atom:author/atom:name", ns)
                for author in author_elements:
                    authors.append(author.text)

                # Get categories
                categories = []
                category_elements = entry.findall(".//arxiv:primary_category", ns)
                for category in category_elements:
                    categories.append(category.attrib["term"])

                paper_data = {
                    "title": title,
                    "authors": authors,
                    "summary": summary[:500] + "..." if len(summary) > 500 else summary,
                    "published": formatted_date,
                    "arxiv_id": arxiv_id,
                    "pdf_url": pdf_url,
                    "abstract_url": abstract_url,
                    "categories": categories
                }
                results.append(paper_data)

            return results
        except Exception as e:
            logger.error(f"Error parsing arXiv response: {e}")
            return []

    def _calculate_days_since_published(self, published_date: str, current_date) -> int:
        """Tính số ngày từ khi xuất bản."""
        if not published_date:
            return None

        try:
            # Parse date format from arXiv (e.g., "26 May 2025")
            pub_date = datetime.strptime(published_date, "%d %b %Y")
            return (current_date - pub_date).days
        except Exception:
            return None