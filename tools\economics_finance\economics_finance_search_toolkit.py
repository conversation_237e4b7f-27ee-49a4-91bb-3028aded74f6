# -*- coding: utf-8 -*-
from typing import List, Dict, Any
import json
from agno.tools import Toolkit
from agno.utils.log import logger

class EconomicsFinanceSearchToolkit(Toolkit):
    """A custom Toolkit for generating search keywords for economics and finance databases.

    This toolkit provides functions to generate search keywords for Yahoo Finance,
    World Bank Data, Trading Economics, Financial News, and Wikipedia Economics, tailored for financial research.
    """

    # == Detailed Instructions for the Agent ==
    instruction = [
        "Bạn là một trợ lý nghiên cứu kinh tế và tài chính, chuyên cung cấp từ khóa tìm kiếm tối ưu cho các cơ sở dữ liệu tài chính.",
        "<PERSON>hi sử dụng các công cụ trong EconomicsFinanceSearchToolkit, tuân thủ các định dạng từ khóa được chỉ định như sau:",
        "- Yahoo Finance: Sử dụng định dạng 'stock symbol' hoặc 'market index' (ví dụ: 'AAPL', 'SPY', '^GSPC', 'BTC-USD').",
        "- World Bank Data: Sử dụng định dạng 'indicator code' hoặc 'economic concept' (ví dụ: 'NY.GDP.MKTP.CD', 'GDP growth', 'inflation rate').",
        "- Trading Economics: Sử dụng định dạng 'country indicator' (ví dụ: 'United States GDP', 'China inflation', 'Japan unemployment').",
        "- Financial News: Sử dụng định dạng 'financial topic' (ví dụ: 'Federal Reserve policy', 'inflation report', 'earnings season').",
        "- Wikipedia Economics: Sử dụng định dạng 'economics concept' (ví dụ: 'monetary policy', 'fiscal policy', 'market efficiency').",
        "Ngoài ra, toolkit cũng hỗ trợ tạo từ khóa cho việc tìm kiếm nội dung mới nhất và trending:",
        "- Yahoo Finance Recent: Tạo từ khóa cho market data và stocks mới theo market type.",
        "- World Bank Recent: Tạo từ khóa cho indicators và countries mới theo region.",
        "- Trading Economics Recent: Tạo từ khóa cho economic news và indicators mới theo country.",
        "- Financial News Recent: Tạo từ khóa cho financial news và topics mới theo category.",
        "- Wikipedia Economics Recent: Tạo từ khóa cho bài viết economics mới được tạo hoặc cập nhật.",
        "Kiểm tra tính hợp lệ của tham số đầu vào và trả về từ khóa phù hợp với từng cơ sở dữ liệu.",
        "Trả về kết quả dưới dạng JSON với trạng thái ('status'), danh sách từ khóa ('keywords'), và thông báo ('message').",
        "Nếu có lỗi, trả về trạng thái 'error' với mô tả lỗi chi tiết."
    ]

    # == Detailed Few-Shot Examples ==
    few_shot_examples = [
        {
            "user": "Tìm thông tin về Apple stock và S&P 500 index.",
            "tool_calls": [
                {
                    "name": "generate_yahoo_finance_keywords",
                    "arguments": {"symbol": "AAPL", "market": "US", "data_type": "stock"}
                },
                {
                    "name": "generate_yahoo_finance_keywords",
                    "arguments": {"symbol": "^GSPC", "market": "US", "data_type": "index"}
                }
            ]
        },
        {
            "user": "Tìm recent market data và trending stocks.",
            "tool_calls": [
                {
                    "name": "generate_yahoo_finance_recent_keywords",
                    "arguments": {"market": "US", "days_back": 7}
                },
                {
                    "name": "generate_yahoo_finance_trending_keywords",
                    "arguments": {"category": "most_active", "market": "US"}
                }
            ]
        },
        {
            "user": "Tìm nghiên cứu về GDP và inflation data.",
            "tool_calls": [
                {
                    "name": "generate_world_bank_keywords",
                    "arguments": {"indicator": "GDP", "country": "United States", "metric": "growth"}
                },
                {
                    "name": "generate_trading_economics_keywords",
                    "arguments": {"country": "United States", "indicator": "inflation", "frequency": "monthly"}
                },
                {
                    "name": "generate_wikipedia_economics_keywords",
                    "arguments": {"concept": "inflation", "field": "macroeconomics"}
                }
            ]
        }
    ]

    def __init__(self):
        """Initializes the EconomicsFinanceSearchToolkit."""
        super().__init__(
            name="economics_finance_search_toolkit",
            tools=[
                self.generate_yahoo_finance_keywords,
                self.generate_world_bank_keywords,
                self.generate_trading_economics_keywords,
                self.generate_financial_news_keywords,
                self.generate_wikipedia_economics_keywords,
                self.generate_yahoo_finance_recent_keywords,
                self.generate_world_bank_recent_keywords,
                self.generate_trading_economics_recent_keywords,
                self.generate_financial_news_recent_keywords,
                self.generate_wikipedia_economics_recent_keywords
            ],
            instructions=self.instruction
        )
        self.few_shot_examples = self.few_shot_examples
        logger.info("EconomicsFinanceSearchToolkit initialized.")

    def generate_yahoo_finance_keywords(self, symbol: str, market: str = None, data_type: str = None) -> str:
        """Generates search keywords for Yahoo Finance.

        Args:
            symbol: The stock symbol or index (e.g., 'AAPL', 'SPY', '^GSPC').
            market: Optional market filter (e.g., 'US', 'Global', 'Crypto').
            data_type: Optional data type (e.g., 'stock', 'index', 'etf', 'crypto').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating Yahoo Finance keywords for symbol: '{symbol}', market: '{market}', data_type: '{data_type}'")
        try:
            if not symbol.strip():
                raise ValueError("Symbol cannot be empty.")

            # Tạo từ khóa cho Yahoo Finance
            keywords = [symbol.upper()]
            if market:
                keywords.append(f"{symbol.upper()} {market}")
            if data_type:
                keywords.append(f"{symbol.upper()} {data_type}")

            # Thêm từ khóa mở rộng
            keywords.extend([
                f"{symbol.upper()} price", f"{symbol.upper()} chart", f"{symbol.upper()} analysis",
                f"{symbol.upper()} news", f"{symbol.upper()} forecast"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Yahoo Finance keywords for symbol '{symbol}', market '{market or 'all'}', data_type '{data_type or 'all'}'.",
                "search_type": "market_data",
                "parameters": {
                    "symbol": symbol,
                    "market": market,
                    "data_type": data_type
                }
            }
            logger.debug(f"Yahoo Finance keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating Yahoo Finance keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate Yahoo Finance keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_world_bank_keywords(self, indicator: str, country: str = None, metric: str = None) -> str:
        """Generates search keywords for World Bank Data."""
        logger.info(f"Generating World Bank keywords for indicator: '{indicator}', country: '{country}', metric: '{metric}'")
        try:
            if not indicator.strip():
                raise ValueError("Indicator cannot be empty.")

            keywords = [indicator]
            if country:
                keywords.append(f"{indicator} {country}")
            if metric:
                keywords.append(f"{indicator} {metric}")
                if country:
                    keywords.append(f"{indicator} {country} {metric}")

            keywords.extend([
                f"{indicator} data", f"{indicator} statistics", f"{indicator} trend",
                f"{indicator} annual", f"{indicator} quarterly"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated World Bank keywords for indicator '{indicator}', country '{country or 'all'}', metric '{metric or 'all'}'.",
                "search_type": "economic_indicators",
                "parameters": {"indicator": indicator, "country": country, "metric": metric}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate World Bank keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_trading_economics_keywords(self, country: str, indicator: str = None, frequency: str = None) -> str:
        """Generates search keywords for Trading Economics."""
        logger.info(f"Generating Trading Economics keywords for country: '{country}', indicator: '{indicator}', frequency: '{frequency}'")
        try:
            if not country.strip():
                raise ValueError("Country cannot be empty.")

            keywords = [country]
            if indicator:
                keywords.append(f"{country} {indicator}")
            if frequency:
                keywords.append(f"{country} {frequency}")
                if indicator:
                    keywords.append(f"{country} {indicator} {frequency}")

            keywords.extend([
                f"{country} economy", f"{country} economic data", f"{country} forecast",
                f"{country} statistics", f"{country} indicators"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Trading Economics keywords for country '{country}', indicator '{indicator or 'all'}', frequency '{frequency or 'all'}'.",
                "search_type": "country_economics",
                "parameters": {"country": country, "indicator": indicator, "frequency": frequency}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Trading Economics keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_financial_news_keywords(self, topic: str, category: str = None) -> str:
        """Generates search keywords for Financial News."""
        logger.info(f"Generating Financial News keywords for topic: '{topic}', category: '{category}'")
        try:
            if not topic.strip():
                raise ValueError("Topic cannot be empty.")

            keywords = [topic]
            if category:
                keywords.append(f"{topic} {category}")

            keywords.extend([
                f"{topic} news", f"{topic} analysis", f"{topic} report",
                f"{topic} update", f"{topic} market"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Financial News keywords for topic '{topic}' and category '{category or 'general'}'.",
                "search_type": "financial_news",
                "parameters": {"topic": topic, "category": category}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Financial News keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_economics_keywords(self, concept: str, field: str = None) -> str:
        """Generates search keywords for Wikipedia economics topics."""
        logger.info(f"Generating Wikipedia economics keywords for concept: '{concept}', field: '{field}'")
        try:
            if not concept.strip():
                raise ValueError("Concept cannot be empty.")

            keywords = [concept]
            if field:
                keywords.append(f"{concept} {field}")

            keywords.extend([
                f"{concept} economics", f"{concept} theory", f"{concept} definition",
                f"{concept} policy", f"{concept} principles"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia economics keywords for concept '{concept}' and field '{field or 'general'}'.",
                "search_type": "economics_concepts",
                "parameters": {"concept": concept, "field": field}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia economics keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    # == Recent/Trending Keywords Functions ==

    def generate_yahoo_finance_recent_keywords(self, market: str = "US", days_back: int = 7) -> str:
        """Generates keywords for recent market data on Yahoo Finance."""
        logger.info(f"Generating Yahoo Finance recent keywords for market: '{market}', days_back: {days_back}")
        try:
            keywords = ["recent market data", "latest prices", "current trading"]
            if market:
                keywords.extend([f"recent {market}", f"latest {market}", f"{market} market"])
            keywords.extend([
                "recent stocks", "latest earnings", "current trends",
                f"last {days_back} days", "recent trading"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Yahoo Finance recent keywords for market '{market}' within last {days_back} days.",
                "search_type": "recent_market_data",
                "parameters": {"market": market, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Yahoo Finance recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_world_bank_recent_keywords(self, region: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent World Bank indicators."""
        logger.info(f"Generating World Bank recent keywords for region: '{region}', days_back: {days_back}")
        try:
            keywords = ["recent indicators", "latest data", "new statistics"]
            if region:
                keywords.extend([f"recent {region}", f"latest {region}", f"{region} data"])
            keywords.extend([
                "recent economic data", "latest releases", "new indicators",
                f"last {days_back} days", "recent updates"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated World Bank recent keywords for region '{region or 'all'}' within last {days_back} days.",
                "search_type": "recent_indicators",
                "parameters": {"region": region, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate World Bank recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_trading_economics_recent_keywords(self, country: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent Trading Economics data."""
        logger.info(f"Generating Trading Economics recent keywords for country: '{country}', days_back: {days_back}")
        try:
            keywords = ["recent economic news", "latest indicators", "new forecasts"]
            if country:
                keywords.extend([f"recent {country}", f"latest {country}", f"{country} updates"])
            keywords.extend([
                "recent data releases", "latest economic updates", "new statistics",
                f"last {days_back} days", "recent forecasts"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Trading Economics recent keywords for country '{country or 'all'}' within last {days_back} days.",
                "search_type": "recent_economic_data",
                "parameters": {"country": country, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Trading Economics recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_financial_news_recent_keywords(self, category: str = None, days_back: int = 7) -> str:
        """Generates keywords for recent financial news."""
        logger.info(f"Generating Financial News recent keywords for category: '{category}', days_back: {days_back}")
        try:
            keywords = ["recent financial news", "latest market news", "breaking financial"]
            if category:
                keywords.extend([f"recent {category}", f"latest {category}", f"{category} news"])
            keywords.extend([
                "recent market updates", "latest financial reports", "breaking news",
                f"last {days_back} days", "recent headlines"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Financial News recent keywords for category '{category or 'all'}' within last {days_back} days.",
                "search_type": "recent_financial_news",
                "parameters": {"category": category, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Financial News recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_economics_recent_keywords(self, days_back: int = 30, language: str = "en") -> str:
        """Generates keywords for recent economics articles on Wikipedia."""
        logger.info(f"Generating Wikipedia economics recent keywords for days_back: {days_back}, language: '{language}'")
        try:
            keywords = [
                "recent economics articles", "new economic theories", "latest economic research",
                "recent economic policies", "new financial concepts", "latest economic analysis",
                f"last {days_back} days economics", "recent economic updates"
            ]

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia economics recent keywords within last {days_back} days for language '{language}'.",
                "search_type": "recent_economics_articles",
                "parameters": {"days_back": days_back, "language": language}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia economics recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)