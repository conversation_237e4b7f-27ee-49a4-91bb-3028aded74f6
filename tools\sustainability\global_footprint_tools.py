from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import re

class GlobalFootprintTool(Toolkit):
    """
    Global Footprint Tool cho tìm kiếm dữ liệu, báo cáo dấu chân sinh thái, ti<PERSON><PERSON> thụ tài nguyên, ng<PERSON><PERSON> vư<PERSON> ngưỡng Trái Đất từ Global Footprint Network.
    """

    def __init__(self):
        super().__init__(
            name="Global Footprint Network Search Tool",
            tools=[self.search_global_footprint]
        )

    async def search_global_footprint(self, query: str, country: Optional[str] = None, year: Optional[int] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm Global Footprint Network cho dữ liệu, báo cáo dấu chân sinh thái.

        Parameters:
        - query: Từ khóa về dấu chân sinh thái, t<PERSON><PERSON> ng<PERSON>, ng<PERSON><PERSON> vượ<PERSON> ngưỡng (ví dụ: 'ecological footprint', 'Earth Overshoot Day', 'biocapacity', 'Vietnam')
        - country: Tên quốc gia (ví dụ: 'Vietnam', 'USA', 'Global')
        - year: Năm dữ liệu (ví dụ: 2022)
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với tiêu đề, mô tả, quốc gia, năm, link báo cáo/dữ liệu
        """
        logger.info(f"Tìm kiếm Global Footprint: query={query}, country={country}, year={year}, limit={limit}")

        try:
            # Global Footprint Network không có API public, sử dụng endpoint web (scraping nhẹ)
            base_url = "https://data.footprintnetwork.org"
            search_url = f"{base_url}/"
            params = {}
            # Tạo URL tìm kiếm quốc gia nếu có
            if country:
                country_slug = country.lower().replace(" ", "-")
                search_url = f"{base_url}/country/{country_slug}"
            headers = {
                "User-Agent": "Mozilla/5.0 (compatible; GlobalFootprintBot/1.0)"
            }
            response = requests.get(search_url, headers=headers, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Global Footprint Network",
                    "message": f"Global Footprint search returned status code {response.status_code}",
                    "query": query
                }

            results = []
            # Đơn giản: lấy các chỉ số, báo cáo, ngày vượt ngưỡng từ trang quốc gia hoặc trang chủ
            # Lấy ngày vượt ngưỡng
            overshoot_match = re.search(r"Earth Overshoot Day.*?(\d{1,2} \w+ \d{4})", response.text, re.DOTALL)
            overshoot_day = overshoot_match.group(1) if overshoot_match else None
            # Lấy các chỉ số dấu chân sinh thái, biocapacity
            for match in re.finditer(r'<tr[^>]*>\s*<td[^>]*>(.*?)</td>\s*<td[^>]*>(.*?)</td>\s*<td[^>]*>(.*?)</td>', response.text, re.DOTALL):
                indicator = re.sub(r'<.*?>', '', match.group(1)).strip()
                value = re.sub(r'<.*?>', '', match.group(2)).strip()
                year_val = re.sub(r'<.*?>', '', match.group(3)).strip()
                if year and str(year) != year_val:
                    continue
                results.append({
                    "indicator": indicator,
                    "value": value,
                    "year": year_val,
                    "country": country or "Global",
                    "footprint_url": search_url
                })
                if len(results) >= limit:
                    break

            # Nếu không có bảng dữ liệu, trả về thông tin ngày vượt ngưỡng nếu có
            if not results and overshoot_day:
                results.append({
                    "indicator": "Earth Overshoot Day",
                    "value": overshoot_day,
                    "year": year or "",
                    "country": country or "Global",
                    "footprint_url": search_url
                })

            return {
                "status": "success",
                "source": "Global Footprint Network",
                "query": query,
                "country": country,
                "year": year,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "ecological footprint",
                    "Earth Overshoot Day",
                    "biocapacity",
                    "carbon footprint",
                    "Vietnam",
                    "USA",
                    "resource consumption",
                    "country comparison",
                    "footprint report"
                ],
                "official_data_url": "https://data.footprintnetwork.org/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Global Footprint: {str(e)}")
            return {
                "status": "error",
                "source": "Global Footprint Network",
                "message": str(e),
                "query": query
            }
