# -*- coding: utf-8 -*-
from typing import List, Dict, Any
import json
from agno.tools import Toolkit
from agno.utils.log import logger

class CosmologySearchToolkit(Toolkit):
    """A custom Toolkit for generating search keywords for cosmology and physics databases.

    This toolkit provides functions to generate search keywords for arXiv,
    CERN Open Data, INSPIRE-HEP, NASA ADS, and Wikipedia Physics, tailored for cosmology research.
    """

    # == Detailed Instructions for the Agent ==
    instruction = [
        "Bạn là một trợ lý nghiên cứu vũ trụ học và vật lý, chuyên cung cấp từ khóa tìm kiếm tối ưu cho các cơ sở dữ liệu khoa học.",
        "<PERSON><PERSON> sử dụng các công cụ trong CosmologySearchToolkit, tuân thủ các định dạng từ khóa được chỉ định như sau:",
        "- arXiv: Sử dụng định dạng 'physics concept' (ví dụ: 'dark matter simulation', 'gravitational waves detection').",
        "- CERN Open Data: Sử dụng định dạng 'experiment dataset' (ví dụ: 'LHC collision data', 'CMS detector muon').",
        "- INSPIRE-HEP: Sử dụng định dạng 'particle physics topic' (ví dụ: 'Higgs boson decay', 'supersymmetry phenomenology').",
        "- NASA ADS: Sử dụng định dạng 'astronomy topic' (ví dụ: 'cosmic microwave background', 'galaxy formation redshift').",
        "- Wikipedia Physics: Sử dụng định dạng 'physics concept' (ví dụ: 'general relativity', 'quantum field theory').",
        "Ngoài ra, toolkit cũng hỗ trợ tạo từ khóa cho việc tìm kiếm nội dung mới nhất và trending:",
        "- arXiv Recent: Tạo từ khóa cho papers và topics mới theo categories.",
        "- CERN Recent: Tạo từ khóa cho datasets và experiments mới theo detector.",
        "- INSPIRE-HEP Trending: Tạo từ khóa cho publications và citations trending theo field.",
        "- NASA ADS Recent: Tạo từ khóa cho astronomy papers mới theo journal.",
        "- Wikipedia Physics Recent: Tạo từ khóa cho bài viết vật lý mới được tạo hoặc cập nhật.",
        "Kiểm tra tính hợp lệ của tham số đầu vào và trả về từ khóa phù hợp với từng cơ sở dữ liệu.",
        "Trả về kết quả dưới dạng JSON với trạng thái ('status'), danh sách từ khóa ('keywords'), và thông báo ('message').",
        "Nếu có lỗi, trả về trạng thái 'error' với mô tả lỗi chi tiết."
    ]

    # == Detailed Few-Shot Examples ==
    few_shot_examples = [
        {
            "user": "Tìm thông tin về dark matter và dark energy.",
            "tool_calls": [
                {
                    "name": "generate_arxiv_keywords",
                    "arguments": {"concept": "dark matter", "category": "astro-ph.CO", "method": "simulation"}
                },
                {
                    "name": "generate_nasa_ads_keywords",
                    "arguments": {"topic": "dark energy", "object_type": "galaxy", "method": "survey"}
                },
                {
                    "name": "generate_wikipedia_physics_keywords",
                    "arguments": {"concept": "dark matter", "field": "cosmology"}
                }
            ]
        },
        {
            "user": "Tìm recent papers và trending topics về gravitational waves.",
            "tool_calls": [
                {
                    "name": "generate_arxiv_recent_keywords",
                    "arguments": {"category": "gr-qc", "days_back": 30}
                },
                {
                    "name": "generate_inspirehep_trending_keywords",
                    "arguments": {"field": "gravitational waves", "period": "month"}
                },
                {
                    "name": "generate_cern_recent_keywords",
                    "arguments": {"experiment": "LIGO", "days_back": 30}
                }
            ]
        },
        {
            "user": "Tìm nghiên cứu về particle physics và cosmology.",
            "tool_calls": [
                {
                    "name": "generate_inspirehep_keywords",
                    "arguments": {"topic": "Higgs boson", "experiment": "LHC", "theory": "Standard Model"}
                },
                {
                    "name": "generate_cern_opendata_keywords",
                    "arguments": {"experiment": "CMS", "dataset": "collision", "energy": "13 TeV"}
                },
                {
                    "name": "generate_arxiv_keywords",
                    "arguments": {"concept": "cosmic inflation", "category": "hep-th", "method": "theory"}
                }
            ]
        }
    ]

    def __init__(self):
        """Initializes the CosmologySearchToolkit."""
        super().__init__(
            name="cosmology_search_toolkit",
            tools=[
                self.generate_arxiv_keywords,
                self.generate_cern_opendata_keywords,
                self.generate_inspirehep_keywords,
                self.generate_nasa_ads_keywords,
                self.generate_wikipedia_physics_keywords,
                self.generate_arxiv_recent_keywords,
                self.generate_cern_recent_keywords,
                self.generate_inspirehep_trending_keywords,
                self.generate_nasa_ads_recent_keywords,
                self.generate_wikipedia_physics_recent_keywords
            ],
            instructions=self.instruction
        )
        self.few_shot_examples = self.few_shot_examples
        logger.info("CosmologySearchToolkit initialized.")

    def generate_arxiv_keywords(self, concept: str, category: str = None, method: str = None) -> str:
        """Generates search keywords for arXiv.

        Args:
            concept: The physics concept (e.g., 'dark matter', 'gravitational waves').
            category: Optional arXiv category (e.g., 'astro-ph.CO', 'gr-qc').
            method: Optional research method (e.g., 'simulation', 'observation', 'theory').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating arXiv keywords for concept: '{concept}', category: '{category}', method: '{method}'")
        try:
            if not concept.strip():
                raise ValueError("Concept cannot be empty.")

            # Tạo từ khóa cho arXiv
            keywords = [concept]
            if method:
                keywords.append(f"{concept} {method}")
            if category:
                keywords.append(f"{concept} {category}")

            # Thêm từ khóa mở rộng
            keywords.extend([
                f"{concept} research", f"{concept} analysis", f"{concept} detection",
                f"{concept} theory", f"{concept} observation"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated arXiv keywords for concept '{concept}', category '{category or 'all'}', method '{method or 'all'}'.",
                "search_type": "physics_papers",
                "parameters": {
                    "concept": concept,
                    "category": category,
                    "method": method
                }
            }
            logger.debug(f"arXiv keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating arXiv keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate arXiv keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_cern_opendata_keywords(self, experiment: str, dataset: str = None, energy: str = None) -> str:
        """Generates search keywords for CERN Open Data."""
        logger.info(f"Generating CERN Open Data keywords for experiment: '{experiment}', dataset: '{dataset}', energy: '{energy}'")
        try:
            if not experiment.strip():
                raise ValueError("Experiment cannot be empty.")

            keywords = [experiment]
            if dataset:
                keywords.append(f"{experiment} {dataset}")
            if energy:
                keywords.append(f"{experiment} {energy}")
                if dataset:
                    keywords.append(f"{experiment} {dataset} {energy}")

            keywords.extend([
                f"{experiment} data", f"{experiment} detector", f"{experiment} collision",
                f"{experiment} analysis", f"{experiment} simulation"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated CERN Open Data keywords for experiment '{experiment}', dataset '{dataset or 'all'}', energy '{energy or 'all'}'.",
                "search_type": "experimental_data",
                "parameters": {"experiment": experiment, "dataset": dataset, "energy": energy}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate CERN Open Data keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_inspirehep_keywords(self, topic: str, experiment: str = None, theory: str = None) -> str:
        """Generates search keywords for INSPIRE-HEP."""
        logger.info(f"Generating INSPIRE-HEP keywords for topic: '{topic}', experiment: '{experiment}', theory: '{theory}'")
        try:
            if not topic.strip():
                raise ValueError("Topic cannot be empty.")

            keywords = [topic]
            if experiment:
                keywords.append(f"{topic} {experiment}")
            if theory:
                keywords.append(f"{topic} {theory}")
                if experiment:
                    keywords.append(f"{topic} {experiment} {theory}")

            keywords.extend([
                f"{topic} phenomenology", f"{topic} physics", f"{topic} particle",
                f"{topic} decay", f"{topic} production"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated INSPIRE-HEP keywords for topic '{topic}', experiment '{experiment or 'all'}', theory '{theory or 'all'}'.",
                "search_type": "particle_physics",
                "parameters": {"topic": topic, "experiment": experiment, "theory": theory}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate INSPIRE-HEP keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_nasa_ads_keywords(self, topic: str, object_type: str = None, method: str = None) -> str:
        """Generates search keywords for NASA ADS."""
        logger.info(f"Generating NASA ADS keywords for topic: '{topic}', object_type: '{object_type}', method: '{method}'")
        try:
            if not topic.strip():
                raise ValueError("Topic cannot be empty.")

            keywords = [topic]
            if object_type:
                keywords.append(f"{topic} {object_type}")
            if method:
                keywords.append(f"{topic} {method}")
                if object_type:
                    keywords.append(f"{topic} {object_type} {method}")

            keywords.extend([
                f"{topic} astronomy", f"{topic} astrophysics", f"{topic} cosmology",
                f"{topic} observation", f"{topic} survey"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated NASA ADS keywords for topic '{topic}', object_type '{object_type or 'all'}', method '{method or 'all'}'.",
                "search_type": "astronomy_papers",
                "parameters": {"topic": topic, "object_type": object_type, "method": method}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate NASA ADS keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_physics_keywords(self, concept: str, field: str = None) -> str:
        """Generates search keywords for Wikipedia physics topics."""
        logger.info(f"Generating Wikipedia physics keywords for concept: '{concept}', field: '{field}'")
        try:
            if not concept.strip():
                raise ValueError("Concept cannot be empty.")

            keywords = [concept]
            if field:
                keywords.append(f"{concept} {field}")

            keywords.extend([
                f"{concept} physics", f"{concept} theory", f"{concept} principle",
                f"{concept} equation", f"{concept} law"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia physics keywords for concept '{concept}' and field '{field or 'general'}'.",
                "search_type": "encyclopedia_articles",
                "parameters": {"concept": concept, "field": field}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia physics keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    # == Recent/Trending Keywords Functions ==

    def generate_arxiv_recent_keywords(self, category: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent papers on arXiv."""
        logger.info(f"Generating arXiv recent keywords for category: '{category}', days_back: {days_back}")
        try:
            keywords = ["recent papers", "new submissions", "latest research"]
            if category:
                keywords.extend([f"recent {category}", f"new {category}", f"latest {category}"])
            keywords.extend([
                "recent discoveries", "new results", "latest findings",
                f"last {days_back} days", "recent publications"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated arXiv recent keywords for category '{category or 'all'}' within last {days_back} days.",
                "search_type": "recent_papers",
                "parameters": {"category": category, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate arXiv recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_cern_recent_keywords(self, experiment: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent CERN datasets."""
        logger.info(f"Generating CERN recent keywords for experiment: '{experiment}', days_back: {days_back}")
        try:
            keywords = ["recent data", "new datasets", "latest releases"]
            if experiment:
                keywords.extend([f"recent {experiment}", f"new {experiment}", f"latest {experiment}"])
            keywords.extend([
                "recent experiments", "new measurements", "latest results",
                f"last {days_back} days", "recent publications"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated CERN recent keywords for experiment '{experiment or 'all'}' within last {days_back} days.",
                "search_type": "recent_datasets",
                "parameters": {"experiment": experiment, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate CERN recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_inspirehep_trending_keywords(self, field: str = None, period: str = "month") -> str:
        """Generates keywords for trending topics on INSPIRE-HEP."""
        logger.info(f"Generating INSPIRE-HEP trending keywords for field: '{field}', period: '{period}'")
        try:
            keywords = [f"trending {period}", f"popular papers", f"highly cited"]
            if field:
                keywords.extend([f"trending {field}", f"popular {field}", f"{field} breakthrough"])
            keywords.extend([
                "most cited", "breakthrough results", "hot topics",
                f"latest {period}", f"trending research"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated INSPIRE-HEP trending keywords for field '{field or 'all'}' within last {period}.",
                "search_type": "trending_publications",
                "parameters": {"field": field, "period": period}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate INSPIRE-HEP trending keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_nasa_ads_recent_keywords(self, journal: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent astronomy papers on NASA ADS."""
        logger.info(f"Generating NASA ADS recent keywords for journal: '{journal}', days_back: {days_back}")
        try:
            keywords = ["recent astronomy", "new astrophysics", "latest cosmology"]
            if journal:
                keywords.extend([f"recent {journal}", f"new {journal}", f"latest {journal}"])
            keywords.extend([
                "recent observations", "new discoveries", "latest surveys",
                f"last {days_back} days", "recent publications"
            ])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated NASA ADS recent keywords for journal '{journal or 'all'}' within last {days_back} days.",
                "search_type": "recent_astronomy",
                "parameters": {"journal": journal, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate NASA ADS recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_physics_recent_keywords(self, days_back: int = 30, language: str = "en") -> str:
        """Generates keywords for recent physics articles on Wikipedia."""
        logger.info(f"Generating Wikipedia physics recent keywords for days_back: {days_back}, language: '{language}'")
        try:
            keywords = [
                "recent physics articles", "new physics discoveries", "latest physics research",
                "recent physics breakthroughs", "new physics theories", "latest physics experiments",
                f"last {days_back} days physics", "recent physics updates"
            ]

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia physics recent keywords within last {days_back} days for language '{language}'.",
                "search_type": "recent_articles",
                "parameters": {"days_back": days_back, "language": language}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia physics recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)