# -*- coding: utf-8 -*-
from typing import List, Dict, Any
import json
from agno.tools import Toolkit
from agno.utils.log import logger

class AIRoboticsSearchToolkit(Toolkit):
    """A custom Toolkit for generating search keywords for AI and Robotics databases.

    This toolkit provides functions to generate search keywords for Papers With Code,
    Hugging Face Hub, arXiv, OpenML, and Wikipedia, tailored for AI and robotics research.
    """

    # == Detailed Instructions for the Agent ==
    instruction = [
        "Bạn là một trợ lý nghiên cứu AI và Robotics, chuyên cung cấp từ khóa tìm kiếm tối ưu cho các cơ sở dữ liệu AI.",
        "<PERSON>hi sử dụng các công cụ trong AIRoboticsSearchToolkit, tuân thủ các định dạng từ khóa được chỉ định như sau:",
        "- Papers With Code: Sử dụng định dạng '<task>/<subtask>' hoặc '<model_architecture>:<application>' (ví dụ: 'computer-vision/object-detection', 'transformer:nlp').",
        "- Hugging Face Hub: Sử dụng định dạng '<model_type>-<variant>' hoặc '<task>::<dataset>' (ví dụ: 'bert-base-uncased', 'text-classification::imdb').",
        "- arXiv: Sử dụng định dạng '<cs.XX>+<cs.YY>:<keyword>' (ví dụ: 'cs.AI+cs.CV:neural rendering', 'cs.LG+cs.NE:reinforcement learning').",
        "- OpenML: Sử dụng định dạng 'data/<category>/<specific>' hoặc 'task/<task_type>' (ví dụ: 'data/image/mnist', 'task/classification').",
        "- Wikipedia AI: Sử dụng định dạng '<algorithm_type> algorithm' hoặc '<technique> in <field>' (ví dụ: 'backpropagation algorithm', 'transformer in NLP').",
        "Ngoài ra, toolkit cũng hỗ trợ tạo từ khóa cho việc tìm kiếm nội dung mới nhất và trending:",
        "- Papers With Code Trending: Tạo từ khóa cho papers trending theo thời gian và danh mục.",
        "- Hugging Face Trending: Tạo từ khóa cho models trending theo task và thời gian.",
        "- arXiv Recent: Tạo từ khóa cho papers mới nhất theo categories và thời gian.",
        "- OpenML Recent: Tạo từ khóa cho datasets mới và phổ biến.",
        "- Wikipedia AI Recent: Tạo từ khóa cho bài viết AI mới được tạo hoặc cập nhật.",
        "Kiểm tra tính hợp lệ của tham số đầu vào và trả về từ khóa phù hợp với từng cơ sở dữ liệu.",
        "Trả về kết quả dưới dạng JSON với trạng thái ('status'), danh sách từ khóa ('keywords'), và thông báo ('message').",
        "Nếu có lỗi, trả về trạng thái 'error' với mô tả lỗi chi tiết."
    ]

    # == Detailed Few-Shot Examples ==
    few_shot_examples = [
        {
            "user": "Tìm thông tin về transformer models trong NLP.",
            "tool_calls": [
                {
                    "name": "generate_papers_with_code_keywords",
                    "arguments": {"task": "nlp/language-modeling", "model_type": "transformer"}
                },
                {
                    "name": "generate_huggingface_keywords",
                    "arguments": {"model_type": "text-generation", "query": "transformer"}
                },
                {
                    "name": "generate_arxiv_keywords",
                    "arguments": {"categories": ["cs.CL", "cs.AI"], "topic": "transformer"}
                },
                {
                    "name": "generate_wikipedia_ai_keywords",
                    "arguments": {"concept": "transformer in NLP"}
                }
            ]
        },
        {
            "user": "Tìm datasets và models mới nhất cho computer vision.",
            "tool_calls": [
                {
                    "name": "generate_papers_with_code_trending_keywords",
                    "arguments": {"category": "computer-vision", "time_period": "week"}
                },
                {
                    "name": "generate_huggingface_trending_keywords",
                    "arguments": {"task": "image-classification", "time_period": "month"}
                },
                {
                    "name": "generate_arxiv_recent_keywords",
                    "arguments": {"categories": ["cs.CV"], "days_back": 7}
                },
                {
                    "name": "generate_openml_recent_keywords",
                    "arguments": {"category": "image", "days_back": 30}
                }
            ]
        },
        {
            "user": "Tìm nghiên cứu về reinforcement learning.",
            "tool_calls": [
                {
                    "name": "generate_papers_with_code_keywords",
                    "arguments": {"task": "reinforcement-learning", "model_type": "deep-rl"}
                },
                {
                    "name": "generate_arxiv_keywords",
                    "arguments": {"categories": ["cs.LG", "cs.AI"], "topic": "reinforcement learning"}
                },
                {
                    "name": "generate_wikipedia_ai_keywords",
                    "arguments": {"concept": "reinforcement learning algorithm"}
                }
            ]
        }
    ]

    def __init__(self):
        """Initializes the AIRoboticsSearchToolkit."""
        super().__init__(
            name="ai_robotics_search_toolkit",
            tools=[
                self.generate_papers_with_code_keywords,
                self.generate_huggingface_keywords,
                self.generate_arxiv_keywords,
                self.generate_openml_keywords,
                self.generate_wikipedia_ai_keywords,
                self.generate_papers_with_code_trending_keywords,
                self.generate_huggingface_trending_keywords,
                self.generate_arxiv_recent_keywords,
                self.generate_openml_recent_keywords,
                self.generate_wikipedia_ai_recent_keywords
            ],
            instructions=self.instruction
        )
        self.few_shot_examples = self.few_shot_examples
        logger.info("AIRoboticsSearchToolkit initialized.")

    def generate_papers_with_code_keywords(self, task: str, model_type: str = None) -> str:
        """Generates search keywords for Papers With Code.

        Args:
            task: The task or domain (e.g., 'computer-vision', 'nlp', 'reinforcement-learning').
            model_type: Optional model type (e.g., 'transformer', 'cnn', 'rnn').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating Papers With Code keywords for task: '{task}', model_type: '{model_type}'")
        try:
            if not task.strip():
                raise ValueError("Task cannot be empty.")

            # Tạo từ khóa cho Papers With Code
            keywords = [task]
            if model_type:
                keywords.append(f"{model_type} {task}")
                keywords.append(f"{task} {model_type}")

            # Thêm từ khóa mở rộng
            keywords.append(f"{task} state-of-the-art")
            keywords.append(f"{task} benchmark")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Papers With Code keywords for task '{task}' and model type '{model_type or 'general'}'.",
                "search_type": "papers_research",
                "parameters": {
                    "task": task,
                    "model_type": model_type
                }
            }
            logger.debug(f"Papers With Code keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating Papers With Code keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate Papers With Code keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_huggingface_keywords(self, model_type: str, query: str = None) -> str:
        """Generates search keywords for Hugging Face Hub.

        Args:
            model_type: The model type or task (e.g., 'text-classification', 'image-classification').
            query: Optional specific query (e.g., 'bert', 'gpt').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating Hugging Face keywords for model_type: '{model_type}', query: '{query}'")
        try:
            if not model_type.strip():
                raise ValueError("Model type cannot be empty.")

            # Tạo từ khóa cho Hugging Face
            keywords = [model_type]
            if query:
                keywords.append(f"{query}")
                keywords.append(f"{query}-{model_type}")

            # Thêm từ khóa mở rộng
            keywords.append(f"{model_type} model")
            keywords.append(f"pretrained {model_type}")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Hugging Face keywords for model type '{model_type}' and query '{query or 'general'}'.",
                "search_type": "models_datasets",
                "parameters": {
                    "model_type": model_type,
                    "query": query
                }
            }
            logger.debug(f"Hugging Face keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating Hugging Face keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate Hugging Face keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_arxiv_keywords(self, categories: List[str], topic: str = None) -> str:
        """Generates search keywords for arXiv.

        Args:
            categories: List of arXiv categories (e.g., ['cs.AI', 'cs.LG']).
            topic: Optional topic keyword (e.g., 'neural networks').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating arXiv keywords for categories: {categories}, topic: '{topic}'")
        try:
            if not categories or len(categories) == 0:
                raise ValueError("Categories cannot be empty.")

            # Tạo từ khóa cho arXiv
            keywords = []
            cat_string = "+".join(categories)

            if topic:
                keywords.append(f"{cat_string}:{topic}")
                keywords.append(f"all:{topic}")
            else:
                keywords.append(cat_string)

            # Thêm từ khóa mở rộng
            for cat in categories:
                keywords.append(f"cat:{cat}")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated arXiv keywords for categories {categories} and topic '{topic or 'general'}'.",
                "search_type": "academic_papers",
                "parameters": {
                    "categories": categories,
                    "topic": topic
                }
            }
            logger.debug(f"arXiv keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating arXiv keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate arXiv keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_openml_keywords(self, query_type: str, category: str = None) -> str:
        """Generates search keywords for OpenML.

        Args:
            query_type: Type of search ('data' or 'task').
            category: Optional category (e.g., 'classification', 'image').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating OpenML keywords for query_type: '{query_type}', category: '{category}'")
        try:
            if not query_type.strip():
                raise ValueError("Query type cannot be empty.")

            # Tạo từ khóa cho OpenML
            keywords = []
            if category:
                keywords.append(f"{query_type}/{category}")
                keywords.append(category)
            else:
                keywords.append(query_type)

            # Thêm từ khóa mở rộng
            if query_type == "data":
                keywords.append("dataset")
                keywords.append("machine learning dataset")
            elif query_type == "task":
                keywords.append("ml task")
                keywords.append("benchmark task")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated OpenML keywords for query type '{query_type}' and category '{category or 'general'}'.",
                "search_type": "datasets_tasks",
                "parameters": {
                    "query_type": query_type,
                    "category": category
                }
            }
            logger.debug(f"OpenML keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating OpenML keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate OpenML keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_ai_keywords(self, concept: str) -> str:
        """Generates search keywords for Wikipedia AI topics.

        Args:
            concept: The AI concept or algorithm (e.g., 'neural networks', 'machine learning').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating Wikipedia AI keywords for concept: '{concept}'")
        try:
            if not concept.strip():
                raise ValueError("Concept cannot be empty.")

            # Tạo từ khóa cho Wikipedia AI
            keywords = [concept]
            keywords.append(f"{concept} algorithm")
            keywords.append(f"{concept} artificial intelligence")
            keywords.append(f"{concept} machine learning")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia AI keywords for concept '{concept}'.",
                "search_type": "ai_concepts",
                "parameters": {
                    "concept": concept
                }
            }
            logger.debug(f"Wikipedia AI keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating Wikipedia AI keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate Wikipedia AI keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    # == Trending/Recent Keywords Functions ==

    def generate_papers_with_code_trending_keywords(self, category: str = None, time_period: str = "week") -> str:
        """Generates keywords for trending papers on Papers With Code.

        Args:
            category: Optional category filter (e.g., 'computer-vision', 'nlp').
            time_period: Time period for trending ('week', 'month', 'year').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating Papers With Code trending keywords for category: '{category}', period: '{time_period}'")
        try:
            # Tạo từ khóa cho trending papers
            keywords = []

            if category:
                keywords.append(f"trending {category}")
                keywords.append(f"{category} popular")
                keywords.append(f"{category} state-of-the-art")
            else:
                keywords.append("trending papers")
                keywords.append("popular research")
                keywords.append("state-of-the-art")

            # Thêm filter thời gian
            keywords.append(f"recent {time_period}")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Papers With Code trending keywords for category '{category or 'all'}' within {time_period}.",
                "search_type": "trending_papers",
                "parameters": {
                    "category": category,
                    "time_period": time_period
                }
            }
            logger.debug(f"Papers With Code trending keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating Papers With Code trending keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate Papers With Code trending keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_huggingface_trending_keywords(self, task: str = None, time_period: str = "week") -> str:
        """Generates keywords for trending models on Hugging Face Hub."""
        logger.info(f"Generating Hugging Face trending keywords for task: '{task}', period: '{time_period}'")
        try:
            keywords = []
            if task:
                keywords.extend([f"trending {task}", f"popular {task}", f"{task} models"])
            else:
                keywords.extend(["trending models", "popular models", "most downloaded"])

            keywords.append(f"recent {time_period}")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Hugging Face trending keywords for task '{task or 'all'}' within {time_period}.",
                "search_type": "trending_models",
                "parameters": {"task": task, "time_period": time_period}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Hugging Face trending keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_arxiv_recent_keywords(self, categories: List[str] = None, days_back: int = 7) -> str:
        """Generates keywords for recent papers on arXiv."""
        logger.info(f"Generating arXiv recent keywords for categories: {categories}, days_back: {days_back}")
        try:
            keywords = []
            if categories:
                cat_string = "+".join(categories)
                keywords.append(f"recent {cat_string}")
                for cat in categories:
                    keywords.append(f"new {cat}")
            else:
                keywords.extend(["recent AI papers", "new machine learning", "latest research"])

            keywords.append(f"last {days_back} days")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated arXiv recent keywords for categories {categories or 'all'} within last {days_back} days.",
                "search_type": "recent_papers",
                "parameters": {"categories": categories, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate arXiv recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_openml_recent_keywords(self, category: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent datasets on OpenML."""
        logger.info(f"Generating OpenML recent keywords for category: '{category}', days_back: {days_back}")
        try:
            keywords = []
            if category:
                keywords.extend([f"recent {category}", f"new {category} datasets"])
            else:
                keywords.extend(["recent datasets", "new datasets", "latest data"])

            keywords.append(f"last {days_back} days")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated OpenML recent keywords for category '{category or 'all'}' within last {days_back} days.",
                "search_type": "recent_datasets",
                "parameters": {"category": category, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate OpenML recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_ai_recent_keywords(self, days_back: int = 30, language: str = "en") -> str:
        """Generates keywords for recent AI articles on Wikipedia."""
        logger.info(f"Generating Wikipedia AI recent keywords for days_back: {days_back}, language: '{language}'")
        try:
            keywords = [
                "recent AI developments", "new machine learning", "latest artificial intelligence",
                "recent robotics", "new deep learning", "latest computer vision",
                f"last {days_back} days AI", "recent AI research"
            ]

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia AI recent keywords within last {days_back} days for language '{language}'.",
                "search_type": "recent_ai_articles",
                "parameters": {"days_back": days_back, "language": language}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia AI recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)