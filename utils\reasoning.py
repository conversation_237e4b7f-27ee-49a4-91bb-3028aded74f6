"""
Module reasoning - <PERSON><PERSON><PERSON> hợp các công cụ reasoning.
"""

import logging
import json
import time
from typing import Dict, Any, List, Optional, Callable, Union

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ReasoningEngine:
    """<PERSON><PERSON><PERSON> kết hợp các công cụ reasoning."""
    
    def __init__(self, 
                 enable_lcot: bool = True,
                 enable_mcot: bool = True,
                 enable_cot_encyclopedia: bool = True,
                 lcot_steps: int = 5,
                 mcot_iterations: int = 3,
                 model_id: Optional[str] = None):
        """
        Khởi tạo reasoning engine.
        
        Args:
            enable_lcot: Bật/tắt LCoT
            enable_mcot: Bật/tắt MCoT
            enable_cot_encyclopedia: Bật/tắt CoT Encyclopedia
            lcot_steps: Số bước LCoT
            mcot_iterations: Số lần lặp MCoT
            model_id: ID của mô hình ngôn ngữ
        """
        self.enable_lcot = enable_lcot
        self.enable_mcot = enable_mcot
        self.enable_cot_encyclopedia = enable_cot_encyclopedia
        self.lcot_steps = lcot_steps
        self.mcot_iterations = mcot_iterations
        self.model_id = model_id
        
        # Khởi tạo các công cụ reasoning
        self._init_reasoning_tools()
    
    def _init_reasoning_tools(self) -> None:
        """Khởi tạo các công cụ reasoning."""
        try:
            from tools.common.cot import LCoTTools, MCoTTools
            
            # Khởi tạo LCoT
            if self.enable_lcot:
                self.lcot_tools = LCoTTools(
                    add_instructions=True,
                    add_few_shot=True,
                    num_steps=self.lcot_steps
                )
                logger.info("Initialized LCoT Tools")
            else:
                self.lcot_tools = None
            
            # Khởi tạo MCoT
            if self.enable_mcot:
                self.mcot_tools = MCoTTools(
                    add_instructions=True,
                    add_few_shot=True,
                    max_iterations=self.mcot_iterations
                )
                logger.info("Initialized MCoT Tools")
            else:
                self.mcot_tools = None
            
            # Khởi tạo CoT Encyclopedia
            if self.enable_cot_encyclopedia:
                try:
                    from tools.common.cot_encyclopedia import CoTEncyclopediaTools
                    self.cot_encyclopedia_tools = CoTEncyclopediaTools()
                    logger.info("Initialized CoT Encyclopedia Tools")
                except ImportError:
                    logger.warning("CoT Encyclopedia Tools not available")
                    self.cot_encyclopedia_tools = None
                    self.enable_cot_encyclopedia = False
            else:
                self.cot_encyclopedia_tools = None
        except ImportError as e:
            logger.error(f"Error initializing reasoning tools: {e}")
            self.lcot_tools = None
            self.mcot_tools = None
            self.cot_encyclopedia_tools = None
            self.enable_lcot = False
            self.enable_mcot = False
            self.enable_cot_encyclopedia = False
    
    def reason(self, 
              prompt: str, 
              reasoning_type: str = "auto", 
              context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Thực hiện reasoning.
        
        Args:
            prompt: Yêu cầu reasoning
            reasoning_type: Loại reasoning (auto, lcot, mcot, cot_encyclopedia, combined)
            context: Ngữ cảnh (tùy chọn)
            
        Returns:
            Kết quả reasoning
        """
        context = context or {}
        
        # Chọn loại reasoning
        if reasoning_type == "auto":
            reasoning_type = self._select_reasoning_type(prompt, context)
        
        # Thực hiện reasoning
        if reasoning_type == "lcot":
            return self._reason_lcot(prompt, context)
        elif reasoning_type == "mcot":
            return self._reason_mcot(prompt, context)
        elif reasoning_type == "cot_encyclopedia":
            return self._reason_cot_encyclopedia(prompt, context)
        elif reasoning_type == "combined":
            return self._reason_combined(prompt, context)
        else:
            logger.warning(f"Unknown reasoning type: {reasoning_type}, using lcot")
            return self._reason_lcot(prompt, context)
    
    def _select_reasoning_type(self, prompt: str, context: Dict[str, Any]) -> str:
        """
        Chọn loại reasoning phù hợp.
        
        Args:
            prompt: Yêu cầu reasoning
            context: Ngữ cảnh
            
        Returns:
            Loại reasoning
        """
        # Kiểm tra xem có user_query trong context không
        if "user_query" in context and self.enable_cot_encyclopedia:
            return "cot_encyclopedia"
        
        # Kiểm tra độ phức tạp của prompt
        prompt_length = len(prompt)
        
        # Nếu prompt quá dài, sử dụng combined
        if prompt_length > 1000 and self.enable_lcot and self.enable_mcot:
            return "combined"
        
        # Nếu prompt chứa nhiều câu hỏi hoặc yêu cầu phân tích nhiều góc độ, sử dụng mcot
        if ("compare" in prompt.lower() or 
            "analyze" in prompt.lower() or 
            "multiple" in prompt.lower() or 
            "different perspectives" in prompt.lower()) and self.enable_mcot:
            return "mcot"
        
        # Mặc định sử dụng lcot
        if self.enable_lcot:
            return "lcot"
        elif self.enable_mcot:
            return "mcot"
        elif self.enable_cot_encyclopedia:
            return "cot_encyclopedia"
        else:
            return "lcot"  # Fallback    
    def _reason_lcot(self, prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Thực hiện reasoning với LCoT.
        
        Args:
            prompt: Yêu cầu reasoning
            context: Ngữ cảnh
            
        Returns:
            Kết quả reasoning
        """
        if not self.enable_lcot or self.lcot_tools is None:
            logger.warning("LCoT is disabled or not available")
            return {"result": "", "reasoning_type": "none", "error": "LCoT is disabled or not available"}
        
        try:
            start_time = time.time()
            
            # Thực hiện LCoT
            try:
                # Kiểm tra xem lcot_reason có cần tham số agent không
                import inspect
                sig = inspect.signature(self.lcot_tools.lcot_reason)
                if 'agent' in sig.parameters:
                    # Nếu cần tham số agent, truyền None
                    result = self.lcot_tools.lcot_reason(prompt, agent=None)
                else:
                    # Nếu không cần tham số agent
                    result = self.lcot_tools.lcot_reason(prompt)
            except Exception as e:
                logger.error(f"Error in LCoT reasoning signature check: {e}")
                # Thử với tham số agent
                try:
                    result = self.lcot_tools.lcot_reason(prompt, agent=None)
                except Exception as e2:
                    logger.error(f"Error in LCoT reasoning with agent=None: {e2}")
                    # Trả về kết quả mặc định
                    result = f"Không thể thực hiện reasoning: {e2}. Trả về context gốc: {prompt}"
            
            end_time = time.time()
            
            return {
                "result": result,
                "reasoning_type": "lcot",
                "time_taken": end_time - start_time,
                "steps": self.lcot_steps
            }
        except Exception as e:
            logger.error(f"Error in LCoT reasoning: {e}")
            return {"result": "", "reasoning_type": "lcot", "error": str(e)}
    
    def _reason_mcot(self, prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Thực hiện reasoning với MCoT.
        
        Args:
            prompt: Yêu cầu reasoning
            context: Ngữ cảnh
            
        Returns:
            Kết quả reasoning
        """
        if not self.enable_mcot or self.mcot_tools is None:
            logger.warning("MCoT is disabled or not available")
            return {"result": "", "reasoning_type": "none", "error": "MCoT is disabled or not available"}
        
        try:
            start_time = time.time()
            
            # Thực hiện MCoT
            result = self.mcot_tools.mcot_reason(prompt)
            
            end_time = time.time()
            
            return {
                "result": result,
                "reasoning_type": "mcot",
                "time_taken": end_time - start_time,
                "iterations": self.mcot_iterations
            }
        except Exception as e:
            logger.error(f"Error in MCoT reasoning: {e}")
            return {"result": "", "reasoning_type": "mcot", "error": str(e)}
    
    def _reason_cot_encyclopedia(self, prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Thực hiện reasoning với CoT Encyclopedia.
        
        Args:
            prompt: Yêu cầu reasoning
            context: Ngữ cảnh
            
        Returns:
            Kết quả reasoning
        """
        if not self.enable_cot_encyclopedia or self.cot_encyclopedia_tools is None:
            logger.warning("CoT Encyclopedia is disabled or not available")
            return {"result": "", "reasoning_type": "none", "error": "CoT Encyclopedia is disabled or not available"}
        
        try:
            start_time = time.time()
            
            # Lấy user_query từ context
            user_query = context.get("user_query", "")
            
            # Nếu không có user_query, sử dụng prompt
            if not user_query:
                user_query = prompt
            
            # Lấy lcot_output từ context hoặc tạo mới
            lcot_output = context.get("lcot_output", "")
            
            # Nếu không có lcot_output, tạo mới với LCoT
            if not lcot_output and self.enable_lcot and self.lcot_tools is not None:
                try:
                    # Kiểm tra xem lcot_reason có cần tham số agent không
                    import inspect
                    sig = inspect.signature(self.lcot_tools.lcot_reason)
                    if 'agent' in sig.parameters:
                        # Nếu cần tham số agent, truyền None
                        lcot_output = self.lcot_tools.lcot_reason(prompt, agent=None)
                    else:
                        # Nếu không cần tham số agent
                        lcot_output = self.lcot_tools.lcot_reason(prompt)
                except Exception as e:
                    logger.error(f"Error in LCoT reasoning signature check: {e}")
                    # Thử với tham số agent
                    try:
                        lcot_output = self.lcot_tools.lcot_reason(prompt, agent=None)
                    except Exception as e2:
                        logger.error(f"Error in LCoT reasoning with agent=None: {e2}")
                        # Trả về kết quả mặc định
                        lcot_output = f"Không thể thực hiện reasoning: {e2}. Trả về context gốc: {prompt}"
            
            # Thực hiện CoT Encyclopedia
            result = self.cot_encyclopedia_tools.cot_encyclopedia(user_query, lcot_output)
            
            end_time = time.time()
            
            return {
                "result": result,
                "reasoning_type": "cot_encyclopedia",
                "time_taken": end_time - start_time,
                "user_query": user_query,
                "lcot_output": lcot_output
            }
        except Exception as e:
            logger.error(f"Error in CoT Encyclopedia reasoning: {e}")
            return {"result": "", "reasoning_type": "cot_encyclopedia", "error": str(e)}    
    def _reason_combined(self, prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Thực hiện reasoning kết hợp.
        
        Args:
            prompt: Yêu cầu reasoning
            context: Ngữ cảnh
            
        Returns:
            Kết quả reasoning
        """
        results = {}
        errors = []
        
        # Thực hiện LCoT
        if self.enable_lcot and self.lcot_tools is not None:
            try:
                lcot_result = self._reason_lcot(prompt, context)
                results["lcot"] = lcot_result
                
                # Thêm lcot_output vào context
                context["lcot_output"] = lcot_result["result"]
            except Exception as e:
                logger.error(f"Error in LCoT reasoning: {e}")
                errors.append(f"LCoT: {str(e)}")
        
        # Thực hiện MCoT
        if self.enable_mcot and self.mcot_tools is not None:
            try:
                mcot_result = self._reason_mcot(prompt, context)
                results["mcot"] = mcot_result
            except Exception as e:
                logger.error(f"Error in MCoT reasoning: {e}")
                errors.append(f"MCoT: {str(e)}")
        
        # Thực hiện CoT Encyclopedia
        if self.enable_cot_encyclopedia and self.cot_encyclopedia_tools is not None and "lcot_output" in context:
            try:
                # Thêm user_query vào context nếu chưa có
                if "user_query" not in context:
                    context["user_query"] = prompt
                
                cot_encyclopedia_result = self._reason_cot_encyclopedia(prompt, context)
                results["cot_encyclopedia"] = cot_encyclopedia_result
            except Exception as e:
                logger.error(f"Error in CoT Encyclopedia reasoning: {e}")
                errors.append(f"CoT Encyclopedia: {str(e)}")
        
        # Kết hợp kết quả
        combined_result = self._combine_results(results)
        
        return {
            "result": combined_result,
            "reasoning_type": "combined",
            "individual_results": results,
            "errors": errors if errors else None
        }
    
    def _combine_results(self, results: Dict[str, Dict[str, Any]]) -> str:
        """
        Kết hợp kết quả từ các phương pháp reasoning.
        
        Args:
            results: Kết quả từ các phương pháp reasoning
            
        Returns:
            Kết quả kết hợp
        """
        # Nếu có CoT Encyclopedia, ưu tiên sử dụng
        if "cot_encyclopedia" in results and results["cot_encyclopedia"].get("result"):
            return results["cot_encyclopedia"]["result"]
        
        # Nếu có MCoT, ưu tiên sử dụng
        if "mcot" in results and results["mcot"].get("result"):
            return results["mcot"]["result"]
        
        # Nếu có LCoT, sử dụng
        if "lcot" in results and results["lcot"].get("result"):
            return results["lcot"]["result"]
        
        # Nếu không có kết quả nào, trả về chuỗi rỗng
        return ""


# Singleton instance
_reasoning_engine_instance = None

def get_reasoning_engine(enable_lcot: bool = True,
                        enable_mcot: bool = True,
                        enable_cot_encyclopedia: bool = True,
                        lcot_steps: int = 5,
                        mcot_iterations: int = 3,
                        model_id: Optional[str] = None) -> ReasoningEngine:
    """
    Lấy instance của ReasoningEngine (singleton pattern).
    
    Args:
        enable_lcot: Bật/tắt LCoT
        enable_mcot: Bật/tắt MCoT
        enable_cot_encyclopedia: Bật/tắt CoT Encyclopedia
        lcot_steps: Số bước LCoT
        mcot_iterations: Số lần lặp MCoT
        model_id: ID của mô hình ngôn ngữ
        
    Returns:
        Instance của ReasoningEngine
    """
    global _reasoning_engine_instance
    if _reasoning_engine_instance is None:
        _reasoning_engine_instance = ReasoningEngine(
            enable_lcot=enable_lcot,
            enable_mcot=enable_mcot,
            enable_cot_encyclopedia=enable_cot_encyclopedia,
            lcot_steps=lcot_steps,
            mcot_iterations=mcot_iterations,
            model_id=model_id
        )
    return _reasoning_engine_instance# Hàm tiện ích
def reason(prompt: str, 
          reasoning_type: str = "auto", 
          context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Thực hiện reasoning.
    
    Args:
        prompt: Yêu cầu reasoning
        reasoning_type: Loại reasoning (auto, lcot, mcot, cot_encyclopedia, combined)
        context: Ngữ cảnh (tùy chọn)
        
    Returns:
        Kết quả reasoning
    """
    engine = get_reasoning_engine()
    return engine.reason(prompt, reasoning_type, context)


def reason_lcot(prompt: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Thực hiện reasoning với LCoT.
    
    Args:
        prompt: Yêu cầu reasoning
        context: Ngữ cảnh (tùy chọn)
        
    Returns:
        Kết quả reasoning
    """
    engine = get_reasoning_engine()
    return engine.reason(prompt, "lcot", context)


def reason_mcot(prompt: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Thực hiện reasoning với MCoT.
    
    Args:
        prompt: Yêu cầu reasoning
        context: Ngữ cảnh (tùy chọn)
        
    Returns:
        Kết quả reasoning
    """
    engine = get_reasoning_engine()
    return engine.reason(prompt, "mcot", context)


def reason_cot_encyclopedia(prompt: str, 
                           user_query: str, 
                           lcot_output: Optional[str] = None) -> Dict[str, Any]:
    """
    Thực hiện reasoning với CoT Encyclopedia.
    
    Args:
        prompt: Yêu cầu reasoning
        user_query: Câu hỏi của người dùng
        lcot_output: Kết quả LCoT (tùy chọn)
        
    Returns:
        Kết quả reasoning
    """
    context = {
        "user_query": user_query
    }
    
    if lcot_output:
        context["lcot_output"] = lcot_output
    
    engine = get_reasoning_engine()
    return engine.reason(prompt, "cot_encyclopedia", context)


def reason_combined(prompt: str, 
                   user_query: Optional[str] = None, 
                   context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Thực hiện reasoning kết hợp.
    
    Args:
        prompt: Yêu cầu reasoning
        user_query: Câu hỏi của người dùng (tùy chọn)
        context: Ngữ cảnh (tùy chọn)
        
    Returns:
        Kết quả reasoning
    """
    context = context or {}
    
    if user_query:
        context["user_query"] = user_query
    
    engine = get_reasoning_engine()
    return engine.reason(prompt, "combined", context)


if __name__ == "__main__":
    # Test reasoning
    prompt = "Explain the concept of transformer models in deep learning and how they work."
    
    print("Testing LCoT reasoning:")
    lcot_result = reason_lcot(prompt)
    print(f"LCoT result: {lcot_result['result'][:100]}...")
    
    print("\nTesting MCoT reasoning:")
    mcot_result = reason_mcot(prompt)
    print(f"MCoT result: {mcot_result['result'][:100]}...")
    
    print("\nTesting CoT Encyclopedia reasoning:")
    user_query = "What are transformer models?"
    cot_encyclopedia_result = reason_cot_encyclopedia(prompt, user_query, lcot_result["result"])
    print(f"CoT Encyclopedia result: {cot_encyclopedia_result['result'][:100]}...")
    
    print("\nTesting combined reasoning:")
    combined_result = reason_combined(prompt, user_query)
    print(f"Combined result: {combined_result['result'][:100]}...")