import asyncio
import json
import logging
from textwrap import dedent
from typing import Optional
import spacy
from sentence_transformers import SentenceTransformer
from sklearn.cluster import KMeans
import numpy as np
from agno.tools import Toolkit
from agno.agent import Agent
from agno.reasoning.step import NextAction, ReasoningStep

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CoTEncyclopediaTools(Toolkit):
    """Toolkit for analyzing and controlling reasoning strategies based on The CoT Encyclopedia."""
    
    def __init__(self, add_instructions: bool = True, add_few_shot: bool = True, n_clusters: int = 2):
        super().__init__(name="cot_encyclopedia")
        
        # Validate input parameters
        if n_clusters < 1:
            raise ValueError("Number of clusters must be at least 1")
        
        self.add_instructions = add_instructions
        self.add_few_shot = add_few_shot
        self.n_clusters = n_clusters
        self.description = "A tool for analyzing, predicting, and controlling reasoning strategies using The CoT Encyclopedia."
        logger.info("Initialized CoTEncyclopediaTools with instructions=%s, few_shot=%s, n_clusters=%s", 
                    add_instructions, add_few_shot, n_clusters)
        
        # Load NLP and embedding models
        self.nlp = spacy.load("en_core_web_sm")
        self.embedder = SentenceTransformer("all-MiniLM-L6-v2")
        
        # Few-shot examples
        self.few_shot_examples = dedent("""\
            Example Query: Solve a quadratic equation
            Step 1: Criteria Identification
            - Identified steps: Analyze problem, apply quadratic formula, compute discriminant.
            Step 2: Semantic Embedding
            - Criteria embedded into vectors for clustering.
            Step 3: Clustering
            - Criteria grouped into clusters: [Analyze, Apply formula], [Compute discriminant].
            Step 4: Contrastive Rubric
            - Cluster 0: Logical steps; Cluster 1: Computational steps.
            Step 5: Report
            - Suggested prompt: "Solve step-by-step, explaining each logical and computational step."
        """) if add_few_shot else ""
        
        self.register(self.cot_encyclopedia_analyze)
    
    async def cot_encyclopedia_analyze(self, query: str, agent: Agent, cot_output: Optional[str] = None) -> str:
        """Analyze CoT using The CoT Encyclopedia framework and return markdown with JSON tool call."""
        try:
            # Validate input
            if not isinstance(query, str) or not query.strip():
                logger.error("Invalid query: %s", query)
                return json.dumps({"error": "Query must be a non-empty string"})
            
            if not isinstance(agent, Agent):
                logger.error("Invalid agent instance: %s", agent)
                return json.dumps({"error": "Invalid agent instance"})
            
            logger.info("Processing CoT Encyclopedia for query: %s", query)
            steps = []
            
            if self.few_shot_examples:
                steps.append(f"### Few-Shot Examples\n{self.few_shot_examples}\n")
            
            # Step 1: Criteria Identification
            if cot_output:
                criteria = self._extract_criteria(cot_output)
            else:
                # Generate mock CoT if none provided
                mock_cot = await self._generate_mock_cot(agent, query)
                criteria = self._extract_criteria(mock_cot)
            
            steps.append(f"### Step 1: Criteria Identification\nCriteria: {', '.join(criteria)}\n")
            
            # Step 2: Semantic Embedding
            embeddings = self.embedder.encode(criteria)
            steps.append("### Step 2: Semantic Embedding\nCriteria embedded into semantic vectors.\n")
            
            # Step 3: Clustering
            labels = self._cluster_criteria(embeddings)
            clusters = {}
            for cluster_id in set(labels):
                cluster_criteria = [criteria[i] for i, label in enumerate(labels) if label == cluster_id]
                clusters[f"Cluster {cluster_id}"] = cluster_criteria
            steps.append(f"### Step 3: Clustering\nClusters: {json.dumps(clusters, indent=2)}\n")
            
            # Step 4: Contrastive Rubric
            rubric = self._generate_rubric(clusters)
            steps.append(f"### Step 4: Contrastive Rubric\nRubric: {json.dumps(rubric, indent=2)}\n")
            
            # Step 5: Report and Suggested Prompt
            suggested_prompt = self._generate_suggested_prompt(clusters)
            steps.append(f"### Step 5: Report\nSuggested Prompt: {suggested_prompt}\n")
            
            output = "\n".join(steps)
            logger.info("CoT Encyclopedia analysis completed for query: %s", query)
            
            # Save reasoning state
            reasoning_step = ReasoningStep(
                title="CoT Encyclopedia Analysis",
                reasoning=output,
                action="Structured reasoning analysis completed",
                next_action=NextAction.CONTINUE,
                confidence=0.9
            )
            self._save_reasoning_step(agent, reasoning_step)
            
            # Build JSON tool call
            tool_call = {
                "tool": "cot_encyclopedia_analyze",
                "parameters": {
                    "query": query,
                    "cot_output": cot_output,
                    "n_clusters": self.n_clusters,
                    "metadata": {
                        "type": "application/json",
                        "version": "1.0"
                    }
                }
            }
            
            return json.dumps({
                "tool_call": tool_call,
                "output": output
            }, indent=2)
            
        except Exception as e:
            logger.error("Error in CoT Encyclopedia processing: %s", str(e))
            return json.dumps({"error": f"CoT Encyclopedia processing failed: {str(e)}"})
    
    def _extract_criteria(self, cot_output: str) -> list:
        """Extract reasoning criteria from CoT output."""
        doc = self.nlp(cot_output)
        criteria = []
        for sent in doc.sents:
            criterion = " ".join([token.text for token in sent if token.pos_ in ["VERB", "NOUN"]])
            if criterion:
                criteria.append(criterion)
        return criteria if criteria else ["default reasoning step"]
    
    async def _generate_mock_cot(self, agent: Agent, query: str) -> str:
        """Generate mock CoT if none provided."""
        mock_cot = [
            f"Step 1: Analyze the query: {query}",
            "Step 2: Identify key concepts and assumptions.",
            "Step 3: Outline a reasoning path.",
            "Step 4: Validate with external information."
        ]
        return "\n".join(mock_cot)
    
    def _cluster_criteria(self, embeddings: np.ndarray) -> list:
        """Cluster criteria using K-means."""
        kmeans = KMeans(n_clusters=self.n_clusters, random_state=42)
        labels = kmeans.fit_predict(embeddings)
        return labels.tolist()
    
    def _generate_rubric(self, clusters: dict) -> dict:
        """Generate contrastive rubric based on clusters."""
        rubric = {}
        for cluster_id, criteria in clusters.items():
            rubric[cluster_id] = {
                "description": f"Strategy involving: {', '.join(criteria)}",
                "strengths": "Logical and structured" if "analyze" in str(criteria).lower() else "Computation-focused",
                "weaknesses": "May miss creative approaches" if "analyze" in str(criteria).lower() else "May lack depth"
            }
        return rubric
    
    def _generate_suggested_prompt(self, clusters: dict) -> str:
        """Generate a suggested prompt based on clusters."""
        strategies = [criteria for cluster in clusters.values() for criteria in cluster]
        return f"Please solve the task step-by-step, focusing on {', '.join(strategies[:2])} and validating each step."
    
    def _save_reasoning_step(self, agent: Agent, step: ReasoningStep):
        """Helper method to save reasoning steps."""
        if agent.session_state is None:
            agent.session_state = {}
        
        agent.session_state.setdefault("reasoning_steps", {}).setdefault(agent.run_id, []).append(
            step.model_dump_json()
        )

if __name__ == "__main__":
    async def main():
        from agno.models.ollama import Ollama
        # Initialize agent and toolkit
        agent = Agent(model=Ollama(id="qwen3:4b"), tools=[CoTEncyclopediaTools()])
        result = await agent.call_tool(
            tool_name="cot_encyclopedia",
            method="cot_encyclopedia_analyze",
            arguments=json.dumps({"query": "Solve a quadratic equation"})
        )
        print(result)
    
    asyncio.run(main())