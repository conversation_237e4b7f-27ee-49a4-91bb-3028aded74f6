from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
from bs4 import BeautifulSoup

class FossilworksTool(Toolkit):
    """
    Fossilworks Tool for searching fossil records and paleontological data from Fossilworks.
    """

    def __init__(self):
        super().__init__(
            name="Fossilworks Tools",
            tools=[
                self.search_fossilworks,
                self.get_fossil_collection,
                self.list_fossil_localities,
                self.get_taxon_occurrences,
                self.search_fossil_strata
            ]
        )

    async def search_fossilworks(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """
        Search Fossilworks for fossil records and paleontological data.

        Parameters:
        - query: Search query using genus/interval, formation/class, or keywords (e.g., 'Triceratops/Maastrichtian', 'Morrison/Dinosauria')
        - limit: Maximum number of results to return (default: 10)

        Returns:
        - JSON with search results including fossil occurrences, taxonomy, age, and Fossilworks URLs
        """
        logger.info(f"Searching Fossilworks for: {query}")

        try:
            # Fossilworks không có API ch<PERSON>h thức, sử dụng web scraping đơn giản với endpoint tìm kiếm occurrences
            search_url = "https://fossilworks.org/cgi-bin/bridge.pl"
            params = {
                "action": "searchCollection",
                "search": query,
                "max_results": limit,
                "format": "json"
            }
            response = requests.get(search_url, params=params)

            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Fossilworks",
                    "message": f"Fossilworks search returned status code {response.status_code}",
                    "query": query
                }

            data = response.json() if response.headers.get("Content-Type", "").startswith("application/json") else None
            results = []
            if data and "collections" in data:
                for item in data["collections"]:
                    occurrence = {
                        "collection_no": item.get("collection_no"),
                        "taxon_name": item.get("taxon_name"),
                        "interval": item.get("interval"),
                        "formation": item.get("formation"),
                        "country": item.get("country"),
                        "state": item.get("state"),
                        "county": item.get("county"),
                        "latitude": item.get("lat"),
                        "longitude": item.get("lng"),
                        "fossilworks_url": f"https://fossilworks.org/cgi-bin/bridge.pl?a=collectionSearch&collection_no={item.get('collection_no')}"
                    }
                    results.append(occurrence)

            return {
                "status": "success",
                "source": "Fossilworks",
                "query": query,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching Fossilworks: {str(e)}")
            return {
                "status": "error",
                "source": "Fossilworks",
                "message": str(e),
                "query": query
            }

    async def get_fossil_collection(self, collection_id: str) -> Dict[str, Any]:
        """
        Get detailed information about a specific fossil collection.

        Parameters:
        - collection_id: The collection number (e.g., '11313' for the Hell Creek Formation)

        Returns:
        - JSON with detailed collection information including location, age, and fossils
        """
        logger.info(f"Getting Fossilworks collection: {collection_id}")

        try:
            url = f"https://fossilworks.org/cgi-bin/bridge.pl"
            params = {
                "collection_no": collection_id,
                "show": "full"
            }
            response = requests.get(url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Fossilworks",
                    "message": f"Failed to fetch collection. Status code: {response.status_code}",
                    "collection_id": collection_id
                }
            
            # Parse HTML response
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract basic information
            info = {}
            for row in soup.select('table.ww_table tr'):
                cells = row.find_all('td')
                if len(cells) == 2:
                    key = cells[0].get_text(strip=True).lower().replace(' ', '_')
                    value = cells[1].get_text(strip=True)
                    info[key] = value
            
            # Extract fossil list
            fossils = []
            for row in soup.select('table.occurenceTable tr')[1:]:  # Skip header
                cells = row.find_all('td')
                if len(cells) > 1:
                    fossil = {
                        'taxon': cells[0].get_text(strip=True),
                        'reference': cells[1].get_text(strip=True)
                    }
                    fossils.append(fossil)
            
            return {
                "status": "success",
                "source": "Fossilworks",
                "collection_id": collection_id,
                "info": info,
                "fossils": fossils,
                "url": f"https://fossilworks.org/cgi-bin/bridge.pl?collection_no={collection_id}"
            }

        except Exception as e:
            log_debug(f"Error getting Fossilworks collection: {str(e)}")
            return {
                "status": "error",
                "source": "Fossilworks",
                "message": str(e),
                "collection_id": collection_id
            }

    async def list_fossil_localities(self, country: Optional[str] = None, 
                                  formation: Optional[str] = None, 
                                  limit: int = 10) -> Dict[str, Any]:
        """
        List fossil-bearing localities with optional filters.

        Parameters:
        - country: Filter by country name (e.g., 'China', 'United States')
        - formation: Filter by geological formation (e.g., 'Hell Creek')
        - limit: Maximum number of results to return (default: 10)

        Returns:
        - JSON with list of localities and their basic information
        """
        logger.info(f"Listing fossil localities - Country: {country}, Formation: {formation}")

        try:
            url = "https://fossilworks.org/cgi-bin/bridge.pl"
            params = {
                "action": "displayPage",
                "page": "localities",
                "show": "both",
                "limit": limit
            }
            
            if country:
                params["country"] = country
            if formation:
                params["formation"] = formation
            
            response = requests.get(url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Fossilworks",
                    "message": f"Failed to fetch localities. Status code: {response.status_code}"
                }
            
            # Parse HTML response
            soup = BeautifulSoup(response.text, 'html.parser')
            localities = []
            
            for row in soup.select('table.localityTable tr')[1:]:  # Skip header
                cells = row.find_all('td')
                if len(cells) > 3:
                    locality = {
                        'name': cells[0].get_text(strip=True),
                        'country': cells[1].get_text(strip=True),
                        'state': cells[2].get_text(strip=True),
                        'formation': cells[3].get_text(strip=True)
                    }
                    localities.append(locality)
            
            return {
                "status": "success",
                "source": "Fossilworks",
                "filters": {"country": country, "formation": formation},
                "localities_count": len(localities),
                "localities": localities
            }

        except Exception as e:
            log_debug(f"Error listing fossil localities: {str(e)}")
            return {
                "status": "error",
                "source": "Fossilworks",
                "message": str(e)
            }

    async def get_taxon_occurrences(self, taxon_name: str, limit: int = 10) -> Dict[str, Any]:
        """
        Get all known occurrences of a specific taxon.

        Parameters:
        - taxon_name: Scientific name of the taxon (e.g., 'Tyrannosaurus rex')
        - limit: Maximum number of occurrences to return (default: 10)

        Returns:
        - JSON with list of occurrences including location and age
        """
        logger.info(f"Getting occurrences for taxon: {taxon_name}")

        try:
            url = "https://fossilworks.org/cgi-bin/bridge.pl"
            params = {
                "a": "displaySearchTaxonOccurrences",
                "taxon_name": taxon_name,
                "max_interval": "all",
                "max_ma": "1000",
                "min_ma": "0",
                "limit": limit
            }
            
            response = requests.get(url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Fossilworks",
                    "message": f"Failed to fetch taxon occurrences. Status code: {response.status_code}",
                    "taxon_name": taxon_name
                }
            
            # Parse HTML response
            soup = BeautifulSoup(response.text, 'html.parser')
            occurrences = []
            
            for row in soup.select('table.occurrenceTable tr')[1:]:  # Skip header
                cells = row.find_all('td')
                if len(cells) > 4:
                    occurrence = {
                        'collection': cells[0].get_text(strip=True),
                        'formation': cells[1].get_text(strip=True),
                        'member': cells[2].get_text(strip=True),
                        'age': cells[3].get_text(strip=True),
                        'location': cells[4].get_text(strip=True)
                    }
                    occurrences.append(occurrence)
            
            return {
                "status": "success",
                "source": "Fossilworks",
                "taxon_name": taxon_name,
                "occurrences_count": len(occurrences),
                "occurrences": occurrences
            }

        except Exception as e:
            log_debug(f"Error getting taxon occurrences: {str(e)}")
            return {
                "status": "error",
                "source": "Fossilworks",
                "message": str(e),
                "taxon_name": taxon_name
            }

    async def search_fossil_strata(self, formation: Optional[str] = None, 
                                member: Optional[str] = None,
                                age: Optional[str] = None,
                                limit: int = 10) -> Dict[str, Any]:
        """
        Search for fossil-bearing strata with various filters.

        Parameters:
        - formation: Name of the geological formation (e.g., 'Hell Creek')
        - member: Specific member of the formation (e.g., 'Upper')
        - age: Geological age (e.g., 'Maastrichtian')
        - limit: Maximum number of results to return (default: 10)

        Returns:
        - JSON with list of matching strata and their fossil content
        """
        logger.info(f"Searching fossil strata - Formation: {formation}, Member: {member}, Age: {age}")

        try:
            url = "https://fossilworks.org/cgi-bin/bridge.pl"
            params = {
                "a": "displayStrata",
                "formation": formation or "",
                "member": member or "",
                "max_interval": age or "",
                "min_interval": age or "",
                "limit": limit
            }
            
            response = requests.get(url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Fossilworks",
                    "message": f"Failed to search strata. Status code: {response.status_code}"
                }
            
            # Parse HTML response
            soup = BeautifulSoup(response.text, 'html.parser')
            strata = []
            
            for row in soup.select('table.strataTable tr')[1:]:  # Skip header
                cells = row.find_all('td')
                if len(cells) > 3:
                    stratum = {
                        'formation': cells[0].get_text(strip=True),
                        'member': cells[1].get_text(strip=True),
                        'age': cells[2].get_text(strip=True),
                        'location': cells[3].get_text(strip=True)
                    }
                    strata.append(stratum)
            
            return {
                "status": "success",
                "source": "Fossilworks",
                "filters": {"formation": formation, "member": member, "age": age},
                "strata_count": len(strata),
                "strata": strata
            }

        except Exception as e:
            log_debug(f"Error searching fossil strata: {str(e)}")
            return {
                "status": "error",
                "source": "Fossilworks",
                "message": str(e)
            }
