from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class BioLibTool(Toolkit):
    """
    BioLib Tool for searching taxonomic and ecological data from BioLib.cz.
    """

    def __init__(self):
        super().__init__(
            name="BioLib Search Tool",
            tools=[self.search_biolib]
        )

    async def search_biolib(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Search BioLib.cz for taxonomic and ecological data.

        Parameters:
        - query: Search query using scientific name, genus.species, or family* (e.g., 'Canis.lupus', 'Felidae*')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with search results including taxonomy, synonyms, ecology, and BioLib URLs
        """
        logger.info(f"Searching BioLib.cz for: {query}")

        try:
            # BioLib API endpoint (unofficial, using web search)
            search_url = "https://www.biolib.cz/en/taxonsearchajax/"
            params = {
                "searcharea": "1",
                "string": query,
                "limit": limit
            }
            response = requests.get(search_url, params=params)

            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "BioLib.cz",
                    "message": f"BioLib search API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for item in data.get("results", []):
                taxon_id = item.get("id")
                scientific_name = item.get("name")
                rank = item.get("rank")
                url = f"https://www.biolib.cz/en/taxon/id{taxon_id}/"

                # Optionally, fetch more details for each taxon
                details = {}
                try:
                    detail_url = f"https://www.biolib.cz/en/taxoninfoajax/"
                    detail_params = {"taxonid": taxon_id}
                    detail_resp = requests.get(detail_url, params=detail_params)
                    if detail_resp.status_code == 200:
                        detail_data = detail_resp.json()
                        details = {
                            "synonyms": detail_data.get("synonyms"),
                            "taxonomy": detail_data.get("taxonomy"),
                            "ecology": detail_data.get("ecology"),
                            "description": detail_data.get("description"),
                        }
                except Exception as detail_error:
                    log_debug(f"Error fetching BioLib details: {str(detail_error)}")

                results.append({
                    "taxon_id": taxon_id,
                    "scientific_name": scientific_name,
                    "rank": rank,
                    "biolib_url": url,
                    **details
                })

            return {
                "status": "success",
                "source": "BioLib.cz",
                "query": query,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching BioLib.cz: {str(e)}")
            return {
                "status": "error",
                "source": "BioLib.cz",
                "message": str(e),
                "query": query
            }
