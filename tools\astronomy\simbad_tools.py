import requests
import json
from typing import Any, Dict, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class SimbadTools(Toolkit):
    def __init__(self, query_objects: bool = True, **kwargs):
        super().__init__(name="simbad_tools", **kwargs)
        self.base_url = "http://simbad.u-strasbg.fr/simbad/sim-id"
        if query_objects:
            self.register(self.query_simbad_object)

    def query_simbad_object(self, object_name: str) -> str:
        """
        Query SIMBAD for a celestial object by name.
        Args:
            object_name (str): Name of the object (e.g., 'Betelgeuse').
        Returns:
            str: JSON string of basic info about the object.
        """
        log_debug(f"Querying SIMBAD for object: {object_name}")
        params = {
            "Ident": object_name,
            "output.format": "VOTable"
        }

        try:
            response = requests.get(self.base_url, params=params)
            response.raise_for_status()
            content = response.text

            # Minimal info: return raw VOTable for now
            return json.dumps({
                "object": object_name,
                "simbad_url": response.url,
                "votable_raw": content
            }, indent=4)
        except Exception as e:
            logger.error(f"Error querying SIMBAD for {object_name}: {e}")
            return json.dumps({"error": str(e)})