from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class OpenAccessGreenTool(Toolkit):
    """
    Open Access Green Tool cho tìm kiếm bài báo, nghiên cứu open access về bền vững, môi trườ<PERSON>, năng lư<PERSON> xanh.
    """

    def __init__(self):
        super().__init__(
            name="Open Access Green Research Search Tool",
            description="Tool cho tìm kiếm bài báo, nghiên cứu open access về bền vững, môi trường, năng lượng xanh.",
            tools=[self.search_open_access_green]
        )

    async def search_open_access_green(self, query: str, year: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm bài báo, nghiên cứu open access về bền vững, môi trường, năng lượ<PERSON> xanh.

        Parameters:
        - query: Từ khóa tìm kiếm (ví dụ: 'renewable energy', 'sustainable agriculture', 'plastic pollution', 'carbon footprint')
        - year: Năm hoặc khoảng năm (ví dụ: '2022', '2018-2022')
        - limit: Số lượng kết quả tối đa (default: 10)

        Returns:
        - JSON với tiêu đề, tác giả, năm, tóm tắt, link bài báo open access
        """
        logger.info(f"Tìm kiếm Open Access Green: query={query}, year={year}, limit={limit}")

        try:
            # Sử dụng CORE API (https://core.ac.uk/) cho open access research
            api_url = "https://core.ac.uk:443/api-v2/search/works"
            # Nếu có key, thêm vào headers, ở đây dùng demo không cần key
            params = {
                "q": query,
                "page": 1,
                "pageSize": limit,
                "sort": "relevance"
            }
            if year:
                params["yearPublished"] = year

            headers = {
                "User-Agent": "Mozilla/5.0 (compatible; OpenAccessGreenBot/1.0)"
            }
            response = requests.get(api_url, params=params, headers=headers, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "CORE Open Access",
                    "message": f"CORE API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for item in data.get("results", []):
                title = item.get("title")
                authors = item.get("authors")
                year_val = item.get("yearPublished")
                abstract = item.get("abstract")
                oa_url = item.get("downloadUrl") or item.get("url")
                journal = item.get("publisher")
                results.append({
                    "title": title,
                    "authors": authors,
                    "year": year_val,
                    "abstract": abstract,
                    "journal": journal,
                    "open_access_url": oa_url
                })

            return {
                "status": "success",
                "source": "CORE Open Access",
                "query": query,
                "year": year,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "renewable energy",
                    "sustainable agriculture",
                    "plastic pollution",
                    "carbon footprint",
                    "green building",
                    "circular economy",
                    "climate adaptation",
                    "zero waste",
                    "biodiversity conservation",
                    "green technology"
                ],
                "official_data_url": "https://core.ac.uk/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Open Access Green: {str(e)}")
            return {
                "status": "error",
                "source": "CORE Open Access",
                "message": str(e),
                "query": query
            }
