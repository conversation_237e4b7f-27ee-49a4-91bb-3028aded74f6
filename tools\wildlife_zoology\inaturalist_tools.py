from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import os

class INaturalistTool(Toolkit):
    """
    iNaturalist Tool for searching wildlife observation data and photos.
    """
    
    def __init__(self):
        super().__init__(
            name="iNaturalist Search Tool",
            tools=[self.search_inaturalist]
        )

    async def search_inaturalist(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """
        Search iNaturalist for wildlife observations and photos.
        
        Parameters:
        - query: Search query using species or region (e.g., "Panthera tigris", "wildlife Vietnam")
        - limit: Maximum number of results to return (default: 10)
        
        Returns:
        - JSON with search results including species information, observations counts, and photos
        """
        logger.info(f"Searching iNaturalist for: {query}")
        
        try:
            # iNaturalist API endpoint for searching taxa
            base_url = "https://api.inaturalist.org/v1"
            
            # Check if query is likely a scientific name (contains space and no common keywords)
            if " " in query and not any(keyword in query.lower() for keyword in ["wildlife", "animals", "fauna", "species"]):
                # This seems like a scientific name search
                params = {
                    "q": query,
                    "per_page": limit
                }
                url = f"{base_url}/taxa"
            else:
                # This seems like a general search
                # Extract potential location if it exists
                location = None
                search_terms = query
                
                location_keywords = ["in", "from", "of", "at"]
                for keyword in location_keywords:
                    if f" {keyword} " in query:
                        parts = query.split(f" {keyword} ", 1)
                        search_terms = parts[0].strip()
                        location = parts[1].strip()
                        break
                
                params = {
                    "q": search_terms,
                    "per_page": limit
                }
                
                if location:
                    params["place_name"] = location
                
                url = f"{base_url}/taxa"
            
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                results = []
                
                for taxon in data.get("results", []):
                    # Get the default photo if available
                    photos = []
                    for photo in taxon.get("taxon_photos", [])[:3]:  # Limit to 3 photos
                        if "photo" in photo:
                            photo_data = photo["photo"]
                            photo_url = photo_data.get("medium_url") or photo_data.get("url")
                            if photo_url:
                                photos.append(photo_url)
                    
                    # Format conservation status
                    conservation_status = None
                    if "conservation_status" in taxon and taxon["conservation_status"]:
                        status = taxon["conservation_status"]
                        conservation_status = {
                            "status": status.get("status"),
                            "authority": status.get("authority"),
                            "description": status.get("description")
                        }
                    
                    # Get Wikipedia extract if available
                    wikipedia_url = None
                    wikipedia_summary = None
                    if "wikipedia_url" in taxon and taxon["wikipedia_url"]:
                        wikipedia_url = taxon["wikipedia_url"]
                        # We could potentially fetch the summary here, but to keep it simple just include the URL
                    
                    taxon_data = {
                        "id": taxon.get("id"),
                        "name": taxon.get("name"),
                        "preferred_common_name": taxon.get("preferred_common_name"),
                        "rank": taxon.get("rank"),
                        "taxonomy": {
                            "kingdom": taxon.get("kingdom_name"),
                            "phylum": taxon.get("phylum_name"),
                            "class": taxon.get("class_name"),
                            "order": taxon.get("order_name"),
                            "family": taxon.get("family_name"),
                            "genus": taxon.get("genus_name"),
                            "species": taxon.get("species_name")
                        },
                        "observations_count": taxon.get("observations_count"),
                        "conservation_status": conservation_status,
                        "wikipedia_url": wikipedia_url,
                        "photos": photos
                    }
                    results.append(taxon_data)
                
                # Now get some observations if available
                observations = []
                if results and "id" in results[0]:
                    try:
                        obs_url = f"{base_url}/observations"
                        obs_params = {
                            "taxon_id": results[0]["id"],
                            "per_page": 5,
                            "order_by": "votes"
                        }
                        
                        obs_response = requests.get(obs_url, params=obs_params)
                        
                        if obs_response.status_code == 200:
                            obs_data = obs_response.json()
                            
                            for obs in obs_data.get("results", []):
                                obs_photos = []
                                for photo in obs.get("photos", []):
                                    photo_url = photo.get("url")
                                    if photo_url:
                                        obs_photos.append(photo_url)
                                
                                observation = {
                                    "id": obs.get("id"),
                                    "observed_on": obs.get("observed_on"),
                                    "place_name": obs.get("place_guess"),
                                    "location": obs.get("location"),
                                    "quality_grade": obs.get("quality_grade"),
                                    "photos": obs_photos
                                }
                                observations.append(observation)
                    except Exception as obs_error:
                        log_debug(f"Error fetching observations: {str(obs_error)}")
                
                return {
                    "status": "success",
                    "source": "iNaturalist",
                    "query": query,
                    "results_count": len(results),
                    "taxa": results,
                    "observations": observations
                }
            else:
                return {
                    "status": "error",
                    "source": "iNaturalist",
                    "message": f"API returned status code {response.status_code}",
                    "query": query
                }
                
        except Exception as e:
            log_debug(f"Error searching iNaturalist: {str(e)}")
            return {
                "status": "error",
                "source": "iNaturalist",
                "message": str(e),
                "query": query
            }