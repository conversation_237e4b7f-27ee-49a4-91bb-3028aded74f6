import json
import logging
from textwrap import dedent
from agno.tools import Toolkit
from typing import Optional
import random

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WriterFbTools(Toolkit):
    """Toolkit for generating high-engagement Facebook status posts."""
    def __init__(self, add_examples: bool = True):
        super().__init__(name="writer_fb")
        self.description = "Toolkit for generating creative, engaging Facebook status posts for personal or business use."
        self.add_examples = add_examples
        if add_examples:
            self.examples = dedent("""
                ## VÍ DỤ STATUS FACEBOOK:
                1. "<PERSON>áng nay thức dậy, thấy nắng vàng len qua cửa sổ. Một ngày mới lại bắt đầu, hãy mỉm cười và làm điều mình yêu thích nhé! ☀️💪"
                2. "Đôi khi chỉ cần một tách cà phê và một bản nhạc hay là đủ để khởi động lại năng lượng cho cả ngày dài. Ai giống mình không? ☕🎶"
                3. "Chúc mọi người cuối tuần vui vẻ, nhiều niềm vui và thật nhiều năng lượng tích cực! 🌻✨"
                4. "Hôm nay bạn đã làm điều gì khiến bản thân tự hào chưa? Hãy chia sẻ cùng mình nhé! #motivation #selflove"
            """)
        else:
            self.examples = ""
        self.register(self.generate_status, name="generate_status")
        self.register(self.generate_status_ideas, name="generate_status_ideas")
        self.register(self.generate_hashtags, name="generate_hashtags")
        self.register(self.analyze_sentiment, name="analyze_sentiment")
        self.register(self.suggest_emojis, name="suggest_emojis")
        self.register(self.suggest_best_time, name="suggest_best_time")
        self.register(self.auto_caption_image, name="auto_caption_image")
        self.register(self.viral_score, name="viral_score")
        # FEW-SHOT EXAMPLES (phong cách GenZ, học thuật, giải trí, giật tít)
        self.few_shot_examples = dedent("""
            ## VÍ DỤ STATUS FACEBOOK ĐA PHONG CÁCH:
            
            ### 1. GenZ/giải trí:
            Đọc ngay kẻo lỡ! 🚨 Chill phết! Du lịch Đà Lạt mà không check-in là phí cả thanh xuân! Ai hóng khônggg? 😋
            
            ### 2. Học thuật:
            Theo nghiên cứu mới nhất về 'giấc ngủ', thiếu ngủ làm giảm 40% hiệu suất làm việc. Bạn đã ngủ đủ chưa? 🧐
            
            ### 3. Giật tít/hook:
            Sự thật phía sau giảm cân khiến ai cũng sốc! 😱 Bạn sẽ bất ngờ với bí quyết này...
            
            ### 4. Call-to-action:
            Nếu bạn cũng quan tâm đến bảo vệ môi trường, hãy comment hoặc thả tim nhé! ❤️👇
            
            ### 5. Câu hỏi tương tác:
            Bạn nghĩ gì về 'làm việc từ xa'? Hãy chia sẻ cảm nhận của bạn nhé! 🤔
            
            ### 6. Story/hài hước:
            Câu chuyện hài hước về lần đầu đi phỏng vấn: Mình run quá, trả lời nhầm tên công ty luôn! Ai từng như mình chưa? 😆
        """)

    def generate_status(self, topic: str, mood: Optional[str] = None, style: Optional[str] = None, format_type: Optional[str] = None, audience: Optional[str] = None, trending: bool = False, include_question: bool = False, include_quote: bool = False, content_type: Optional[str] = None, hook: bool = True, genz: bool = False, academic: bool = False, entertaining: bool = False, story: Optional[str] = None, fact: Optional[str] = None, call_to_action: Optional[str] = None) -> str:
        """Generate a Facebook status with advanced, style-specific logic for GenZ, academic, entertaining, story, fact, CTA, etc."""
        # Hooks/giật tít
        hooks = [
            "Đọc ngay kẻo lỡ! 🚨", "Bạn sẽ bất ngờ với điều này...", f"Sự thật phía sau {topic} khiến ai cũng sốc! 😱", f"Đừng bỏ qua nếu bạn quan tâm {topic}!", "Cảnh báo: Nội dung cực hot! 🔥"
        ]
        # GenZ slang/emoji
        genz_phrases = [
            "Chill phết!", "Xịn xò chưa nè!", "Cày view cùng mình nhé!", "Đỉnh của chóp!", "Ai hóng khônggg?", "Quá là mlem luôn! 😋", "Cảm xúc này ai hiểu hông?", "Tới công chuyện luôn!", "Coi mà không like là dỗi á! 😜"
        ]
        # Academic/entertaining openers
        academic_openers = [
            f"Theo nghiên cứu mới nhất về '{topic}', ...", f"Dữ liệu cho thấy {topic} đang trở thành xu hướng nổi bật.", f"Phân tích sâu về '{topic}':", f"Tài liệu học thuật chỉ ra rằng '{topic}' có ảnh hưởng lớn đến xã hội."
        ]
        entertaining_openers = [
            f"Nếu bạn đang buồn, đọc ngay status này về '{topic}' để cười xỉu! 😂", f"Câu chuyện hài hước về '{topic}' bắt đầu như sau...", f"Ai thích drama về '{topic}' giơ tay! 🙋‍♂️", f"Chuyện thật như đùa về '{topic}':"
        ]
        # Story logic
        if story:
            story_block = f"Chuyện là: {story}"
        else:
            story_block = ""
        # Fact logic
        if fact:
            fact_block = f"Sự thật thú vị: {fact}"
        else:
            fact_block = ""
        # Call-to-action logic
        if call_to_action:
            cta_block = call_to_action
        else:
            cta_block = ""
        # Diverse status structures
        formats = {
            "cau_hoi": lambda t: f"Bạn nghĩ gì về '{t}'? Hãy chia sẻ cảm nhận của bạn nhé! 🤔",
            "ke_chuyen": lambda t: story_block if story_block else f"Chuyện là hôm nay mình gặp một điều thú vị về '{t}'. Ai muốn nghe kể không? 😄",
            "truyen_cam_hung": lambda t: f"'{t}' không chỉ là một chủ đề, mà còn là động lực để mình cố gắng mỗi ngày. Còn bạn thì sao? 💪✨",
            "keo_call_to_action": lambda t: cta_block if cta_block else f"Nếu bạn cũng quan tâm đến '{t}', hãy comment hoặc thả tim nhé! ❤️👇",
            "thong_bao": lambda t: f"Thông báo: Sắp tới mình sẽ chia sẻ nhiều hơn về '{t}'. Đừng bỏ lỡ nhé! 🔔",
            "truyen_thong": lambda t: f"Hôm nay mình muốn chia sẻ về '{t}'.",
            "story": lambda t: story_block if story_block else f"Có ai từng trải qua điều này với '{t}' chưa? Mình vừa trải nghiệm xong, cảm xúc khó tả!",
            "fun_fact": lambda t: fact_block if fact_block else f"Bạn có biết? '{t}' có một sự thật thú vị mà ít ai biết! 🧐"
        }
        import random
        fmt = format_type if format_type in formats else random.choice(list(formats.keys()))
        status = formats[fmt](topic)
        # Add hook/giật tít đầu status
        if hook:
            status = random.choice(hooks) + ' ' + status
        # GenZ style
        if genz:
            status += ' ' + random.choice(genz_phrases)
        # Academic style
        if academic:
            status = random.choice(academic_openers) + '\n' + status
        # Entertaining style
        if entertaining:
            status = random.choice(entertaining_openers) + '\n' + status
        # Mood & style
        if mood:
            status += f" Tâm trạng: {mood}."
        if style == "hài hước":
            status += " Ai đọc mà không cười là phí cả ngày đó nha! 😆"
        elif style == "truyền cảm hứng":
            status += " Hy vọng mọi người sẽ có thêm động lực từ những dòng này! 💪✨"
        elif style == "ngắn_gọn":
            status = status[:80] + ("..." if len(status) > 80 else "")
        elif style == "cảm_xúc":
            status += " Đôi khi chỉ cần một dòng chia sẻ để thấy nhẹ lòng hơn. 💖"
        # Audience targeting
        if audience:
            status = f"[{audience.upper()}] " + status
        # Trending hashtag
        if trending:
            status += " #trending #hot"
        # Add a question
        if include_question:
            status += f" Bạn nghĩ sao về chủ đề này? Comment nhé!"
        # Add a quote
        if include_quote:
            quotes = [
                "'Cuộc sống là hành trình, không phải đích đến.'",
                "'Hãy sống là chính mình, bình thường nhưng không tầm thường.'",
                "'Mỗi ngày là một cơ hội mới để thay đổi.'"
            ]
            status += "\n" + random.choice(quotes)
        # Auto emoji
        if "vui" in (mood or "").lower():
            status += " 😁"
        elif "buồn" in (mood or "").lower():
            status += " 😢"
        return json.dumps({"status": status, "topic": topic, "mood": mood, "style": style, "format_type": fmt, "audience": audience, "trending": trending, "include_question": include_question, "include_quote": include_quote, "hook": hook, "genz": genz, "academic": academic, "entertaining": entertaining, "story": story, "fact": fact, "call_to_action": call_to_action})

    def analyze_sentiment(self, text: str) -> str:
        """Simple sentiment analysis for a Facebook status."""
        # Very basic rule-based sentiment
        positive = ["vui", "hạnh phúc", "tuyệt vời", "yêu", "thành công", "cười", "tích cực"]
        negative = ["buồn", "chán", "thất vọng", "khó khăn", "mệt", "lo lắng", "stress"]
        score = 0
        for w in positive:
            if w in text.lower():
                score += 1
        for w in negative:
            if w in text.lower():
                score -= 1
        sentiment = "tích cực" if score > 0 else ("tiêu cực" if score < 0 else "trung tính")
        return json.dumps({"sentiment": sentiment, "score": score, "text": text})

    def suggest_emojis(self, text: str) -> str:
        """Suggest emojis for a Facebook status based on keywords."""
        emoji_map = {
            "vui": "😁", "buồn": "😢", "yêu": "❤️", "thành công": "🏆", "cà phê": "☕", "nắng": "☀️", "âm nhạc": "🎶", "du lịch": "✈️", "bạn bè": "👫", "gia đình": "👨‍👩‍👧‍👦"
        }
        found = [emoji for word, emoji in emoji_map.items() if word in text.lower()]
        return json.dumps({"suggested_emojis": found, "text": text})

    def generate_status_ideas(self, theme: str, diversity: bool = True) -> str:
        """Generate a list of diverse Facebook status ideas for a given theme."""
        ideas = [
            f"Chia sẻ một kỷ niệm đáng nhớ về {theme}.",
            f"Đặt câu hỏi cho bạn bè về chủ đề {theme}.",
            f"Viết một câu chuyện ngắn hài hước liên quan đến {theme}.",
            f"Chia sẻ cảm xúc hoặc bài học rút ra từ {theme}.",
            f"Đăng ảnh hoặc video về {theme} kèm caption sáng tạo.",
            f"Tạo poll hỏi ý kiến bạn bè về {theme}.",
            f"Gợi ý một thử thách nhỏ liên quan đến {theme} cho mọi người cùng tham gia.",
            f"Chia sẻ một câu nói truyền cảm hứng về {theme}.",
            f"Kể một sự thật thú vị về {theme} mà ít ai biết."
        ]
        if not diversity:
            ideas = ideas[:5]
        return json.dumps({"ideas": ideas, "theme": theme, "diversity": diversity})

    def generate_hashtags(self, topic: str, extra: Optional[list] = None) -> str:
        """Generate a list of relevant hashtags for a Facebook status, with optional extra tags."""
        hashtags = [f"#{topic.replace(' ', '').lower()}", "#cuocsong", "#chiaSe", "#songtichcuc", "#inspiration", "#motivation"]
        if extra:
            hashtags.extend([f"#{tag}" if not tag.startswith("#") else tag for tag in extra])
        # Loại bỏ trùng lặp, giữ thứ tự xuất hiện đầu tiên
        seen = set()
        unique_hashtags = []
        for tag in hashtags:
            if tag not in seen:
                unique_hashtags.append(tag)
                seen.add(tag)
        return json.dumps({"hashtags": unique_hashtags, "topic": topic, "extra": extra})

    def suggest_best_time(self, weekday: Optional[str] = None, audience: Optional[str] = None) -> str:
        """Suggest the best time to post on Facebook based on weekday and audience."""
        # Simple rules, can be replaced by ML/statistics
        mapping = {
            "sinh viên": "19:00-21:00",
            "dân văn phòng": "12:00-13:00 hoặc 20:00-22:00",
            "phụ huynh": "20:00-22:00",
            "giới trẻ": "21:00-23:00",
            "default": "18:00-21:00"
        }
        if audience and audience.lower() in mapping:
            best = mapping[audience.lower()]
        else:
            best = mapping["default"]
        if weekday and weekday.lower() in ["thứ 7", "chủ nhật", "saturday", "sunday"]:
            best = "9:00-11:00 hoặc 19:00-22:00"
        return json.dumps({"best_time": best, "weekday": weekday, "audience": audience})

    def auto_caption_image(self, image_desc: str, mood: Optional[str] = None) -> str:
        """Generate a creative caption for an image description and mood."""
        # Simple template-based
        caption = f"{image_desc.capitalize()} - khoảnh khắc đáng nhớ!"
        if mood:
            caption += f" Cảm xúc: {mood}."
        caption += " #photooftheday #memories"
        return json.dumps({"caption": caption, "image_desc": image_desc, "mood": mood})

    def viral_score(self, text: str) -> str:
        """Estimate a simple 'viral score' for a status (rule-based)."""
        score = 0
        if any(x in text.lower() for x in ["hot", "trend", "viral", "share", "comment"]):
            score += 2
        if "?" in text:
            score += 1
        if any(x in text for x in ["❤️", "😆", "✨", "🔥", "😁"]):
            score += 1
        if len(text) < 100:
            score += 1
        return json.dumps({"viral_score": score, "text": text})

    def generate_academic_status(self, topic: str, fact: Optional[str] = None, question: Optional[str] = None) -> str:
        """Generate a highly academic Facebook status with fact, citation, and question."""
        import random
        openers = [
            f"Theo nghiên cứu mới nhất về '{topic}', ...",
            f"Dữ liệu cho thấy {topic} đang trở thành xu hướng nổi bật.",
            f"Tài liệu học thuật chỉ ra rằng '{topic}' có ảnh hưởng lớn đến xã hội."
        ]
        status = random.choice(openers)
        if fact:
            status += f"\nSự thật khoa học: {fact}"
        if question:
            status += f"\nBạn nghĩ gì về vấn đề này? {question}"
        status += "\n#họcthuật #research #insight"
        return json.dumps({"status": status, "topic": topic, "fact": fact, "question": question})

    def generate_genz_status(self, topic: str, mood: Optional[str] = None) -> str:
        """Generate a GenZ-style Facebook status with slang, emoji, and trendiness."""
        import random
        phrases = [
            f"{topic} mà không thử thì hơi phí nha! 😎",
            f"Ai hóng {topic} khônggg? Quá là mlem luôn! 😋",
            f"Tới công chuyện với {topic} luôn! Đỉnh của chóp!",
            f"{topic} chill phết! Ai đi cùng không? 🥳"
        ]
        status = random.choice(phrases)
        if mood:
            status += f" Tâm trạng: {mood}."
        status += " #genz #trend #fyp"
        return json.dumps({"status": status, "topic": topic, "mood": mood})

    def generate_entertaining_status(self, topic: str, story: Optional[str] = None) -> str:
        """Generate an entertaining/funny Facebook status with story or joke."""
        import random
        openers = [
            f"Nếu bạn đang buồn, đọc ngay status này về '{topic}' để cười xỉu! 😂",
            f"Câu chuyện hài hước về '{topic}' bắt đầu như sau...",
            f"Ai thích drama về '{topic}' giơ tay! 🙋‍♂️"
        ]
        status = random.choice(openers)
        if story:
            status += f"\nChuyện là: {story}"
        else:
            status += f"\nCó ai từng trải qua điều này với '{topic}' chưa? Mình vừa trải nghiệm xong, cảm xúc khó tả!"
        status += "\n#giảitri #hài_hước #storytime"
        return json.dumps({"status": status, "topic": topic, "story": story})

    def generate_fact_status(self, topic: str, fact: str) -> str:
        """Generate a status that highlights an interesting fact about the topic."""
        status = f"Bạn có biết? {fact} 🧐\nChủ đề: {topic}"
        status += "\n#fact #didyouknow #interesting"
        return json.dumps({"status": status, "topic": topic, "fact": fact})

    def generate_cta_status(self, topic: str, call_to_action: str) -> str:
        """Generate a status with a strong call-to-action for engagement."""
        status = f"Nếu bạn cũng quan tâm đến '{topic}', {call_to_action} ❤️👇"
        status += "\n#calltoaction #engage #fb"
        return json.dumps({"status": status, "topic": topic, "call_to_action": call_to_action})

    # News style
    def generate_news_status(self, topic: str, headline: Optional[str] = None, summary: Optional[str] = None, source: Optional[str] = None) -> str:
        """Generate a news-style Facebook status with headline, summary, and source."""
        import datetime
        today = datetime.date.today().strftime('%d/%m/%Y')
        if not headline:
            headline = f"TIN NÓNG: {topic.upper()}"
        status = f"{headline}\n"
        if summary:
            status += f"{summary}\n"
        status += f"Cập nhật ngày: {today}."
        if source:
            status += f"\nNguồn: {source}"
        status += "\n#news #tinnong #capnhat"
        return json.dumps({"status": status, "topic": topic, "headline": headline, "summary": summary, "source": source})

        # Register news style tool
        self.register(self.generate_news_status, name="generate_news_status")
        # Review style
    def generate_review_status(self, product: str, rating: int, pros: Optional[str] = None, cons: Optional[str] = None, recommend: Optional[bool] = None) -> str:
        """Generate a review-style Facebook status for a product/service."""
        stars = '⭐' * max(1, min(rating, 5))
        status = f"Đánh giá: {product}\nĐiểm: {stars} ({rating}/5)"
        if pros:
            status += f"\nƯu điểm: {pros}"
        if cons:
            status += f"\nNhược điểm: {cons}"
        if recommend is not None:
            status += f"\n{'Rất nên thử!' if recommend else 'Cân nhắc trước khi mua nhé!'}"
        status += "\n#review #danhgia #fb"
        return json.dumps({"status": status, "product": product, "rating": rating, "pros": pros, "cons": cons, "recommend": recommend})

    def generate_critique_status(self, subject: str, critique: str, suggestion: Optional[str] = None) -> str:
        """Generate a critique-style Facebook status (phê bình, góp ý)."""
        status = f"Phê bình: {subject}\n{critique}"
        if suggestion:
            status += f"\nGợi ý cải thiện: {suggestion}"
        status += "\n#phebinh #critique #feedback"
        return json.dumps({"status": status, "subject": subject, "critique": critique, "suggestion": suggestion})

    def generate_advertisement_status(self, product: str, slogan: Optional[str] = None, offer: Optional[str] = None, cta: Optional[str] = None) -> str:
        """Generate an advertisement-style Facebook status."""
        status = f"🔥 {product.upper()} 🔥"
        if slogan:
            status += f"\n{slogan}"
        if offer:
            status += f"\nƯu đãi: {offer}"
        if cta:
            status += f"\n{cta}"
        else:
            status += "\nInbox ngay để nhận ưu đãi!"
        status += "\n#ad #quangcao #promo"
        return json.dumps({"status": status, "product": product, "slogan": slogan, "offer": offer, "cta": cta})

        # Register new styles
        self.register(self.generate_review_status, name="generate_review_status")
        self.register(self.generate_critique_status, name="generate_critique_status")
        self.register(self.generate_advertisement_status, name="generate_advertisement_status")
        # ...existing code...
if __name__ == "__main__":
    toolkit = WriterFbTools()
    print(toolkit.generate_status("du lịch Đà Lạt", mood="phấn khích", style="hài hước"))
    print(toolkit.generate_status_ideas("du lịch"))
    print(toolkit.generate_hashtags("du lịch Đà Lạt"))
