# -*- coding: utf-8 -*-
"""
AI Robotics Tools Package

This package provides comprehensive tools for AI and Robotics research, including:
- Papers With Code integration for research papers and implementations
- Hugging Face Hub for models and datasets
- arXiv for academic papers
- OpenML for machine learning datasets and tasks
- Wikipedia AI for concepts and algorithms
- Search toolkit for generating optimized keywords

Each tool supports both regular search and "get top new" functionality for finding
trending and recent content.
"""

# Import individual tools
from .papers_with_code_tools import PapersWithCodeTools
from .huggingface_tools import HuggingFaceTools
from .arxiv_tools import ArxivTools
from .openml_tools import OpenMLTools
from .wikipedia_ai_tools import WikipediaAITools
from .ai_robotics_search_toolkit import AIRoboticsSearchToolkit

# Import legacy tool for backward compatibility
from .ai_robotics_tools import AIRoboticsToolkit as AIRoboticsTools

__all__ = [
    "PapersWithCodeTools",
    "HuggingFaceTools",
    "ArxivTools",
    "OpenMLTools",
    "WikipediaAITools",
    "AIRoboticsSearchToolkit",
    "AIRoboticsTools"  # Legacy
]

__version__ = "2.0.0"
__author__ = "AI Robotics Tools Team"
__description__ = "Comprehensive AI and Robotics research tools with trending/recent content support"
