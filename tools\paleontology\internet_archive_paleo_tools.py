from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
from bs4 import BeautifulSoup
import json
from datetime import datetime

class InternetArchivePaleoTool(Toolkit):
    """
    Bộ công cụ để tìm kiếm và truy xuất tài liệu cổ sinh vật học từ Internet Archive.
    """

    def __init__(self):
        super().__init__(
            name="Internet Archive Paleontology Tools",
            tools=[
                self.search_internet_archive_paleo,
                self.get_item_metadata,
                self.download_item,
                self.search_by_year_range,
                self.get_popular_paleo_books
            ]
        )

    async def search_internet_archive_paleo(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Search Internet Archive for paleontology books, papers, and expedition reports.

        Parameters:
        - query: Search string (e.g., 'osborn.1916.dinosaur', 'andrews.mongolia.1928')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with search results including title, year, authors, description, and archive URLs
        """
        logger.info(f"Searching Internet Archive for paleontology: {query}")

        try:
            search_url = "https://archive.org/advancedsearch.php"
            params = {
                "q": f"({query}) AND (subject:paleontology OR subject:fossil OR subject:dinosaurs OR subject:expedition)",
                "fl[]": "identifier,title,creator,year,description,subject",
                "rows": limit,
                "output": "json"
            }
            response = requests.get(search_url, params=params)

            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Internet Archive",
                    "message": f"Archive search API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            docs = data.get("response", {}).get("docs", [])
            results = []
            for item in docs:
                identifier = item.get("identifier")
                archive_url = f"https://archive.org/details/{identifier}" if identifier else None
                results.append({
                    "identifier": identifier,
                    "title": item.get("title"),
                    "authors": item.get("creator"),
                    "year": item.get("year"),
                    "description": item.get("description"),
                    "subjects": item.get("subject"),
                    "archive_url": archive_url
                })

            return {
                "status": "success",
                "source": "Internet Archive",
                "query": query,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching Internet Archive Paleo: {str(e)}")
            return {
                "status": "error",
                "source": "Internet Archive",
                "message": str(e),
                "query": query
            }

    async def get_item_metadata(self, identifier: str) -> Dict[str, Any]:
        """
        Lấy siêu dữ liệu chi tiết của một mục trong Internet Archive.

        Parameters:
        - identifier: Định danh duy nhất của mục trong Internet Archive

        Returns:
        - JSON chứa siêu dữ liệu chi tiết của mục
        """
        logger.info(f"Lấy siêu dữ liệu cho mục: {identifier}")

        try:
            metadata_url = f"https://archive.org/metadata/{identifier}"
            response = requests.get(metadata_url)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Internet Archive",
                    "message": f"Không thể lấy siêu dữ liệu. Mã lỗi: {response.status_code}",
                    "identifier": identifier
                }
            
            data = response.json()
            
            if not data.get("metadata"):
                return {
                    "status": "error",
                    "source": "Internet Archive",
                    "message": "Không tìm thấy siêu dữ liệu cho mục này",
                    "identifier": identifier
                }
            
            # Trích xuất thông tin cơ bản
            metadata = data.get("metadata", {})
            files = data.get("files", [])
            
            # Lọc các file có thể đọc trực tuyến
            readable_files = [f for f in files if f.get("format") in ["Text PDF", "JPEG", "Single Page Processed JP2 ZIP"]
                            and not f.get("name", "").endswith("_djvu.txt")]
            
            # Sắp xếp theo kích thước file (lớn nhất trước)
            readable_files.sort(key=lambda x: int(x.get("size", 0)), reverse=True)
            
            # Giới hạn số lượng file trả về
            readable_files = readable_files[:5]
            
            # Tạo URL xem trực tuyến
            for file in readable_files:
                file["read_url"] = f"https://archive.org/download/{identifier}/{file['name']}"
            
            return {
                "status": "success",
                "source": "Internet Archive",
                "identifier": identifier,
                "title": metadata.get("title", "Không có tiêu đề"),
                "creator": metadata.get("creator"),
                "publisher": metadata.get("publisher"),
                "date": metadata.get("date"),
                "description": metadata.get("description"),
                "subjects": metadata.get("subject", []),
                "language": metadata.get("language"),
                "readable_files": readable_files,
                "url": f"https://archive.org/details/{identifier}",
                "metadata_url": metadata_url
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy siêu dữ liệu: {str(e)}")
            return {
                "status": "error",
                "source": "Internet Archive",
                "message": str(e),
                "identifier": identifier
            }

    async def download_item(self, identifier: str, filename: Optional[str] = None) -> Dict[str, Any]:
        """
        Tải xuống một tệp từ Internet Archive.

        Parameters:
        - identifier: Định danh của mục trong Internet Archive
        - filename: Tên tệp cụ thể để tải xuống (tùy chọn)

        Returns:
        - JSON chứa thông tin tải xuống
        """
        logger.info(f"Tải xuống mục: {identifier}, Tệp: {filename or 'Tệp chính'}")

        try:
            # Lấy thông tin chi tiết về mục
            metadata = await self.get_item_metadata(identifier)
            
            if metadata.get("status") != "success":
                return metadata
            
            # Nếu không chỉ định tên tệp, sử dụng tệp đầu tiên có thể đọc được
            if not filename and metadata.get("readable_files"):
                file_info = metadata["readable_files"][0]
                filename = file_info["name"]
            elif not filename:
                return {
                    "status": "error",
                    "source": "Internet Archive",
                    "message": "Không tìm thấy tệp nào để tải xuống",
                    "identifier": identifier
                }
            
            # Tạo URL tải xuống trực tiếp
            download_url = f"https://archive.org/download/{identifier}/{filename}"
            
            return {
                "status": "success",
                "source": "Internet Archive",
                "identifier": identifier,
                "filename": filename,
                "download_url": download_url,
                "direct_download": True,
                "note": "Sử dụng URL tải xuống để tải tệp trực tiếp"
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi tạo URL tải xuống: {str(e)}")
            return {
                "status": "error",
                "source": "Internet Archive",
                "message": str(e),
                "identifier": identifier
            }

    async def search_by_year_range(self, start_year: int, end_year: int, 
                                query: str = "paleontology", 
                                limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm tài liệu trong một khoảng thời gian cụ thể.

        Parameters:
        - start_year: Năm bắt đầu
        - end_year: Năm kết thúc
        - query: Từ khóa tìm kiếm (mặc định: 'paleontology')
        - limit: Số kết quả tối đa (mặc định: 5)

        Returns:
        - JSON chứa danh sách các tài liệu phù hợp
        """
        logger.info(f"Tìm kiếm tài liệu từ {start_year} đến {end_year}: {query}")

        try:
            search_url = "https://archive.org/advancedsearch.php"
            params = {
                "q": f"{query} AND date:[{start_year} TO {end_year}]",
                "fl[]": ["identifier", "title", "creator", "year", "description", "subject"],
                "rows": limit,
                "page": 1,
                "output": "json",
                "sort[]": ["downloads desc"],
                "callback": ""
            }
            
            response = requests.get(search_url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Internet Archive",
                    "message": f"Lỗi API: {response.status_code}",
                    "query": query,
                    "start_year": start_year,
                    "end_year": end_year
                }
            
            data = response.json()
            results = []
            
            for doc in data.get("response", {}).get("docs", []):
                results.append({
                    "identifier": doc.get("identifier"),
                    "title": doc.get("title"),
                    "creator": doc.get("creator"),
                    "year": doc.get("year"),
                    "description": doc.get("description"),
                    "subjects": doc.get("subject", []),
                    "url": f"https://archive.org/details/{doc.get('identifier')}"
                })
            
            return {
                "status": "success",
                "source": "Internet Archive",
                "query": query,
                "start_year": start_year,
                "end_year": end_year,
                "results_count": len(results),
                "results": results
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm theo khoảng năm: {str(e)}")
            return {
                "status": "error",
                "source": "Internet Archive",
                "message": str(e),
                "query": query,
                "start_year": start_year,
                "end_year": end_year
            }

    async def get_popular_paleo_books(self, limit: int = 5) -> Dict[str, Any]:
        """
        Lấy danh sách các sách cổ sinh vật học phổ biến nhất trên Internet Archive.

        Parameters:
        - limit: Số lượng kết quả tối đa (mặc định: 5)

        Returns:
        - JSON chứa danh sách các sách phổ biến
        """
        logger.info(f"Lấy danh sách {limit} sách cổ sinh vật học phổ biến")

        try:
            search_url = "https://archive.org/advancedsearch.php"
            params = {
                "q": "paleontology AND mediatype:(texts) AND format:(Book)",
                "fl[]": ["identifier", "title", "creator", "year", "downloads", "avg_rating"],
                "rows": limit,
                "page": 1,
                "output": "json",
                "sort[]": ["downloads desc"],
                "callback": ""
            }
            
            response = requests.get(search_url, params=params)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Internet Archive",
                    "message": f"Lỗi API: {response.status_code}"
                }
            
            data = response.json()
            books = []
            
            for doc in data.get("response", {}).get("docs", []):
                books.append({
                    "identifier": doc.get("identifier"),
                    "title": doc.get("title"),
                    "creator": doc.get("creator"),
                    "year": doc.get("year"),
                    "downloads": doc.get("downloads"),
                    "avg_rating": doc.get("avg_rating"),
                    "url": f"https://archive.org/details/{doc.get('identifier')}"
                })
            
            return {
                "status": "success",
                "source": "Internet Archive",
                "books_count": len(books),
                "books": books
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy sách phổ biến: {str(e)}")
            return {
                "status": "error",
                "source": "Internet Archive",
                "message": str(e)
            }
