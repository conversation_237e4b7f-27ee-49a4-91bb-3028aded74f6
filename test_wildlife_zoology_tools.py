#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script cho Wildlife Zoology Tools
"""

import sys
import os
import json
import random
import asyncio
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gbif_tools():
    """Test GBIF Tools"""
    print("🌍 Testing GBIF Tools...")
    try:
        from tools.wildlife_zoology.gbif_tools import GBIFTool
        
        gbif_tool = GBIFTool()
        
        print("  - Testing GBIF instantiation...")
        print("    ✅ GBIF Tools instantiated")
        
        # Test get_top_new
        print("  - Testing GBIF get_top_new...")
        assert hasattr(gbif_tool, 'get_top_new')
        print("    ✅ GBIF get_top_new method exists")
        
        return True
        
    except Exception as e:
        print(f"    ❌ GBIF Tools failed: {str(e)}")
        return False

def test_wildlife_zoology_search_toolkit():
    """Test Wildlife Zoology Search Toolkit"""
    print("🔍 Testing Wildlife Zoology Search Toolkit...")
    try:
        from tools.wildlife_zoology.wildlife_zoology_search_toolkit import WildlifeZoologySearchToolkit
        
        toolkit = WildlifeZoologySearchToolkit()
        
        print("  - Testing species data search...")
        result = toolkit.search_species_data("Panthera leo", "mammals", "africa", "taxonomy")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Species data search works")
        
        print("  - Testing conservation status search...")
        result = toolkit.search_conservation_status("endangered", "protection", "global", "mammals")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Conservation status search works")
        
        print("  - Testing habitat information search...")
        result = toolkit.search_habitat_information("forest", "tropical", "tropical", "deforestation")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Habitat information search works")
        
        print("  - Testing comprehensive wildlife search...")
        result = toolkit.comprehensive_wildlife_search("tiger", "all", "endangered", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive wildlife search works")
        
        print("  - Testing biodiversity trends search...")
        result = toolkit.search_biodiversity_trends("population", "decadal", "global", "species_count")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Biodiversity trends search works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Wildlife Zoology Search Toolkit failed: {str(e)}")
        return False

def test_other_wildlife_zoology_tools():
    """Test other wildlife zoology tools"""
    print("🦋 Testing Other Wildlife Zoology Tools...")
    try:
        # Test iNaturalist Tools
        from tools.wildlife_zoology.inaturalist_tools import INaturalistTool
        
        inaturalist_tool = INaturalistTool()
        
        print("  - Testing iNaturalist Tools...")
        print("    ✅ iNaturalist Tools instantiated")
        
        # Test EOL Tools
        from tools.wildlife_zoology.eol_tools import EOLTool
        
        eol_tool = EOLTool()
        
        print("  - Testing EOL Tools...")
        print("    ✅ EOL Tools instantiated")
        
        # Test Wikidata Species Tools
        from tools.wildlife_zoology.wikidata_species_tools import WikidataSpeciesTool
        
        wikidata_tool = WikidataSpeciesTool()
        
        print("  - Testing Wikidata Species Tools...")
        print("    ✅ Wikidata Species Tools instantiated")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Other Wildlife Zoology Tools failed: {str(e)}")
        return False

def test_random_wildlife_zoology_functionality():
    """Test random wildlife zoology functionality"""
    print("\n🎲 Testing Random Wildlife Zoology Functionality...")
    
    try:
        # Random species data test
        from tools.wildlife_zoology.wildlife_zoology_search_toolkit import WildlifeZoologySearchToolkit
        toolkit = WildlifeZoologySearchToolkit()
        
        taxonomic_groups = ["mammals", "birds", "reptiles", "amphibians", "fish"]
        taxonomic_group = random.choice(taxonomic_groups)
        result = toolkit.search_species_data("", taxonomic_group, "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random species data {taxonomic_group} search test passed")
        
        # Random conservation status test
        threat_levels = ["critically_endangered", "endangered", "vulnerable", "near_threatened"]
        threat_level = random.choice(threat_levels)
        result = toolkit.search_conservation_status(threat_level, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random conservation {threat_level} test passed")
        
        # Random habitat test
        habitat_types = ["forest", "grassland", "wetland", "marine", "desert"]
        habitat_type = random.choice(habitat_types)
        result = toolkit.search_habitat_information(habitat_type, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random habitat {habitat_type} test passed")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Random Wildlife Zoology Functionality failed: {str(e)}")
        return False

def test_wildlife_zoology_search_variations():
    """Test various wildlife zoology search variations"""
    print("\n🌿 Testing Wildlife Zoology Search Variations...")
    
    try:
        from tools.wildlife_zoology.wildlife_zoology_search_toolkit import WildlifeZoologySearchToolkit
        toolkit = WildlifeZoologySearchToolkit()
        
        # Test comprehensive search with different parameters
        print("  - Testing comprehensive search variations...")
        result = toolkit.comprehensive_wildlife_search("elephant", "conservation", "terrestrial", "advanced")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive search variations work")
        
        # Test biodiversity trends with different parameters
        print("  - Testing biodiversity trends variations...")
        result = toolkit.search_biodiversity_trends("", "annual", "regional", "abundance")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Biodiversity trends variations work")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Wildlife Zoology Search Variations failed: {str(e)}")
        return False

async def test_async_gbif():
    """Test async GBIF functionality"""
    print("\n⚡ Testing Async GBIF...")
    
    try:
        from tools.wildlife_zoology.gbif_tools import GBIFTool
        
        gbif_tool = GBIFTool()
        
        # Test async get_top_new
        print("  - Testing async get_top_new...")
        result = await gbif_tool.get_top_new("species", 5, "month", "mammals")
        assert result["status"] == "success"
        print("    ✅ Async get_top_new works")
        
        # Test async search_gbif
        print("  - Testing async search_gbif...")
        result = await gbif_tool.search_gbif("Panthera leo", 5)
        assert "status" in result
        print("    ✅ Async search_gbif works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Async GBIF failed: {str(e)}")
        return False

def test_gbif_get_top_new_content_types():
    """Test GBIF get_top_new with different content types"""
    print("\n🔬 Testing GBIF get_top_new Content Types...")
    
    try:
        from tools.wildlife_zoology.gbif_tools import GBIFTool
        
        gbif_tool = GBIFTool()
        
        # Test get_top_new with different content types
        content_types = ["species", "occurrences", "datasets", "research", "conservation"]
        
        async def test_content_type(content_type):
            print(f"  - Testing get_top_new for {content_type}...")
            result = await gbif_tool.get_top_new(content_type, 3, "week", "birds")
            assert result["status"] == "success"
            print(f"    ✅ get_top_new for {content_type} works")
        
        # Run async tests
        async def run_all_tests():
            for content_type in content_types:
                await test_content_type(content_type)
        
        asyncio.run(run_all_tests())
        
        return True
        
    except Exception as e:
        print(f"    ❌ GBIF get_top_new Content Types failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 WILDLIFE ZOOLOGY TOOLS TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing Wildlife Zoology channel tools...")
    print()
    
    test_results = []
    
    # Test all wildlife zoology tools
    test_functions = [
        ("GBIF Tools", test_gbif_tools),
        ("Wildlife Zoology Search Toolkit", test_wildlife_zoology_search_toolkit),
        ("Other Wildlife Zoology Tools", test_other_wildlife_zoology_tools),
        ("Random Wildlife Zoology Functionality", test_random_wildlife_zoology_functionality),
        ("Wildlife Zoology Search Variations", test_wildlife_zoology_search_variations),
        ("GBIF get_top_new Content Types", test_gbif_get_top_new_content_types)
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()
    
    # Test async functionality
    print(f"\n{'='*20} Async Tests {'='*20}")
    try:
        async_result = asyncio.run(test_async_gbif())
        test_results.append(("Async GBIF", async_result))
    except Exception as e:
        print(f"❌ Async tests failed: {str(e)}")
        test_results.append(("Async GBIF", False))
    
    # Summary
    print("\n" + "="*60)
    print("📋 WILDLIFE ZOOLOGY TOOLS TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All wildlife zoology tools are working correctly!")
        print("✨ Wildlife Zoology channel fully functional!")
        print("🦋 Ready for biodiversity research and conservation analysis!")
    elif passed >= total * 0.8:
        print("✅ Excellent performance - most functionality working!")
    elif passed >= total * 0.6:
        print("✅ Good performance - majority working!")
    else:
        print("⚠️  Some issues detected. Please check the error messages above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
