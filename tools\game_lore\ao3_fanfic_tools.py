from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime

class AO3FanficTools(Toolkit):
    """
    AO3 Fanfic Tools cho tìm kiếm, phân tích và theo dõi fanfiction từ Archive of Our Own (AO3).
    """

    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(
            name="ao3_fanfic_tools",
            **kwargs
        )

        self.base_url = "https://archiveofourown.org"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (compatible; AO3FanficBot/1.0)"
        }

        if enable_search:
            self.register(self.search_ao3_fanfic)
            self.register(self.get_top_new_fanfics)
            self.register(self.analyze_fandom_trends)
            self.register(self.get_author_works)
            self.register(self.search_by_tags)

    def search_ao3_fanfic(self, query: str, tag: Optional[str] = None, limit: int = 5) -> str:
        """
        Tìm kiếm AO3 cho fanfiction theo từ khóa, tag, pairing hoặc fandom.

        Args:
        - query: Tên nhân vật, pairing, fandom, hoặc từ khóa (ví dụ: 'Dragon Age Solas', 'Mass Effect Shepard', 'Harry Potter', 'Star Wars')
        - tag: Tag hoặc thể loại cụ thể (ví dụ: 'romance', 'hurt/comfort', 'AU')
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON string với tiêu đề, tác giả, fandom, tóm tắt, tags, link AO3
        """
        log_debug(f"Tìm kiếm AO3: query={query}, tag={tag}")

        try:
            search_url = f"{self.base_url}/works/search"
            params = {
                "work_search[query]": query,
                "work_search[sort_column]": "revised_at",
                "work_search[language_id]": "en",
                "commit": "Search"
            }
            if tag:
                params["work_search[other_tag_names]"] = tag

            response = requests.get(search_url, params=params, headers=self.headers, timeout=15)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "source": "AO3",
                    "message": f"AO3 search returned status code {response.status_code}",
                    "query": query
                }, indent=2)

            works = self._parse_works_from_html(response.text, limit)

            return json.dumps({
                "status": "success",
                "source": "AO3",
                "query": query,
                "tag": tag,
                "results_count": len(works),
                "results": works,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm AO3: {str(e)}")
            return json.dumps({
                "status": "error",
                "source": "AO3",
                "message": str(e),
                "query": query
            }, indent=2)

    def get_top_new_fanfics(self, fandom: str = "", limit: int = 10,
                           time_period: str = "week") -> str:
        """
        Lấy fanfiction mới nhất và phổ biến nhất từ AO3.

        Args:
        - fandom: Tên fandom cụ thể (để trống để lấy tất cả)
        - limit: Số lượng kết quả (default: 10)
        - time_period: Khoảng thời gian ('day', 'week', 'month')

        Returns:
        - JSON string với danh sách fanfic mới và hot nhất
        """
        log_debug(f"Lấy top new fanfics: fandom={fandom}, period={time_period}")

        try:
            # Tìm kiếm theo thời gian
            search_url = f"{self.base_url}/works/search"
            params = {
                "work_search[sort_column]": "revised_at",
                "work_search[language_id]": "en",
                "commit": "Search"
            }

            if fandom:
                params["work_search[fandom_names]"] = fandom

            response = requests.get(search_url, params=params, headers=self.headers, timeout=15)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to fetch top new fanfics: {response.status_code}"
                }, indent=2)

            works = self._parse_works_from_html(response.text, limit)

            # Thêm thông tin trending
            for work in works:
                work["trending_score"] = self._calculate_trending_score(work)

            return json.dumps({
                "status": "success",
                "source": "AO3",
                "fandom": fandom or "All Fandoms",
                "time_period": time_period,
                "results_count": len(works),
                "top_new_fanfics": works,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy top new fanfics: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def analyze_fandom_trends(self, fandom: str, analysis_depth: str = "basic") -> str:
        """
        Phân tích xu hướng và thống kê của một fandom trên AO3.

        Args:
        - fandom: Tên fandom để phân tích
        - analysis_depth: Mức độ phân tích ('basic', 'detailed')

        Returns:
        - JSON string với thống kê fandom, tags phổ biến, xu hướng
        """
        log_debug(f"Phân tích fandom trends: {fandom}")

        try:
            # Lấy dữ liệu fandom
            search_url = f"{self.base_url}/works/search"
            params = {
                "work_search[fandom_names]": fandom,
                "work_search[sort_column]": "kudos_count",
                "work_search[language_id]": "en",
                "commit": "Search"
            }

            response = requests.get(search_url, params=params, headers=self.headers, timeout=15)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to analyze fandom: {response.status_code}"
                }, indent=2)

            works = self._parse_works_from_html(response.text, 20)

            # Phân tích tags
            tag_analysis = self._analyze_tags(works)

            # Phân tích tác giả
            author_analysis = self._analyze_authors(works)

            # Xu hướng thời gian
            time_trends = self._analyze_time_trends(works) if analysis_depth == "detailed" else {}

            return json.dumps({
                "status": "success",
                "source": "AO3",
                "fandom": fandom,
                "analysis_depth": analysis_depth,
                "total_works_analyzed": len(works),
                "tag_analysis": tag_analysis,
                "author_analysis": author_analysis,
                "time_trends": time_trends,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi phân tích fandom trends: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def get_author_works(self, author_name: str, limit: int = 10) -> str:
        """
        Lấy danh sách tác phẩm của một tác giả trên AO3.

        Args:
        - author_name: Tên tác giả
        - limit: Số lượng tác phẩm tối đa

        Returns:
        - JSON string với danh sách tác phẩm của tác giả
        """
        log_debug(f"Lấy tác phẩm của tác giả: {author_name}")

        try:
            search_url = f"{self.base_url}/works/search"
            params = {
                "work_search[creators]": author_name,
                "work_search[sort_column]": "revised_at",
                "work_search[language_id]": "en",
                "commit": "Search"
            }

            response = requests.get(search_url, params=params, headers=self.headers, timeout=15)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to get author works: {response.status_code}"
                }, indent=2)

            works = self._parse_works_from_html(response.text, limit)

            # Thống kê tác giả
            author_stats = self._calculate_author_stats(works, author_name)

            return json.dumps({
                "status": "success",
                "source": "AO3",
                "author": author_name,
                "works_count": len(works),
                "author_statistics": author_stats,
                "works": works,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy tác phẩm tác giả: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_by_tags(self, tags: List[str], exclude_tags: List[str] = None,
                      limit: int = 10) -> str:
        """
        Tìm kiếm fanfiction theo tags cụ thể.

        Args:
        - tags: Danh sách tags cần tìm
        - exclude_tags: Danh sách tags cần loại trừ
        - limit: Số lượng kết quả

        Returns:
        - JSON string với kết quả tìm kiếm theo tags
        """
        log_debug(f"Tìm kiếm theo tags: {tags}")

        try:
            search_url = f"{self.base_url}/works/search"
            params = {
                "work_search[other_tag_names]": ", ".join(tags),
                "work_search[sort_column]": "kudos_count",
                "work_search[language_id]": "en",
                "commit": "Search"
            }

            if exclude_tags:
                params["work_search[excluded_tag_names]"] = ", ".join(exclude_tags)

            response = requests.get(search_url, params=params, headers=self.headers, timeout=15)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to search by tags: {response.status_code}"
                }, indent=2)

            works = self._parse_works_from_html(response.text, limit)

            # Phân tích tag matching
            tag_matching = self._analyze_tag_matching(works, tags)

            return json.dumps({
                "status": "success",
                "source": "AO3",
                "search_tags": tags,
                "exclude_tags": exclude_tags or [],
                "results_count": len(works),
                "tag_matching_analysis": tag_matching,
                "results": works,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm theo tags: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    # Helper methods
    def _parse_works_from_html(self, html_content: str, limit: int) -> List[dict]:
        """Parse works from AO3 HTML response."""
        import re
        works = []

        for match in re.finditer(r'<li class="work blurb group" id="work_(\d+)">(.+?)</li>', html_content, re.DOTALL):
            if len(works) >= limit:
                break
            work_id = match.group(1)
            work_html = match.group(2)

            # Tiêu đề
            title_match = re.search(r'<h4 class="heading">\s*<a href="([^"]+)"[^>]*>([^<]+)</a>', work_html)
            title = title_match.group(2).strip() if title_match else None
            work_url = self.base_url + title_match.group(1) if title_match else None

            # Tác giả
            author_match = re.search(r'rel="author">([^<]+)</a>', work_html)
            author = author_match.group(1).strip() if author_match else None

            # Fandom
            fandom_match = re.search(r'<h5 class="fandoms heading">(.+?)</h5>', work_html)
            fandom = re.sub(r'<.*?>', '', fandom_match.group(1)).strip() if fandom_match else None

            # Tóm tắt
            summary_match = re.search(r'<blockquote class="userstuff summary">(.+?)</blockquote>', work_html, re.DOTALL)
            summary = re.sub(r'<.*?>', '', summary_match.group(1)).strip() if summary_match else None

            # Tags
            tags = []
            for tag_match in re.finditer(r'<li class="[^"]*tags[^"]*"><a[^>]+>([^<]+)</a>', work_html):
                tags.append(tag_match.group(1).strip())

            # Stats
            stats = self._extract_work_stats(work_html)

            works.append({
                "work_id": work_id,
                "title": title,
                "author": author,
                "fandom": fandom,
                "summary": summary,
                "tags": tags,
                "ao3_url": work_url,
                "stats": stats
            })

        return works

    def _extract_work_stats(self, work_html: str) -> dict:
        """Extract statistics from work HTML."""
        import re
        stats = {}

        # Kudos
        kudos_match = re.search(r'<dd class="kudos">\s*<a[^>]*>(\d+)</a>', work_html)
        stats["kudos"] = int(kudos_match.group(1)) if kudos_match else 0

        # Comments
        comments_match = re.search(r'<dd class="comments">\s*<a[^>]*>(\d+)</a>', work_html)
        stats["comments"] = int(comments_match.group(1)) if comments_match else 0

        # Bookmarks
        bookmarks_match = re.search(r'<dd class="bookmarks">\s*<a[^>]*>(\d+)</a>', work_html)
        stats["bookmarks"] = int(bookmarks_match.group(1)) if bookmarks_match else 0

        # Hits
        hits_match = re.search(r'<dd class="hits">(\d+)</dd>', work_html)
        stats["hits"] = int(hits_match.group(1)) if hits_match else 0

        return stats

    def _calculate_trending_score(self, work: dict) -> float:
        """Calculate trending score based on engagement metrics."""
        stats = work.get("stats", {})
        kudos = stats.get("kudos", 0)
        comments = stats.get("comments", 0)
        bookmarks = stats.get("bookmarks", 0)
        hits = stats.get("hits", 1)

        # Weighted scoring
        engagement_rate = (kudos * 2 + comments * 3 + bookmarks * 1.5) / max(hits, 1)
        return round(engagement_rate * 100, 2)

    def _analyze_tags(self, works: List[dict]) -> dict:
        """Analyze tag frequency and trends."""
        tag_counts = {}
        total_tags = 0

        for work in works:
            for tag in work.get("tags", []):
                tag_counts[tag] = tag_counts.get(tag, 0) + 1
                total_tags += 1

        # Top tags
        top_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:10]

        return {
            "total_unique_tags": len(tag_counts),
            "total_tag_instances": total_tags,
            "top_tags": [{"tag": tag, "count": count, "percentage": round(count/total_tags*100, 1)}
                        for tag, count in top_tags],
            "tag_diversity": round(len(tag_counts) / max(total_tags, 1), 3)
        }

    def _analyze_authors(self, works: List[dict]) -> dict:
        """Analyze author statistics."""
        author_counts = {}
        total_works = len(works)

        for work in works:
            author = work.get("author")
            if author:
                author_counts[author] = author_counts.get(author, 0) + 1

        # Top authors
        top_authors = sorted(author_counts.items(), key=lambda x: x[1], reverse=True)[:5]

        return {
            "total_unique_authors": len(author_counts),
            "total_works": total_works,
            "top_authors": [{"author": author, "works": count, "percentage": round(count/total_works*100, 1)}
                           for author, count in top_authors],
            "author_diversity": round(len(author_counts) / max(total_works, 1), 3)
        }

    def _analyze_time_trends(self, works: List[dict]) -> dict:
        """Analyze temporal trends (placeholder - would need publication dates)."""
        return {
            "trend_analysis": "Detailed time analysis requires publication date extraction",
            "sample_size": len(works),
            "note": "This would analyze publication patterns, update frequency, etc."
        }

    def _calculate_author_stats(self, works: List[dict], author_name: str) -> dict:
        """Calculate statistics for a specific author."""
        total_kudos = sum(work.get("stats", {}).get("kudos", 0) for work in works)
        total_comments = sum(work.get("stats", {}).get("comments", 0) for work in works)
        total_bookmarks = sum(work.get("stats", {}).get("bookmarks", 0) for work in works)
        total_hits = sum(work.get("stats", {}).get("hits", 0) for work in works)

        # Fandoms
        fandoms = list(set(work.get("fandom") for work in works if work.get("fandom")))

        # Average stats
        work_count = len(works)
        avg_kudos = round(total_kudos / max(work_count, 1), 1)
        avg_comments = round(total_comments / max(work_count, 1), 1)

        return {
            "total_works": work_count,
            "total_kudos": total_kudos,
            "total_comments": total_comments,
            "total_bookmarks": total_bookmarks,
            "total_hits": total_hits,
            "average_kudos_per_work": avg_kudos,
            "average_comments_per_work": avg_comments,
            "fandoms_written_for": fandoms,
            "fandom_diversity": len(fandoms)
        }

    def _analyze_tag_matching(self, works: List[dict], search_tags: List[str]) -> dict:
        """Analyze how well works match the searched tags."""
        tag_matches = {tag: 0 for tag in search_tags}
        total_works = len(works)

        for work in works:
            work_tags = [tag.lower() for tag in work.get("tags", [])]
            for search_tag in search_tags:
                if search_tag.lower() in work_tags:
                    tag_matches[search_tag] += 1

        return {
            "search_tags": search_tags,
            "tag_match_rates": {tag: {"matches": count, "percentage": round(count/max(total_works, 1)*100, 1)}
                               for tag, count in tag_matches.items()},
            "total_works_analyzed": total_works
        }
