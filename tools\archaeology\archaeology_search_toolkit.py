# -*- coding: utf-8 -*-
from typing import List, Dict, Any
import json
from agno.tools import Toolkit
from agno.utils.log import logger

class ArchaeologySearchToolkit(Toolkit):
    """A custom Toolkit for generating search keywords for archaeological databases.

    This toolkit provides functions to generate search keywords for Archnet,
    British Museum, Met Museum, Internet Archive, and Wikipedia, tailored for archaeological research.
    """

    # == Detailed Instructions for the Agent ==
    instruction = [
        "Bạn là một trợ lý nghiên cứu khảo cổ học, chuyên cung cấp từ khóa tìm kiếm tối ưu cho các cơ sở dữ liệu khảo cổ.",
        "<PERSON>hi sử dụng các công cụ trong ArchaeologySearchToolkit, tuân thủ các định dạng từ khóa được chỉ định như sau:",
        "- Archnet: Sử dụng định dạng 'region/site:period' hoặc 'culture:artifact_type' (ví dụ: 'Mesopotamia/Ur:Early Dynastic', 'Maya:ceramic').",
        "- British Museum: Sử dụng định dạng 'culture,object_type,period' hoặc 'registration_number' (ví dụ: 'Egyptian,statue,New Kingdom', '1872,0121.1').",
        "- Met Museum: Sử dụng định dạng 'culture object_type' hoặc 'period:region' (ví dụ: 'Greek sculpture', 'Roman:Italy').",
        "- Internet Archive: Sử dụng định dạng 'site.year.author' hoặc 'subject:archaeology' (ví dụ: 'pompeii.1912.mau', 'subject:mesopotamian archaeology').",
        "- Wikipedia: Sử dụng định dạng 'culture archaeology' hoặc 'site_name' (ví dụ: 'Maya archaeology', 'Angkor Wat').",
        "Ngoài ra, toolkit cũng hỗ trợ tạo từ khóa cho việc tìm kiếm nội dung mới nhất và phổ biến:",
        "- Archnet Recent: Tạo từ khóa cho discoveries và sites mới theo region.",
        "- Museum Recent: Tạo từ khóa cho acquisitions và artifacts mới theo department.",
        "- Archive Recent: Tạo từ khóa cho publications và reports mới theo subject.",
        "- Wikipedia Recent: Tạo từ khóa cho bài viết khảo cổ mới được tạo hoặc cập nhật.",
        "Kiểm tra tính hợp lệ của tham số đầu vào và trả về từ khóa phù hợp với từng cơ sở dữ liệu.",
        "Trả về kết quả dưới dạng JSON với trạng thái ('status'), danh sách từ khóa ('keywords'), và thông báo ('message').",
        "Nếu có lỗi, trả về trạng thái 'error' với mô tả lỗi chi tiết."
    ]

    # == Detailed Few-Shot Examples ==
    few_shot_examples = [
        {
            "user": "Tìm thông tin về Egyptian artifacts.",
            "tool_calls": [
                {
                    "name": "generate_british_museum_keywords",
                    "arguments": {"culture": "Egyptian", "object_type": "statue", "period": "New Kingdom"}
                },
                {
                    "name": "generate_met_museum_keywords",
                    "arguments": {"culture": "Egyptian", "object_type": "sculpture"}
                },
                {
                    "name": "generate_wikipedia_archaeology_keywords",
                    "arguments": {"concept": "Egyptian archaeology"}
                }
            ]
        },
        {
            "user": "Tìm recent discoveries và popular sites.",
            "tool_calls": [
                {
                    "name": "generate_archnet_recent_keywords",
                    "arguments": {"region": "Middle East", "days_back": 30}
                },
                {
                    "name": "generate_museum_recent_keywords",
                    "arguments": {"museum": "british", "department": "Egyptian Antiquities"}
                },
                {
                    "name": "generate_archive_recent_keywords",
                    "arguments": {"subject": "archaeology", "days_back": 30}
                }
            ]
        },
        {
            "user": "Tìm nghiên cứu về Mesopotamian civilization.",
            "tool_calls": [
                {
                    "name": "generate_archnet_keywords",
                    "arguments": {"region": "Mesopotamia", "site": "Ur", "period": "Early Dynastic"}
                },
                {
                    "name": "generate_internet_archive_keywords",
                    "arguments": {"subject": "mesopotamian archaeology", "type": "texts"}
                },
                {
                    "name": "generate_wikipedia_archaeology_keywords",
                    "arguments": {"concept": "Mesopotamian archaeology"}
                }
            ]
        }
    ]

    def __init__(self):
        """Initializes the ArchaeologySearchToolkit."""
        super().__init__(
            name="archaeology_search_toolkit",
            tools=[
                self.generate_archnet_keywords,
                self.generate_british_museum_keywords,
                self.generate_met_museum_keywords,
                self.generate_internet_archive_keywords,
                self.generate_wikipedia_archaeology_keywords,
                self.generate_archnet_recent_keywords,
                self.generate_museum_recent_keywords,
                self.generate_archive_recent_keywords,
                self.generate_wikipedia_archaeology_recent_keywords
            ],
            instructions=self.instruction
        )
        self.few_shot_examples = self.few_shot_examples
        logger.info("ArchaeologySearchToolkit initialized.")

    def generate_archnet_keywords(self, region: str, site: str = None, period: str = None) -> str:
        """Generates search keywords for Archnet.

        Args:
            region: The geographical region (e.g., 'Mesopotamia', 'Mediterranean').
            site: Optional specific site name (e.g., 'Ur', 'Babylon').
            period: Optional time period (e.g., 'Early Dynastic', 'Classical').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating Archnet keywords for region: '{region}', site: '{site}', period: '{period}'")
        try:
            if not region.strip():
                raise ValueError("Region cannot be empty.")

            # Tạo từ khóa cho Archnet
            keywords = [region]
            if site:
                keywords.append(f"{region}/{site}")
                if period:
                    keywords.append(f"{region}/{site}:{period}")
            if period:
                keywords.append(f"{region}:{period}")

            # Thêm từ khóa mở rộng
            keywords.append(f"{region} archaeology")
            keywords.append(f"{region} sites")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Archnet keywords for region '{region}', site '{site or 'general'}', period '{period or 'all'}'.",
                "search_type": "archaeological_sites",
                "parameters": {
                    "region": region,
                    "site": site,
                    "period": period
                }
            }
            logger.debug(f"Archnet keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating Archnet keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate Archnet keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_british_museum_keywords(self, culture: str, object_type: str = None, period: str = None) -> str:
        """Generates search keywords for British Museum.

        Args:
            culture: The culture or civilization (e.g., 'Egyptian', 'Greek', 'Roman').
            object_type: Optional object type (e.g., 'statue', 'vase', 'coin').
            period: Optional time period (e.g., 'Classical', 'Hellenistic').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating British Museum keywords for culture: '{culture}', object_type: '{object_type}', period: '{period}'")
        try:
            if not culture.strip():
                raise ValueError("Culture cannot be empty.")

            # Tạo từ khóa cho British Museum
            keywords = [culture]
            if object_type:
                keywords.append(f"{culture},{object_type}")
                if period:
                    keywords.append(f"{culture},{object_type},{period}")
            if period:
                keywords.append(f"{culture},{period}")

            # Thêm từ khóa mở rộng
            keywords.append(f"{culture} artifacts")
            keywords.append(f"{culture} collection")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated British Museum keywords for culture '{culture}', object type '{object_type or 'general'}', period '{period or 'all'}'.",
                "search_type": "museum_artifacts",
                "parameters": {
                    "culture": culture,
                    "object_type": object_type,
                    "period": period
                }
            }
            logger.debug(f"British Museum keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating British Museum keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate British Museum keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_met_museum_keywords(self, culture: str, object_type: str = None) -> str:
        """Generates search keywords for Met Museum."""
        logger.info(f"Generating Met Museum keywords for culture: '{culture}', object_type: '{object_type}'")
        try:
            if not culture.strip():
                raise ValueError("Culture cannot be empty.")

            keywords = [culture]
            if object_type:
                keywords.append(f"{culture} {object_type}")
            keywords.extend([f"{culture} art", f"{culture} artifacts"])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Met Museum keywords for culture '{culture}' and object type '{object_type or 'general'}'.",
                "search_type": "museum_artifacts",
                "parameters": {"culture": culture, "object_type": object_type}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Met Museum keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_internet_archive_keywords(self, subject: str, type: str = "texts") -> str:
        """Generates search keywords for Internet Archive."""
        logger.info(f"Generating Internet Archive keywords for subject: '{subject}', type: '{type}'")
        try:
            if not subject.strip():
                raise ValueError("Subject cannot be empty.")

            keywords = [f"subject:{subject}", subject]
            if "archaeology" not in subject.lower():
                keywords.append(f"{subject} archaeology")
            keywords.extend([f"{subject} excavation", f"{subject} research"])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Internet Archive keywords for subject '{subject}' and type '{type}'.",
                "search_type": "archive_publications",
                "parameters": {"subject": subject, "type": type}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Internet Archive keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_archaeology_keywords(self, concept: str) -> str:
        """Generates search keywords for Wikipedia archaeology topics."""
        logger.info(f"Generating Wikipedia archaeology keywords for concept: '{concept}'")
        try:
            if not concept.strip():
                raise ValueError("Concept cannot be empty.")

            keywords = [concept]
            if "archaeology" not in concept.lower():
                keywords.append(f"{concept} archaeology")
            keywords.extend([f"{concept} civilization", f"{concept} culture", f"{concept} history"])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia archaeology keywords for concept '{concept}'.",
                "search_type": "encyclopedia_articles",
                "parameters": {"concept": concept}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia archaeology keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    # == Recent/Popular Keywords Functions ==

    def generate_archnet_recent_keywords(self, region: str = None, days_back: int = 30) -> str:
        """Generates keywords for recent discoveries on Archnet."""
        logger.info(f"Generating Archnet recent keywords for region: '{region}', days_back: {days_back}")
        try:
            keywords = ["recent discoveries", "new excavations", "latest findings"]
            if region:
                keywords.extend([f"recent {region}", f"{region} discoveries", f"new {region} sites"])
            keywords.append(f"last {days_back} days")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Archnet recent keywords for region '{region or 'all'}' within last {days_back} days.",
                "search_type": "recent_discoveries",
                "parameters": {"region": region, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Archnet recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_museum_recent_keywords(self, museum: str, department: str = None) -> str:
        """Generates keywords for recent museum acquisitions."""
        logger.info(f"Generating museum recent keywords for museum: '{museum}', department: '{department}'")
        try:
            keywords = ["recent acquisitions", "new additions", "latest artifacts"]
            if department:
                keywords.extend([f"recent {department}", f"{department} acquisitions"])
            keywords.extend([f"new {museum} collection", f"recent {museum} artifacts"])

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated museum recent keywords for {museum} museum, department '{department or 'all'}'.",
                "search_type": "recent_acquisitions",
                "parameters": {"museum": museum, "department": department}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate museum recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_archive_recent_keywords(self, subject: str, days_back: int = 30) -> str:
        """Generates keywords for recent archive publications."""
        logger.info(f"Generating archive recent keywords for subject: '{subject}', days_back: {days_back}")
        try:
            keywords = [f"recent {subject}", f"new {subject} publications", f"latest {subject} research"]
            keywords.extend([f"recent archaeology", f"new excavation reports"])
            keywords.append(f"last {days_back} days")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated archive recent keywords for subject '{subject}' within last {days_back} days.",
                "search_type": "recent_publications",
                "parameters": {"subject": subject, "days_back": days_back}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate archive recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_archaeology_recent_keywords(self, days_back: int = 30, language: str = "en") -> str:
        """Generates keywords for recent archaeology articles on Wikipedia."""
        logger.info(f"Generating Wikipedia archaeology recent keywords for days_back: {days_back}, language: '{language}'")
        try:
            keywords = [
                "recent archaeological discoveries", "new excavations", "latest archaeological findings",
                "recent archaeology articles", "new archaeological sites", "latest cultural heritage",
                f"last {days_back} days archaeology", "recent archaeological research"
            ]

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia archaeology recent keywords within last {days_back} days for language '{language}'.",
                "search_type": "recent_articles",
                "parameters": {"days_back": days_back, "language": language}
            }
        except Exception as e:
            result = {"status": "error", "message": f"Failed to generate Wikipedia archaeology recent keywords: {str(e)}"}
        return json.dumps(result, ensure_ascii=False, indent=4)