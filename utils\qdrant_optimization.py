"""
Module tối ưu hóa Qdrant.
"""

import logging
import time
import asyncio
from typing import Dict, Any, List, Optional, Callable, TypeVar, Tuple, Union


# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QdrantOptimizer:
    """Lớp tối ưu hóa Qdrant."""
    
    def __init__(self, 
                 batch_size: int = 100,
                 max_concurrent_requests: int = 5,
                 cache_ttl: int = 3600,
                 optimize_search: bool = True,
                 optimize_upsert: bool = True):
        """
        Khởi tạo tối ưu hóa Qdrant.
        
        Args:
            batch_size: <PERSON>í<PERSON> thước batch cho các thao tác hàng loạt
            max_concurrent_requests: <PERSON><PERSON> lượ<PERSON> request đồng thời tối đa
            cache_ttl: Thời gian sống của cache (giây)
            optimize_search: Bật/tắt tối ưu hóa tìm kiếm
            optimize_upsert: Bật/tắt tối ưu hóa upsert
        """
        self.batch_size = batch_size
        self.max_concurrent_requests = max_concurrent_requests
        self.cache_ttl = cache_ttl
        self.optimize_search = optimize_search
        self.optimize_upsert = optimize_upsert
        self.semaphore = asyncio.Semaphore(max_concurrent_requests)
        
        # Không cần API optimizer, chỉ dùng Qdrant client chuẩn
    
    def optimize_qdrant_client(self, client: Any) -> Any:
        """
        Tối ưu hóa Qdrant client.
        
        Args:
            client: Qdrant client
            
        Returns:
            Qdrant client đã được tối ưu hóa
        """
        logger.info("Optimizing Qdrant client...")
        
        # Không cần tối ưu hóa phương thức, trả về client chuẩn
        return client
    
    async def optimized_search(self, 
                              client: Any, 
                              collection_name: str, 
                              query_vector: List[float], 
                              limit: int = 10, 
                              filter_dict: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Tìm kiếm tối ưu hóa.
        
        Args:
            client: Qdrant client
            collection_name: Tên collection
            query_vector: Vector truy vấn
            limit: Số lượng kết quả tối đa
            filter_dict: Bộ lọc
            
        Returns:
            Kết quả tìm kiếm
        """
        async with self.semaphore:
            try:
                # Tìm kiếm với timeout và retry
                search_method = getattr(client, "search")
                
                if asyncio.iscoroutinefunction(search_method):
                    # Async client
                    results = await search_method(
                        collection_name=collection_name,
                        query_vector=query_vector,
                        limit=limit,
                        filter=filter_dict
                    )
                else:
                    # Sync client
                    results = search_method(
                        collection_name=collection_name,
                        query_vector=query_vector,
                        limit=limit,
                        filter=filter_dict
                    )
                
                return self._process_search_results(results)
            except Exception as e:
                logger.error(f"Error in optimized_search: {e}")
                return []
    
    async def optimized_batch_search(self, 
                                    client: Any, 
                                    collection_name: str, 
                                    query_vectors: List[List[float]], 
                                    limit: int = 10, 
                                    filter_dict: Optional[Dict[str, Any]] = None) -> List[List[Dict[str, Any]]]:
        """
        Tìm kiếm hàng loạt tối ưu hóa.
        
        Args:
            client: Qdrant client
            collection_name: Tên collection
            query_vectors: Danh sách vector truy vấn
            limit: Số lượng kết quả tối đa
            filter_dict: Bộ lọc
            
        Returns:
            Kết quả tìm kiếm hàng loạt
        """
        # Chia thành các batch
        batches = [query_vectors[i:i+self.batch_size] for i in range(0, len(query_vectors), self.batch_size)]
        
        all_results = []
        
        for batch in batches:
            async with self.semaphore:
                try:
                    # Tìm kiếm hàng loạt với timeout và retry
                    search_batch_method = getattr(client, "search_batch", None)
                    
                    if search_batch_method:
                        # Sử dụng search_batch nếu có
                        if asyncio.iscoroutinefunction(search_batch_method):
                            # Async client
                            batch_results = await search_batch_method(
                                collection_name=collection_name,
                                query_vectors=batch,
                                limit=limit,
                                filter=filter_dict
                            )
                        else:
                            # Sync client
                            batch_results = search_batch_method(
                                collection_name=collection_name,
                                query_vectors=batch,
                                limit=limit,
                                filter=filter_dict
                            )
                        
                        all_results.extend(batch_results)
                    else:
                        # Fallback: sử dụng search cho từng vector
                        for vector in batch:
                            result = await self.optimized_search(
                                client=client,
                                collection_name=collection_name,
                                query_vector=vector,
                                limit=limit,
                                filter_dict=filter_dict
                            )
                            all_results.append(result)
                except Exception as e:
                    logger.error(f"Error in optimized_batch_search: {e}")
                    # Thêm kết quả trống cho batch lỗi
                    all_results.extend([[] for _ in range(len(batch))])
        
        return all_results
    
    async def optimized_upsert(self, 
                              client: Any, 
                              collection_name: str, 
                              points: List[Dict[str, Any]]) -> bool:
        """
        Upsert tối ưu hóa.
        
        Args:
            client: Qdrant client
            collection_name: Tên collection
            points: Danh sách điểm cần upsert
            
        Returns:
            Kết quả upsert
        """
        # Chia thành các batch
        batches = [points[i:i+self.batch_size] for i in range(0, len(points), self.batch_size)]
        
        success = True
        
        for batch in batches:
            async with self.semaphore:
                try:
                    # Upsert với timeout và retry
                    upsert_method = getattr(client, "upsert")
                    
                    if asyncio.iscoroutinefunction(upsert_method):
                        # Async client
                        await upsert_method(
                            collection_name=collection_name,
                            points=batch
                        )
                    else:
                        # Sync client
                        upsert_method(
                            collection_name=collection_name,
                            points=batch
                        )
                except Exception as e:
                    logger.error(f"Error in optimized_upsert: {e}")
                    success = False
        
        return success
    
    def _process_search_results(self, results: Any) -> List[Dict[str, Any]]:
        """
        Xử lý kết quả tìm kiếm.
        
        Args:
            results: Kết quả tìm kiếm từ Qdrant
            
        Returns:
            Kết quả đã xử lý
        """
        processed_results = []
        
        # Kiểm tra loại kết quả
        if hasattr(results, "__iter__"):
            # Danh sách kết quả
            for result in results:
                if hasattr(result, "payload") and hasattr(result, "score"):
                    # ScoredPoint
                    processed_results.append({
                        "id": getattr(result, "id", None),
                        "payload": result.payload,
                        "score": result.score,
                        "vector": getattr(result, "vector", None)
                    })
                elif isinstance(result, dict):
                    # Dict
                    processed_results.append(result)
        
        return processed_results


# Singleton instance
_qdrant_optimizer_instance = None

def get_qdrant_optimizer(batch_size: int = 100,
                        max_concurrent_requests: int = 5,
                        cache_ttl: int = 3600,
                        optimize_search: bool = True,
                        optimize_upsert: bool = True) -> QdrantOptimizer:
    """
    Lấy instance của QdrantOptimizer (singleton pattern).
    
    Args:
        batch_size: Kích thước batch cho các thao tác hàng loạt
        max_concurrent_requests: Số lượng request đồng thời tối đa
        cache_ttl: Thời gian sống của cache (giây)
        optimize_search: Bật/tắt tối ưu hóa tìm kiếm
        optimize_upsert: Bật/tắt tối ưu hóa upsert
        
    Returns:
        Instance của QdrantOptimizer
    """
    global _qdrant_optimizer_instance
    if _qdrant_optimizer_instance is None:
        _qdrant_optimizer_instance = QdrantOptimizer(
            batch_size=batch_size,
            max_concurrent_requests=max_concurrent_requests,
            cache_ttl=cache_ttl,
            optimize_search=optimize_search,
            optimize_upsert=optimize_upsert
        )
    return _qdrant_optimizer_instance


# Hàm tiện ích
def optimize_qdrant_client(client: Any) -> Any:
    """
    Tối ưu hóa Qdrant client.
    
    Args:
        client: Qdrant client
        
    Returns:
        Qdrant client đã được tối ưu hóa
    """
    optimizer = get_qdrant_optimizer()
    return optimizer.optimize_qdrant_client(client)


async def optimized_search(client: Any, 
                          collection_name: str, 
                          query_vector: List[float], 
                          limit: int = 10, 
                          filter_dict: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Tìm kiếm tối ưu hóa.
    
    Args:
        client: Qdrant client
        collection_name: Tên collection
        query_vector: Vector truy vấn
        limit: Số lượng kết quả tối đa
        filter_dict: Bộ lọc
        
    Returns:
        Kết quả tìm kiếm
    """
    optimizer = get_qdrant_optimizer()
    return await optimizer.optimized_search(
        client=client,
        collection_name=collection_name,
        query_vector=query_vector,
        limit=limit,
        filter_dict=filter_dict
    )


if __name__ == "__main__":
    # Test tối ưu hóa Qdrant
    print("This module provides Qdrant optimization utilities.")