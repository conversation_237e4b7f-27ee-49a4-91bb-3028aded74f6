#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script cho Political Science Tools
"""

import sys
import os
import json
import random
import asyncio
from datetime import datetime

# Add the tools directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_cfr_tools():
    """Test CFR Tools"""
    print("🏛️ Testing CFR Tools...")
    try:
        from tools.political_science.cfr_tools import CFRTool
        
        cfr_tool = CFRTool()
        
        print("  - Testing CFR instantiation...")
        print("    ✅ CFR Tools instantiated")
        
        # Test get_top_new (async)
        print("  - Testing CFR get_top_new...")
        assert hasattr(cfr_tool, 'get_top_new')
        print("    ✅ CFR get_top_new method exists")
        
        return True
        
    except Exception as e:
        print(f"    ❌ CFR Tools failed: {str(e)}")
        return False

def test_political_science_search_toolkit():
    """Test Political Science Search Toolkit"""
    print("🔍 Testing Political Science Search Toolkit...")
    try:
        from tools.political_science.political_science_search_toolkit import PoliticalScienceSearchToolkit
        
        toolkit = PoliticalScienceSearchToolkit()
        
        print("  - Testing government policies search...")
        result = toolkit.search_government_policies("economic", "USA", "legislation", "current")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Government policies search works")
        
        print("  - Testing international relations search...")
        result = toolkit.search_international_relations("asia_pacific", "bilateral", "security", "current")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ International relations search works")
        
        print("  - Testing political systems search...")
        result = toolkit.search_political_systems("democracy", "Germany", "institutions", "institutional")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Political systems search works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Political Science Search Toolkit failed: {str(e)}")
        return False

def test_other_political_science_tools():
    """Test other political science tools"""
    print("📊 Testing Other Political Science Tools...")
    try:
        # Test CIA Factbook Tools
        from tools.political_science.cia_factbook_tools import CIAFactbookTool
        
        cia_tool = CIAFactbookTool()
        
        print("  - Testing CIA Factbook Tools...")
        print("    ✅ CIA Factbook Tools instantiated")
        
        # Test UN Library Tools
        from tools.political_science.un_library_tools import UNLibraryTool
        
        un_tool = UNLibraryTool()
        
        print("  - Testing UN Library Tools...")
        print("    ✅ UN Library Tools instantiated")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Other Political Science Tools failed: {str(e)}")
        return False

def test_random_political_science_functionality():
    """Test random political science functionality"""
    print("\n🎲 Testing Random Political Science Functionality...")
    
    try:
        # Random policy search test
        from tools.political_science.political_science_search_toolkit import PoliticalScienceSearchToolkit
        toolkit = PoliticalScienceSearchToolkit()
        
        policies = ["economic", "social", "foreign", "environmental", "security"]
        policy = random.choice(policies)
        result = toolkit.search_government_policies(policy, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random policy {policy} search test passed")
        
        # Random IR search test
        regions = ["asia_pacific", "europe", "americas", "africa", "middle_east"]
        region = random.choice(regions)
        result = toolkit.search_international_relations(region, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random IR {region} search test passed")
        
        # Random electoral system test
        systems = ["proportional", "majoritarian", "mixed", "ranked_choice"]
        system = random.choice(systems)
        result = toolkit.search_electoral_systems(system, "", "", "")
        data = json.loads(result)
        assert "search_parameters" in data
        print(f"  🎯 Random electoral {system} test passed")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Random Political Science Functionality failed: {str(e)}")
        return False

def test_political_science_search_variations():
    """Test various political science search variations"""
    print("\n🗳️ Testing Political Science Search Variations...")
    
    try:
        from tools.political_science.political_science_search_toolkit import PoliticalScienceSearchToolkit
        toolkit = PoliticalScienceSearchToolkit()
        
        # Test comprehensive political search
        print("  - Testing comprehensive political search...")
        result = toolkit.comprehensive_political_search("democracy", "all", "global", "standard")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Comprehensive political search works")
        
        # Test electoral systems with different parameters
        print("  - Testing electoral systems variations...")
        result = toolkit.search_electoral_systems("", "Canada", "national", "representation")
        data = json.loads(result)
        assert "search_parameters" in data
        print("    ✅ Electoral systems variations work")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Political Science Search Variations failed: {str(e)}")
        return False

async def test_async_cfr():
    """Test async CFR functionality"""
    print("\n⚡ Testing Async CFR...")
    
    try:
        from tools.political_science.cfr_tools import CFRTool
        
        cfr_tool = CFRTool()
        
        # Test async get_top_new
        print("  - Testing async get_top_new...")
        result = await cfr_tool.get_top_new("analysis", 5, "month", "asia")
        assert result["status"] == "success"
        print("    ✅ Async get_top_new works")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Async CFR failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 POLITICAL SCIENCE TOOLS TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing Political Science channel tools...")
    print()
    
    test_results = []
    
    # Test all political science tools
    test_functions = [
        ("CFR Tools", test_cfr_tools),
        ("Political Science Search Toolkit", test_political_science_search_toolkit),
        ("Other Political Science Tools", test_other_political_science_tools),
        ("Random Political Science Functionality", test_random_political_science_functionality),
        ("Political Science Search Variations", test_political_science_search_variations)
    ]
    
    for test_name, test_func in test_functions:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        test_results.append((test_name, result))
        print()
    
    # Test async functionality
    print(f"\n{'='*20} Async Tests {'='*20}")
    try:
        async_result = asyncio.run(test_async_cfr())
        test_results.append(("Async CFR", async_result))
    except Exception as e:
        print(f"❌ Async tests failed: {str(e)}")
        test_results.append(("Async CFR", False))
    
    # Summary
    print("\n" + "="*60)
    print("📋 POLITICAL SCIENCE TOOLS TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} test categories passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All political science tools are working correctly!")
        print("✨ Political Science channel fully functional!")
        print("🏛️ Ready for political analysis and research!")
    elif passed >= total * 0.8:
        print("✅ Excellent performance - most functionality working!")
    elif passed >= total * 0.6:
        print("✅ Good performance - majority working!")
    else:
        print("⚠️  Some issues detected. Please check the error messages above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
