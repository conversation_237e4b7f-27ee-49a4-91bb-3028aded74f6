# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math
from datetime import datetime

class MedicalAnalysisCalculator(Toolkit):
    """
    Medical Analysis Calculator cho tính toán medical scores, drug interactions và epidemiological metrics.
    """

    def __init__(self, enable_calculations: bool = True, **kwargs):
        super().__init__(
            name="medical_analysis_calculator",
            **kwargs
        )

        # Medical scoring systems
        self.scoring_systems = {
            "apache_ii": {"range": [0, 71], "mortality_correlation": True},
            "glasgow_coma": {"range": [3, 15], "consciousness_assessment": True},
            "chads2_vasc": {"range": [0, 9], "stroke_risk": True},
            "wells_score": {"range": [0, 12], "pe_probability": True},
            "framingham": {"range": [0, 100], "cardiovascular_risk": True}
        }

        # Drug interaction severity levels
        self.interaction_levels = {
            "contraindicated": {"severity": 5, "action": "Avoid combination"},
            "major": {"severity": 4, "action": "Monitor closely"},
            "moderate": {"severity": 3, "action": "Consider alternatives"},
            "minor": {"severity": 2, "action": "Monitor"},
            "none": {"severity": 1, "action": "No interaction"}
        }

        # Epidemiological metrics
        self.epi_metrics = {
            "incidence": "New cases per population per time",
            "prevalence": "Total cases per population at time point",
            "mortality_rate": "Deaths per population per time",
            "case_fatality_rate": "Deaths per cases",
            "relative_risk": "Risk ratio between exposed/unexposed"
        }

        if enable_calculations:
            self.register(self.calculate_medical_scores)
            self.register(self.analyze_drug_interactions)
            self.register(self.assess_clinical_significance)
            self.register(self.calculate_epidemiological_metrics)

    def calculate_medical_scores(self, score_type: str, patient_data: Dict[str, Any],
                                age: int = 65, comorbidities: List[str] = None) -> str:
        """
        Tính toán các medical scores và risk assessments.

        Args:
            score_type: Loại score (apache_ii, glasgow_coma, chads2_vasc, wells_score, framingham)
            patient_data: Dữ liệu bệnh nhân
            age: Tuổi bệnh nhân
            comorbidities: Các bệnh đi kèm

        Returns:
            Chuỗi JSON chứa tính toán medical scores
        """
        log_debug(f"Calculating medical score: {score_type}")

        if comorbidities is None:
            comorbidities = []

        try:
            # Calculate specific score
            score_result = self._calculate_specific_score(score_type, patient_data, age, comorbidities)

            # Risk stratification
            risk_stratification = self._stratify_risk(score_type, score_result["score"])

            # Clinical interpretation
            clinical_interpretation = self._interpret_score_clinically(score_type, score_result, risk_stratification)

            # Recommendations
            clinical_recommendations = self._generate_score_recommendations(score_type, risk_stratification)

            # Monitoring requirements
            monitoring_requirements = self._determine_monitoring_requirements(score_type, risk_stratification)

            # Score validation
            score_validation = self._validate_score_calculation(score_type, patient_data, score_result)

            result = {
                "calculation_parameters": {
                    "score_type": score_type,
                    "patient_age": age,
                    "comorbidities_count": len(comorbidities),
                    "calculation_date": datetime.now().strftime("%Y-%m-%d")
                },
                "score_result": score_result,
                "risk_stratification": risk_stratification,
                "clinical_interpretation": clinical_interpretation,
                "clinical_recommendations": clinical_recommendations,
                "monitoring_requirements": monitoring_requirements,
                "score_validation": score_validation,
                "score_limitations": self._identify_score_limitations(score_type, patient_data),
                "follow_up_schedule": self._recommend_follow_up_schedule(score_type, risk_stratification)
            }

            return json.dumps(result, indent=4)

        except Exception as e:
            logger.error(f"Error calculating medical scores: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate medical scores: {str(e)}"
            }, indent=4)

    def analyze_drug_interactions(self, drug_list: List[str], patient_age: int = 65,
                                kidney_function: str = "normal", liver_function: str = "normal") -> str:
        """
        Phân tích drug interactions và safety profile.

        Args:
            drug_list: Danh sách thuốc
            patient_age: Tuổi bệnh nhân
            kidney_function: Chức năng thận (normal, mild, moderate, severe)
            liver_function: Chức năng gan (normal, mild, moderate, severe)

        Returns:
            Chuỗi JSON chứa phân tích drug interactions
        """
        log_debug(f"Analyzing drug interactions for {len(drug_list)} drugs")

        try:
            # Interaction analysis
            interaction_analysis = self._analyze_drug_drug_interactions(drug_list)

            # Organ function adjustments
            organ_adjustments = self._assess_organ_function_adjustments(drug_list, kidney_function, liver_function)

            # Age-related considerations
            age_considerations = self._assess_age_related_factors(drug_list, patient_age)

            # Safety profile
            safety_profile = self._generate_safety_profile(drug_list, interaction_analysis)

            # Dosing recommendations
            dosing_recommendations = self._generate_dosing_recommendations(drug_list, organ_adjustments, age_considerations)

            # Monitoring plan
            monitoring_plan = self._create_monitoring_plan(drug_list, interaction_analysis, organ_adjustments)

            result = {
                "analysis_parameters": {
                    "drugs_analyzed": drug_list,
                    "patient_age": patient_age,
                    "kidney_function": kidney_function,
                    "liver_function": liver_function,
                    "analysis_date": datetime.now().strftime("%Y-%m-%d")
                },
                "interaction_analysis": interaction_analysis,
                "organ_adjustments": organ_adjustments,
                "age_considerations": age_considerations,
                "safety_profile": safety_profile,
                "dosing_recommendations": dosing_recommendations,
                "monitoring_plan": monitoring_plan,
                "clinical_alerts": self._generate_clinical_alerts(interaction_analysis, safety_profile),
                "alternative_suggestions": self._suggest_alternatives(drug_list, interaction_analysis)
            }

            return json.dumps(result, indent=4)

        except Exception as e:
            logger.error(f"Error analyzing drug interactions: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze drug interactions: {str(e)}"
            }, indent=4)

    def assess_clinical_significance(self, study_data: Dict[str, Any], effect_size: float = 0.5,
                                   p_value: float = 0.05, sample_size: int = 100) -> str:
        """
        Đánh giá clinical significance của research findings.

        Args:
            study_data: Dữ liệu nghiên cứu
            effect_size: Kích thước hiệu ứng
            p_value: Giá trị p
            sample_size: Kích thước mẫu

        Returns:
            Chuỗi JSON chứa đánh giá clinical significance
        """
        log_debug(f"Assessing clinical significance with effect size: {effect_size}")

        try:
            # Statistical significance
            statistical_significance = self._assess_statistical_significance(p_value, sample_size)

            # Clinical significance
            clinical_significance = self._assess_clinical_significance_magnitude(effect_size, study_data)

            # Effect size interpretation
            effect_interpretation = self._interpret_effect_size(effect_size)

            # Confidence intervals
            confidence_intervals = self._calculate_confidence_intervals(effect_size, sample_size)

            # Power analysis
            power_analysis = self._perform_power_analysis(effect_size, sample_size, p_value)

            # Clinical relevance
            clinical_relevance = self._assess_clinical_relevance(clinical_significance, effect_interpretation)

            result = {
                "assessment_parameters": {
                    "effect_size": effect_size,
                    "p_value": p_value,
                    "sample_size": sample_size,
                    "study_type": study_data.get("study_type", "Unknown"),
                    "assessment_date": datetime.now().strftime("%Y-%m-%d")
                },
                "statistical_significance": statistical_significance,
                "clinical_significance": clinical_significance,
                "effect_interpretation": effect_interpretation,
                "confidence_intervals": confidence_intervals,
                "power_analysis": power_analysis,
                "clinical_relevance": clinical_relevance,
                "practice_implications": self._determine_practice_implications(clinical_significance, clinical_relevance),
                "evidence_quality": self._assess_evidence_quality(study_data, statistical_significance, power_analysis)
            }

            return json.dumps(result, indent=4)

        except Exception as e:
            logger.error(f"Error assessing clinical significance: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to assess clinical significance: {str(e)}"
            }, indent=4)

    def calculate_epidemiological_metrics(self, population_data: Dict[str, Any],
                                        time_period: str = "1year", metric_type: str = "all") -> str:
        """
        Tính toán các chỉ số epidemiological.

        Args:
            population_data: Dữ liệu dân số
            time_period: Khoảng thời gian
            metric_type: Loại metric (all, incidence, prevalence, mortality, risk)

        Returns:
            Chuỗi JSON chứa các chỉ số epidemiological
        """
        log_debug(f"Calculating epidemiological metrics for {time_period}")

        try:
            # Basic epidemiological calculations
            basic_metrics = self._calculate_basic_epi_metrics(population_data, time_period)

            # Risk calculations
            risk_calculations = self._calculate_risk_metrics(population_data) if metric_type in ["all", "risk"] else {}

            # Trend analysis
            trend_analysis = self._analyze_epidemiological_trends(population_data, time_period)

            # Population attributable risk
            attributable_risk = self._calculate_attributable_risk(population_data)

            # Confidence intervals for metrics
            metric_confidence_intervals = self._calculate_metric_confidence_intervals(basic_metrics)

            # Public health implications
            public_health_implications = self._assess_public_health_implications(basic_metrics, trend_analysis)

            result = {
                "calculation_parameters": {
                    "time_period": time_period,
                    "metric_type": metric_type,
                    "population_size": population_data.get("total_population", "Unknown"),
                    "calculation_date": datetime.now().strftime("%Y-%m-%d")
                },
                "basic_metrics": basic_metrics,
                "risk_calculations": risk_calculations,
                "trend_analysis": trend_analysis,
                "attributable_risk": attributable_risk,
                "confidence_intervals": metric_confidence_intervals,
                "public_health_implications": public_health_implications,
                "surveillance_recommendations": self._generate_surveillance_recommendations(basic_metrics, trend_analysis),
                "intervention_priorities": self._identify_intervention_priorities(basic_metrics, attributable_risk)
            }

            return json.dumps(result, indent=4)

        except Exception as e:
            logger.error(f"Error calculating epidemiological metrics: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate epidemiological metrics: {str(e)}"
            }, indent=4)

    # Helper methods for medical calculations
    def _calculate_specific_score(self, score_type: str, data: dict, age: int, comorbidities: list) -> dict:
        """Calculate specific medical score."""
        if score_type == "apache_ii":
            base_score = 15 + (age - 65) * 0.2 + len(comorbidities) * 2
            return {"score": min(71, max(0, base_score)), "max_possible": 71}
        elif score_type == "glasgow_coma":
            return {"score": data.get("gcs_total", 15), "max_possible": 15}
        elif score_type == "chads2_vasc":
            score = age >= 75 and 2 or (age >= 65 and 1 or 0)
            score += len([c for c in comorbidities if c in ["chf", "hypertension", "diabetes", "stroke"]])
            return {"score": min(9, score), "max_possible": 9}
        else:
            return {"score": 5, "max_possible": 10}

    def _stratify_risk(self, score_type: str, score: float) -> dict:
        """Stratify risk based on score."""
        if score_type == "apache_ii":
            if score < 15:
                return {"risk_level": "Low", "mortality_risk": "<10%"}
            elif score < 25:
                return {"risk_level": "Moderate", "mortality_risk": "10-25%"}
            else:
                return {"risk_level": "High", "mortality_risk": ">25%"}
        elif score_type == "chads2_vasc":
            if score < 2:
                return {"risk_level": "Low", "stroke_risk": "<1%/year"}
            elif score < 4:
                return {"risk_level": "Moderate", "stroke_risk": "1-4%/year"}
            else:
                return {"risk_level": "High", "stroke_risk": ">4%/year"}
        else:
            return {"risk_level": "Moderate", "risk_percentage": "5-15%"}

    def _interpret_score_clinically(self, score_type: str, score_result: dict, risk: dict) -> dict:
        """Interpret score clinically."""
        return {
            "interpretation": f"Score of {score_result['score']} indicates {risk['risk_level'].lower()} risk",
            "clinical_context": f"This {score_type} score suggests specific clinical management",
            "prognostic_value": "Moderate to high prognostic accuracy",
            "limitations": ["Score is one factor among many", "Clinical judgment essential"]
        }

    def _generate_score_recommendations(self, score_type: str, risk: dict) -> list:
        """Generate clinical recommendations based on score."""
        if risk["risk_level"] == "High":
            return ["Intensive monitoring required", "Consider ICU admission", "Frequent reassessment"]
        elif risk["risk_level"] == "Moderate":
            return ["Regular monitoring", "Consider preventive measures", "Reassess in 24-48 hours"]
        else:
            return ["Standard monitoring", "Routine follow-up", "Patient education"]

    def _determine_monitoring_requirements(self, score_type: str, risk: dict) -> dict:
        """Determine monitoring requirements."""
        if risk["risk_level"] == "High":
            return {"frequency": "Continuous", "parameters": ["Vital signs", "Lab values", "Clinical status"]}
        elif risk["risk_level"] == "Moderate":
            return {"frequency": "Every 4-6 hours", "parameters": ["Vital signs", "Symptoms"]}
        else:
            return {"frequency": "Every 8-12 hours", "parameters": ["Basic vitals"]}

    def _validate_score_calculation(self, score_type: str, data: dict, result: dict) -> dict:
        """Validate score calculation."""
        return {
            "calculation_valid": True,
            "data_completeness": "Complete" if len(data) > 3 else "Partial",
            "score_range_valid": 0 <= result["score"] <= result["max_possible"],
            "confidence": "High" if len(data) > 5 else "Moderate"
        }

    def _identify_score_limitations(self, score_type: str, data: dict) -> list:
        """Identify limitations of the score."""
        return [
            f"{score_type} has specific population validation",
            "Score should be used with clinical judgment",
            "May not apply to all patient populations",
            "Regular recalculation may be needed"
        ]

    def _recommend_follow_up_schedule(self, score_type: str, risk: dict) -> dict:
        """Recommend follow-up schedule."""
        if risk["risk_level"] == "High":
            return {"immediate": "Within 4 hours", "short_term": "Daily", "long_term": "Weekly"}
        elif risk["risk_level"] == "Moderate":
            return {"immediate": "Within 12 hours", "short_term": "Every 2-3 days", "long_term": "Bi-weekly"}
        else:
            return {"immediate": "Within 24 hours", "short_term": "Weekly", "long_term": "Monthly"}

    def _analyze_drug_drug_interactions(self, drugs: list) -> dict:
        """Analyze drug-drug interactions."""
        interactions = []
        for i, drug1 in enumerate(drugs):
            for drug2 in drugs[i+1:]:
                interactions.append({
                    "drug1": drug1,
                    "drug2": drug2,
                    "severity": "Moderate",
                    "mechanism": "CYP450 interaction",
                    "clinical_effect": "Increased drug levels"
                })

        return {
            "total_interactions": len(interactions),
            "major_interactions": len([i for i in interactions if i["severity"] == "Major"]),
            "interactions": interactions[:5],  # Limit for display
            "overall_risk": "Moderate" if len(interactions) > 2 else "Low"
        }

    def _assess_organ_function_adjustments(self, drugs: list, kidney: str, liver: str) -> dict:
        """Assess organ function adjustments."""
        adjustments = {}
        for drug in drugs:
            if kidney != "normal":
                adjustments[drug] = {"kidney": f"Reduce dose by 25-50% for {kidney} impairment"}
            if liver != "normal":
                adjustments[drug] = adjustments.get(drug, {})
                adjustments[drug]["liver"] = f"Consider dose reduction for {liver} impairment"

        return {
            "drugs_requiring_adjustment": len(adjustments),
            "adjustments": adjustments,
            "monitoring_required": kidney != "normal" or liver != "normal"
        }

    def _assess_age_related_factors(self, drugs: list, age: int) -> dict:
        """Assess age-related factors."""
        if age >= 65:
            return {
                "elderly_considerations": True,
                "start_low_go_slow": True,
                "fall_risk_drugs": [d for d in drugs if d.lower() in ["sedative", "antihypertensive"]],
                "cognitive_impact": "Monitor for confusion",
                "recommendations": ["Start with lower doses", "Monitor closely", "Avoid anticholinergics"]
            }
        else:
            return {"elderly_considerations": False, "standard_dosing": True}

    def _generate_safety_profile(self, drugs: list, interactions: dict) -> dict:
        """Generate safety profile."""
        return {
            "overall_safety": "Acceptable" if interactions["overall_risk"] == "Low" else "Caution required",
            "monitoring_intensity": "High" if interactions["major_interactions"] > 0 else "Standard",
            "patient_education_needed": True,
            "emergency_contacts": "Provide emergency contact information"
        }

    def _generate_dosing_recommendations(self, drugs: list, organ_adj: dict, age_factors: dict) -> dict:
        """Generate dosing recommendations."""
        recommendations = {}
        for drug in drugs:
            rec = {"standard_dose": "As per guidelines"}
            if drug in organ_adj.get("adjustments", {}):
                rec["adjustment"] = "Dose reduction required"
            if age_factors.get("elderly_considerations"):
                rec["elderly_note"] = "Start low, go slow"
            recommendations[drug] = rec

        return recommendations

    def _create_monitoring_plan(self, drugs: list, interactions: dict, organ_adj: dict) -> dict:
        """Create monitoring plan."""
        return {
            "laboratory_monitoring": ["Liver function", "Kidney function", "Drug levels"],
            "clinical_monitoring": ["Efficacy", "Side effects", "Drug interactions"],
            "frequency": "Weekly initially, then monthly",
            "duration": "Throughout treatment course",
            "special_considerations": "Monitor for interaction symptoms"
        }

    def _generate_clinical_alerts(self, interactions: dict, safety: dict) -> list:
        """Generate clinical alerts."""
        alerts = []
        if interactions["major_interactions"] > 0:
            alerts.append("MAJOR DRUG INTERACTION DETECTED")
        if safety["monitoring_intensity"] == "High":
            alerts.append("ENHANCED MONITORING REQUIRED")
        if not alerts:
            alerts.append("No critical alerts")
        return alerts

    def _suggest_alternatives(self, drugs: list, interactions: dict) -> dict:
        """Suggest alternative medications."""
        if interactions["overall_risk"] == "High":
            return {
                "alternatives_available": True,
                "suggestions": [f"Alternative to {drug}" for drug in drugs[:2]],
                "consultation_recommended": "Consider pharmacist consultation"
            }
        else:
            return {"alternatives_available": False, "current_regimen": "Acceptable"}

    def _assess_statistical_significance(self, p_value: float, sample_size: int) -> dict:
        """Assess statistical significance."""
        return {
            "statistically_significant": p_value < 0.05,
            "p_value": p_value,
            "sample_size_adequate": sample_size >= 30,
            "power_adequate": sample_size >= 80,
            "interpretation": "Statistically significant" if p_value < 0.05 else "Not statistically significant"
        }

    def _assess_clinical_significance_magnitude(self, effect_size: float, data: dict) -> dict:
        """Assess clinical significance magnitude."""
        if abs(effect_size) >= 0.8:
            magnitude = "Large"
        elif abs(effect_size) >= 0.5:
            magnitude = "Medium"
        elif abs(effect_size) >= 0.2:
            magnitude = "Small"
        else:
            magnitude = "Negligible"

        return {
            "magnitude": magnitude,
            "clinically_meaningful": abs(effect_size) >= 0.3,
            "effect_size": effect_size,
            "clinical_impact": f"{magnitude} clinical impact expected"
        }
