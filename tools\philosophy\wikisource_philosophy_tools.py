from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
from bs4 import BeautifulSoup
import re

class WikisourcePhilosophyTool(Toolkit):
    """
    Wikisource Philosophy Tool for searching and retrieving original philosophical works and translations from Wikisource.
    """

    def __init__(self):
        super().__init__(
            name="Wikisource Philosophy Tools",
            description="Collection of tools for working with philosophical texts from Wikisource.",
            tools=[
                self.search_wikisource_philosophy,
                self.get_philosophy_text,
                self.list_philosophy_authors,
                self.get_philosophy_chapter,
                self.search_philosophy_quotes
            ]
        )

    async def search_wikisource_philosophy(self, query: str, language: str = "en", limit: int = 5) -> Dict[str, Any]:
        """
        Search Wikisource for original philosophical works and translations.

        Parameters:
        - query: Name of philosophical work, author, or concept (e.g., 'Thus S<PERSON><PERSON>', 'Medit<PERSON> <PERSON>', 'Plato Republic')
        - language: Wikisource language code (default: 'en')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with search results including title, author, snippet, and Wikisource URLs
        """
        logger.info(f"Searching Wikisource ({language}) for philosophy: {query}")

        try:
            # Wikisource uses the MediaWiki API for search
            api_url = f"https://{language}.wikisource.org/w/api.php"
            params = {
                "action": "query",
                "list": "search",
                "srsearch": query,
                "format": "json",
                "srlimit": limit
            }
            response = requests.get(api_url, params=params)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikisource",
                    "message": f"Wikisource API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            search_results = data.get("query", {}).get("search", [])
            results = []
            for item in search_results:
                title = item.get("title", "")
                snippet = item.get("snippet", "")
                page_url = f"https://{language}.wikisource.org/wiki/{title.replace(' ', '_')}"
                results.append({
                    "title": title,
                    "snippet": snippet,
                    "wikisource_url": page_url
                })

            return {
                "status": "success",
                "source": "Wikisource",
                "query": query,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching Wikisource Philosophy: {str(e)}")
            return {
                "status": "error",
                "source": "Wikisource",
                "message": str(e),
                "query": query
            }

    async def get_philosophy_text(self, title: str, language: str = "en") -> Dict[str, Any]:
        """
        Retrieve the full text of a philosophical work from Wikisource.

        Parameters:
        - title: Title of the work as it appears on Wikisource (e.g., 'The_Republic')
        - language: Wikisource language code (default: 'en')

        Returns:
        - JSON with the work's title, author, and full text content
        """
        logger.info(f"Retrieving text from Wikisource: {title}")

        try:
            # Get the main page content
            api_url = f"https://{language}.wikisource.org/w/api.php"
            params = {
                "action": "parse",
                "page": title,
                "format": "json",
                "prop": "text|sections"
            }
            
            response = requests.get(api_url, params=params)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikisource",
                    "message": f"Failed to retrieve text. Status code: {response.status_code}",
                    "title": title
                }

            data = response.json()
            if 'error' in data:
                return {
                    "status": "error",
                    "source": "Wikisource",
                    "message": data['error']['info'],
                    "title": title
                }

            # Extract text content using BeautifulSoup
            html_content = data['parse']['text']['*']
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove edit sections and other wiki-specific elements
            for element in soup.select('.mw-editsection, .noprint, .metadata'):
                element.decompose()
            
            # Get clean text
            text = '\n'.join(p.get_text().strip() for p in soup.find_all(['p', 'h1', 'h2', 'h3']) 
                             if p.get_text().strip())
            
            # Get author if available
            author = ""
            author_tag = soup.select_one('.author')
            if author_tag:
                author = author_tag.get_text().strip()

            return {
                "status": "success",
                "source": "Wikisource",
                "title": title,
                "author": author,
                "text": text,
                "url": f"https://{language}.wikisource.org/wiki/{title.replace(' ', '_')}"
            }


        except Exception as e:
            log_debug(f"Error retrieving text from Wikisource: {str(e)}")
            return {
                "status": "error",
                "source": "Wikisource",
                "message": str(e),
                "title": title
            }

    async def list_philosophy_authors(self, language: str = "en", period: Optional[str] = None) -> Dict[str, Any]:
        """
        List philosophical authors available on Wikisource.

        Parameters:
        - language: Wikisource language code (default: 'en')
        - period: Optional time period filter (e.g., 'ancient', 'medieval', 'modern')

        Returns:
        - JSON with list of authors and their major works
        """
        logger.info(f"Listing philosophy authors from Wikisource ({language}) - Period: {period or 'All'}")

        try:
            # This is a simplified implementation that uses category pages
            category = "Philosophers"
            if period:
                category = f"{period.capitalize()}_{category}"
            
            api_url = f"https://{language}.wikisource.org/w/api.php"
            params = {
                "action": "query",
                "list": "categorymembers",
                "cmtitle": f"Category:{category}",
                "cmlimit": 50,  # Limit to 50 authors
                "format": "json"
            }
            
            response = requests.get(api_url, params=params)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikisource",
                    "message": f"Failed to retrieve authors. Status code: {response.status_code}"
                }

            data = response.json()
            authors = []
            
            for member in data.get('query', {}).get('categorymembers', []):
                if member.get('ns') == 0:  # Only include main namespace pages
                    authors.append({
                        "name": member.get('title', ''),
                        "pageid": member.get('pageid', ''),
                        "url": f"https://{language}.wikisource.org/wiki/{member.get('title', '').replace(' ', '_')}"
                    })


            return {
                "status": "success",
                "source": "Wikisource",
                "language": language,
                "period": period,
                "authors_count": len(authors),
                "authors": authors
            }


        except Exception as e:
            log_debug(f"Error listing philosophy authors: {str(e)}")
            return {
                "status": "error",
                "source": "Wikisource",
                "message": str(e)
            }

    async def get_philosophy_chapter(self, work_title: str, chapter: str, language: str = "en") -> Dict[str, Any]:
        """
        Retrieve a specific chapter from a philosophical work.

        Parameters:
        - work_title: Title of the work (e.g., 'The_Republic')
        - chapter: Chapter number or name (e.g., 'Book_I' or '1')
        - language: Wikisource language code (default: 'en')

        Returns:
        - JSON with chapter title, content, and navigation links
        """
        logger.info(f"Retrieving chapter '{chapter}' from '{work_title}'")

        try:
            # First get the page content to find the chapter section
            api_url = f"https://{language}.wikisource.org/w/api.php"
            params = {
                "action": "parse",
                "page": work_title,
                "format": "json",
                "prop": "sections"
            }
            response = requests.get(api_url, params=params)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikisource",
                    "message": f"Failed to retrieve chapter. Status code: {response.status_code}",
                    "work": work_title,
                    "chapter": chapter
                }


            # This is a simplified implementation - in a real implementation,
            # you would need to parse the sections and find the requested chapter
            # and return the chapter content
            return {
                "status": "success",
                "source": "Wikisource",
                "work": work_title,
                "chapter": chapter,
                "chapter_content": "This is a placeholder for the actual chapter content."
            }


        except Exception as e:
            log_debug(f"Error retrieving chapter: {str(e)}")
            return {
                "status": "error",
                "source": "Wikisource",
                "message": str(e),
                "work": work_title,
                "chapter": chapter
            }

    async def search_philosophy_quotes(self, query: str, author: Optional[str] = None, work: Optional[str] = None, language: str = "error", source: str = "Wikisource") -> Dict[str, Any]:
        """
        Search for philosophical quotes matching the query.

        Parameters:
        - query: Search terms to match in quotes
        - author: Optional author filter
        - work: Optional work title filter
        - language: Wikisource language code (default: 'en')

        Returns:
        - JSON with matching quotes and their sources
        """
        logger.info(f"Searching for quotes matching: {query}")
        if author:
            logger.info(f"Author filter: {author}")
        if work:
            logger.info(f"Work filter: {work}")

        try:
            # This is a simplified implementation that searches within a work
            search_terms = [f'"{query}"']
            if author:
                search_terms.append(f'author:"{author}"')
            if work:
                search_terms.append(f'incategory:"{work}"')

            search_query = ' '.join(search_terms)
            
            # In a real implementation, this would call the API with search_query
            # and parse the results to find matching quotes
            
            return {
                "status": "success",
                "source": "Wikisource",
                "query": query,
                "author": author or "N/A",
                "work": work or "N/A",
                "quotes": [
                    "This is a placeholder for actual quote search results.",
                    "In a real implementation, this would return matching quotes."
                ]
            }

        except Exception as e:
            log_debug(f"Error searching quotes: {str(e)}")
            return {
                "status": "error",
                "source": "Wikisource",
                "message": str(e),
                "query": query
            }
