#!/usr/bin/env python3
"""
Comprehensive test cho Cosmology Facebook Team
Test nhiều loại câu hỏi và đánh giá chất lượng output
"""

import sys
import time
import logging
from typing import List, Dict, Any

# Add current directory to path
sys.path.append('.')

# Setup logging
logging.basicConfig(level=logging.INFO)

def comprehensive_test():
    """Test toàn diện workflow với nhiều loại câu hỏi"""
    print("🧪 COMPREHENSIVE COSMOLOGY TEAM TEST")
    print("="*60)
    
    try:
        from cosmology_fb import CosmologyFbWorkflow
        
        # Tạo workflow
        print("📝 Initializing Cosmology Workflow...")
        workflow = CosmologyFbWorkflow()
        print("✅ Workflow initialized successfully!")
        
        # Test cases với độ khó khác nhau
        test_cases = [
            {
                "category": "Basic Concepts",
                "question": "What is dark matter?",
                "expected_keywords": ["dark matter", "invisible", "gravity", "galaxy", "universe"]
            },
            {
                "category": "Advanced Physics", 
                "question": "How do gravitational waves help us understand black hole mergers?",
                "expected_keywords": ["gravitational waves", "LIGO", "spacetime", "black hole", "merger"]
            },
            {
                "category": "Quantum Cosmology",
                "question": "What role do quantum fluctuations play in cosmic structure formation?",
                "expected_keywords": ["quantum", "fluctuations", "inflation", "CMB", "structure"]
            },
            {
                "category": "Current Research",
                "question": "What are the latest discoveries about dark energy?",
                "expected_keywords": ["dark energy", "expansion", "universe", "acceleration", "cosmological"]
            },
            {
                "category": "Complex Theory",
                "question": "How does the cosmic microwave background support the Big Bang theory?",
                "expected_keywords": ["CMB", "Big Bang", "radiation", "temperature", "universe"]
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔬 TEST {i}/{len(test_cases)}: {test_case['category']}")
            print(f"❓ Question: {test_case['question']}")
            print("⏳ Processing...")
            
            start_time = time.time()
            
            try:
                # Run workflow
                result = workflow.process_cosmology_query(test_case['question'])
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                # Analyze result
                analysis = analyze_result(result, test_case['expected_keywords'])
                
                test_result = {
                    "test_number": i,
                    "category": test_case['category'],
                    "question": test_case['question'],
                    "processing_time": processing_time,
                    "result_length": len(result),
                    "keyword_score": analysis['keyword_score'],
                    "has_hashtags": analysis['has_hashtags'],
                    "has_emojis": analysis['has_emojis'],
                    "has_call_to_action": analysis['has_call_to_action'],
                    "quality_score": calculate_quality_score(analysis, processing_time),
                    "result_preview": result[:200] + "..." if len(result) > 200 else result
                }
                
                results.append(test_result)
                
                print(f"✅ Completed in {processing_time:.2f}s")
                print(f"📊 Quality Score: {test_result['quality_score']:.1f}/10")
                print(f"📝 Result Preview: {test_result['result_preview']}")
                
            except Exception as e:
                print(f"❌ Test failed: {e}")
                results.append({
                    "test_number": i,
                    "category": test_case['category'],
                    "question": test_case['question'],
                    "error": str(e),
                    "quality_score": 0
                })
        
        # Generate summary report
        generate_summary_report(results)
        
        return results
        
    except Exception as e:
        print(f"❌ Comprehensive test failed: {e}")
        return []

def analyze_result(result: str, expected_keywords: List[str]) -> Dict[str, Any]:
    """Phân tích chất lượng của result"""
    result_lower = result.lower()
    
    # Check keywords
    found_keywords = [kw for kw in expected_keywords if kw.lower() in result_lower]
    keyword_score = len(found_keywords) / len(expected_keywords) * 100
    
    # Check Facebook post elements
    has_hashtags = '#' in result
    has_emojis = any(ord(char) > 127 for char in result)  # Simple emoji detection
    has_call_to_action = any(phrase in result_lower for phrase in [
        'comment', 'share', 'what do you think', 'drop', 'tell us', 'question'
    ])
    
    return {
        'keyword_score': keyword_score,
        'found_keywords': found_keywords,
        'has_hashtags': has_hashtags,
        'has_emojis': has_emojis,
        'has_call_to_action': has_call_to_action
    }

def calculate_quality_score(analysis: Dict[str, Any], processing_time: float) -> float:
    """Tính điểm chất lượng tổng thể"""
    score = 0
    
    # Keyword relevance (40%)
    score += analysis['keyword_score'] * 0.4 / 100
    
    # Facebook elements (30%)
    facebook_score = 0
    if analysis['has_hashtags']:
        facebook_score += 1
    if analysis['has_emojis']:
        facebook_score += 1
    if analysis['has_call_to_action']:
        facebook_score += 1
    score += (facebook_score / 3) * 0.3
    
    # Performance (20%)
    if processing_time < 30:
        performance_score = 1.0
    elif processing_time < 60:
        performance_score = 0.7
    else:
        performance_score = 0.3
    score += performance_score * 0.2
    
    # Content length (10%)
    # Assume good length is 500-2000 characters
    # This would need the result length, but we'll estimate
    score += 0.1  # Default good score for length
    
    return score * 10  # Convert to 0-10 scale

def generate_summary_report(results: List[Dict[str, Any]]):
    """Tạo báo cáo tổng kết"""
    print("\n" + "="*60)
    print("📋 COMPREHENSIVE TEST SUMMARY REPORT")
    print("="*60)
    
    if not results:
        print("❌ No test results to analyze")
        return
    
    # Overall statistics
    successful_tests = [r for r in results if 'error' not in r]
    failed_tests = [r for r in results if 'error' in r]
    
    print(f"📊 OVERALL STATISTICS:")
    print(f"✅ Successful Tests: {len(successful_tests)}/{len(results)}")
    print(f"❌ Failed Tests: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        avg_quality = sum(r['quality_score'] for r in successful_tests) / len(successful_tests)
        avg_time = sum(r['processing_time'] for r in successful_tests) / len(successful_tests)
        
        print(f"🎯 Average Quality Score: {avg_quality:.1f}/10")
        print(f"⏱️ Average Processing Time: {avg_time:.1f}s")
        
        # Category breakdown
        print(f"\n📈 PERFORMANCE BY CATEGORY:")
        categories = {}
        for result in successful_tests:
            cat = result['category']
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(result['quality_score'])
        
        for category, scores in categories.items():
            avg_score = sum(scores) / len(scores)
            print(f"  {category}: {avg_score:.1f}/10")
        
        # Best and worst performing tests
        best_test = max(successful_tests, key=lambda x: x['quality_score'])
        worst_test = min(successful_tests, key=lambda x: x['quality_score'])
        
        print(f"\n🏆 BEST PERFORMING TEST:")
        print(f"  Category: {best_test['category']}")
        print(f"  Score: {best_test['quality_score']:.1f}/10")
        print(f"  Time: {best_test['processing_time']:.1f}s")
        
        print(f"\n⚠️ LOWEST PERFORMING TEST:")
        print(f"  Category: {worst_test['category']}")
        print(f"  Score: {worst_test['quality_score']:.1f}/10")
        print(f"  Time: {worst_test['processing_time']:.1f}s")
    
    # Failed tests details
    if failed_tests:
        print(f"\n❌ FAILED TESTS DETAILS:")
        for test in failed_tests:
            print(f"  Test {test['test_number']}: {test['category']}")
            print(f"    Error: {test['error']}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if successful_tests:
        if avg_quality >= 8:
            print("  🎉 Excellent performance! Team is working very well.")
        elif avg_quality >= 6:
            print("  👍 Good performance with room for improvement.")
        else:
            print("  ⚠️ Performance needs improvement. Check tool configurations.")
        
        if avg_time > 45:
            print("  ⏱️ Consider optimizing processing time.")
        
        # Check specific issues
        hashtag_rate = sum(1 for r in successful_tests if r['has_hashtags']) / len(successful_tests)
        if hashtag_rate < 0.8:
            print("  #️⃣ Consider improving hashtag generation.")
        
        emoji_rate = sum(1 for r in successful_tests if r['has_emojis']) / len(successful_tests)
        if emoji_rate < 0.8:
            print("  😊 Consider improving emoji usage for engagement.")

def main():
    """Main function"""
    print("🚀 Starting Comprehensive Cosmology Team Test\n")
    
    results = comprehensive_test()
    
    if results:
        print("\n🎉 Comprehensive test completed!")
        return 0
    else:
        print("\n❌ Comprehensive test failed.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)
