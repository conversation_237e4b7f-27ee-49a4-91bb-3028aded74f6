from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class SacredTextsTool(Toolkit):
    """
    Sacred Texts Tool for searching religious and mythological scriptures from sacred-texts.com.
    """

    def __init__(self):
        super().__init__(
            name="Sacred Texts Tools",
            tools=[
                self.search_sacred_texts,
                self.get_text_content,
                self.browse_by_religion,
                self.search_by_author,
                self.get_popular_texts
            ]
        )

    async def search_sacred_texts(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Search sacred-texts.com for religious and mythological scriptures.

        Parameters:
        - query: Name of scripture, author, or tradition (e.g., 'Bhagavad Gita', 'Egyptian Book of the Dead', 'Norse Edda')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with search results including title, summary, category, and Sacred Texts URLs
        """
        logger.info(f"Searching Sacred Texts for: {query}")

        try:
            # sacred-texts.com không có <PERSON> ch<PERSON> thứ<PERSON>, sử dụng Google Custom Search hoặc web scraping đơn giản
            # Ở đây mô phỏng tìm kiếm bằng Google Custom Search (cần cấu hình API key để sử dụng thực tế)
            search_url = "https://www.googleapis.com/customsearch/v1"
            api_key = "YOUR_GOOGLE_API_KEY"  # Thay bằng API key thật nếu có
            cx = "YOUR_CUSTOM_SEARCH_ENGINE_ID"  # Thay bằng CSE ID thật nếu có

            params = {
                "q": f"site:sacred-texts.com {query}",
                "key": api_key,
                "cx": cx,
                "num": limit
            }

            response = requests.get(search_url, params=params)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Sacred Texts",
                    "message": f"Search API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            items = data.get("items", [])
            results = []
            for item in items:
                results.append({
                    "title": item.get("title"),
                    "snippet": item.get("snippet"),
                    "link": item.get("link"),
                    "displayLink": item.get("displayLink"),
                    "sacred_texts_url": item.get("link")
                })

            return {
                "status": "success",
                "source": "Sacred Texts",
                "query": query,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "Bhagavad Gita",
                    "Egyptian Book of the Dead",
                    "Norse Edda",
                    "Buddhist sutra",
                    "Christian apocrypha",
                    "Confucian Analects",
                    "mythology <culture>",
                    "scripture <religion>"
                ],
                "official_data_url": "https://www.sacred-texts.com/"
            }

        except Exception as e:
            log_debug(f"Error searching Sacred Texts: {str(e)}")
            return {
                "status": "error",
                "source": "Sacred Texts",
                "message": str(e),
                "query": query
            }

    async def get_text_content(self, text_url: str) -> Dict[str, Any]:
        """
        Lấy nội dung đầy đủ của một văn bản từ sacred-texts.com.

        Parameters:
        - text_url: URL đầy đủ đến văn bản trên sacred-texts.com

        Returns:
        - JSON chứa nội dung văn bản và siêu dữ liệu
        """
        logger.info(f"Đang lấy nội dung từ: {text_url}")

        try:
            # Kiểm tra xem URL có thuộc sacred-texts.com không
            if not text_url.startswith("https://www.sacred-texts.com/"):
                return {
                    "status": "error",
                    "source": "Sacred Texts",
                    "message": "URL không hợp lệ. Vui lòng sử dụng URL từ sacred-texts.com",
                    "text_url": text_url
                }
            
            # Thêm tiền tố nếu cần (kiểm tra lại vì đã kiểm tra ở trên)
            if not text_url.startswith("https://www.sacred-texts.com/"):
                text_url = f"https://www.sacred-texts.com/{text_url.lstrip('/')}"
            
            response = requests.get(text_url, timeout=15)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Sacred Texts",
                    "message": f"Không thể tải nội dung. Mã lỗi: {response.status_code}",
                    "text_url": text_url
                }
            
            from bs4 import BeautifulSoup
            
            # Phân tích HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Lấy tiêu đề
            title = soup.find('h1')
            title_text = title.get_text().strip() if title else "Không có tiêu đề"
            
            # Lấy tác giả (nếu có)
            author_tag = soup.find('h2')
            author = author_tag.get_text().replace("by", "").strip() if author_tag else "Không rõ tác giả"
            
            # Lấy nội dung chính
            content_div = soup.find('div', {'class': 'main'}) or soup.find('div', {'id': 'content'}) or soup
            
            # Xóa các phần không cần thiết
            for element in content_div(['script', 'style', 'nav', 'footer', 'header', 'iframe']):
                element.decompose()
            
            # Lấy văn bản thuần
            text_content = content_div.get_text('\n', strip=True)
            
            # Lấy tất cả các liên kết trong nội dung
            links = []
            for link in content_div.find_all('a', href=True):
                href = link['href']
                if not href.startswith('http'):
                    href = f"https://www.sacred-texts.com/{href.lstrip('/')}"
                links.append({
                    "text": link.get_text(strip=True)[:100],
                    "url": href
                })
            
            # Lấy hình ảnh (nếu có)
            images = []
            for img in content_div.find_all('img', src=True):
                src = img['src']
                if not src.startswith('http'):
                    src = f"https://www.sacred-texts.com/{src.lstrip('/')}"
                images.append({
                    "alt": img.get('alt', ''),
                    "src": src
                })
            
            return {
                "status": "success",
                "source": "Sacred Texts",
                "url": text_url,
                "title": title_text,
                "author": author,
                "content": text_content,
                "links": links[:10],  # Giới hạn số lượng liên kết
                "images": images[:5],  # Giới hạn số lượng hình ảnh
                "content_length": len(text_content),
                "extraction_time": response.elapsed.total_seconds()
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi lấy nội dung văn bản: {str(e)}")
            return {
                "status": "error",
                "source": "Sacred Texts",
                "message": str(e),
                "text_url": text_url
            }

    async def browse_by_religion(self, religion: str, limit: int = 5) -> Dict[str, Any]:
        """
        Duyệt văn bản theo tôn giáo hoặc truyền thống.

        Parameters:
        - religion: Tên tôn giáo hoặc truyền thống (ví dụ: 'buddhism', 'christianity', 'hindu')
        - limit: Số lượng kết quả tối đa (mặc định: 5)

        Returns:
        - JSON với danh sách các văn bản thuộc tôn giáo/truyền thống đó
        """
        logger.info(f"Duyệt văn bản theo tôn giáo: {religion}")

        try:
            # Tạo URL duyệt theo thư mục tôn giáo
            base_url = "https://www.sacred-texts.com/"
            
            # Danh sách các tôn giáo phổ biến và đường dẫn tương ứng
            religion_paths = {
                "buddhism": "bud/index.htm",
                "christianity": "chr/index.htm",
                "hinduism": "hin/index.htm",
                "islam": "isl/index.htm",
                "judaism": "jud/index.htm",
                "taoism": "tao/index.htm",
                "shinto": "shi/index.htm",
                "zoroastrianism": "zor/index.htm",
                "egypt": "egy/index.htm",
                "nordic": "neu/index.htm",
                "celtic": "neu/celt/index.htm",
                "greek": "cla/greek/index.htm",
                "roman": "cla/greek/index.htm",
                "native american": "nam/index.htm",
                "african": "afr/index.htm",
                "pacific": "pac/index.htm"
            }
            
            # Chuẩn hóa tên tôn giáo
            religion_lower = religion.lower()
            religion_path = religion_paths.get(religion_lower)
            
            if not religion_path:
                # Thử tìm tôn giáo phù hợp nhất
                for key in religion_paths:
                    if religion_lower in key:
                        religion_path = religion_paths[key]
                        break
            
            if not religion_path:
                return {
                    "status": "error",
                    "source": "Sacred Texts",
                    "message": "Không tìm thấy tôn giáo/truyền thống phù hợp. Vui lòng thử các giá trị sau:" + 
                             ", ".join(religion_paths.keys()),
                    "religion": religion
                }
            
            # Tạo URL đầy đủ
            browse_url = f"{base_url}{religion_path}"
            
            # Lấy nội dung trang
            response = requests.get(browse_url, timeout=15)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Sacred Texts",
                    "message": f"Không thể tải trang tôn giáo. Mã lỗi: {response.status_code}",
                    "religion": religion,
                    "url": browse_url
                }
            
            from bs4 import BeautifulSoup
            
            # Phân tích HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Lấy tiêu đề trang
            title = soup.find('h1')
            title_text = title.get_text().strip() if title else f"Văn bản {religion.capitalize()}"
            
            # Tìm tất cả các liên kết trong nội dung chính
            main_content = soup.find('div', {'class': 'main'}) or soup
            links = []
            
            for link in main_content.find_all('a', href=True):
                if len(links) >= limit:
                    break
                    
                href = link['href']
                if not href.startswith('http'):
                    href = f"{base_url.rstrip('/')}/{href.lstrip('/')}"
                
                # Bỏ qua các liên kết không phải văn bản
                if any(x in href.lower() for x in ['index.htm', '#', 'mailto:', 'javascript:']):
                    continue
                
                links.append({
                    "title": link.get_text(strip=True)[:100],
                    "url": href
                })
            
            return {
                "status": "success",
                "source": "Sacred Texts",
                "religion": religion,
                "title": title_text,
                "url": browse_url,
                "texts_count": len(links),
                "texts": links,
                "suggested_searches": [
                    f"{religion} sacred texts",
                    f"{religion} religious scriptures",
                    f"{religion} holy books"
                ]
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi duyệt văn bản theo tôn giáo: {str(e)}")
            return {
                "status": "error",
                "source": "Sacred Texts",
                "message": str(e),
                "religion": religion
            }

    async def search_by_author(self, author: str, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm văn bản theo tác giả.

        Parameters:
        - author: Tên tác giả cần tìm (ví dụ: 'Plato', 'Confucius')
        - limit: Số lượng kết quả tối đa (mặc định: 5)

        Returns:
        - JSON với danh sách các văn bản của tác giả
        """
        logger.info(f"Tìm kiếm văn bản theo tác giả: {author}")

        try:
            # Sử dụng Google Custom Search API (cần cấu hình trước)
            search_url = "https://www.googleapis.com/customsearch/v1"
            api_key = "YOUR_GOOGLE_API_KEY"  # Cần thay thế bằng API key thật
            cx = "YOUR_CUSTOM_SEARCH_ENGINE_ID"  # Cần thay thế bằng Search Engine ID
            
            params = {
                "q": f'site:sacred-texts.com inauthor:"{author}"',
                "key": api_key,
                "cx": cx,
                "num": limit
            }
            
            response = requests.get(search_url, params=params, timeout=15)
            
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Sacred Texts",
                    "message": f"Lỗi tìm kiếm. Mã lỗi: {response.status_code}",
                    "author": author
                }
            
            data = response.json()
            results = []
            
            for item in data.get('items', [])[:limit]:
                results.append({
                    "title": item.get('title'),
                    "snippet": item.get('snippet'),
                    "link": item.get('link'),
                    "displayLink": item.get('displayLink')
                })
            
            return {
                "status": "success",
                "source": "Sacred Texts",
                "author": author,
                "results_count": len(results),
                "results": results,
                "search_url": f"https://www.sacred-texts.com/author/{author.lower().replace(' ', '-')}",
                "note": "Kết quả được cung cấp bởi Google Custom Search API. Vui lòng cấu hình API key để sử dụng tính năng này."
            }
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm theo tác giả: {str(e)}")
            return {
                "status": "error",
                "source": "Sacred Texts",
                "message": str(e),
                "author": author
            }
