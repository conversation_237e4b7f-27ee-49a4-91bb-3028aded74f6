"""
Module làm sạch text cho Qwen output và tối ưu hóa cho Qdrant storage.
"""

import re
import logging
from typing import Optional, Dict, Any, List
import html

# Setup logging
logger = logging.getLogger(__name__)

class TextCleaner:
    """Class để làm sạch text từ Qwen output"""
    
    def __init__(self):
        """Initialize text cleaner"""
        # Patterns để remove thinking tags
        self.thinking_patterns = [
            r'<think>.*?</think>',  # <think>...</think>
            r'<thinking>.*?</thinking>',  # <thinking>...</thinking>
            r'<thought>.*?</thought>',  # <thought>...</thought>
            r'<reasoning>.*?</reasoning>',  # <reasoning>...</reasoning>
        ]
        
        # Patterns để clean markdown artifacts
        self.markdown_patterns = [
            r'```[a-zA-Z]*\n',  # Code block start
            r'\n```',  # Code block end
            r'^\s*\*\*\*+\s*$',  # Horizontal rules
            r'^\s*---+\s*$',  # Horizontal rules
        ]
        
        # Patterns để clean HTML artifacts
        self.html_patterns = [
            r'<[^>]+>',  # HTML tags
            r'&[a-zA-Z]+;',  # HTML entities
        ]
        
        # Patterns để clean extra whitespace
        self.whitespace_patterns = [
            r'\n\s*\n\s*\n',  # Multiple newlines
            r'[ \t]+',  # Multiple spaces/tabs
            r'^\s+',  # Leading whitespace
            r'\s+$',  # Trailing whitespace
        ]
    
    def clean_qwen_output(self, text: str) -> str:
        """
        Làm sạch output từ Qwen model
        
        Args:
            text: Raw text từ Qwen
            
        Returns:
            Cleaned text
        """
        if not text or not isinstance(text, str):
            return ""
        
        cleaned = text
        
        # 1. Remove thinking tags (case insensitive, multiline)
        for pattern in self.thinking_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE | re.DOTALL)
        
        # 2. Clean HTML entities
        cleaned = html.unescape(cleaned)
        
        # 3. Remove HTML tags
        for pattern in self.html_patterns:
            cleaned = re.sub(pattern, '', cleaned)
        
        # 4. Clean markdown artifacts
        for pattern in self.markdown_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.MULTILINE)
        
        # 5. Normalize whitespace
        cleaned = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned)  # Max 2 newlines
        cleaned = re.sub(r'[ \t]+', ' ', cleaned)  # Single spaces
        cleaned = cleaned.strip()  # Remove leading/trailing whitespace
        
        return cleaned
    
    def clean_for_qdrant(self, text: str, max_length: int = 8000) -> str:
        """
        Làm sạch text để lưu vào Qdrant
        
        Args:
            text: Text cần clean
            max_length: Độ dài tối đa
            
        Returns:
            Text đã clean và truncate
        """
        # Clean basic text
        cleaned = self.clean_qwen_output(text)
        
        # Remove special characters that might cause issues
        cleaned = re.sub(r'[^\w\s\.\,\!\?\-\:\;\(\)\[\]\{\}\"\'\/\@\#\$\%\&\*\+\=\<\>\~\`\|\\\n]', '', cleaned)
        
        # Truncate if too long
        if len(cleaned) > max_length:
            cleaned = cleaned[:max_length].rsplit(' ', 1)[0] + "..."
        
        return cleaned
    
    def extract_main_content(self, text: str) -> str:
        """
        Trích xuất nội dung chính từ text (loại bỏ metadata, headers, etc.)
        
        Args:
            text: Input text
            
        Returns:
            Main content
        """
        cleaned = self.clean_qwen_output(text)
        
        # Remove common prefixes/suffixes
        prefixes_to_remove = [
            r'^(User:|Assistant:|Human:|AI:|Bot:)\s*',
            r'^(Question:|Answer:|Response:)\s*',
            r'^(Input:|Output:)\s*',
        ]
        
        for prefix in prefixes_to_remove:
            cleaned = re.sub(prefix, '', cleaned, flags=re.IGNORECASE | re.MULTILINE)
        
        # Remove timestamps and metadata
        cleaned = re.sub(r'\d{4}-\d{2}-\d{2}[\s\T]\d{2}:\d{2}:\d{2}', '', cleaned)
        cleaned = re.sub(r'\[.*?\]', '', cleaned)  # Remove bracketed content
        
        return cleaned.strip()
    
    def prepare_for_embedding(self, text: str) -> str:
        """
        Chuẩn bị text cho embedding (remove formatting, keep semantic content)
        
        Args:
            text: Input text
            
        Returns:
            Text ready for embedding
        """
        cleaned = self.extract_main_content(text)
        
        # Remove formatting but keep structure
        cleaned = re.sub(r'\*\*(.*?)\*\*', r'\1', cleaned)  # Bold
        cleaned = re.sub(r'\*(.*?)\*', r'\1', cleaned)  # Italic
        cleaned = re.sub(r'`(.*?)`', r'\1', cleaned)  # Code
        cleaned = re.sub(r'#{1,6}\s*', '', cleaned)  # Headers
        cleaned = re.sub(r'^\s*[\-\*\+]\s*', '', cleaned, flags=re.MULTILINE)  # List items
        cleaned = re.sub(r'^\s*\d+\.\s*', '', cleaned, flags=re.MULTILINE)  # Numbered lists
        
        # Normalize punctuation
        cleaned = re.sub(r'[\.]{2,}', '.', cleaned)
        cleaned = re.sub(r'[!]{2,}', '!', cleaned)
        cleaned = re.sub(r'[\?]{2,}', '?', cleaned)
        
        return cleaned.strip()
    
    def split_into_chunks(self, text: str, chunk_size: int = 1000, overlap: int = 100) -> List[str]:
        """
        Chia text thành chunks để lưu vào Qdrant
        
        Args:
            text: Input text
            chunk_size: Kích thước chunk
            overlap: Overlap giữa các chunks
            
        Returns:
            List of chunks
        """
        cleaned = self.prepare_for_embedding(text)
        
        if len(cleaned) <= chunk_size:
            return [cleaned] if cleaned else []
        
        chunks = []
        start = 0
        
        while start < len(cleaned):
            end = start + chunk_size
            
            # Try to break at sentence boundary
            if end < len(cleaned):
                # Look for sentence endings
                sentence_end = max(
                    cleaned.rfind('.', start, end),
                    cleaned.rfind('!', start, end),
                    cleaned.rfind('?', start, end)
                )
                
                if sentence_end > start + chunk_size // 2:
                    end = sentence_end + 1
                else:
                    # Fallback to word boundary
                    word_end = cleaned.rfind(' ', start, end)
                    if word_end > start + chunk_size // 2:
                        end = word_end
            
            chunk = cleaned[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
            if start >= len(cleaned):
                break
        
        return chunks

# Singleton instance
_text_cleaner_instance = None

def get_text_cleaner() -> TextCleaner:
    """
    Get singleton instance of TextCleaner
    
    Returns:
        TextCleaner instance
    """
    global _text_cleaner_instance
    if _text_cleaner_instance is None:
        _text_cleaner_instance = TextCleaner()
    return _text_cleaner_instance

# Convenience functions
def clean_qwen_output(text: str) -> str:
    """Clean Qwen model output"""
    cleaner = get_text_cleaner()
    return cleaner.clean_qwen_output(text)

def clean_for_qdrant(text: str, max_length: int = 8000) -> str:
    """Clean text for Qdrant storage"""
    cleaner = get_text_cleaner()
    return cleaner.clean_for_qdrant(text, max_length)

def prepare_for_embedding(text: str) -> str:
    """Prepare text for embedding"""
    cleaner = get_text_cleaner()
    return cleaner.prepare_for_embedding(text)

def split_into_chunks(text: str, chunk_size: int = 1000, overlap: int = 100) -> List[str]:
    """Split text into chunks"""
    cleaner = get_text_cleaner()
    return cleaner.split_into_chunks(text, chunk_size, overlap)

if __name__ == "__main__":
    # Test the text cleaner
    test_text = """
    <think>
    This is thinking content that should be removed.
    </think>
    
    **This is the actual content** that should be kept.
    
    Some more content with <html>tags</html> and &amp; entities.
    """
    
    cleaner = get_text_cleaner()
    cleaned = cleaner.clean_qwen_output(test_text)
    print("Cleaned text:", cleaned)
