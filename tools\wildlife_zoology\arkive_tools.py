from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class ArkiveTool(Toolkit):
    """
    Arkive Tool for searching archived wildlife species information and media.
    """

    def __init__(self):
        super().__init__(
            name="Arkive Search Tool",
            tools=[self.search_arkive]
        )

    async def search_arkive(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Search Arkive (archived) for species information and media.

        Parameters:
        - query: Search query using scientific or common names (e.g., 'Panthera tigris', 'giant panda')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with search results including species information, descriptions, images, and archived URLs
        """
        logger.info(f"Searching Arkive for: {query}")

        try:
            # Arkive.org đã ngừng hoạt động, sử dụng Internet Archive để tìm kiếm
            search_url = "https://web.archive.org/cdx/search/cdx"
            params = {
                "url": "www.arkive.org/*" + query.replace(" ", "-").lower() + "/*",
                "output": "json",
                "limit": limit,
                "fl": "original,timestamp"
            }
            response = requests.get(search_url, params=params)

            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Arkive (Archive.org)",
                    "message": f"Archive search API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            # Bỏ dòng đầu tiên là header
            for item in data[1:]:
                original_url, timestamp = item
                archive_url = f"https://web.archive.org/web/{timestamp}/{original_url}"
                # Có thể mở rộng: crawl thêm nội dung mô tả, hình ảnh từ archive_url nếu cần
                results.append({
                    "archived_url": archive_url,
                    "original_url": original_url,
                    "timestamp": timestamp
                })

            return {
                "status": "success",
                "source": "Arkive (Archive.org)",
                "query": query,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching Arkive: {str(e)}")
            return {
                "status": "error",
                "source": "Arkive (Archive.org)",
                "message": str(e),
                "query": query
            }
