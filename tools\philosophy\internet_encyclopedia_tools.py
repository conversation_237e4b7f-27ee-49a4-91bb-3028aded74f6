from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json
import re
import aiohttp
import asyncio
from urllib.parse import urljoin, quote
from bs4 import BeautifulSoup

from agno.tools import Toolkit
from agno.utils.log import logger

# Các hằng số
IEP_BASE_URL = "https://iep.utm.edu"
IEP_SEARCH_URL = f"{IEP_BASE_URL}/search/"
CACHE_EXPIRY_DAYS = 7

@dataclass
class IEPArticle:
    """Lớp đại diện cho một bài viết trong IEP"""
    title: str
    url: str
    summary: str = ""
    authors: List[str] = None
    categories: List[str] = None
    last_updated: str = ""
    word_count: int = 0

class InternetEncyclopediaPhilosophyTool(Toolkit):
    """
    Công cụ tìm kiếm thông tin triết học từ Internet Encyclopedia of Philosophy (IEP)
    """

    def __init__(self):
        super().__init__(
            name="Công cụ IEP Triết học",
            description="""
            Công cụ tìm kiếm và phân tích thông tin triết học từ 
            Internet Encyclopedia of Philosophy (IEP). Hỗ trợ tìm kiếm 
            bài viết, triết gia, trường phái và khái niệm triết học.
            """,
            tools=[
                self.search_iep_articles,
                self.get_philosopher_info,
                self.get_philosophy_schools,
                self.get_concept_details,
                self.get_recommended_articles
            ]
        )
        self.session = None
        self.cache = {}
        self._load_cache()

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def _load_cache(self) -> None:
        """Tải dữ liệu cache từ file"""
        try:
            with open(".iep_cache.json", 'r', encoding='utf-8') as f:
                self.cache = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            self.cache = {}

    def _save_cache(self) -> None:
        """Lưu dữ liệu cache vào file"""
        with open(".iep_cache.json", 'w', encoding='utf-8') as f:
            json.dump(self.cache, f, ensure_ascii=False, indent=2)

    def _get_cache(self, key: str) -> Any:
        """Lấy dữ liệu từ cache"""
        cached = self.cache.get(key)
        if cached and datetime.now().timestamp() < cached.get('expires', 0):
            return cached['data']
        return None

    def _set_cache(self, key: str, data: Any, ttl: int = 604800) -> None:
        """Lưu dữ liệu vào cache (mặc định 7 ngày)"""
        self.cache[key] = {
            'data': data,
            'expires': datetime.now().timestamp() + ttl
        }
        self._save_cache()

    async def _make_request(self, url: str, use_cache: bool = True) -> str:
        """Gửi yêu cầu HTTP với cơ chế cache"""
        cache_key = f"req_{url}"
        
        # Kiểm tra cache
        if use_cache:
            cached = self._get_cache(cache_key)
            if cached is not None:
                return cached
        
        # Gửi yêu cầu mới
        try:
            async with self.session.get(url) as response:
                response.raise_for_status()
                content = await response.text()
                
                # Lưu vào cache
                self._set_cache(cache_key, content)
                return content
                
        except Exception as e:
            logger.error(f"Lỗi khi gửi yêu cầu đến {url}: {str(e)}")
            raise

    async def search_iep_articles(
        self, 
        query: str, 
        category: Optional[str] = None,
        limit: int = 5,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Tìm kiếm bài viết trong IEP
        
        Parameters:
        - query: Từ khóa tìm kiếm
        - category: Danh mục tìm kiếm (tùy chọn)
        - limit: Số lượng kết quả tối đa
        - use_cache: Sử dụng cache hay không
        
        Returns:
        - Dict chứa kết quả tìm kiếm
        """
        logger.info(f"Tìm kiếm IEP: {query}, danh mục={category}")
        
        try:
            # Tạo URL tìm kiếm
            search_params = {"q": query, "page": 1}
            if category:
                search_params["cat"] = category
                
            search_url = f"{IEP_SEARCH_URL}?{urlencode(search_params)}"
            
            # Gửi yêu cầu tìm kiếm
            content = await self._make_request(search_url, use_cache)
            soup = BeautifulSoup(content, 'html.parser')
            
            # Phân tích kết quả
            results = []
            result_items = soup.select('div.entry-content li')
            
            for item in result_items[:limit]:
                link = item.find('a')
                if not link:
                    continue
                    
                title = link.get_text(strip=True)
                url = urljoin(IEP_BASE_URL, link['href'])
                
                # Lấy thông tin bổ sung nếu cần
                summary = ""
                if item.span:
                    summary = item.span.get_text(strip=True)
                
                results.append({
                    "title": title,
                    "url": url,
                    "summary": summary
                })
            
            return {
                "status": "success",
                "query": query,
                "category": category,
                "results_count": len(results),
                "results": results,
                "search_url": search_url,
                "suggested_searches": self._get_search_suggestions()
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi tìm kiếm IEP: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "query": query,
                "category": category
            }
    
    async def get_philosopher_info(
        self, 
        philosopher_name: str,
        detailed: bool = False
    ) -> Dict[str, Any]:
        """
        Lấy thông tin về một triết gia
        
        Parameters:
        - philosopher_name: Tên triết gia
        - detailed: Có lấy thông tin chi tiết hay không
        """
        try:
            # Tìm kiếm bài viết về triết gia
            search_result = await self.search_iep_articles(philosopher_name, "philosophers")
            
            if search_result["results_count"] == 0:
                return {
                    "status": "not_found",
                    "message": f"Không tìm thấy thông tin về triết gia: {philosopher_name}",
                    "suggestions": self._get_philosopher_suggestions()
                }
            
            # Lấy thông tin chi tiết nếu cần
            result = search_result["results"][0]
            if detailed:
                article_content = await self._get_article_details(result["url"])
                result.update(article_content)
            
            return {
                "status": "success",
                "philosopher": philosopher_name,
                "data": result,
                "related_philosophers": self._get_related_philosophers(philosopher_name)
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy thông tin triết gia: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "philosopher": philosopher_name
            }
    
    async def _get_article_details(self, url: str) -> Dict[str, Any]:
        """Lấy thông tin chi tiết của bài viết"""
        try:
            content = await self._make_request(url)
            soup = BeautifulSoup(content, 'html.parser')
            
            # Trích xuất thông tin
            article = {
                "content": "\n\n".join([p.get_text() for p in soup.select('div.entry-content p')]),
                "authors": [a.get_text(strip=True) for a in soup.select('div.entry-author a')],
                "last_updated": soup.select_one('div.entry-date').get_text(strip=True) if soup.select_one('div.entry-date') else "",
                "sections": [
                    {"title": h2.get_text(strip=True), "id": h2.get('id', '')} 
                    for h2 in soup.select('div.entry-content h2')
                ],
                "related_articles": [
                    {"title": a.get_text(strip=True), "url": urljoin(IEP_BASE_URL, a['href'])}
                    for a in soup.select('div.related-articles a')
                ]
            }
            return article
            
        except Exception as e:
            logger.error(f"Lỗi khi lấy chi tiết bài viết: {str(e)}")
            return {}
    
    def _get_search_suggestions(self) -> List[Dict[str, str]]:
        """Trả về danh sách gợi ý tìm kiếm"""
        return [
            {"query": "existentialism", "description": "Chủ nghĩa hiện sinh"},
            {"query": "Plato", "description": "Triết gia Hy Lạp cổ đại"},
            {"query": "ethics", "description": "Đạo đức học"},
            {"query": "metaphysics", "description": "Siêu hình học"},
            {"query": "philosophy of mind", "description": "Triết học tinh thần"},
            {"query": "Eastern philosophy", "description": "Triết học phương Đông"},
            {"query": "political philosophy", "description": "Triết học chính trị"}
        ]
    
    def _get_philosopher_suggestions(self) -> List[str]:
        """Trả về danh sách gợi ý triết gia"""
        return [
            "Plato", "Aristotle", "Immanuel Kant", "Friedrich Nietzsche",
            "Jean-Paul Sartre", "Simone de Beauvoir", "Ludwig Wittgenstein",
            "Martin Heidegger", "Confucius", "Laozi"
        ]
    
    def _get_related_philosophers(self, philosopher: str) -> List[str]:
        """Trả về danh sách triết gia liên quan"""
        # Đây là dữ liệu mẫu, có thể mở rộng thành cơ sở dữ liệu thực
        related_map = {
            "Plato": ["Aristotle", "Socrates", "Plotinus", "Augustine"],
            "Aristotle": ["Plato", "Thomas Aquinas", "John Locke", "Immanuel Kant"],
            "Immanuel Kant": ["David Hume", "G.W.F. Hegel", "Arthur Schopenhauer"],
            "Friedrich Nietzsche": ["Arthur Schopenhauer", "Martin Heidegger", "Michel Foucault"],
            "Jean-Paul Sartre": ["Simone de Beauvoir", "Albert Camus", "Maurice Merleau-Ponty"]
        }
        return related_map.get(philosopher, [])
    
    async def get_philosophy_schools(self) -> Dict[str, Any]:
        """Lấy danh sách các trường phái triết học"""
        try:
            schools = [
                {"name": "Stoicism", "description": "Chủ nghĩa Khắc kỷ"},
                {"name": "Existentialism", "description": "Chủ nghĩa Hiện sinh"},
                {"name": "Pragmatism", "description": "Chủ nghĩa Thực dụng"},
                {"name": "Phenomenology", "description": "Hiện tượng học"},
                {"name": "Analytic Philosophy", "description": "Triết học Phân tích"},
                {"name": "Continental Philosophy", "description": "Triết học Lục địa"},
                {"name": "Confucianism", "description": "Nho giáo"},
                {"name": "Daoism", "description": "Đạo giáo"}
            ]
            return {
                "status": "success",
                "count": len(schools),
                "schools": schools
            }
        except Exception as e:
            error_msg = f"Lỗi khi lấy danh sách trường phái: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg
            }
    
    async def get_concept_details(self, concept: str) -> Dict[str, Any]:
        """Lấy thông tin chi tiết về một khái niệm triết học"""
        try:
            search_result = await self.search_iep_articles(concept)
            
            if search_result["results_count"] == 0:
                return {
                    "status": "not_found",
                    "message": f"Không tìm thấy thông tin về khái niệm: {concept}",
                    "suggested_concepts": [
                        "free will", "ethics", "metaphysics", "epistemology",
                        "aesthetics", "logic", "political philosophy"
                    ]
                }
            
            # Lấy thông tin chi tiết của bài viết đầu tiên
            result = search_result["results"][0]
            article_content = await self._get_article_details(result["url"])
            result.update(article_content)
            
            return {
                "status": "success",
                "concept": concept,
                "data": result
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy thông tin khái niệm: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "concept": concept
            }
    
    async def get_recommended_articles(self, interest: str = None) -> Dict[str, Any]:
        """
        Lấy danh sách bài viết được đề xuất dựa trên sở thích
        
        Parameters:
        - interest: Lĩnh vực quan tâm (tùy chọn)
        """
        try:
            # Đây là dữ liệu mẫu, có thể thay thế bằng thuật toán đề xuất thực tế
            recommendations = {
                "ethics": [
                    {"title": "Virtue Ethics", "url": f"{IEP_BASE_URL}/virtue-ethics/"},
                    {"title": "Utilitarianism", "url": f"{IEP_BASE_URL}/util-a/"},
                    {"title": "Deontological Ethics", "url": f"{IEP_BASE_URL}/ethics/"}
                ],
                "metaphysics": [
                    {"title": "Time", "url": f"{IEP_BASE_URL}/time/"},
                    {"title": "Causation", "url": f"{IEP_BASE_URL}/causation/"},
                    {"title": "Free Will", "url": f"{IEP_BASE_URL}/freewill/"}
                ],
                "epistemology": [
                    {"title": "Skepticism", "url": f"{IEP_BASE_URL}/skeptimo/"},
                    {"title": "Theories of Truth", "url": f"{IEP_BASE_URL}/truth/"},
                    {"title": "Rationalism vs. Empiricism", "url": f"{IEP_BASE_URL}/rationalis/"}
                ]
            }
            
            if interest and interest.lower() in recommendations:
                articles = recommendations[interest.lower()]
            else:
                # Trả về tất cả nếu không có sở thích cụ thể
                articles = [item for sublist in recommendations.values() for item in sublist][:5]
            
            return {
                "status": "success",
                "interest": interest or "general",
                "articles": articles,
                "available_interests": list(recommendations.keys())
            }
            
        except Exception as e:
            error_msg = f"Lỗi khi lấy bài viết đề xuất: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "status": "error",
                "message": error_msg,
                "interest": interest
            }