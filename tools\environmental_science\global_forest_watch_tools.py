from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class GlobalForestWatchTool(Toolkit):
    """
    Global Forest Watch Tool cho tìm kiếm dữ liệu rừng, mô<PERSON> tr<PERSON>, cả<PERSON> bá<PERSON> mấ<PERSON> rừng, và bản đồ từ Global Forest Watch.
    """

    def __init__(self):
        super().__init__(
            name="Global Forest Watch Search Tool",
            tools=[self.search_gfw]
        )

    async def search_gfw(self, query: str, country: Optional[str] = None, year: Optional[int] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm dữ liệu rừng, cảnh báo môi trường từ Global Forest Watch.

        Parameters:
        - query: Từ khóa về rừng, môi trường, cảnh báo (ví dụ: 'deforestation', 'tree cover loss', 'fire alerts')
        - country: Tê<PERSON> quốc gia hoặc mã ISO (ví dụ: 'Brazil', 'IDN')
        - year: <PERSON><PERSON><PERSON> dữ liệu (ví dụ: 2022)
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với dữ liệu rừng, cảnh báo, bản đồ, và link Global Forest Watch
        """
        logger.info(f"Tìm kiếm Global Forest Watch: query={query}, country={country}, year={year}")

        try:
            # GFW API endpoint (ví dụ: tree cover loss, fire alerts, country stats)
            base_url = "https://data-api.globalforestwatch.org"
            results = []

            # Tree cover loss (ví dụ)
            if "tree cover loss" in query.lower() or "deforestation" in query.lower():
                endpoint = f"{base_url}/v1/loss"
                params = {}
                if country:
                    params["iso"] = country
                if year:
                    params["year"] = year
                params["limit"] = limit
                resp = requests.get(endpoint, params=params, timeout=15)
                if resp.status_code == 200:
                    data = resp.json()
                    for item in data.get("data", [])[:limit]:
                        results.append({
                            "type": "tree_cover_loss",
                            "year": item.get("year"),
                            "area_ha": item.get("area__ha"),
                            "iso": item.get("iso"),
                            "gfw_url": f"https://www.globalforestwatch.org/dashboards/country/{item.get('iso', '').upper()}/"
                        })
            # Fire alerts (ví dụ)
            elif "fire" in query.lower():
                endpoint = f"{base_url}/v1/umd-fire-alerts"
                params = {}
                if country:
                    params["iso"] = country
                if year:
                    params["year"] = year
                params["limit"] = limit
                resp = requests.get(endpoint, params=params, timeout=15)
                if resp.status_code == 200:
                    data = resp.json()
                    for item in data.get("data", [])[:limit]:
                        results.append({
                            "type": "fire_alert",
                            "date": item.get("date"),
                            "latitude": item.get("lat"),
                            "longitude": item.get("lon"),
                            "iso": item.get("iso"),
                            "gfw_url": f"https://www.globalforestwatch.org/dashboards/country/{item.get('iso', '').upper()}/"
                        })
            # Country stats (ví dụ)
            else:
                endpoint = f"{base_url}/v1/country-stats"
                params = {}
                if country:
                    params["iso"] = country
                params["limit"] = limit
                resp = requests.get(endpoint, params=params, timeout=15)
                if resp.status_code == 200:
                    data = resp.json()
                    for item in data.get("data", [])[:limit]:
                        results.append({
                            "type": "country_stats",
                            "iso": item.get("iso"),
                            "tree_cover": item.get("tree_cover"),
                            "tree_cover_loss": item.get("tree_cover_loss"),
                            "tree_cover_gain": item.get("tree_cover_gain"),
                            "gfw_url": f"https://www.globalforestwatch.org/dashboards/country/{item.get('iso', '').upper()}/"
                        })

            return {
                "status": "success",
                "source": "Global Forest Watch",
                "query": query,
                "country": country,
                "year": year,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "tree cover loss <country> <year>",
                    "deforestation <country> <year>",
                    "fire alerts <country> <year>",
                    "forest gain <country>",
                    "environmental stats <country>"
                ],
                "official_data_url": "https://www.globalforestwatch.org/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Global Forest Watch: {str(e)}")
            return {
                "status": "error",
                "source": "Global Forest Watch",
                "message": str(e),
                "query": query
            }
