from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class PubMedTool(Toolkit):
    """
    PubMed Tool cho tìm kiếm bài báo y khoa, ng<PERSON><PERSON><PERSON> cứu, tổng quan từ PubMed.
    """

    def __init__(self):
        super().__init__(
            name="PubMed Search Tool",
            tools=[self.search_pubmed]
        )

    async def search_pubmed(self, query: str, limit: int = 10, sort: str = "relevance") -> Dict[str, Any]:
        """
        Tìm kiếm PubMed cho bài báo y khoa, nghi<PERSON><PERSON> cứu, tổng quan.

        Parameters:
        - query: Từ khóa tìm kiếm (ví dụ: 'covid-19 treatment', 'diabetes mellitus', 'cancer immunotherapy')
        - limit: Số lượng kết quả tối đa (default: 10)
        - sort: Sắp xếp kết quả ('relevance', 'pub+date', ...)

        Returns:
        - <PERSON><PERSON><PERSON> với tiêu đề, t<PERSON><PERSON>, n<PERSON><PERSON>, t<PERSON><PERSON> tắ<PERSON>, PMID, link PubMed
        """
        logger.info(f"Tìm kiếm PubMed: query={query}, limit={limit}, sort={sort}")

        try:
            # Bước 1: ESearch để lấy danh sách PMID
            esearch_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi"
            esearch_params = {
                "db": "pubmed",
                "term": query,
                "retmax": limit,
                "retmode": "json",
                "sort": sort
            }
            esearch_resp = requests.get(esearch_url, params=esearch_params)
            if esearch_resp.status_code != 200:
                return {
                    "status": "error",
                    "source": "PubMed",
                    "message": f"ESearch API trả về mã lỗi {esearch_resp.status_code}",
                    "query": query
                }
            idlist = esearch_resp.json().get("esearchresult", {}).get("idlist", [])
            if not idlist:
                return {
                    "status": "success",
                    "source": "PubMed",
                    "query": query,
                    "results_count": 0,
                    "results": []
                }

            # Bước 2: ESummary để lấy thông tin chi tiết
            esummary_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi"
            esummary_params = {
                "db": "pubmed",
                "id": ",".join(idlist),
                "retmode": "json"
            }
            esummary_resp = requests.get(esummary_url, params=esummary_params)
            if esummary_resp.status_code != 200:
                return {
                    "status": "error",
                    "source": "PubMed",
                    "message": f"ESummary API trả về mã lỗi {esummary_resp.status_code}",
                    "query": query
                }
            summaries = esummary_resp.json().get("result", {})
            results = []
            for pmid in idlist:
                doc = summaries.get(pmid, {})
                title = doc.get("title")
                authors = [a.get("name") for a in doc.get("authors", [])]
                pubdate = doc.get("pubdate")
                source = doc.get("source")
                doi = doc.get("elocationid")
                article_url = f"https://pubmed.ncbi.nlm.nih.gov/{pmid}/"
                # Lấy abstract (nếu cần, dùng EFetch)
                abstract = None
                try:
                    efetch_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi"
                    efetch_params = {
                        "db": "pubmed",
                        "id": pmid,
                        "retmode": "xml"
                    }
                    efetch_resp = requests.get(efetch_url, params=efetch_params)
                    if efetch_resp.status_code == 200:
                        import xml.etree.ElementTree as ET
                        root = ET.fromstring(efetch_resp.text)
                        abst = root.find(".//AbstractText")
                        if abst is not None:
                            abstract = abst.text
                except Exception as abs_err:
                    log_debug(f"Lỗi lấy abstract PubMed: {str(abs_err)}")

                results.append({
                    "pmid": pmid,
                    "title": title,
                    "authors": authors,
                    "pubdate": pubdate,
                    "journal": source,
                    "doi": doi,
                    "abstract": abstract,
                    "pubmed_url": article_url
                })

            return {
                "status": "success",
                "source": "PubMed",
                "query": query,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "<disease> treatment",
                    "<drug> side effects",
                    "<clinical trial>",
                    "<symptom> diagnosis",
                    "<public health topic>"
                ],
                "official_data_url": "https://pubmed.ncbi.nlm.nih.gov/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm PubMed: {str(e)}")
            return {
                "status": "error",
                "source": "PubMed",
                "message": str(e),
                "query": query
            }
