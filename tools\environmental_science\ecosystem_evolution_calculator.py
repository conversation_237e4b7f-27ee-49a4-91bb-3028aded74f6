# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math
from datetime import datetime

class EcosystemEvolutionCalculator(Toolkit):
    """
    Ecosystem Evolution Calculator cho tính toán ecosystem divergence, biodiversity evolution và environmental change.
    """

    def __init__(self, enable_calculations: bool = True, **kwargs):
        super().__init__(
            name="ecosystem_evolution_calculator",
            **kwargs
        )
        
        # Ecosystem succession stages
        self.succession_stages = {
            "pioneer": {"diversity": 0.2, "stability": 0.3, "productivity": 0.4},
            "early_succession": {"diversity": 0.5, "stability": 0.4, "productivity": 0.7},
            "mid_succession": {"diversity": 0.8, "stability": 0.6, "productivity": 0.9},
            "late_succession": {"diversity": 0.9, "stability": 0.8, "productivity": 0.8},
            "climax": {"diversity": 1.0, "stability": 1.0, "productivity": 0.7}
        }
        
        # Environmental change rates (per decade)
        self.change_rates = {
            "climate_change": 0.15,
            "habitat_fragmentation": 0.25,
            "species_introduction": 0.3,
            "pollution_impact": 0.2,
            "human_disturbance": 0.4,
            "natural_succession": 0.05
        }
        
        # Biodiversity metrics
        self.biodiversity_factors = {
            "species_richness": 0.3,
            "genetic_diversity": 0.25,
            "functional_diversity": 0.2,
            "ecosystem_services": 0.15,
            "resilience_capacity": 0.1
        }
        
        if enable_calculations:
            self.register(self.calculate_ecosystem_divergence)
            self.register(self.estimate_biodiversity_evolution)
            self.register(self.analyze_environmental_change)
            self.register(self.predict_ecosystem_trends)

    def calculate_ecosystem_divergence(self, ecosystem1: str, ecosystem2: str,
                                     separation_years: int = None,
                                     divergence_driver: str = "climate") -> str:
        """
        Tính toán ecosystem divergence và environmental adaptation.
        
        Args:
            ecosystem1: Hệ sinh thái thứ nhất
            ecosystem2: Hệ sinh thái thứ hai
            separation_years: Thời gian tách biệt (years)
            divergence_driver: Yếu tố thúc đẩy divergence
            
        Returns:
            Chuỗi JSON chứa tính toán ecosystem divergence
        """
        log_debug(f"Calculating ecosystem divergence between {ecosystem1} and {ecosystem2}")
        
        try:
            if separation_years is None:
                separation_years = 100  # Default 100 years
            
            # Calculate divergence metrics
            change_rate = self.change_rates.get(f"{divergence_driver}_change", 0.2)
            divergence_index = 1 - math.exp(-change_rate * (separation_years / 10))
            
            # Environmental gradient analysis
            environmental_distance = separation_years * change_rate / 10
            ecological_similarity = max(0, 1 - environmental_distance)
            
            # Species composition changes
            species_turnover = min(1.0, separation_years * 0.01)
            endemic_species_ratio = separation_years * 0.005
            
            # Ecosystem function divergence
            functional_divergence = {
                "primary_productivity": round(divergence_index * 0.6, 3),
                "nutrient_cycling": round(divergence_index * 0.5, 3),
                "water_regulation": round(divergence_index * 0.7, 3),
                "carbon_storage": round(divergence_index * 0.4, 3)
            }
            
            # Adaptation mechanisms
            adaptation_mechanisms = {
                "phenotypic_plasticity": round(divergence_index * 0.8, 3),
                "genetic_adaptation": round(divergence_index * 0.3, 3),
                "behavioral_changes": round(divergence_index * 0.9, 3),
                "community_restructuring": round(divergence_index * 0.7, 3)
            }
            
            # Environmental pressures
            environmental_pressures = {
                "temperature_change": 0.8 if divergence_driver == "climate" else 0.3,
                "precipitation_change": 0.7 if divergence_driver == "climate" else 0.2,
                "habitat_modification": 0.9 if divergence_driver == "human_disturbance" else 0.4,
                "species_interactions": 0.6
            }
            
            # Resilience assessment
            resilience_analysis = {
                "ecosystem_stability": max(0.1, 1 - divergence_index),
                "recovery_capacity": max(0.2, 1 - (divergence_index * 0.8)),
                "adaptive_capacity": round(divergence_index * 0.6, 3),
                "vulnerability_level": "High" if divergence_index > 0.7 else "Moderate"
            }
            
            result = {
                "ecosystem_comparison": {
                    "ecosystem1": ecosystem1,
                    "ecosystem2": ecosystem2,
                    "separation_years": separation_years,
                    "divergence_driver": divergence_driver
                },
                "divergence_analysis": {
                    "divergence_index": round(divergence_index, 3),
                    "environmental_distance": round(environmental_distance, 3),
                    "ecological_similarity": round(ecological_similarity, 3),
                    "change_rate": change_rate
                },
                "species_dynamics": {
                    "species_turnover": round(species_turnover, 3),
                    "endemic_species_ratio": round(endemic_species_ratio, 3),
                    "biodiversity_change": "Declining" if divergence_index > 0.6 else "Stable"
                },
                "functional_divergence": functional_divergence,
                "adaptation_mechanisms": adaptation_mechanisms,
                "environmental_pressures": environmental_pressures,
                "resilience_analysis": resilience_analysis,
                "conservation_priority": self._assess_conservation_priority(divergence_index, ecosystem1),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error calculating ecosystem divergence: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate ecosystem divergence: {str(e)}"
            }, indent=4)

    def estimate_biodiversity_evolution(self, habitat_type: str, evolution_years: int = 50,
                                      disturbance_level: str = "moderate") -> str:
        """
        Ước tính biodiversity evolution và species dynamics.
        """
        log_debug(f"Estimating biodiversity evolution for {habitat_type} over {evolution_years} years")
        
        try:
            # Disturbance level parameters
            disturbance_params = {
                "low": {"extinction_rate": 0.001, "speciation_rate": 0.002, "migration_rate": 0.01},
                "moderate": {"extinction_rate": 0.005, "speciation_rate": 0.003, "migration_rate": 0.02},
                "high": {"extinction_rate": 0.02, "speciation_rate": 0.001, "migration_rate": 0.05},
                "extreme": {"extinction_rate": 0.05, "speciation_rate": 0.0005, "migration_rate": 0.1}
            }
            
            params = disturbance_params.get(disturbance_level, disturbance_params["moderate"])
            
            # Calculate biodiversity trajectory
            net_change_rate = params["speciation_rate"] + params["migration_rate"] - params["extinction_rate"]
            biodiversity_change_factor = (1 + net_change_rate) ** evolution_years
            
            # Species dynamics over time
            species_dynamics = []
            initial_species = 100  # Baseline species count
            
            for decade in range(0, evolution_years, 10):
                decade_species = initial_species * ((1 + net_change_rate) ** decade)
                extinctions = int(decade_species * params["extinction_rate"] * 10)
                new_species = int(decade_species * params["speciation_rate"] * 10)
                immigrants = int(decade_species * params["migration_rate"] * 10)
                
                species_dynamics.append({
                    "decade": decade,
                    "total_species": int(decade_species),
                    "extinctions": extinctions,
                    "new_species": new_species,
                    "immigrant_species": immigrants,
                    "net_change": new_species + immigrants - extinctions
                })
            
            # Functional group analysis
            functional_groups = {
                "primary_producers": {
                    "change_factor": biodiversity_change_factor * 0.8,
                    "stability": "High" if disturbance_level == "low" else "Moderate"
                },
                "herbivores": {
                    "change_factor": biodiversity_change_factor * 0.9,
                    "stability": "Moderate" if disturbance_level in ["low", "moderate"] else "Low"
                },
                "carnivores": {
                    "change_factor": biodiversity_change_factor * 1.2,
                    "stability": "Low" if disturbance_level in ["high", "extreme"] else "Moderate"
                },
                "decomposers": {
                    "change_factor": biodiversity_change_factor * 0.7,
                    "stability": "High"
                }
            }
            
            # Ecosystem services impact
            ecosystem_services = {
                "pollination": round(max(0.3, 1 - (params["extinction_rate"] * evolution_years * 5)), 2),
                "pest_control": round(max(0.4, 1 - (params["extinction_rate"] * evolution_years * 3)), 2),
                "soil_formation": round(max(0.6, 1 - (params["extinction_rate"] * evolution_years * 2)), 2),
                "climate_regulation": round(max(0.5, 1 - (params["extinction_rate"] * evolution_years * 4)), 2)
            }
            
            # Genetic diversity trends
            genetic_diversity = {
                "population_bottlenecks": int(evolution_years * params["extinction_rate"] * 20),
                "gene_flow_reduction": round(params["extinction_rate"] * 100, 1),
                "inbreeding_risk": "High" if params["extinction_rate"] > 0.01 else "Low",
                "adaptive_potential": "Reduced" if params["extinction_rate"] > 0.015 else "Maintained"
            }
            
            result = {
                "biodiversity_evolution": {
                    "habitat_type": habitat_type,
                    "evolution_years": evolution_years,
                    "disturbance_level": disturbance_level,
                    "biodiversity_change_factor": round(biodiversity_change_factor, 2)
                },
                "species_dynamics": species_dynamics,
                "functional_groups": functional_groups,
                "ecosystem_services": ecosystem_services,
                "genetic_diversity": genetic_diversity,
                "conservation_implications": self._generate_conservation_implications(params, evolution_years),
                "management_recommendations": self._generate_management_recommendations(disturbance_level, habitat_type),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error estimating biodiversity evolution: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to estimate biodiversity evolution: {str(e)}"
            }, indent=4)

    def analyze_environmental_change(self, change_type: str, impact_duration_years: int = 30,
                                   ecosystem_vulnerability: str = "moderate") -> str:
        """
        Phân tích environmental change impacts và ecosystem responses.
        """
        log_debug(f"Analyzing environmental change: {change_type}")
        
        try:
            # Change impact parameters
            vulnerability_params = {
                "low": {"sensitivity": 0.3, "exposure": 0.4, "adaptive_capacity": 0.8},
                "moderate": {"sensitivity": 0.6, "exposure": 0.6, "adaptive_capacity": 0.5},
                "high": {"sensitivity": 0.8, "exposure": 0.8, "adaptive_capacity": 0.3},
                "extreme": {"sensitivity": 0.9, "exposure": 0.9, "adaptive_capacity": 0.1}
            }
            
            vuln_params = vulnerability_params.get(ecosystem_vulnerability, vulnerability_params["moderate"])
            
            # Calculate impact severity
            impact_severity = (vuln_params["sensitivity"] * vuln_params["exposure"]) / vuln_params["adaptive_capacity"]
            impact_severity = min(2.0, impact_severity)  # Cap at 2.0
            
            # Temporal impact phases
            impact_phases = {
                "immediate": {
                    "duration_years": min(5, impact_duration_years * 0.2),
                    "impact_level": round(impact_severity * 0.8, 2),
                    "characteristics": ["Acute stress", "Population declines", "Behavioral changes"]
                },
                "short_term": {
                    "duration_years": min(15, impact_duration_years * 0.5),
                    "impact_level": round(impact_severity * 1.0, 2),
                    "characteristics": ["Community restructuring", "Range shifts", "Adaptation responses"]
                },
                "long_term": {
                    "duration_years": impact_duration_years * 0.3,
                    "impact_level": round(impact_severity * 0.6, 2),
                    "characteristics": ["Evolutionary responses", "New equilibrium", "Recovery processes"]
                }
            }
            
            # Ecosystem response mechanisms
            response_mechanisms = {
                "resistance": {
                    "effectiveness": round(vuln_params["adaptive_capacity"], 2),
                    "duration": "Short-term",
                    "examples": ["Physiological tolerance", "Behavioral avoidance", "Microhabitat use"]
                },
                "resilience": {
                    "effectiveness": round(vuln_params["adaptive_capacity"] * 0.8, 2),
                    "duration": "Medium-term", 
                    "examples": ["Population recovery", "Community reassembly", "Functional redundancy"]
                },
                "transformation": {
                    "effectiveness": round(vuln_params["adaptive_capacity"] * 0.6, 2),
                    "duration": "Long-term",
                    "examples": ["Novel ecosystems", "Species replacement", "Functional shifts"]
                }
            }
            
            # Cascading effects
            cascading_effects = {
                "trophic_cascades": "Likely" if impact_severity > 1.2 else "Possible",
                "habitat_modification": "Significant" if impact_severity > 1.0 else "Moderate",
                "species_interactions": "Disrupted" if impact_severity > 1.5 else "Altered",
                "ecosystem_services": "Compromised" if impact_severity > 1.3 else "Maintained"
            }
            
            # Recovery potential
            recovery_assessment = {
                "recovery_time": f"{max(10, int(impact_severity * 15))} years",
                "recovery_completeness": "Partial" if impact_severity > 1.5 else "Full",
                "alternative_states": "Possible" if impact_severity > 1.8 else "Unlikely",
                "intervention_needs": "High" if impact_severity > 1.6 else "Moderate"
            }
            
            result = {
                "environmental_change": {
                    "change_type": change_type,
                    "impact_duration_years": impact_duration_years,
                    "ecosystem_vulnerability": ecosystem_vulnerability,
                    "impact_severity": round(impact_severity, 2)
                },
                "vulnerability_assessment": vuln_params,
                "impact_phases": impact_phases,
                "response_mechanisms": response_mechanisms,
                "cascading_effects": cascading_effects,
                "recovery_assessment": recovery_assessment,
                "mitigation_strategies": self._generate_mitigation_strategies(change_type, impact_severity),
                "monitoring_priorities": self._identify_monitoring_priorities(change_type, vuln_params),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error analyzing environmental change: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze environmental change: {str(e)}"
            }, indent=4)

    def predict_ecosystem_trends(self, ecosystem_type: str, prediction_years: int = 50,
                               climate_scenario: str = "moderate_warming") -> str:
        """
        Dự đoán ecosystem trends và future states.
        """
        log_debug(f"Predicting ecosystem trends for {ecosystem_type} over {prediction_years} years")
        
        try:
            # Climate scenario parameters
            scenario_params = {
                "low_warming": {"temp_change": 1.5, "precip_change": 0.05, "extreme_events": 0.2},
                "moderate_warming": {"temp_change": 2.5, "precip_change": 0.1, "extreme_events": 0.4},
                "high_warming": {"temp_change": 4.0, "precip_change": 0.15, "extreme_events": 0.7},
                "extreme_warming": {"temp_change": 6.0, "precip_change": 0.25, "extreme_events": 1.0}
            }
            
            params = scenario_params.get(climate_scenario, scenario_params["moderate_warming"])
            
            # Ecosystem trajectory predictions
            trajectory_predictions = {
                "species_composition": {
                    "change_magnitude": round(params["temp_change"] * 0.15, 2),
                    "direction": "Thermophilic species increase",
                    "timeline": f"{prediction_years // 3} years for major shifts"
                },
                "productivity": {
                    "change_magnitude": round(params["temp_change"] * 0.1 - params["extreme_events"] * 0.2, 2),
                    "direction": "Variable depending on water availability",
                    "timeline": f"{prediction_years // 4} years for detectable changes"
                },
                "carbon_storage": {
                    "change_magnitude": round(params["temp_change"] * 0.12, 2),
                    "direction": "Potential decrease in many ecosystems",
                    "timeline": f"{prediction_years // 2} years for significant changes"
                }
            }
            
            # Threshold analysis
            threshold_analysis = {
                "tipping_points": {
                    "probability": round(params["temp_change"] * 0.2, 2),
                    "potential_triggers": ["Drought frequency", "Fire regimes", "Pest outbreaks"],
                    "consequences": "Rapid state transitions"
                },
                "critical_thresholds": {
                    "temperature": f"+{params['temp_change']}°C regional warming",
                    "precipitation": f"{params['precip_change']*100}% change in annual precipitation",
                    "disturbance": f"{params['extreme_events']*100}% increase in extreme events"
                }
            }
            
            # Adaptation scenarios
            adaptation_scenarios = [
                {
                    "scenario": "Natural Adaptation",
                    "probability": 0.6,
                    "description": "Ecosystem adapts through natural processes",
                    "timeline": f"{prediction_years} years",
                    "success_likelihood": "Moderate"
                },
                {
                    "scenario": "Assisted Migration",
                    "probability": 0.3,
                    "description": "Human-assisted species and ecosystem migration",
                    "timeline": f"{prediction_years // 2} years",
                    "success_likelihood": "High with intervention"
                },
                {
                    "scenario": "Novel Ecosystem",
                    "probability": 0.4,
                    "description": "Formation of new ecosystem configurations",
                    "timeline": f"{prediction_years // 3} years",
                    "success_likelihood": "Variable"
                }
            ]
            
            # Management implications
            management_implications = {
                "conservation_priorities": self._identify_conservation_priorities(ecosystem_type, params),
                "restoration_needs": self._assess_restoration_needs(params, prediction_years),
                "monitoring_requirements": self._define_monitoring_requirements(ecosystem_type, params),
                "policy_recommendations": self._generate_policy_recommendations(climate_scenario, prediction_years)
            }
            
            result = {
                "prediction_parameters": {
                    "ecosystem_type": ecosystem_type,
                    "prediction_years": prediction_years,
                    "climate_scenario": climate_scenario,
                    "temperature_change": params["temp_change"]
                },
                "trajectory_predictions": trajectory_predictions,
                "threshold_analysis": threshold_analysis,
                "adaptation_scenarios": adaptation_scenarios,
                "management_implications": management_implications,
                "uncertainty_factors": [
                    "Climate model accuracy",
                    "Species interaction complexity",
                    "Human intervention effectiveness",
                    "Extreme event frequency"
                ],
                "research_priorities": self._identify_research_priorities(ecosystem_type, climate_scenario),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error predicting ecosystem trends: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to predict ecosystem trends: {str(e)}"
            }, indent=4)

    # Helper methods
    def _assess_conservation_priority(self, divergence: float, ecosystem: str) -> Dict[str, str]:
        """Assess conservation priority based on divergence."""
        if divergence > 0.8:
            return {"priority": "Critical", "urgency": "Immediate action required"}
        elif divergence > 0.6:
            return {"priority": "High", "urgency": "Action needed within 5 years"}
        else:
            return {"priority": "Moderate", "urgency": "Monitoring and planning"}

    def _generate_conservation_implications(self, params: Dict, years: int) -> List[str]:
        """Generate conservation implications."""
        implications = ["Habitat protection", "Species monitoring"]
        if params["extinction_rate"] > 0.01:
            implications.extend(["Captive breeding programs", "Genetic rescue"])
        return implications

    def _generate_management_recommendations(self, disturbance: str, habitat: str) -> List[str]:
        """Generate management recommendations."""
        recommendations = ["Reduce human disturbance", "Maintain connectivity"]
        if disturbance in ["high", "extreme"]:
            recommendations.extend(["Active restoration", "Assisted migration"])
        return recommendations

    def _generate_mitigation_strategies(self, change_type: str, severity: float) -> List[str]:
        """Generate mitigation strategies."""
        strategies = ["Habitat protection", "Corridor establishment"]
        if severity > 1.5:
            strategies.extend(["Active intervention", "Species translocation"])
        return strategies

    def _identify_monitoring_priorities(self, change_type: str, params: Dict) -> List[str]:
        """Identify monitoring priorities."""
        return [
            "Population trends",
            "Habitat quality",
            "Species interactions",
            "Environmental conditions"
        ]

    def _identify_conservation_priorities(self, ecosystem: str, params: Dict) -> List[str]:
        """Identify conservation priorities."""
        return [
            "Protect climate refugia",
            "Maintain habitat connectivity",
            "Monitor keystone species",
            "Preserve genetic diversity"
        ]

    def _assess_restoration_needs(self, params: Dict, years: int) -> Dict[str, str]:
        """Assess restoration needs."""
        return {
            "urgency": "High" if params["temp_change"] > 3.0 else "Moderate",
            "scope": "Landscape-scale" if years > 30 else "Local",
            "approach": "Adaptive management"
        }

    def _define_monitoring_requirements(self, ecosystem: str, params: Dict) -> Dict[str, str]:
        """Define monitoring requirements."""
        return {
            "frequency": "Annual" if params["temp_change"] > 2.5 else "Biennial",
            "indicators": "Multi-trophic",
            "technology": "Remote sensing + field surveys"
        }

    def _generate_policy_recommendations(self, scenario: str, years: int) -> List[str]:
        """Generate policy recommendations."""
        return [
            "Strengthen protected area networks",
            "Implement climate adaptation strategies",
            "Enhance international cooperation",
            "Invest in ecosystem restoration"
        ]

    def _identify_research_priorities(self, ecosystem: str, scenario: str) -> List[str]:
        """Identify research priorities."""
        return [
            "Climate-ecosystem interactions",
            "Species adaptation mechanisms",
            "Ecosystem service valuation",
            "Restoration effectiveness"
        ]
