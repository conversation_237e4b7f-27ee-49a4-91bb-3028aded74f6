from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class WikipediaEvolutionTool(Toolkit):
    """
    Wikipedia Evolution Tool for searching evolutionary biology concepts and summaries.
    """

    def __init__(self):
        super().__init__(
            name="Wikipedia Evolution Search Tool",
            tools=[self.search_wikipedia_evo]
        )

    async def search_wikipedia_evo(self, query: str, language: str = "en") -> Dict[str, Any]:
        """
        Search Wikipedia for evolutionary biology concepts, events, or species.

        Parameters:
        - query: Evolutionary concept, event, or species (e.g., 'genetic drift', 'Cambrian explosion', 'mammalian evolution')
        - language: Wikipedia language code (default: 'en')

        Returns:
        - JSON with summary, page URL, and related topics
        """
        logger.info(f"Searching Wikipedia ({language}) for: {query}")

        try:
            # Wikipedia API endpoint
            api_url = f"https://{language}.wikipedia.org/api/rest_v1/page/summary/{query.replace(' ', '_')}"
            response = requests.get(api_url)

            if response.status_code == 404:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": "No article found for query",
                    "query": query
                }
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": f"Wikipedia API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            summary = data.get("extract")
            page_url = data.get("content_urls", {}).get("desktop", {}).get("page")
            title = data.get("title")
            thumbnail = data.get("thumbnail", {}).get("source")

            # Optionally, get related topics (using search API)
            related = []
            try:
                search_url = f"https://{language}.wikipedia.org/w/api.php"
                search_params = {
                    "action": "query",
                    "list": "search",
                    "srsearch": query,
                    "format": "json",
                    "srlimit": 5
                }
                search_resp = requests.get(search_url, params=search_params)
                if search_resp.status_code == 200:
                    search_data = search_resp.json()
                    for item in search_data.get("query", {}).get("search", []):
                        if item.get("title") != title:
                            related.append(item.get("title"))
            except Exception as rel_err:
                log_debug(f"Error fetching related Wikipedia topics: {str(rel_err)}")

            return {
                "status": "success",
                "source": "Wikipedia",
                "query": query,
                "title": title,
                "summary": summary,
                "page_url": page_url,
                "thumbnail": thumbnail,
                "related_topics": related
            }

        except Exception as e:
            log_debug(f"Error searching Wikipedia: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "query": query
            }
