import requests
import json
from typing import Dict, List, Optional, Any
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class WikipediaConspiracyTools(Toolkit):
    """
    Tools for searching and retrieving conspiracy theory information from Wikipedia.
    
    This tool allows querying Wikipedia for information about various conspiracy theories,
    providing structured data about their history, claims, and related information.
    """
    
    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(name="wikipedia_conspiracy_tools", **kwargs)
        self.base_url = "https://en.wikipedia.org/w/api.php"
        if enable_search:
            self.register(self.search_conspiracies)
    
    def search_conspiracies(self, query: str, limit: int = 5) -> str:
        """
        Search for conspiracy theories on Wikipedia.
        
        Args:
            query (str): Search query (e.g., "moon landing hoax", "9/11 conspiracy")
            limit (int, optional): Maximum number of results to return. Defaults to 5.
            
        Returns:
            str: JSON string containing search results with article details
            
        Example:
            search_conspiracies("JFK assassination", 3)
        """
        log_debug(f"Searching Wikipedia for conspiracy theories: {query}")
        
        params = {
            "action": "query",
            "format": "json",
            "list": "search",
            "srsearch": f"{query} conspiracy theory",
            "srlimit": limit,
            "srprop": "snippet|titlesnippet",
            "utf8": "",
            "formatversion": "2"
        }
        
        try:
            response = requests.get(
                self.base_url,
                params=params,
                headers={"User-Agent": "AgnoConspiracyTools/1.0"},
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            
            results = []
            if 'query' in data and 'search' in data['query']:
                for item in data['query']['search']:
                    results.append({
                        "title": item.get('title', ''),
                        "url": f"https://en.wikipedia.org/wiki/{item['title'].replace(' ', '_')}",
                        "snippet": item.get('snippet', '').replace('<span class="searchmatch">', '').replace('</span>', ''),
                        "pageid": item.get('pageid')
                    })
            
            # If no results found, provide some default conspiracy theory suggestions
            if not results:
                return self._get_default_results(query)
            
            return json.dumps({
                "status": "success",
                "source": "Wikipedia",
                "query": query,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)
            
        except requests.RequestException as e:
            logger.error(f"Error querying Wikipedia: {e}")
            return self._get_error_response(query, str(e))
    
    def _get_default_results(self, query: str) -> str:
        """Return default results when no search results are found."""
        default_results = [
            {
                "title": "List of conspiracy theories",
                "url": "https://en.wikipedia.org/wiki/List_of_conspiracy_theories",
                "snippet": "A list of notable conspiracy theories from various fields.",
                "pageid": ""
            },
            {
                "title": "Conspiracy theory",
                "url": "https://en.wikipedia.org/wiki/Conspiracy_theory",
                "snippet": "Overview of conspiracy theories and their characteristics.",
                "pageid": ""
            }
        ]
        
        return json.dumps({
            "status": "success",
            "source": "Wikipedia",
            "query": query,
            "message": "No direct results found. Here are some general conspiracy theory resources.",
            "results": default_results,
            "result_count": len(default_results)
        }, indent=2, ensure_ascii=False)
    
    def _get_error_response(self, query: str, error_msg: str) -> str:
        """Return a structured error response."""
        return json.dumps({
            "status": "error",
            "source": "Wikipedia",
            "query": query,
            "message": f"Failed to retrieve results: {error_msg}",
            "results": []
        }, indent=2, ensure_ascii=False)
