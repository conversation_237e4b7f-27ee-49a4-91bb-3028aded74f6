import asyncio
from typing import Iterator

from agno.agent import Agent
from agno.models.ollama import Ollama
from tools.astronomy.nasa_ads_tools import NasaAdsTools
from tools.writer.writer_research import WriterResearchTools
from agno.team.team import Team
from agno.memory.v2.memory import Memory
from agno.storage.agent.sqlite import SqliteAgentStorage
from agno.knowledge.agent import AgentKnowledge
from agno.utils.log import logger
import logging


knowledge = AgentKnowledge(
    knowledge_base="tmp/research_agent_storage.db",
    table_name="research_agent_storage",
)


memory = Memory(model=Ollama(id="qwen3:4b"))


research_agent = Agent(
    model=Ollama(id="qwen3:4b"),
    name="Research Agent",
    description="A research agent that can search by NASA ADS and writer a research report based on the findings like a professional.",
    storage=SqliteAgentStorage(
        db_file="tmp/research_agent_storage.db",
        table_name="research_agent_storage",
    ),
    tools=[NasaAdsTools()],
    instructions=("""
        You are a research agent that can search for academic papers and summarize them.
        Use the tools provided to find relevant papers and summarize their findings.
        If you find a paper that is relevant to the user's query, summarize its findings.
        If you do not find a relevant paper, say "No relevant papers found."
    """),
    memory=memory,
    markdown=True,
    add_datetime_to_instructions=True,
    show_tool_calls=True,
)

writer_agent = Agent(
    model=Ollama(id="qwen3:4b"),
    name="Writer Agent",
    knowledge=knowledge,
    storage=SqliteAgentStorage(
        db_file="tmp/writer_agent_storage.db",
        table_name="writer_agent_storage",
    ),
    tools=[WriterResearchTools()],
    instructions=("""
        You are a writing agent that can assist with generating high-quality research reports.
        Your knowledge base contains in research_agent_storage.db.
        Use the knowledge base to find relevant information and examples.
        Use the WriterResearchTools to enhance the report writing process.
    """),
    memory=memory,
    markdown=True,
    show_tool_calls=True,
    add_datetime_to_instructions=True,
)

research_team = Team(
    name="Research Team",
    model=Ollama(id="qwen3:4b"),
    mode="route",
    storage=SqliteAgentStorage(
        db_file="tmp/research_team_storage.db",
        table_name="research_team_storage",
    ),
    members=[research_agent, writer_agent],
    instructions=("""
        You are a team of research and writing agents.
        First, the research agent will search for relevant academic papers using the NasaAdsTools and summarize their findings.
        Then, the writing agent will assist in generating a high-quality research report based on the findings.
        The research agent will use the WriterResearchTools to enhance the report writing process.
        The team will also ensure that the report meets quality standards and is well-structured.
        The team will coordinate and ensure that the research and writing processes are efficient and effective.
    """),
    memory=memory,
    markdown=True,
    add_datetime_to_instructions=True,
    show_members_responses=True,
)

def run_team(team, user_input):
    try:
        # Use print_response for synchronous CLI output (per Agno context7 docs)
        team.print_response(user_input)
    except Exception as e:
        logging.error(f"Error running team: {e}")
        print(f"An error occurred while running the team: {e}")


def chat_loop():
    try:
        while True:
            user_input = input("User: ")
            if user_input.lower() in ["exit", "quit"]:
                print("Exiting chat loop.")
                break
            team = research_team  # create a new Team instance for each input
            run_team(team, user_input)
            print("User input sent to research team.")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    chat_loop()
