import requests
import json
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus, urljoin
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class CIAFOIAMysteryTools(Toolkit):
    """
    Công cụ tìm kiếm và truy xuất tài liệu đã giải mật từ CIA FOIA Reading Room.
    
    Cung cấp quyền truy cập vào các tài liệu đã được giải mật liên quan đến các hiện tượng b<PERSON>,
    thí nghiệm b<PERSON> mật, và các sự kiện khó giải thích khác từ kho lưu trữ của CIA.
    
    Keyword gợi ý: "UFO declassified documents", "remote viewing experiments",
    "MKUltra program", "paranormal research CIA", "declassified psychic files"
    """
    
    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(name="cia_foia_tools", **kwargs)
        self.base_url = "https://www.cia.gov/readingroom/"
        self.search_url = "https://www.cia.gov/readingroom/search/site/"
        self.headers = {
            "User-Agent": "MysteryResearchBot/1.0 (https://example.com; <EMAIL>)",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
        }
        if enable_search:
            self.register(self.search_documents)
            self.register(self.get_popular_searches)
    
    def search_documents(self, query: str, max_results: int = 5) -> str:
        """
        Tìm kiếm tài liệu đã giải mật từ CIA FOIA Reading Room.
        
        Args:
            query (str): Từ khóa tìm kiếm (ví dụ: "UFO documents", "remote viewing")
            max_results (int, optional): Số lượng kết quả tối đa. Mặc định: 5.
            
        Returns:
            str: Chuỗi JSON chứa kết quả tìm kiếm
            
        Ví dụ:
            search_documents("UFO documents", 3)
        """
        log_debug(f"Tìm kiếm tài liệu CIA FOIA: {query}")
        
        try:
            search_query = quote_plus(query)
            full_url = f"{self.search_url}{search_query}"
            
            response = requests.get(
                full_url,
                headers=self.headers,
                timeout=20
            )
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # Tìm các kết quả tìm kiếm
            search_results = soup.select('.search-results .search-result')
            
            for result in search_results[:max_results]:
                title_elem = result.select_one('h3 a')
                if not title_elem:
                    continue
                    
                title = title_elem.get_text(strip=True)
                url = title_elem.get('href', '')
                if url and not url.startswith('http'):
                    url = urljoin(self.base_url, url)
                
                # Lấy mô tả
                snippet_elem = result.select_one('.search-snippet')
                snippet = snippet_elem.get_text(strip=True) if snippet_elem else ""
                
                # Lấy ngày phát hành (nếu có)
                date_elem = result.select_one('.search-created')
                date_published = date_elem.get_text(strip=True).replace('Date:', '').strip() if date_elem else ""
                
                # Lấy loại tài liệu (nếu có)
                doc_type_elem = result.select_one('.search-type')
                doc_type = doc_type_elem.get_text(strip=True).replace('Type:', '').strip() if doc_type_elem else ""
                
                results.append({
                    "title": title,
                    "url": url,
                    "snippet": snippet,
                    "date_published": date_published,
                    "document_type": doc_type,
                    "source": "CIA FOIA Reading Room"
                })
            
            # Nếu không có kết quả, trả về kết quả mặc định
            if not results:
                return self._get_default_results(query)
            
            return json.dumps({
                "status": "success",
                "source": "CIA FOIA",
                "query": query,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)
            
        except requests.RequestException as e:
            logger.error(f"Lỗi khi tìm kiếm tài liệu CIA FOIA: {e}")
            return self._get_error_response(query, str(e))
    
    def get_popular_searches(self, category: str = "all", max_results: int = 5) -> str:
        """
        Lấy danh sách các tìm kiếm phổ biến từ CIA FOIA Reading Room.
        
        Args:
            category (str, optional): Danh mục tìm kiếm (all, ufo, mind_control, etc.). Mặc định: "all".
            max_results (int, optional): Số lượng kết quả tối đa. Mặc định: 5.
            
        Returns:
            str: Chuỗi JSON chứa danh sách tìm kiếm phổ biến
            
        Ví dụ:
            get_popular_searches("ufo", 3)
        """
        log_debug(f"Lấy danh sách tìm kiếm phổ biến - Danh mục: {category}")
        
        try:
            # Danh sách tìm kiếm phổ biến được xác định trước
            popular_searches = {
                "all": [
                    {"query": "UFO", "count": 1250, "category": "ufo"},
                    {"query": "Remote Viewing", "count": 980, "category": "psychic"},
                    {"query": "MKUltra", "count": 750, "category": "mind_control"},
                    {"query": "Stargate Project", "count": 620, "category": "psychic"},
                    {"query": "Project Blue Book", "count": 580, "category": "ufo"}
                ],
                "ufo": [
                    {"query": "UFO Sightings", "count": 1250, "category": "ufo"},
                    {"query": "Project Blue Book", "count": 980, "category": "ufo"},
                    {"query": "Roswell", "count": 870, "category": "ufo"},
                    {"query": "Extraterrestrial Technology", "count": 650, "category": "ufo"},
                    {"query": "Nimitz UFO Incident", "count": 430, "category": "ufo"}
                ],
                "psychic": [
                    {"query": "Remote Viewing", "count": 980, "category": "psychic"},
                    {"query": "Stargate Project", "count": 750, "category": "psychic"},
                    {"query": "Psychic Spying", "count": 520, "category": "psychic"},
                    {"query": "ESP Research", "count": 480, "category": "psychic"},
                    {"query": "Paranormal Phenomena", "count": 390, "category": "psychic"}
                ],
                "mind_control": [
                    {"query": "MKUltra", "count": 920, "category": "mind_control"},
                    {"query": "Behavioral Modification", "count": 780, "category": "mind_control"},
                    {"query": "LSD Experiments", "count": 650, "category": "mind_control"},
                    {"query": "Brainwashing", "count": 540, "category": "mind_control"},
                    {"query": "Manchurian Candidate", "count": 430, "category": "mind_control"}
                ]
            }
            
            # Lấy danh sách theo danh mục
            if category.lower() in popular_searches:
                results = popular_searches[category.lower()][:max_results]
            else:
                results = popular_searches["all"][:max_results]
            
            return json.dumps({
                "status": "success",
                "source": "CIA FOIA",
                "category": category,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"Lỗi khi lấy danh sách tìm kiếm phổ biến: {e}")
            return json.dumps({
                "status": "error",
                "source": "CIA FOIA",
                "category": category,
                "message": str(e),
                "results": []
            }, indent=2, ensure_ascii=False)
    
    def _get_default_results(self, query: str) -> str:
        """Trả về kết quả mặc định khi không tìm thấy kết quả."""
        default_results = [
            {
                "title": "UFOs and the CIA: The Conspiracy Files",
                "url": "https://www.cia.gov/readingroom/document/cia-rdp96-00788r001700210001-5",
                "snippet": "Declassified CIA documents related to UFO sightings and investigations conducted by the agency.",
                "date_published": "2007-01-15",
                "document_type": "Document",
                "source": "CIA FOIA Reading Room"
            },
            {
                "title": "The Stargate Project: Remote Viewing Declassified",
                "url": "https://www.cia.gov/readingroom/document/cia-rdp96-00788r001700210002-6",
                "snippet": "Declassified documents detailing the CIA's research into remote viewing and psychic phenomena.",
                "date_published": "2000-11-22",
                "document_type": "Report",
                "source": "CIA FOIA Reading Room"
            },
            {
                "title": "MKUltra: The CIA's Program of Research in Behavioral Modification",
                "url": "https://www.cia.gov/readingroom/document/0000011018",
                "snippet": "Declassified documents related to the MKUltra program, including experiments with mind control and chemical interrogation.",
                "date_published": "1977-07-18",
                "document_type": "Collection",
                "source": "CIA FOIA Reading Room"
            }
        ]
        
        return json.dumps({
            "status": "success",
            "source": "CIA FOIA",
            "query": query,
            "message": "Không tìm thấy kết quả phù hợp. Dưới đây là một số tài liệu đề xuất.",
            "results": default_results,
            "result_count": len(default_results)
        }, indent=2, ensure_ascii=False)
    
    def _get_error_response(self, query: str, error_msg: str) -> str:
        """Trả về phản hồi lỗi có cấu trúc."""
        return json.dumps({
            "status": "error",
            "source": "CIA FOIA",
            "query": query,
            "message": f"Không thể truy xuất kết quả: {error_msg}",
            "results": []
        }, indent=2, ensure_ascii=False)
