#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mythology & Religion Calculator - Công cụ tính toán chuyên biệt cho thần thoại và tôn giáo
"""

from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
from datetime import datetime
import random
import math


class MythologyReligionCalculator(Toolkit):
    """
    Calculator chuyên biệt cho mythology và religion với các phương pháp tính toán
    về deity power levels, cultural influence, mythology similarity, và complexity.
    """

    def __init__(self, enable_calculation: bool = True, **kwargs):
        super().__init__(name="mythology_religion_calculator", **kwargs)
        
        # Calculation configuration
        self.calculation_methods = {
            "power": "Deity power level quantification",
            "influence": "Cultural influence measurement",
            "similarity": "Mythology similarity analysis",
            "spread": "Religious spread calculation",
            "complexity": "Mythological complexity assessment"
        }
        
        if enable_calculation:
            self.register(self.calculate_deity_power_level)
            self.register(self.calculate_cultural_influence)
            self.register(self.calculate_mythology_similarity)
            self.register(self.calculate_religious_spread)
            self.register(self.calculate_mythological_complexity)

    def calculate_deity_power_level(self, deity_data: str, power_metrics: str = "comprehensive",
                                  calculation_method: str = "weighted", normalization: str = "standard") -> str:
        """
        Tính toán power level của các vị thần dựa trên multiple metrics.
        
        Args:
            deity_data: Dữ liệu về vị thần (JSON string hoặc deity name)
            power_metrics: Metrics đo power (comprehensive, domain_based, worship_based, narrative_based)
            calculation_method: Phương pháp tính (weighted, hierarchical, network, fuzzy)
            normalization: Chuẩn hóa (none, standard, z_score, percentile)
            
        Returns:
            Chuỗi JSON chứa deity power level analysis
        """
        log_debug(f"Calculating deity power level with {calculation_method} method")
        
        try:
            # Parse deity data
            parsed_deity = self._parse_deity_data(deity_data)
            
            # Power metric configuration
            metrics_config = self._configure_power_metrics(power_metrics)
            
            # Domain power assessment
            domain_power = self._assess_domain_power(parsed_deity, metrics_config)
            
            # Worship influence assessment
            worship_influence = self._assess_worship_influence(parsed_deity, metrics_config)
            
            # Narrative power assessment
            narrative_power = self._assess_narrative_power(parsed_deity, metrics_config)
            
            # Hierarchical position assessment
            hierarchical_position = self._assess_hierarchical_position(parsed_deity, metrics_config)
            
            # Composite power calculation
            composite_power = self._calculate_composite_power_level(
                domain_power, worship_influence, narrative_power, hierarchical_position,
                calculation_method, metrics_config
            )
            
            # Power normalization
            normalized_power = self._normalize_power_level(composite_power, normalization)
            
            # Power classification
            power_classification = self._classify_deity_power(normalized_power)

            result = {
                "calculation_parameters": {
                    "deity_data": deity_data,
                    "power_metrics": power_metrics,
                    "calculation_method": calculation_method,
                    "normalization": normalization,
                    "calculation_approach": "Deity power level quantification"
                },
                "power_assessment": {
                    "overall_power_level": normalized_power.get("overall_power", 0),
                    "power_tier": power_classification.get("tier", "Minor"),
                    "power_rank": power_classification.get("rank", "Unknown"),
                    "power_category": power_classification.get("category", "Regional"),
                    "comparative_strength": power_classification.get("comparative_strength", "Moderate")
                },
                "power_components": {
                    "domain_power": domain_power.get("power_score", 0),
                    "worship_influence": worship_influence.get("influence_score", 0),
                    "narrative_power": narrative_power.get("narrative_score", 0),
                    "hierarchical_position": hierarchical_position.get("position_score", 0)
                },
                "metrics_breakdown": self._generate_power_metrics_breakdown(domain_power, worship_influence, narrative_power, hierarchical_position),
                "power_factors": self._identify_power_factors(normalized_power),
                "comparative_analysis": self._perform_comparative_power_analysis(normalized_power),
                "power_evolution": self._analyze_power_evolution(parsed_deity, normalized_power),
                "calculation_metadata": self._generate_power_calculation_metadata(parsed_deity, normalized_power),
                "timestamp": datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            log_debug(f"Error calculating deity power level: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def calculate_cultural_influence(self, cultural_entity: str, influence_metrics: str = "comprehensive",
                                   measurement_method: str = "multi_dimensional", temporal_weighting: str = "balanced") -> str:
        """
        Tính toán ảnh hưởng văn hóa của mythology/religion entities.
        
        Args:
            cultural_entity: Thực thể văn hóa cần đo
            influence_metrics: Metrics đo influence (comprehensive, artistic, social, political, economic)
            measurement_method: Phương pháp đo (multi_dimensional, weighted, network, diffusion)
            temporal_weighting: Trọng số thời gian (historical, balanced, contemporary, exponential)
            
        Returns:
            Chuỗi JSON chứa cultural influence analysis
        """
        log_debug(f"Calculating cultural influence for {cultural_entity}")
        
        try:
            # Parse cultural entity data
            parsed_entity = self._parse_cultural_entity_data(cultural_entity)
            
            # Influence metrics configuration
            metrics_config = self._configure_influence_metrics(influence_metrics)
            
            # Artistic influence measurement
            artistic_influence = self._measure_artistic_influence(parsed_entity, metrics_config)
            
            # Social influence measurement
            social_influence = self._measure_social_influence(parsed_entity, metrics_config)
            
            # Political influence measurement
            political_influence = self._measure_political_influence(parsed_entity, metrics_config)
            
            # Economic influence measurement
            economic_influence = self._measure_economic_influence(parsed_entity, metrics_config)
            
            # Temporal influence analysis
            temporal_influence = self._analyze_temporal_influence(parsed_entity, temporal_weighting)
            
            # Geographic influence analysis
            geographic_influence = self._analyze_geographic_influence(parsed_entity)
            
            # Composite influence calculation
            composite_influence = self._calculate_composite_cultural_influence(
                artistic_influence, social_influence, political_influence, economic_influence,
                temporal_influence, geographic_influence, measurement_method
            )

            result = {
                "calculation_parameters": {
                    "cultural_entity": cultural_entity,
                    "influence_metrics": influence_metrics,
                    "measurement_method": measurement_method,
                    "temporal_weighting": temporal_weighting,
                    "calculation_approach": "Cultural influence measurement"
                },
                "influence_assessment": {
                    "overall_influence_score": composite_influence.get("overall_score", 0),
                    "influence_magnitude": composite_influence.get("magnitude", "Moderate"),
                    "influence_reach": composite_influence.get("reach", "Regional"),
                    "influence_duration": composite_influence.get("duration", "Centuries"),
                    "influence_sustainability": composite_influence.get("sustainability", "Stable")
                },
                "influence_dimensions": {
                    "artistic_influence": artistic_influence.get("influence_score", 0),
                    "social_influence": social_influence.get("influence_score", 0),
                    "political_influence": political_influence.get("influence_score", 0),
                    "economic_influence": economic_influence.get("influence_score", 0)
                },
                "temporal_analysis": temporal_influence,
                "geographic_analysis": geographic_influence,
                "influence_pathways": self._map_influence_pathways(composite_influence),
                "influence_multipliers": self._identify_influence_multipliers(composite_influence),
                "comparative_influence": self._perform_comparative_influence_analysis(composite_influence),
                "influence_projections": self._project_future_influence(composite_influence),
                "timestamp": datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            log_debug(f"Error calculating cultural influence: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def calculate_mythology_similarity(self, mythology_a: str, mythology_b: str, 
                                     similarity_metrics: str = "comprehensive", comparison_method: str = "multi_factor") -> str:
        """
        Tính toán độ tương đồng giữa các hệ thống thần thoại.
        
        Args:
            mythology_a: Hệ thống thần thoại thứ nhất
            mythology_b: Hệ thống thần thoại thứ hai
            similarity_metrics: Metrics đo similarity (comprehensive, structural, thematic, narrative)
            comparison_method: Phương pháp so sánh (multi_factor, weighted, cosine, jaccard)
            
        Returns:
            Chuỗi JSON chứa mythology similarity analysis
        """
        log_debug(f"Calculating mythology similarity: {mythology_a} vs {mythology_b}")
        
        try:
            # Parse mythology data
            mythology_a_data = self._parse_mythology_data(mythology_a)
            mythology_b_data = self._parse_mythology_data(mythology_b)
            
            # Similarity metrics configuration
            metrics_config = self._configure_similarity_metrics(similarity_metrics)
            
            # Structural similarity analysis
            structural_similarity = self._analyze_structural_similarity(mythology_a_data, mythology_b_data, metrics_config)
            
            # Thematic similarity analysis
            thematic_similarity = self._analyze_thematic_similarity(mythology_a_data, mythology_b_data, metrics_config)
            
            # Narrative similarity analysis
            narrative_similarity = self._analyze_narrative_similarity(mythology_a_data, mythology_b_data, metrics_config)
            
            # Character similarity analysis
            character_similarity = self._analyze_character_similarity(mythology_a_data, mythology_b_data, metrics_config)
            
            # Cultural context similarity
            cultural_similarity = self._analyze_cultural_context_similarity(mythology_a_data, mythology_b_data)
            
            # Composite similarity calculation
            composite_similarity = self._calculate_composite_similarity(
                structural_similarity, thematic_similarity, narrative_similarity,
                character_similarity, cultural_similarity, comparison_method
            )
            
            # Similarity interpretation
            similarity_interpretation = self._interpret_mythology_similarity(composite_similarity)

            result = {
                "calculation_parameters": {
                    "mythology_a": mythology_a,
                    "mythology_b": mythology_b,
                    "similarity_metrics": similarity_metrics,
                    "comparison_method": comparison_method,
                    "calculation_approach": "Mythology similarity analysis"
                },
                "similarity_assessment": {
                    "overall_similarity": composite_similarity.get("overall_similarity", 0),
                    "similarity_level": similarity_interpretation.get("level", "Moderate"),
                    "similarity_confidence": composite_similarity.get("confidence", "Medium"),
                    "relationship_type": similarity_interpretation.get("relationship_type", "Distant"),
                    "cultural_connection": similarity_interpretation.get("cultural_connection", "Possible")
                },
                "similarity_dimensions": {
                    "structural_similarity": structural_similarity.get("similarity_score", 0),
                    "thematic_similarity": thematic_similarity.get("similarity_score", 0),
                    "narrative_similarity": narrative_similarity.get("similarity_score", 0),
                    "character_similarity": character_similarity.get("similarity_score", 0),
                    "cultural_similarity": cultural_similarity.get("similarity_score", 0)
                },
                "similarity_details": self._generate_similarity_details(structural_similarity, thematic_similarity, narrative_similarity, character_similarity),
                "common_elements": self._identify_common_elements(mythology_a_data, mythology_b_data),
                "unique_elements": self._identify_unique_elements(mythology_a_data, mythology_b_data),
                "influence_analysis": self._analyze_potential_influence(mythology_a_data, mythology_b_data, composite_similarity),
                "comparative_context": self._provide_comparative_context(composite_similarity),
                "timestamp": datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            log_debug(f"Error calculating mythology similarity: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def calculate_religious_spread(self, religion_data: str, spread_metrics: str = "comprehensive",
                                 calculation_model: str = "diffusion", temporal_analysis: str = "historical") -> str:
        """
        Tính toán sự lan truyền của các truyền thống tôn giáo.
        
        Args:
            religion_data: Dữ liệu về tôn giáo
            spread_metrics: Metrics đo spread (comprehensive, geographic, demographic, institutional)
            calculation_model: Mô hình tính toán (diffusion, network, gravity, epidemic)
            temporal_analysis: Phân tích thời gian (historical, contemporary, predictive)
            
        Returns:
            Chuỗi JSON chứa religious spread analysis
        """
        log_debug(f"Calculating religious spread using {calculation_model} model")
        
        try:
            # Parse religion data
            parsed_religion = self._parse_religion_data(religion_data)
            
            # Spread metrics configuration
            metrics_config = self._configure_spread_metrics(spread_metrics)
            
            # Geographic spread analysis
            geographic_spread = self._analyze_geographic_spread(parsed_religion, metrics_config)
            
            # Demographic spread analysis
            demographic_spread = self._analyze_demographic_spread(parsed_religion, metrics_config)
            
            # Institutional spread analysis
            institutional_spread = self._analyze_institutional_spread(parsed_religion, metrics_config)
            
            # Temporal spread analysis
            temporal_spread = self._analyze_temporal_spread(parsed_religion, temporal_analysis)
            
            # Spread velocity calculation
            spread_velocity = self._calculate_spread_velocity(parsed_religion, temporal_spread)
            
            # Spread resistance analysis
            spread_resistance = self._analyze_spread_resistance(parsed_religion, geographic_spread)
            
            # Composite spread calculation
            composite_spread = self._calculate_composite_religious_spread(
                geographic_spread, demographic_spread, institutional_spread,
                temporal_spread, spread_velocity, calculation_model
            )

            result = {
                "calculation_parameters": {
                    "religion_data": religion_data,
                    "spread_metrics": spread_metrics,
                    "calculation_model": calculation_model,
                    "temporal_analysis": temporal_analysis,
                    "calculation_approach": "Religious spread calculation"
                },
                "spread_assessment": {
                    "overall_spread_score": composite_spread.get("overall_score", 0),
                    "spread_magnitude": composite_spread.get("magnitude", "Moderate"),
                    "spread_velocity": spread_velocity.get("velocity", "Medium"),
                    "geographic_reach": geographic_spread.get("reach", "Regional"),
                    "demographic_penetration": demographic_spread.get("penetration", "Moderate")
                },
                "spread_dimensions": {
                    "geographic_spread": geographic_spread.get("spread_score", 0),
                    "demographic_spread": demographic_spread.get("spread_score", 0),
                    "institutional_spread": institutional_spread.get("spread_score", 0),
                    "temporal_spread": temporal_spread.get("spread_score", 0)
                },
                "spread_analysis": {
                    "spread_velocity": spread_velocity,
                    "spread_resistance": spread_resistance,
                    "spread_facilitators": self._identify_spread_facilitators(composite_spread),
                    "spread_barriers": self._identify_spread_barriers(spread_resistance)
                },
                "spread_patterns": self._identify_spread_patterns(composite_spread),
                "future_projections": self._project_future_spread(composite_spread, spread_velocity),
                "comparative_spread": self._perform_comparative_spread_analysis(composite_spread),
                "timestamp": datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            log_debug(f"Error calculating religious spread: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def calculate_mythological_complexity(self, mythology_data: str, complexity_factors: str = "comprehensive",
                                        calculation_approach: str = "entropy", analysis_depth: str = "standard") -> str:
        """
        Tính toán độ phức tạp của các hệ thống thần thoại.
        
        Args:
            mythology_data: Dữ liệu về hệ thống thần thoại
            complexity_factors: Các yếu tố complexity (comprehensive, structural, narrative, relational)
            calculation_approach: Phương pháp tính (entropy, graph_theory, information_theory, network)
            analysis_depth: Độ sâu phân tích (basic, standard, advanced, expert)
            
        Returns:
            Chuỗi JSON chứa mythological complexity analysis
        """
        log_debug(f"Calculating mythological complexity using {calculation_approach} approach")
        
        try:
            # Parse mythology data
            parsed_mythology = self._parse_mythology_complexity_data(mythology_data)
            
            # Complexity factors configuration
            factors_config = self._configure_complexity_factors(complexity_factors)
            
            # Structural complexity analysis
            structural_complexity = self._analyze_structural_complexity(parsed_mythology, factors_config)
            
            # Narrative complexity analysis
            narrative_complexity = self._analyze_narrative_complexity(parsed_mythology, factors_config)
            
            # Relational complexity analysis
            relational_complexity = self._analyze_relational_complexity(parsed_mythology, factors_config)
            
            # Symbolic complexity analysis
            symbolic_complexity = self._analyze_symbolic_complexity(parsed_mythology, factors_config)
            
            # Information entropy calculation
            entropy_calculation = self._calculate_mythology_entropy(parsed_mythology, calculation_approach)
            
            # Composite complexity calculation
            composite_complexity = self._calculate_composite_mythological_complexity(
                structural_complexity, narrative_complexity, relational_complexity,
                symbolic_complexity, entropy_calculation, calculation_approach
            )
            
            # Complexity interpretation
            complexity_interpretation = self._interpret_mythological_complexity(composite_complexity)

            result = {
                "calculation_parameters": {
                    "mythology_data": mythology_data,
                    "complexity_factors": complexity_factors,
                    "calculation_approach": calculation_approach,
                    "analysis_depth": analysis_depth,
                    "calculation_method": "Mythological complexity assessment"
                },
                "complexity_assessment": {
                    "overall_complexity": composite_complexity.get("overall_complexity", 0),
                    "complexity_level": complexity_interpretation.get("level", "Moderate"),
                    "complexity_grade": complexity_interpretation.get("grade", "B"),
                    "study_difficulty": complexity_interpretation.get("study_difficulty", "Intermediate"),
                    "comprehension_time": complexity_interpretation.get("comprehension_time", "Months")
                },
                "complexity_dimensions": {
                    "structural_complexity": structural_complexity.get("complexity_score", 0),
                    "narrative_complexity": narrative_complexity.get("complexity_score", 0),
                    "relational_complexity": relational_complexity.get("complexity_score", 0),
                    "symbolic_complexity": symbolic_complexity.get("complexity_score", 0)
                },
                "entropy_analysis": entropy_calculation,
                "complexity_factors": self._identify_complexity_contributors(composite_complexity),
                "simplification_opportunities": self._identify_simplification_opportunities(composite_complexity),
                "learning_pathway": self._suggest_learning_pathway(composite_complexity),
                "comparative_complexity": self._perform_comparative_complexity_analysis(composite_complexity),
                "timestamp": datetime.now().isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            log_debug(f"Error calculating mythological complexity: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    # Helper methods (basic implementations)
    def _parse_deity_data(self, deity_data: str) -> dict:
        """Parse deity data."""
        return {
            "deity_name": deity_data,
            "pantheon": "Unknown",
            "domains": random.randint(2, 8),
            "worship_sites": random.randint(5, 50)
        }
