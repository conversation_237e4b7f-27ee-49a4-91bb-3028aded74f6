# -*- coding: utf-8 -*-
from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import json
import math
from datetime import datetime

class CulturalEvolutionCalculator(Toolkit):
    """
    Cultural Evolution Calculator cho tính toán cultural divergence, artifact evolution và civilization development.
    """

    def __init__(self, enable_calculations: bool = True, **kwargs):
        super().__init__(
            name="cultural_evolution_calculator",
            **kwargs
        )
        
        # Cultural development milestones (years ago)
        self.cultural_milestones = {
            "stone_tools": 3300000,
            "fire_control": 1000000,
            "language": 300000,
            "art": 65000,
            "agriculture": 12000,
            "writing": 5500,
            "metallurgy": 5000,
            "cities": 4500,
            "printing": 580,
            "industrial_revolution": 250,
            "digital_age": 50
        }
        
        # Cultural evolution rates (changes per millennium)
        self.evolution_rates = {
            "technology": 0.1,      # Slow in prehistoric times
            "social_organization": 0.05,
            "artistic_expression": 0.08,
            "religious_beliefs": 0.03,
            "trade_networks": 0.12,
            "language_change": 0.15,
            "settlement_patterns": 0.06
        }
        
        # Civilization complexity factors
        self.complexity_factors = {
            "hunter_gatherer": {"complexity": 1, "population": 50, "specialization": 0.1},
            "agricultural": {"complexity": 5, "population": 500, "specialization": 0.3},
            "chiefdom": {"complexity": 15, "population": 2000, "specialization": 0.5},
            "state": {"complexity": 50, "population": 50000, "specialization": 0.8},
            "empire": {"complexity": 200, "population": 1000000, "specialization": 0.95}
        }
        
        if enable_calculations:
            self.register(self.calculate_cultural_divergence)
            self.register(self.estimate_artifact_evolution)
            self.register(self.analyze_civilization_development)
            self.register(self.predict_cultural_trends)

    def calculate_cultural_divergence(self, culture1: str, culture2: str, 
                                    separation_years: int = None, 
                                    cultural_aspect: str = "technology") -> str:
        """
        Tính toán cultural divergence giữa hai nền văn hóa.
        
        Args:
            culture1: Văn hóa thứ nhất
            culture2: Văn hóa thứ hai
            separation_years: Thời gian tách biệt (years)
            cultural_aspect: Khía cạnh văn hóa để phân tích
            
        Returns:
            Chuỗi JSON chứa tính toán cultural divergence
        """
        log_debug(f"Calculating cultural divergence between {culture1} and {culture2}")
        
        try:
            # Estimate separation time if not provided
            if separation_years is None:
                # Use historical estimates or default
                separation_years = 5000  # Default 5000 years
            
            # Calculate divergence metrics
            evolution_rate = self.evolution_rates.get(cultural_aspect, 0.1)
            divergence_index = 1 - math.exp(-evolution_rate * (separation_years / 1000))
            
            # Cultural distance analysis
            cultural_distance = separation_years * evolution_rate / 1000
            similarity_index = max(0, 1 - cultural_distance)
            
            # Innovation diffusion analysis
            diffusion_rate = 0.02  # 2% per century
            shared_innovations = math.exp(-diffusion_rate * (separation_years / 100))
            
            # Environmental influence
            environmental_factors = {
                "geographic_isolation": 0.8 if separation_years > 10000 else 0.4,
                "climate_adaptation": 0.6,
                "resource_availability": 0.7,
                "migration_patterns": 0.5
            }
            
            # Cultural traits analysis
            trait_categories = {
                "material_culture": {
                    "divergence": round(divergence_index * 0.8, 3),
                    "examples": ["Tools", "Architecture", "Pottery", "Weapons"]
                },
                "social_organization": {
                    "divergence": round(divergence_index * 0.6, 3),
                    "examples": ["Kinship systems", "Leadership", "Social roles", "Hierarchy"]
                },
                "symbolic_systems": {
                    "divergence": round(divergence_index * 0.9, 3),
                    "examples": ["Language", "Art", "Religion", "Mythology"]
                },
                "subsistence_strategies": {
                    "divergence": round(divergence_index * 0.5, 3),
                    "examples": ["Hunting", "Agriculture", "Trade", "Crafts"]
                }
            }
            
            # Convergent evolution detection
            convergent_traits = []
            if environmental_factors["climate_adaptation"] > 0.5:
                convergent_traits.append("Climate adaptation strategies")
            if shared_innovations > 0.3:
                convergent_traits.append("Technological solutions")
            
            result = {
                "cultural_comparison": {
                    "culture1": culture1,
                    "culture2": culture2,
                    "separation_years": separation_years,
                    "cultural_aspect": cultural_aspect
                },
                "divergence_analysis": {
                    "divergence_index": round(divergence_index, 3),
                    "cultural_distance": round(cultural_distance, 3),
                    "similarity_index": round(similarity_index, 3),
                    "evolution_rate": evolution_rate
                },
                "innovation_diffusion": {
                    "shared_innovations": round(shared_innovations, 3),
                    "diffusion_rate": diffusion_rate,
                    "isolation_effect": "High" if separation_years > 10000 else "Medium"
                },
                "environmental_influences": environmental_factors,
                "cultural_traits": trait_categories,
                "convergent_evolution": {
                    "detected_traits": convergent_traits,
                    "convergence_strength": len(convergent_traits) / 4,
                    "adaptive_responses": "Similar environmental pressures"
                },
                "historical_context": self._get_historical_context(separation_years),
                "archaeological_evidence": self._generate_archaeological_evidence(culture1, culture2),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error calculating cultural divergence: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to calculate cultural divergence: {str(e)}"
            }, indent=4)

    def estimate_artifact_evolution(self, artifact_type: str, time_period_years: int = 1000, 
                                  cultural_context: str = "agricultural") -> str:
        """
        Ước tính artifact evolution qua thời gian.
        
        Args:
            artifact_type: Loại artifact
            time_period_years: Khoảng thời gian (years)
            cultural_context: Bối cảnh văn hóa
            
        Returns:
            Chuỗi JSON chứa ước tính artifact evolution
        """
        log_debug(f"Estimating artifact evolution for {artifact_type} over {time_period_years} years")
        
        try:
            # Get cultural complexity
            context_params = self.complexity_factors.get(cultural_context, self.complexity_factors["agricultural"])
            
            # Calculate evolution metrics
            innovation_rate = 0.1 * context_params["specialization"]  # Higher specialization = more innovation
            complexity_growth = (1 + innovation_rate) ** (time_period_years / 100)
            
            # Artifact development stages
            development_stages = []
            stages_count = max(3, int(time_period_years / 200))  # One stage per 200 years
            
            for stage in range(stages_count):
                stage_years = (stage + 1) * (time_period_years / stages_count)
                stage_complexity = (1 + innovation_rate) ** (stage_years / 100)
                
                development_stages.append({
                    "stage": stage + 1,
                    "time_years": int(stage_years),
                    "complexity_level": round(stage_complexity, 2),
                    "innovations": self._generate_artifact_innovations(artifact_type, stage),
                    "cultural_drivers": self._get_cultural_drivers(cultural_context, stage)
                })
            
            # Technological diffusion
            diffusion_analysis = {
                "local_adoption": "Rapid" if context_params["specialization"] > 0.5 else "Gradual",
                "regional_spread": f"{time_period_years // 50} generations",
                "cross_cultural_transfer": "High" if time_period_years > 500 else "Limited",
                "preservation_likelihood": self._assess_preservation(artifact_type)
            }
            
            # Functional evolution
            functional_changes = {
                "efficiency_improvement": round((complexity_growth - 1) * 100, 1),
                "specialization_increase": round(context_params["specialization"] * 100, 1),
                "aesthetic_development": "Significant" if time_period_years > 800 else "Minimal",
                "symbolic_meaning": "Evolved" if cultural_context in ["chiefdom", "state", "empire"] else "Basic"
            }
            
            # Archaeological visibility
            archaeological_record = {
                "preservation_probability": self._calculate_preservation_probability(artifact_type, time_period_years),
                "site_distribution": "Widespread" if time_period_years > 1000 else "Localized",
                "stratigraphic_sequence": f"{time_period_years // 100} distinct layers",
                "dating_methods": ["Radiocarbon", "Stratigraphy", "Typology"]
            }
            
            result = {
                "artifact_analysis": {
                    "artifact_type": artifact_type,
                    "time_period_years": time_period_years,
                    "cultural_context": cultural_context,
                    "complexity_factor": context_params["complexity"]
                },
                "evolution_metrics": {
                    "innovation_rate": innovation_rate,
                    "complexity_growth_factor": round(complexity_growth, 2),
                    "development_velocity": "Rapid" if innovation_rate > 0.05 else "Gradual",
                    "technological_momentum": "High" if complexity_growth > 2 else "Moderate"
                },
                "development_stages": development_stages,
                "diffusion_analysis": diffusion_analysis,
                "functional_evolution": functional_changes,
                "archaeological_record": archaeological_record,
                "comparative_analysis": self._compare_artifact_traditions(artifact_type),
                "research_implications": self._generate_research_implications(artifact_type, time_period_years),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error estimating artifact evolution: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to estimate artifact evolution: {str(e)}"
            }, indent=4)

    def analyze_civilization_development(self, civilization: str, development_span_years: int = 2000, 
                                       starting_complexity: str = "chiefdom") -> str:
        """
        Phân tích civilization development và social evolution.
        
        Args:
            civilization: Tên nền văn minh
            development_span_years: Khoảng thời gian phát triển
            starting_complexity: Mức độ phức tạp ban đầu
            
        Returns:
            Chuỗi JSON chứa phân tích civilization development
        """
        log_debug(f"Analyzing civilization development for {civilization}")
        
        try:
            # Get starting parameters
            start_params = self.complexity_factors.get(starting_complexity, self.complexity_factors["chiefdom"])
            
            # Calculate development trajectory
            growth_rate = 0.02  # 2% complexity increase per century
            final_complexity = start_params["complexity"] * ((1 + growth_rate) ** (development_span_years / 100))
            
            # Population growth
            population_growth_rate = 0.015  # 1.5% per century
            final_population = start_params["population"] * ((1 + population_growth_rate) ** (development_span_years / 100))
            
            # Development phases
            phases = []
            phase_duration = development_span_years // 4
            
            for i in range(4):
                phase_years = (i + 1) * phase_duration
                phase_complexity = start_params["complexity"] * ((1 + growth_rate) ** (phase_years / 100))
                phase_population = start_params["population"] * ((1 + population_growth_rate) ** (phase_years / 100))
                
                phases.append({
                    "phase": i + 1,
                    "name": ["Formation", "Expansion", "Consolidation", "Transformation"][i],
                    "duration_years": phase_duration,
                    "complexity_level": round(phase_complexity, 1),
                    "population": int(phase_population),
                    "key_developments": self._generate_civilization_developments(i),
                    "challenges": self._generate_civilization_challenges(i)
                })
            
            # Social stratification
            stratification_analysis = {
                "social_layers": min(10, int(final_complexity / 10)),
                "specialization_degree": min(0.95, start_params["specialization"] + (development_span_years / 10000)),
                "elite_emergence": "Established" if final_complexity > 50 else "Emerging",
                "institutional_complexity": "High" if final_complexity > 100 else "Medium"
            }
            
            # Cultural achievements
            achievements = {
                "monumental_architecture": final_complexity > 30,
                "writing_system": final_complexity > 40,
                "legal_codes": final_complexity > 60,
                "artistic_traditions": final_complexity > 20,
                "trade_networks": final_complexity > 25,
                "technological_innovations": int(final_complexity / 10)
            }
            
            # Sustainability factors
            sustainability = {
                "resource_management": "Advanced" if final_complexity > 80 else "Developing",
                "environmental_adaptation": "High",
                "social_cohesion": "Strong" if development_span_years < 1500 else "Challenged",
                "external_pressures": "Managed" if final_complexity > 70 else "Vulnerable"
            }
            
            result = {
                "civilization_analysis": {
                    "civilization": civilization,
                    "development_span_years": development_span_years,
                    "starting_complexity": starting_complexity,
                    "final_complexity_level": round(final_complexity, 1)
                },
                "growth_metrics": {
                    "complexity_growth_factor": round(final_complexity / start_params["complexity"], 2),
                    "population_growth_factor": round(final_population / start_params["population"], 2),
                    "development_rate": growth_rate,
                    "trajectory": "Exponential" if growth_rate > 0.015 else "Linear"
                },
                "development_phases": phases,
                "social_stratification": stratification_analysis,
                "cultural_achievements": achievements,
                "sustainability_analysis": sustainability,
                "comparative_context": self._compare_civilizations(civilization, final_complexity),
                "archaeological_signature": self._generate_archaeological_signature(civilization, final_complexity),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error analyzing civilization development: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to analyze civilization development: {str(e)}"
            }, indent=4)

    def predict_cultural_trends(self, cultural_system: str, prediction_years: int = 500, 
                              current_state: str = "traditional") -> str:
        """
        Dự đoán cultural trends và evolutionary trajectory.
        
        Args:
            cultural_system: Hệ thống văn hóa
            prediction_years: Số năm dự đoán
            current_state: Trạng thái hiện tại
            
        Returns:
            Chuỗi JSON chứa dự đoán cultural trends
        """
        log_debug(f"Predicting cultural trends for {cultural_system} over {prediction_years} years")
        
        try:
            # Cultural change rates by state
            change_rates = {
                "traditional": 0.01,    # 1% per century
                "transitional": 0.05,   # 5% per century
                "modernizing": 0.15,    # 15% per century
                "globalized": 0.25      # 25% per century
            }
            
            current_rate = change_rates.get(current_state, 0.05)
            
            # Calculate future changes
            cultural_change_factor = (1 + current_rate) ** (prediction_years / 100)
            modernization_index = min(1.0, current_rate * (prediction_years / 100))
            
            # Trend predictions
            trend_categories = {
                "technology_adoption": {
                    "rate": round(cultural_change_factor * 0.8, 2),
                    "timeline": f"{prediction_years // 3} years to mainstream adoption",
                    "resistance_factors": ["Traditional values", "Economic constraints", "Infrastructure"]
                },
                "social_organization": {
                    "rate": round(cultural_change_factor * 0.4, 2),
                    "timeline": f"{prediction_years // 2} years for significant change",
                    "change_drivers": ["Urbanization", "Education", "Economic development"]
                },
                "belief_systems": {
                    "rate": round(cultural_change_factor * 0.2, 2),
                    "timeline": f"{prediction_years} years for major shifts",
                    "persistence_factors": ["Cultural identity", "Community bonds", "Ritual practices"]
                },
                "material_culture": {
                    "rate": round(cultural_change_factor * 0.9, 2),
                    "timeline": f"{prediction_years // 4} years for visible changes",
                    "innovation_areas": ["Tools", "Architecture", "Art", "Clothing"]
                }
            }
            
            # External influences
            external_factors = {
                "globalization_pressure": 0.7 if current_state in ["modernizing", "globalized"] else 0.3,
                "environmental_challenges": 0.6,
                "technological_diffusion": 0.8,
                "economic_integration": 0.5,
                "cultural_exchange": 0.4
            }
            
            # Preservation vs. change
            cultural_dynamics = {
                "preservation_strength": max(0.1, 1 - modernization_index),
                "change_momentum": modernization_index,
                "hybrid_formations": "Likely" if 0.3 < modernization_index < 0.7 else "Unlikely",
                "cultural_resilience": "High" if current_state == "traditional" else "Medium"
            }
            
            # Future scenarios
            scenarios = [
                {
                    "scenario": "Gradual Integration",
                    "probability": 0.6,
                    "description": "Slow adoption of new elements while maintaining core traditions",
                    "timeframe": f"{prediction_years} years"
                },
                {
                    "scenario": "Rapid Transformation",
                    "probability": 0.3,
                    "description": "Accelerated change due to external pressures",
                    "timeframe": f"{prediction_years // 2} years"
                },
                {
                    "scenario": "Cultural Revival",
                    "probability": 0.1,
                    "description": "Strengthening of traditional practices in response to change",
                    "timeframe": f"{prediction_years // 3} years"
                }
            ]
            
            result = {
                "prediction_parameters": {
                    "cultural_system": cultural_system,
                    "prediction_timeframe": prediction_years,
                    "current_state": current_state,
                    "base_change_rate": current_rate
                },
                "change_projections": {
                    "cultural_change_factor": round(cultural_change_factor, 2),
                    "modernization_index": round(modernization_index, 3),
                    "overall_trajectory": "Rapid change" if current_rate > 0.1 else "Gradual evolution",
                    "transformation_timeline": f"{prediction_years} years"
                },
                "trend_categories": trend_categories,
                "external_influences": external_factors,
                "cultural_dynamics": cultural_dynamics,
                "future_scenarios": scenarios,
                "risk_factors": [
                    "Cultural homogenization",
                    "Loss of traditional knowledge",
                    "Social fragmentation",
                    "Identity conflicts"
                ],
                "preservation_strategies": self._generate_preservation_strategies(cultural_system),
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
            return json.dumps(result, indent=4)
            
        except Exception as e:
            logger.error(f"Error predicting cultural trends: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"Failed to predict cultural trends: {str(e)}"
            }, indent=4)

    # Helper methods
    def _get_historical_context(self, years: int) -> Dict[str, Any]:
        """Get historical context for time period."""
        if years < 1000:
            return {"period": "Recent history", "context": "Historical documentation available"}
        elif years < 10000:
            return {"period": "Prehistoric", "context": "Archaeological evidence primary"}
        else:
            return {"period": "Deep prehistory", "context": "Limited archaeological evidence"}

    def _generate_archaeological_evidence(self, culture1: str, culture2: str) -> List[str]:
        """Generate archaeological evidence types."""
        return [
            "Lithic tool traditions",
            "Ceramic styles and techniques",
            "Settlement patterns",
            "Burial practices",
            "Artistic motifs",
            "Trade goods distribution"
        ]

    def _generate_artifact_innovations(self, artifact_type: str, stage: int) -> List[str]:
        """Generate artifact innovations for development stage."""
        base_innovations = ["Improved materials", "Enhanced functionality", "Better craftsmanship"]
        if stage > 2:
            base_innovations.extend(["Decorative elements", "Standardization", "Mass production"])
        return base_innovations

    def _get_cultural_drivers(self, context: str, stage: int) -> List[str]:
        """Get cultural drivers for development stage."""
        drivers = ["Population growth", "Resource availability"]
        if context in ["state", "empire"]:
            drivers.extend(["Political centralization", "Trade expansion"])
        return drivers

    def _assess_preservation(self, artifact_type: str) -> str:
        """Assess preservation likelihood."""
        preservation_map = {
            "stone": "Excellent",
            "ceramic": "Good",
            "metal": "Good",
            "bone": "Fair",
            "wood": "Poor",
            "textile": "Very poor"
        }
        return preservation_map.get(artifact_type.lower(), "Fair")

    def _calculate_preservation_probability(self, artifact_type: str, years: int) -> float:
        """Calculate preservation probability."""
        base_prob = {"stone": 0.9, "ceramic": 0.7, "metal": 0.6, "bone": 0.3, "wood": 0.1}.get(artifact_type.lower(), 0.5)
        decay_factor = math.exp(-years / 10000)  # Exponential decay
        return round(base_prob * decay_factor, 3)

    def _compare_artifact_traditions(self, artifact_type: str) -> Dict[str, Any]:
        """Compare with other artifact traditions."""
        return {
            "regional_variants": "Multiple traditions identified",
            "technological_convergence": "Similar solutions developed independently",
            "diffusion_patterns": "Evidence of cultural exchange"
        }

    def _generate_research_implications(self, artifact_type: str, years: int) -> List[str]:
        """Generate research implications."""
        return [
            "Chronological sequence establishment",
            "Cultural interaction patterns",
            "Technological development trajectories",
            "Social organization insights"
        ]

    def _generate_civilization_developments(self, phase: int) -> List[str]:
        """Generate civilization developments by phase."""
        developments = [
            ["Settlement establishment", "Basic agriculture", "Social differentiation"],
            ["Population growth", "Craft specialization", "Trade networks"],
            ["Monumental architecture", "Writing systems", "Legal codes"],
            ["Cultural flowering", "Territorial expansion", "Institutional complexity"]
        ]
        return developments[phase] if phase < len(developments) else ["Advanced developments"]

    def _generate_civilization_challenges(self, phase: int) -> List[str]:
        """Generate civilization challenges by phase."""
        challenges = [
            ["Resource acquisition", "Environmental adaptation", "Group coordination"],
            ["Population pressure", "Resource management", "Social tensions"],
            ["Administrative complexity", "External threats", "Internal conflicts"],
            ["Sustainability issues", "Cultural change", "System maintenance"]
        ]
        return challenges[phase] if phase < len(challenges) else ["Complex challenges"]

    def _compare_civilizations(self, civilization: str, complexity: float) -> Dict[str, Any]:
        """Compare with other civilizations."""
        return {
            "complexity_ranking": "High" if complexity > 100 else "Medium" if complexity > 50 else "Low",
            "contemporary_civilizations": "Multiple complex societies",
            "unique_features": "Distinctive cultural adaptations"
        }

    def _generate_archaeological_signature(self, civilization: str, complexity: float) -> Dict[str, Any]:
        """Generate archaeological signature."""
        return {
            "site_hierarchy": "Multi-tiered settlement system",
            "material_culture": "Diverse and specialized artifacts",
            "monumental_evidence": "Present" if complexity > 50 else "Limited",
            "preservation_potential": "High" if complexity > 80 else "Medium"
        }

    def _generate_preservation_strategies(self, cultural_system: str) -> List[str]:
        """Generate cultural preservation strategies."""
        return [
            "Documentation of traditional practices",
            "Intergenerational knowledge transfer",
            "Cultural education programs",
            "Community-based preservation initiatives",
            "Digital archiving projects"
        ]
