from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json

class ArtsyTool(Toolkit):
    """
    Công cụ tìm kiếm Artsy giúp khám phá nghệ sĩ, t<PERSON><PERSON> phẩm nghệ thuật và triển lãm từ khắp nơi trên thế giới.
    
    <PERSON><PERSON><PERSON> từ khóa tìm kiếm gợi ý:
    - T<PERSON><PERSON> ngh<PERSON> sĩ (Banksy, Picasso, Van <PERSON>, v.v.)
    - Trư<PERSON><PERSON> phái nghệ thuật (Impressionism, Contemporary, Street Art, v.v.)
    - <PERSON><PERSON><PERSON> h<PERSON>nh nghệ thuật (Painting, Sculpture, Photography, v.v.)
    - <PERSON><PERSON>n lãm theo địa điểm (New York, Paris, Tokyo, v.v.)
    - <PERSON><PERSON> đ<PERSON> nghệ thuật (Abstract, Portrait, Landscape, v.v.)
    """
    def __init__(self):
        super().__init__(
            name="<PERSON><PERSON>ng cụ tìm kiếm Artsy",
            tools=[self.search_artsy]
        )
        self.api_url = "https://api.artsy.net/api"
        self.search_types = ["all", "artwork", "artist", "show", "fair", "gene", "partner"]

    def search_artsy(self, query: str, search_type: str = "all", limit: int = 5) -> str:
        """
        Tìm kiếm trên Artsy theo từ khóa và loại nội dung.
        
        Args:
            query: Từ khóa tìm kiếm (tên nghệ sĩ, tác phẩm, triển lãm...)
            search_type: Loại nội dung (all, artwork, artist, show, fair, gene, partner)
            limit: Số lượng kết quả trả về (tối đa 10)
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm Artsy với từ khóa: {query}, loại: {search_type}")
        
        if search_type not in self.search_types:
            search_type = "all"
            
        limit = max(1, min(limit, 10))  # Giới hạn trong khoảng 1-10
        
        try:
            # Giả lập kết quả tìm kiếm
            results = []
            
            if search_type in ["all", "artwork"]:
                results.extend([
                    {
                        "title": f"{query} #{i+1}",
                        "artist": f"Nghệ sĩ {query}",
                        "type": "artwork",
                        "medium": "Oil on canvas",
                        "dimensions": {"cm": {"width": 100 + i*10, "height": 120 + i*10}, "in": {"width": 39.4 + i*4, "height": 47.2 + i*4}},
                        "date": "2025",
                        "price": f"${(i+1)*1000} - ${(i+2)*1000}" if i % 2 == 0 else "Price on request",
                        "gallery": f"{query} Gallery",
                        "url": f"https://www.artsy.net/artwork/{query.lower().replace(' ', '-')}-{i}",
                        "image_url": f"https://d32dm0rphc51dk.cloudfront.net/abc123_{i}/square.jpg"
                    } for i in range(min(limit, 3))
                ])
                
            if search_type in ["all", "artist"] and len(results) < limit:
                results.extend([
                    {
                        "name": f"{query} {i+1}",
                        "type": "artist",
                        "nationality": "Vietnamese" if i % 2 == 0 else "International",
                        "birthday": f"19{80+i}",
                        "hometown": "Hà Nội, Việt Nam" if i % 2 == 0 else "New York, USA",
                        "artworks_count": 100 + i*10,
                        "followers_count": 5000 + i*1000,
                        "biography": f"{query} is a renowned artist known for their unique style and innovative techniques.",
                        "url": f"https://www.artsy.net/artist/{query.lower().replace(' ', '-')}-{i}",
                        "image_url": f"https://d32dm0rphc51dk.cloudfront.net/xyz456_{i}/square.jpg"
                    } for i in range(min(limit - len(results), 2))
                ])
                
            if search_type in ["all", "show"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"{query} Exhibition {i+1}",
                        "type": "show",
                        "gallery": f"{query} Gallery",
                        "location": "Hồ Chí Minh, Việt Nam" if i % 2 == 0 else "Paris, France",
                        "start_date": "2025-01-01",
                        "end_date": "2025-03-31",
                        "description": f"A major exhibition featuring works related to {query}.",
                        "artists_count": 5 + i,
                        "artworks_count": 20 + i*5,
                        "url": f"https://www.artsy.net/show/{query.lower().replace(' ', '-')}-exhibition-{i}",
                        "image_url": f"https://d32dm0rphc51dk.cloudfront.net/def789_{i}/square.jpg"
                    } for i in range(min(limit - len(results), 2))
                ])
                
            if search_type == "gene" and not results:
                results = [
                    {
                        "name": f"{query} Art Movement",
                        "type": "gene",
                        "description": f"A comprehensive look at the {query} art movement and its influence on contemporary art.",
                        "artworks_count": 1000 + len(query)*10,
                        "artists_count": 100 + len(query),
                        "url": f"https://www.artsy.net/gene/{query.lower().replace(' ', '-')}",
                        "image_url": "https://d32dm0rphc51dk.cloudfront.net/ghi012/square.jpg"
                    }
                ]
            
            result = {
                "status": "success",
                "source": "Artsy",
                "query": query,
                "search_type": search_type,
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Artsy: {str(e)}")
            result = {
                "status": "error",
                "source": "Artsy",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm tác phẩm {query}",
                        "url": f"https://www.artsy.net/search?q={query}",
                        "summary": f"Tìm kiếm tác phẩm nghệ thuật liên quan đến {query} trên Artsy"
                    },
                    {
                        "title": f"Nghệ sĩ {query}",
                        "url": f"https://www.artsy.net/search/artists?q={query}",
                        "summary": f"Tìm kiếm thông tin về nghệ sĩ {query} trên Artsy"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
