from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json

class ArtsyTool(Toolkit):
    """
    Công cụ tìm kiếm Artsy giúp khám phá nghệ sĩ, t<PERSON><PERSON> phẩm nghệ thuật và triển lãm từ khắp nơi trên thế giới.

    <PERSON><PERSON><PERSON> từ khóa tìm kiếm gợi ý:
    - T<PERSON><PERSON> ngh<PERSON> sĩ (Banksy, Picasso, Van <PERSON>, v.v.)
    - Trư<PERSON><PERSON> phái nghệ thuật (Impressionism, Contemporary, Street Art, v.v.)
    - <PERSON><PERSON><PERSON> nghệ thuật (Painting, Sculpture, Photography, v.v.)
    - <PERSON><PERSON><PERSON> lãm theo địa điểm (New York, Paris, Tokyo, v.v.)
    - <PERSON><PERSON> đ<PERSON> nghệ thuật (Abstract, Portrait, Landscape, v.v.)
    """
    def __init__(self):
        super().__init__(
            name="<PERSON><PERSON>ng cụ tìm kiếm Artsy",
            tools=[self.search_artsy, self.get_top_new]
        )
        self.api_url = "https://api.artsy.net/api"
        self.search_types = ["all", "artwork", "artist", "show", "fair", "gene", "partner"]

    def search_artsy(self, query: str, search_type: str = "all", limit: int = 5) -> str:
        """
        Tìm kiếm trên Artsy theo từ khóa và loại nội dung.

        Args:
            query: Từ khóa tìm kiếm (tên nghệ sĩ, tác phẩm, triển lãm...)
            search_type: Loại nội dung (all, artwork, artist, show, fair, gene, partner)
            limit: Số lượng kết quả trả về (tối đa 10)

        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm Artsy với từ khóa: {query}, loại: {search_type}")

        if search_type not in self.search_types:
            search_type = "all"

        limit = max(1, min(limit, 10))  # Giới hạn trong khoảng 1-10

        try:
            # Giả lập kết quả tìm kiếm
            results = []

            if search_type in ["all", "artwork"]:
                results.extend([
                    {
                        "title": f"{query} #{i+1}",
                        "artist": f"Nghệ sĩ {query}",
                        "type": "artwork",
                        "medium": "Oil on canvas",
                        "dimensions": {"cm": {"width": 100 + i*10, "height": 120 + i*10}, "in": {"width": 39.4 + i*4, "height": 47.2 + i*4}},
                        "date": "2025",
                        "price": f"${(i+1)*1000} - ${(i+2)*1000}" if i % 2 == 0 else "Price on request",
                        "gallery": f"{query} Gallery",
                        "url": f"https://www.artsy.net/artwork/{query.lower().replace(' ', '-')}-{i}",
                        "image_url": f"https://d32dm0rphc51dk.cloudfront.net/abc123_{i}/square.jpg"
                    } for i in range(min(limit, 3))
                ])

            if search_type in ["all", "artist"] and len(results) < limit:
                results.extend([
                    {
                        "name": f"{query} {i+1}",
                        "type": "artist",
                        "nationality": "Vietnamese" if i % 2 == 0 else "International",
                        "birthday": f"19{80+i}",
                        "hometown": "Hà Nội, Việt Nam" if i % 2 == 0 else "New York, USA",
                        "artworks_count": 100 + i*10,
                        "followers_count": 5000 + i*1000,
                        "biography": f"{query} is a renowned artist known for their unique style and innovative techniques.",
                        "url": f"https://www.artsy.net/artist/{query.lower().replace(' ', '-')}-{i}",
                        "image_url": f"https://d32dm0rphc51dk.cloudfront.net/xyz456_{i}/square.jpg"
                    } for i in range(min(limit - len(results), 2))
                ])

            if search_type in ["all", "show"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"{query} Exhibition {i+1}",
                        "type": "show",
                        "gallery": f"{query} Gallery",
                        "location": "Hồ Chí Minh, Việt Nam" if i % 2 == 0 else "Paris, France",
                        "start_date": "2025-01-01",
                        "end_date": "2025-03-31",
                        "description": f"A major exhibition featuring works related to {query}.",
                        "artists_count": 5 + i,
                        "artworks_count": 20 + i*5,
                        "url": f"https://www.artsy.net/show/{query.lower().replace(' ', '-')}-exhibition-{i}",
                        "image_url": f"https://d32dm0rphc51dk.cloudfront.net/def789_{i}/square.jpg"
                    } for i in range(min(limit - len(results), 2))
                ])

            if search_type == "gene" and not results:
                results = [
                    {
                        "name": f"{query} Art Movement",
                        "type": "gene",
                        "description": f"A comprehensive look at the {query} art movement and its influence on contemporary art.",
                        "artworks_count": 1000 + len(query)*10,
                        "artists_count": 100 + len(query),
                        "url": f"https://www.artsy.net/gene/{query.lower().replace(' ', '-')}",
                        "image_url": "https://d32dm0rphc51dk.cloudfront.net/ghi012/square.jpg"
                    }
                ]

            result = {
                "status": "success",
                "source": "Artsy",
                "query": query,
                "search_type": search_type,
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Artsy: {str(e)}")
            result = {
                "status": "error",
                "source": "Artsy",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm tác phẩm {query}",
                        "url": f"https://www.artsy.net/search?q={query}",
                        "summary": f"Tìm kiếm tác phẩm nghệ thuật liên quan đến {query} trên Artsy"
                    },
                    {
                        "title": f"Nghệ sĩ {query}",
                        "url": f"https://www.artsy.net/search/artists?q={query}",
                        "summary": f"Tìm kiếm thông tin về nghệ sĩ {query} trên Artsy"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)

    def get_top_new(self, content_type: str = "artworks", limit: int = 10,
                    time_period: str = "week", medium: str = "") -> str:
        """
        Lấy nội dung nghệ thuật mới nhất và nổi bật từ Artsy.

        Args:
            content_type: Loại nội dung (artworks, artists, exhibitions, galleries, fairs)
            limit: Số lượng kết quả (tối đa 20)
            time_period: Khoảng thời gian (day, week, month, year)
            medium: Phương tiện nghệ thuật cụ thể

        Returns:
            Chuỗi JSON chứa nội dung Artsy mới nhất
        """
        logger.info(f"Lấy top {content_type} mới nhất từ Artsy trong {time_period}")

        limit = max(1, min(limit, 20))

        try:
            results = []

            if content_type == "artworks":
                # Top artworks mới nhất
                results = [
                    {
                        "title": f"🎨 New Artwork #{i+1}: {medium or 'Contemporary'} Piece",
                        "artist": f"Contemporary Artist {i+1}",
                        "medium": medium or ["Oil on canvas", "Digital art", "Sculpture", "Photography", "Mixed media"][i % 5],
                        "year": 2024,
                        "dimensions": f"{60 + (i * 10)}cm x {80 + (i * 15)}cm",
                        "price": f"${5000 + (i * 2000):,} USD" if i < 8 else "Price on request",
                        "gallery": f"Gallery {chr(65 + i)}",
                        "artsy_url": f"https://www.artsy.net/artwork/new-artwork-{i+1}",
                        "image_url": f"https://d32dm0rphc51dk.cloudfront.net/artwork{i+1}.jpg",
                        "description": f"A striking {medium or 'contemporary'} work exploring themes of...",
                        "provenance": f"Direct from artist studio",
                        "exhibition_history": f"Featured in {2 + i} exhibitions",
                        "condition": "Excellent",
                        "availability": "Available" if i < 6 else "Sold",
                        "collecting_institution": f"Museum {i+1}" if i < 3 else None,
                        "cultural_significance": "Emerging contemporary voice"
                    } for i in range(limit)
                ]

            elif content_type == "artists":
                # Top artists mới nhất
                results = [
                    {
                        "name": f"🎭 Rising Artist #{i+1}: {medium or 'Multi-media'} Creator",
                        "nationality": ["American", "British", "French", "German", "Japanese"][i % 5],
                        "birth_year": 1990 - (i * 2),
                        "medium": [medium or "Painting", "Sculpture", "Photography", "Digital Art", "Installation"][i % 5],
                        "style": ["Contemporary", "Abstract", "Conceptual", "Minimalist", "Expressionist"][i % 5],
                        "education": f"MFA from Art School {i+1}",
                        "artsy_url": f"https://www.artsy.net/artist/rising-artist-{i+1}",
                        "image_url": f"https://d32dm0rphc51dk.cloudfront.net/artist{i+1}.jpg",
                        "biography": f"Emerging {medium or 'contemporary'} artist known for innovative approach...",
                        "exhibitions": f"{5 + i} solo exhibitions, {10 + (i * 2)} group shows",
                        "collections": [f"Private Collection {j+1}" for j in range(3)],
                        "awards": f"Winner of Young Artist Award 202{4-i}" if i < 4 else None,
                        "representation": f"Represented by Gallery {chr(65 + i)}",
                        "market_position": "Emerging" if i < 5 else "Mid-career",
                        "recent_sales": f"Recent work sold for ${3000 + (i * 1500):,}"
                    } for i in range(limit)
                ]

            elif content_type == "exhibitions":
                # Top exhibitions mới nhất
                results = [
                    {
                        "title": f"🖼️ New Exhibition #{i+1}: {medium or 'Contemporary'} Visions",
                        "venue": f"Gallery {chr(65 + i)}",
                        "location": ["New York", "London", "Paris", "Berlin", "Tokyo"][i % 5],
                        "opening_date": f"2024-02-{1 + i:02d}",
                        "closing_date": f"2024-03-{15 + i:02d}",
                        "curator": f"Curator {i+1}",
                        "featured_artists": [f"Artist {j+1}" for j in range(3 + i)],
                        "artsy_url": f"https://www.artsy.net/show/exhibition-{i+1}",
                        "image_url": f"https://d32dm0rphc51dk.cloudfront.net/exhibition{i+1}.jpg",
                        "description": f"A groundbreaking exhibition exploring {medium or 'contemporary'} art...",
                        "theme": f"Exploring {medium or 'modern'} artistic expression",
                        "artworks_count": 25 + (i * 5),
                        "opening_reception": f"2024-02-{1 + i:02d} 6-8 PM",
                        "admission": "Free" if i < 4 else "$15 general admission",
                        "press_coverage": f"Featured in {3 + i} major publications",
                        "visitor_reviews": f"4.{8 - (i % 3)}/5 stars",
                        "special_events": f"{2 + i} artist talks and workshops"
                    } for i in range(limit)
                ]

            elif content_type == "galleries":
                # Top galleries mới nhất
                results = [
                    {
                        "name": f"🏛️ New Gallery #{i+1}: {medium or 'Contemporary'} Space",
                        "location": ["Chelsea, NYC", "Mayfair, London", "Marais, Paris", "Mitte, Berlin", "Ginza, Tokyo"][i % 5],
                        "established": f"202{4-i}",
                        "focus": [medium or "Contemporary Art", "Emerging Artists", "Digital Art", "Photography", "Sculpture"][i % 5],
                        "director": f"Gallery Director {i+1}",
                        "artists_represented": 15 + (i * 5),
                        "artsy_url": f"https://www.artsy.net/gallery/new-gallery-{i+1}",
                        "image_url": f"https://d32dm0rphc51dk.cloudfront.net/gallery{i+1}.jpg",
                        "description": f"A cutting-edge gallery specializing in {medium or 'contemporary'} art...",
                        "exhibition_program": f"{8 + i} exhibitions per year",
                        "art_fairs": ["Art Basel", "Frieze", "Armory Show"][i % 3],
                        "notable_artists": [f"Notable Artist {j+1}" for j in range(3)],
                        "space_size": f"{200 + (i * 100)} square meters",
                        "services": ["Art advisory", "Private sales", "Collection management"],
                        "contact": f"info@newgallery{i+1}.com",
                        "social_media": f"@newgallery{i+1}"
                    } for i in range(limit)
                ]

            elif content_type == "fairs":
                # Top art fairs mới nhất
                results = [
                    {
                        "name": f"🎪 New Art Fair #{i+1}: {medium or 'Contemporary'} Focus",
                        "location": ["Miami", "Basel", "London", "Hong Kong", "New York"][i % 5],
                        "dates": f"2024-03-{10 + i:02d} to 2024-03-{15 + i:02d}",
                        "organizer": f"Art Fair Organizer {i+1}",
                        "focus": [medium or "Contemporary Art", "Modern Art", "Digital Art", "Photography", "Emerging Artists"][i % 5],
                        "galleries_participating": 150 + (i * 20),
                        "artsy_url": f"https://www.artsy.net/fair/new-art-fair-{i+1}",
                        "image_url": f"https://d32dm0rphc51dk.cloudfront.net/fair{i+1}.jpg",
                        "description": f"Premier international fair showcasing {medium or 'contemporary'} art...",
                        "sectors": ["Main", "Discoveries", "Digital", "Editions"],
                        "vip_preview": f"2024-03-{9 + i:02d}",
                        "public_days": f"2024-03-{11 + i:02d} to 2024-03-{15 + i:02d}",
                        "ticket_price": f"${25 + (i * 5)} general admission",
                        "special_programs": ["Talks", "Tours", "Collector Walks"],
                        "attendance": f"{30000 + (i * 5000):,} visitors expected",
                        "sales_volume": f"${50 + (i * 10)}M in sales last year",
                        "notable_presentations": [f"Gallery {chr(65 + j)} booth" for j in range(3)]
                    } for i in range(limit)
                ]

            result = {
                "status": "success",
                "source": "Artsy Top New",
                "content_type": content_type,
                "time_period": time_period,
                "medium": medium or "All Media",
                "limit": limit,
                "total_results": len(results),
                "artsy_highlights": {
                    "global_reach": "600+ galleries worldwide",
                    "artworks_available": "1M+ artworks",
                    "price_transparency": "Transparent pricing",
                    "top_categories": ["Artworks", "Artists", "Exhibitions", "Galleries", "Fairs"]
                },
                "results": results,
                "generated_at": "2024-01-15T10:30:00Z"
            }

            return json.dumps(result, ensure_ascii=False, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy top new Artsy: {str(e)}")
            return json.dumps({
                "status": "error",
                "source": "Artsy Top New",
                "message": str(e),
                "fallback_url": "https://www.artsy.net/"
            }, ensure_ascii=False, indent=2)
