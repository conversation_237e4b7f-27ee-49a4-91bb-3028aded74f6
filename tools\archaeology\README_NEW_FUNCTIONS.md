# Archaeology Tools - Enhanced Architecture & "Get Recent/Popular" Functions

Tài liệu này mô tả việc cải tiến archaeology tools và các hàm mới được thêm vào để lấy nội dung recent/popular.

## Tổng quan

Archaeology tools đã được cải tiến từ 5 file riêng biệt thành hệ thống hoàn chỉnh:

1. **Archnet Tools** - `archnet_tools.py` (đã cải tiến)
2. **British Museum Tools** - `british_museum_tools.py` (đã cải tiến)
3. **Met Museum Tools** - `met_museum_tools.py` (giữ nguyên)
4. **Internet Archive Tools** - `internet_archive_archaeo_tools.py` (giữ nguyên)
5. **Wikipedia Archaeology Tools** - `wikipedia_archaeology_tools.py` (giữ nguyên)
6. **Archaeology Search Toolkit** - `archaeology_search_toolkit.py` (mới)

## Chi tiết các cải tiến

### 1. Archnet Tools (<PERSON><PERSON> cả<PERSON> tiến)

#### Hàm hiện có:
- `search_archnet(query, limit=5)` - Chuyển từ async sang sync

#### Hàm mới:
- `get_recent_discoveries(limit=10, days_back=30, region=None)` - Lấy discoveries mới
- `get_trending_sites(limit=10, region=None)` - Lấy sites phổ biến

**Ví dụ sử dụng:**
```python
from tools.archaeology.archnet_tools import ArchnetTools

tool = ArchnetTools()
recent = tool.get_recent_discoveries(5, 30, "Middle East")
trending = tool.get_trending_sites(5, "Mediterranean")
```

### 2. British Museum Tools (Đã cải tiến)

#### Hàm hiện có:
- `search_british_museum(query, limit=5)` - Chuyển từ async sang sync

#### Hàm mới:
- `get_recent_acquisitions(limit=10, days_back=90, department=None)` - Lấy acquisitions mới
- `get_popular_artifacts(limit=10, department=None)` - Lấy artifacts phổ biến

**Ví dụ sử dụng:**
```python
from tools.archaeology.british_museum_tools import BritishMuseumTools

tool = BritishMuseumTools()
recent = tool.get_recent_acquisitions(5, 90, "Egyptian Antiquities")
popular = tool.get_popular_artifacts(5, "Greek and Roman Antiquities")
```

### 3. Met Museum Tools (Cần cải tiến)

**Hàm cần thêm:**
- `get_recent_additions(limit=10, days_back=90, department=None)`
- `get_highlighted_objects(limit=10, department=None)`

### 4. Internet Archive Tools (Cần cải tiến)

**Hàm cần thêm:**
- `get_recent_publications(limit=10, days_back=30, subject=None)`
- `get_popular_archaeology_books(limit=10, subject=None)`

### 5. Wikipedia Archaeology Tools (Cần cải tiến)

**Hàm cần thêm:**
- `get_recent_archaeology_articles(limit=10, days_back=30, language="en")`
- `get_trending_archaeology_topics(limit=10, region=None)`

### 6. Archaeology Search Toolkit (Mới)

Tool chia sub question hoàn toàn mới cho archaeology research.

#### Hàm tạo từ khóa thông thường:
- `generate_archnet_keywords(region, site=None, period=None)`
- `generate_british_museum_keywords(culture, object_type=None, period=None)`
- `generate_met_museum_keywords(culture, object_type=None)`
- `generate_internet_archive_keywords(subject, type="texts")`
- `generate_wikipedia_archaeology_keywords(concept)`

#### Hàm tạo từ khóa cho recent/popular:
- `generate_archnet_recent_keywords(region=None, days_back=30)`
- `generate_museum_recent_keywords(museum, department=None)`
- `generate_archive_recent_keywords(subject, days_back=30)`
- `generate_wikipedia_archaeology_recent_keywords(days_back=30, language="en")`

**Ví dụ sử dụng:**
```python
from tools.archaeology.archaeology_search_toolkit import ArchaeologySearchToolkit

toolkit = ArchaeologySearchToolkit()
keywords = toolkit.generate_archnet_keywords("Mesopotamia", "Ur", "Early Dynastic")
recent_keywords = toolkit.generate_archnet_recent_keywords("Middle East", 30)
```

## Tính năng chung

### Cải tiến Architecture
- **Sync Operations**: Chuyển từ async sang sync để consistent với astronomy/ai_robotics
- **Caching System**: Thêm cache với TTL phù hợp cho từng loại content
- **Retry Mechanism**: Exponential backoff cho API calls
- **Error Handling**: Comprehensive error handling với fallback data

### Fallback Data
Tất cả các hàm đều có cơ chế fallback data chất lượng cao:
- **Archnet**: Archaeological sites từ các regions nổi tiếng
- **British Museum**: Famous artifacts như Rosetta Stone, Elgin Marbles
- **Met Museum**: Art objects từ major civilizations
- **Internet Archive**: Archaeological publications và reports
- **Wikipedia**: High-quality archaeology articles

### Định dạng từ khóa đặc biệt

#### Archnet:
- `region/site:period` (e.g., 'Mesopotamia/Ur:Early Dynastic')
- `culture:artifact_type` (e.g., 'Maya:ceramic')

#### British Museum:
- `culture,object_type,period` (e.g., 'Egyptian,statue,New Kingdom')
- `registration_number` (e.g., '1872,0121.1')

#### Met Museum:
- `culture object_type` (e.g., 'Greek sculpture')
- `period:region` (e.g., 'Roman:Italy')

#### Internet Archive:
- `site.year.author` (e.g., 'pompeii.1912.mau')
- `subject:archaeology` (e.g., 'subject:mesopotamian archaeology')

#### Wikipedia:
- `culture archaeology` (e.g., 'Maya archaeology')
- `site_name` (e.g., 'Angkor Wat')

## Cấu trúc thư mục

```
tools/archaeology/
├── __init__.py ✅ (mới)
├── archnet_tools.py ✅ (đã cải tiến)
├── british_museum_tools.py ✅ (đã cải tiến)
├── met_museum_tools.py (cần cải tiến)
├── internet_archive_archaeo_tools.py (cần cải tiến)
├── wikipedia_archaeology_tools.py (cần cải tiến)
├── archaeology_search_toolkit.py ✅ (mới)
├── test_new_functions.py ✅ (mới)
└── README_NEW_FUNCTIONS.md ✅ (mới)
```

## Test

Chạy file test để kiểm tra các hàm đã cải tiến:
```bash
cd tools/archaeology
python test_new_functions.py
```

## Trạng thái hiện tại

### ✅ Đã hoàn thành:
1. **Archnet Tools** - Hoàn chỉnh với 2 hàm mới
2. **British Museum Tools** - Hoàn chỉnh với 2 hàm mới
3. **Archaeology Search Toolkit** - Hoàn chỉnh với 9 hàm
4. **Package Structure** - __init__.py, test script, README

### ⏳ Cần hoàn thiện:
1. **Met Museum Tools** - Cần thêm 2 hàm get_recent/popular
2. **Internet Archive Tools** - Cần thêm 2 hàm get_recent/popular
3. **Wikipedia Archaeology Tools** - Cần thêm 2 hàm get_recent/popular

## So sánh với Astronomy/AI Robotics

| Feature | Astronomy | AI Robotics | Archaeology |
|---------|-----------|-------------|-------------|
| Số tool riêng biệt | 5 + 1 toolkit | 5 + 1 toolkit | 5 + 1 toolkit |
| Hàm get_recent/popular | ✅ 4/5 tools | ✅ 5/5 tools | ✅ 2/5 tools |
| Search toolkit | ✅ | ✅ | ✅ |
| Fallback data | ✅ | ✅ | ✅ |
| Sync operations | ✅ | ✅ | ✅ (2/5) |

## Roadmap

### Phase 1 (Hoàn thành):
- ✅ Archnet Tools enhancement
- ✅ British Museum Tools enhancement  
- ✅ Archaeology Search Toolkit
- ✅ Package structure

### Phase 2 (Tiếp theo):
- ⏳ Met Museum Tools enhancement
- ⏳ Internet Archive Tools enhancement
- ⏳ Wikipedia Archaeology Tools enhancement
- ⏳ Complete testing

### Phase 3 (Tương lai):
- 🔮 Advanced search algorithms
- 🔮 Cross-database correlation
- 🔮 AI-powered content recommendation

Archaeology tools đang trên đường trở thành hệ thống research tools hoàn chỉnh tương tự astronomy và ai_robotics!
