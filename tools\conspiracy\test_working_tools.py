#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho các tool đã hoạt động trong conspiracy tools.
"""

import sys
import os
import json

# Thê<PERSON> thư mục gốc vào Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_above_top_secret():
    """Test Above Top Secret tools."""
    print("=== Testing Above Top Secret Tools ===")
    try:
        from tools.conspiracy.above_top_secret_tools import AboveTopSecretTools
        
        tool = AboveTopSecretTools()
        
        # Test regular search
        print("--- Regular Search ---")
        result1 = tool.search_discussions("UFO sightings", 3)
        print("Search result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test recent discussions
        print("\n--- Recent Discussions ---")
        result2 = tool.get_recent_discussions(3, 7, "UFOs")
        print("Recent discussions result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # Test trending topics
        print("\n--- Trending Topics ---")
        result3 = tool.get_trending_topics(3, "week")
        print("Trending topics result:", result3[:200] + "..." if len(result3) > 200 else result3)
        
        print("✅ Above Top Secret Tools: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing Above Top Secret: {e}")
    print()

def test_conspiracy_search_toolkit():
    """Test Conspiracy Search Toolkit."""
    print("=== Testing Conspiracy Search Toolkit ===")
    try:
        from tools.conspiracy.conspiracy_search_toolkit import ConspiracySearchToolkit
        
        toolkit = ConspiracySearchToolkit()
        
        # Test regular keyword generation
        print("--- Above Top Secret Keywords ---")
        result1 = toolkit.generate_above_top_secret_keywords("UFO sightings", "UFOs")
        print(result1)
        
        print("\n--- Biblioteca Pleyades Keywords ---")
        result2 = toolkit.generate_biblioteca_pleyades_keywords("ancient aliens", "ancient mysteries")
        print(result2)
        
        print("\n--- CIA FOIA Keywords ---")
        result3 = toolkit.generate_cia_foia_keywords("MKUltra", "mind control")
        print(result3)
        
        # Test recent/trending keyword generation
        print("\n--- Above Top Secret Trending Keywords ---")
        result4 = toolkit.generate_above_top_secret_trending_keywords("week", "Conspiracy Theories")
        print(result4)
        
        print("✅ Conspiracy Search Toolkit: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing Search Toolkit: {e}")
    print()

def test_working_tools():
    """Test các tool đã hoạt động."""
    print("=== Testing Working Tools ===")
    
    # Test Biblioteca Pleyades
    try:
        print("--- Biblioteca Pleyades Tools ---")
        from tools.conspiracy.bibliotecapleyades_tools import BibliotecaPleyadesTools
        tool = BibliotecaPleyadesTools()
        result = tool.search_articles("ancient aliens", 2)
        print("Search result:", result[:150] + "..." if len(result) > 150 else result)
        print("✅ Biblioteca Pleyades Tools: SUCCESS")
    except Exception as e:
        print(f"❌ Biblioteca Pleyades tools error: {e}")
    
    # Test CIA FOIA
    try:
        print("\n--- CIA FOIA Tools ---")
        from tools.conspiracy.cia_foia_conspiracy_tools import CIAFOIAConspiracyTools
        tool = CIAFOIAConspiracyTools()
        result1 = tool.search_documents("MKUltra", 2)
        print("Search result:", result1[:150] + "..." if len(result1) > 150 else result1)
        
        result2 = tool.get_popular_searches()
        print("Popular searches:", result2[:150] + "..." if len(result2) > 150 else result2)
        print("✅ CIA FOIA Tools: SUCCESS")
    except Exception as e:
        print(f"❌ CIA FOIA tools error: {e}")
    
    print()

def test_package_import_working():
    """Test package-level imports cho các tool hoạt động."""
    print("=== Testing Working Package Imports ===")
    try:
        from tools.conspiracy import (
            AboveTopSecretTools,
            BibliotecaPleyadesTools,
            CIAFOIAConspiracyTools,
            ConspiracySearchToolkit
        )
        print("✅ Working package imports successful")
        
        # Test instantiation
        tools = [
            AboveTopSecretTools(),
            BibliotecaPleyadesTools(),
            CIAFOIAConspiracyTools(),
            ConspiracySearchToolkit()
        ]
        print("✅ Working tool instantiation successful")
        
    except Exception as e:
        print(f"❌ Package import error: {e}")
    print()

def main():
    """Chạy tất cả các test cho tools hoạt động."""
    print("Testing Working Conspiracy Tools Functions")
    print("=" * 50)
    print()
    
    # Test các tool đã hoạt động
    test_above_top_secret()
    test_conspiracy_search_toolkit()
    test_working_tools()
    test_package_import_working()
    
    print("=" * 50)
    print("Testing completed!")
    print("\n📊 Summary:")
    print("✅ Working: Above Top Secret, Biblioteca Pleyades, CIA FOIA, Search Toolkit")
    print("⏳ Need fixing: Conspiracy Archive, Wikipedia Conspiracy")

if __name__ == "__main__":
    main()
