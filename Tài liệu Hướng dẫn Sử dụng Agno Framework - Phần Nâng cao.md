# Tài liệu Hướng dẫn Sử dụng Agno Framework - <PERSON>ần Nâng cao

Phần tài liệu này tiếp nối phần c<PERSON> bản, đi sâu vào các chủ đề nâng cao hơn trong Agno Framework, bao gồm tương tác với kỹ thuật Chunking và Reranking, lưu trữ và truy xuất dữ liệu với Qdrant local, và xây dựng Agent Teams.






---



# Hướng dẫn Sử dụng Chunking và Reranker trong Agno Framework

## Giới thiệu

Trong các ứng dụng xử lý ngôn ngữ tự nhiên, đặc biệt là các hệ thống Hỏi-<PERSON><PERSON><PERSON> (Question Answering) hoặc Tìm kiếm dựa trên ngữ nghĩa (Semantic Search) sử dụng kỹ thuật Retrieval-Augmented Generation (RAG), việc xử lý các tài liệu lớn là một thách thức phổ biến. Hai kỹ thuật quan trọng để giải quyết vấn đề này là **Chunking** (Phân đoạn) và **Reranking** (Sắp xếp lại).

*   **Chunking:** Chia nhỏ các tài liệu lớn thành các đoạn (chunk) nhỏ hơn, dễ quản lý hơn. Điều này giúp mô hình embedder tạo ra các vector biểu diễn (embedding) chính xác hơn cho từng phần nội dung và giúp quá trình truy xuất thông tin hiệu quả hơn.
*   **Reranking:** Sau khi truy xuất một tập hợp các chunk có khả năng liên quan đến câu hỏi của người dùng (thường dựa trên độ tương đồng vector), reranker sẽ đánh giá lại mức độ liên quan của các chunk này một cách chi tiết hơn và sắp xếp lại chúng theo thứ tự phù hợp nhất.

Agno Framework cung cấp các module và tiện ích để hỗ trợ cả hai quá trình này. Hướng dẫn này sẽ chỉ cách sử dụng các chức năng chunking của Agno và cách tích hợp các mô hình embedder (`nomic-embed-text:latest`) và reranker (`BAAI/bge-reranker-v2-m3`) cụ thể mà bạn yêu cầu.

## 1. Chunking (Phân đoạn) Tài liệu

Agno xử lý việc phân đoạn tài liệu thông qua module `agno.document`. Module này cung cấp các lớp và phương thức để tải và chia nhỏ tài liệu.

### Các khái niệm chính:

*   **`Document`**: Đại diện cho một tài liệu nguồn (ví dụ: file text, PDF, trang web).
*   **`DocumentSplitter`**: Lớp cơ sở cho các chiến lược chia nhỏ tài liệu.
*   **Các lớp `Splitter` cụ thể**: Agno có thể cung cấp các triển khai khác nhau (ví dụ: `CharacterSplitter`, `RecursiveCharacterTextSplitter` - tương tự như trong LangChain) để chia tài liệu theo ký tự, câu, hoặc các quy tắc phức tạp hơn. Quá trình phân tích mã nguồn cho thấy Agno có các cơ chế tương tự.

### Ví dụ sử dụng Chunking:

Giả sử bạn có một file văn bản lớn `my_document.txt`.

```python
from agno.document import Document
from agno.document.splitter import CharacterSplitter # Hoặc một Splitter phù hợp khác

# Đường dẫn đến file tài liệu của bạn
doc_path = "/path/to/your/my_document.txt"

# Tạo đối tượng Document từ file
# Agno có thể có các hàm tiện ích để tải trực tiếp từ file
# Ví dụ: document = Document.from_file(doc_path)
# Hoặc bạn đọc nội dung file và tạo Document:
try:
    with open(doc_path, "r", encoding="utf-8") as f:
        content = f.read()
    document = Document(content=content, name="MySampleDoc")
except FileNotFoundError:
    print(f"Lỗi: Không tìm thấy file {doc_path}")
    exit()

# Chọn và cấu hình Splitter
# Ví dụ: Chia theo ký tự, mỗi chunk tối đa 1000 ký tự, chồng lấp 100 ký tự
splitter = CharacterSplitter(
    chunk_size=1000,
    chunk_overlap=100,
    # separator="\n\n" # Có thể chỉ định ký tự phân tách nếu cần
)

# Thực hiện chia nhỏ tài liệu
chunks = splitter.split_document(document)

# In ra số lượng chunk và nội dung chunk đầu tiên để kiểm tra
print(f"Số lượng chunk được tạo: {len(chunks)}")
if chunks:
    print("\nNội dung chunk đầu tiên:")
    print(chunks[0].content)
    print("--- Metadata chunk đầu tiên ---")
    print(chunks[0].metadata)

```

**Giải thích:**

1.  Chúng ta import các lớp cần thiết từ `agno.document`.
2.  Tải nội dung tài liệu vào đối tượng `Document`.
3.  Khởi tạo một `Splitter` (ví dụ: `CharacterSplitter`) với các tham số như `chunk_size` (kích thước chunk mong muốn) và `chunk_overlap` (số lượng ký tự chồng lấp giữa các chunk liên tiếp để tránh mất ngữ cảnh).
4.  Gọi phương thức `split_document` để thực hiện việc chia nhỏ.
5.  Kết quả trả về là một danh sách các đối tượng `Document`, mỗi đối tượng đại diện cho một chunk. Mỗi chunk thường chứa nội dung và metadata (ví dụ: nguồn gốc, vị trí trong tài liệu gốc).

## 2. Sử dụng Embedder (`nomic-embed-text:latest`)

Sau khi có các chunk, bước tiếp theo là chuyển đổi chúng thành các vector embedding bằng một mô hình embedder. Agno hỗ trợ nhiều loại embedder thông qua module `agno.embedder`.

Để sử dụng `nomic-embed-text:latest`, bạn có thể cần một môi trường hỗ trợ chạy mô hình này, ví dụ như Ollama. Agno cung cấp tích hợp với Ollama.

### Tích hợp `nomic-embed-text` qua Ollama:

**Bước 1: Đảm bảo Ollama đang chạy và đã tải model `nomic-embed-text`**

Bạn cần cài đặt Ollama và chạy lệnh sau trong terminal để tải model:

```bash
ollama pull nomic-embed-text:latest
```

Đảm bảo Ollama server đang chạy (thường là mặc định sau khi cài đặt).

**Bước 2: Sử dụng `OllamaEmbedder` trong Agno**

```python
from agno.embedder.ollama import OllamaEmbedder
# Giả sử 'chunks' là danh sách các Document đã được chia nhỏ từ bước trước

# Khởi tạo OllamaEmbedder, trỏ đến model nomic-embed-text
embedder = OllamaEmbedder(
    model="nomic-embed-text:latest",
    api_url="http://localhost:11434" # URL mặc định của Ollama API
)

# Lấy nội dung text từ các chunk
chunk_contents = [chunk.content for chunk in chunks]

# Tạo embeddings cho các chunk
# Lưu ý: Quá trình này có thể mất thời gian tùy thuộc vào số lượng chunk và hiệu năng máy
try:
    print("Đang tạo embeddings...")
    embeddings = embedder.embed_texts(texts=chunk_contents)
    print(f"Đã tạo thành công {len(embeddings)} embeddings.")

    # Kiểm tra kích thước của embedding đầu tiên
    if embeddings:
        print(f"Kích thước embedding: {len(embeddings[0])}")

    # Tại đây, bạn có thể lưu các chunk và embeddings tương ứng vào Vector DB (như Qdrant)
    # Ví dụ: vector_db.add_documents(chunks, embeddings)

except Exception as e:
    print(f"Lỗi khi tạo embeddings: {e}")

```

**Giải thích:**

1.  Import `OllamaEmbedder` từ `agno.embedder.ollama`.
2.  Khởi tạo `OllamaEmbedder`, chỉ định tên model (`nomic-embed-text:latest`) và URL của Ollama API (thường là `http://localhost:11434`).
3.  Trích xuất nội dung văn bản từ danh sách các `Document` chunk.
4.  Gọi phương thức `embed_texts` để tạo vector embedding cho tất cả các chunk. Phương thức này sẽ gửi yêu cầu đến Ollama API để thực hiện việc embedding.
5.  Kết quả `embeddings` là một danh sách các vector (list of floats), tương ứng với từng chunk.

## 3. Sử dụng Reranker (`BAAI/bge-reranker-v2-m3`)

Sau khi bạn truy xuất một danh sách các chunk tiềm năng từ Vector DB dựa trên câu hỏi của người dùng, reranker sẽ giúp đánh giá lại và sắp xếp chúng theo mức độ liên quan chính xác hơn.

Agno hỗ trợ tích hợp các mô hình reranker, thường là từ Hugging Face, thông qua module `agno.reranker`.

### Tích hợp `BAAI/bge-reranker-v2-m3`:

Agno có thể sử dụng các mô hình từ Hugging Face Hub. Bạn cần đảm bảo đã cài đặt các thư viện cần thiết (thường là `transformers` và `torch` hoặc `tensorflow`).

```python
from agno.reranker.hf import HfReranker
# Giả sử bạn đã có:
# - query: str = "Câu hỏi của người dùng"
# - retrieved_chunks: List[Document] = Danh sách các chunk được truy xuất từ Vector DB

# Khởi tạo HfReranker với model chỉ định
reranker = HfReranker(model="BAAI/bge-reranker-v2-m3")

# Lấy nội dung từ các chunk đã truy xuất
retrieved_contents = [chunk.content for chunk in retrieved_chunks]

# Thực hiện rerank
try:
    print("Đang thực hiện rerank...")
    # Phương thức rerank thường nhận câu hỏi và danh sách nội dung các chunk
    # Nó trả về danh sách các chỉ số (index) của các chunk theo thứ tự đã sắp xếp lại
    reranked_indices = reranker.rerank(query=query, documents=retrieved_contents)

    # Sắp xếp lại danh sách chunk gốc dựa trên chỉ số trả về
    reranked_chunks = [retrieved_chunks[i] for i in reranked_indices]

    print("Đã hoàn thành rerank.")
    print("Thứ tự chunk sau khi rerank (nội dung):")
    for i, chunk in enumerate(reranked_chunks):
        print(f"{i+1}. {chunk.content[:100]}...") # In 100 ký tự đầu

except Exception as e:
    print(f"Lỗi khi thực hiện rerank: {e}")

```

**Giải thích:**

1.  Import `HfReranker` từ `agno.reranker.hf`.
2.  Khởi tạo `HfReranker` với tên model trên Hugging Face Hub (`BAAI/bge-reranker-v2-m3`). Agno (hoặc thư viện `transformers` bên dưới) sẽ tự động tải model nếu chưa có.
3.  Chuẩn bị câu hỏi (`query`) và danh sách nội dung các chunk đã được truy xuất (`retrieved_contents`).
4.  Gọi phương thức `rerank` (tên phương thức có thể khác một chút tùy phiên bản Agno, cần kiểm tra documentation hoặc mã nguồn cụ thể) để thực hiện việc sắp xếp lại. Phương thức này tính toán điểm số liên quan giữa câu hỏi và từng chunk, sau đó trả về thứ tự mới.
5.  Sắp xếp lại danh sách `retrieved_chunks` ban đầu dựa trên kết quả `reranked_indices` để có được danh sách chunk theo thứ tự liên quan nhất.

## Kết hợp trong Luồng RAG

Một luồng RAG hoàn chỉnh sử dụng chunking, embedding và reranking trong Agno sẽ trông giống như sau (ở mức ý tưởng):

1.  **Tải và Chunking:** Sử dụng `agno.document` và `DocumentSplitter` để chia tài liệu thành các `chunks`.
2.  **Embedding:** Sử dụng `OllamaEmbedder` (với `nomic-embed-text`) để tạo `embeddings` cho các `chunks`.
3.  **Lưu trữ:** Lưu `chunks` và `embeddings` vào một Vector Database (ví dụ: Qdrant, sẽ được đề cập trong phần sau).
4.  **Truy vấn:** Khi có `query` từ người dùng:
    a.  Tạo embedding cho `query` bằng cùng `OllamaEmbedder`.
    b.  Tìm kiếm các chunk tương đồng nhất trong Vector DB dựa trên query embedding, nhận về `retrieved_chunks`.
5.  **Reranking:** Sử dụng `HfReranker` (với `BAAI/bge-reranker-v2-m3`) để sắp xếp lại `retrieved_chunks` thành `reranked_chunks`.
6.  **Tạo Ngữ cảnh:** Chọn top N chunk từ `reranked_chunks` để tạo thành ngữ cảnh (context).
7.  **Tạo Phản hồi:** Cung cấp `query` và `context` cho một LLM (ví dụ: `ollama/qwen2:1.5b` thông qua `agno.agent` hoặc `agno.models`) để tạo ra câu trả lời cuối cùng cho người dùng.

## Kết luận

Agno Framework cung cấp các công cụ cần thiết để thực hiện chunking và tích hợp các mô hình embedder, reranker tùy chỉnh. Bằng cách sử dụng `DocumentSplitter` để chia nhỏ tài liệu, `OllamaEmbedder` để tích hợp `nomic-embed-text`, và `HfReranker` để tích hợp `BAAI/bge-reranker-v2-m3`, bạn có thể xây dựng các luồng RAG mạnh mẽ và hiệu quả, tận dụng khả năng biểu diễn ngữ nghĩa của embedder và khả năng đánh giá lại mức độ liên quan chi tiết của reranker.




---



# Hướng dẫn Sử dụng Qdrant Local với Agno Framework

## Giới thiệu về Qdrant

Qdrant là một cơ sở dữ liệu vector (Vector Database) mã nguồn mở, được thiết kế để lưu trữ, tìm kiếm và quản lý các vector embedding hiệu quả. Nó đặc biệt hữu ích trong các ứng dụng như tìm kiếm ngữ nghĩa, hệ thống gợi ý, nhận dạng khuôn mặt, và là một thành phần quan trọng trong kiến trúc Retrieval-Augmented Generation (RAG).

Agno Framework cung cấp khả năng tích hợp với Qdrant thông qua module `agno.vectordb.qdrant`, cho phép bạn dễ dàng lưu trữ các chunk tài liệu và vector embedding tương ứng, sau đó thực hiện tìm kiếm tương đồng để truy xuất thông tin liên quan.

Hướng dẫn này tập trung vào việc thiết lập và sử dụng Qdrant chạy trên máy cục bộ (local) cùng với Agno.

## 1. Thiết lập Qdrant Local

Cách đơn giản nhất để chạy Qdrant local là sử dụng Docker.

**Bước 1: Cài đặt Docker**

Nếu bạn chưa cài đặt Docker, hãy làm theo hướng dẫn trên trang chủ Docker: [https://docs.docker.com/get-docker/](https://docs.docker.com/get-docker/)

**Bước 2: Chạy Qdrant Container**

Mở terminal và chạy lệnh sau:

```bash
docker run -p 6333:6333 -p 6334:6334 \
    -v $(pwd)/qdrant_storage:/qdrant/storage:z \
    qdrant/qdrant:latest
```

**Giải thích lệnh:**

*   `docker run`: Chạy một container mới.
*   `-p 6333:6333`: Ánh xạ cổng 6333 của container (cổng REST API mặc định của Qdrant) ra cổng 6333 trên máy host của bạn.
*   `-p 6334:6334`: Ánh xạ cổng 6334 của container (cổng gRPC mặc định) ra cổng 6334 trên máy host.
*   `-v $(pwd)/qdrant_storage:/qdrant/storage:z`: Tạo một volume tên là `qdrant_storage` trong thư mục hiện tại của bạn và ánh xạ nó vào thư mục lưu trữ dữ liệu của Qdrant trong container. Điều này đảm bảo dữ liệu của bạn được lưu trữ bền vững ngay cả khi container bị xóa và tạo lại. `:z` là tùy chọn cho SELinux, có thể cần thiết trên một số hệ thống Linux.
*   `qdrant/qdrant:latest`: Sử dụng image Qdrant mới nhất từ Docker Hub.

Sau khi chạy lệnh này, Qdrant sẽ khởi động và sẵn sàng nhận kết nối tại `http://localhost:6333`.

## 2. Tích hợp Qdrant vào Agno

Agno sử dụng lớp `QdrantDb` trong module `agno.vectordb.qdrant` để tương tác với Qdrant.

### Khởi tạo `QdrantDb`:

```python
from agno.vectordb.qdrant import QdrantDb
from qdrant_client.http.models import Distance, VectorParams

# Cấu hình kết nối đến Qdrant local
qdrant_url = "http://localhost:6333" # Hoặc chỉ "localhost" nếu dùng client gRPC mặc định
# qdrant_api_key = None # Không cần API key cho local trừ khi bạn cấu hình

# Tên collection bạn muốn sử dụng trong Qdrant
collection_name = "my_agno_documents"

# Kích thước vector embedding (phải khớp với output của embedder bạn dùng)
# Ví dụ: nomic-embed-text thường có kích thước 768
vector_size = 768 # Thay đổi nếu embedder của bạn khác

# Khởi tạo đối tượng QdrantDb
vector_db = QdrantDb(
    collection=collection_name,
    url=qdrant_url,
    # api_key=qdrant_api_key,
    # prefer_grpc=False, # Đặt True nếu muốn dùng gRPC (cổng 6334)
)

# (Tùy chọn nhưng khuyến nghị) Tạo collection nếu chưa tồn tại
# Bạn cần chỉ định kích thước vector và hàm đo khoảng cách
try:
    vector_db.create_collection(
        vector_size=vector_size,
        distance=Distance.COSINE # Hoặc Distance.EUCLID, Distance.DOT
    )
    print(f"Collection 	'{collection_name}	' đã được tạo hoặc đã tồn tại.")
except Exception as e:
    # Xử lý trường hợp collection đã tồn tại với cấu hình khác
    print(f"Lỗi khi tạo collection: {e}")
    # Có thể bạn muốn xóa và tạo lại nếu cần:
    # vector_db.delete_collection()
    # vector_db.create_collection(vector_size=vector_size, distance=Distance.COSINE)

```

**Giải thích:**

1.  Import `QdrantDb` và các thành phần cần thiết từ `qdrant_client` (thư viện Qdrant Python mà Agno sử dụng bên dưới).
2.  Xác định URL của Qdrant local (`http://localhost:6333`).
3.  Đặt tên cho collection sẽ chứa dữ liệu của bạn.
4.  **Quan trọng:** Chỉ định `vector_size` phải khớp chính xác với số chiều của vector mà mô hình embedder (`nomic-embed-text` trong trường hợp này) tạo ra.
5.  Khởi tạo `QdrantDb` với tên collection và URL.
6.  Sử dụng `create_collection` để đảm bảo collection tồn tại với cấu hình đúng (kích thước vector và hàm đo khoảng cách - `COSINE` thường là lựa chọn tốt cho embeddings văn bản).

## 3. Lưu trữ Chunks và Embeddings vào Qdrant

Sau khi bạn đã có danh sách các `chunks` (đối tượng `Document` từ Agno) và `embeddings` (danh sách các vector) tương ứng từ các bước trước (chunking và embedding), bạn có thể lưu chúng vào Qdrant.

```python
# Giả sử bạn đã có:
# - chunks: List[Document] = Danh sách các chunk tài liệu
# - embeddings: List[List[float]] = Danh sách các vector embedding tương ứng
# - vector_db: QdrantDb = Đối tượng QdrantDb đã khởi tạo

if chunks and embeddings and len(chunks) == len(embeddings):
    print(f"Chuẩn bị thêm {len(chunks)} điểm dữ liệu vào Qdrant...")
    try:
        # Agno cung cấp phương thức để thêm documents và embeddings
        # Tên phương thức có thể là add_documents, upsert_documents, etc.
        # Cần kiểm tra tài liệu Agno hoặc mã nguồn cho tên chính xác.
        # Giả sử tên là upsert_documents:
        vector_db.upsert_documents(documents=chunks, embeddings=embeddings)
        print("Đã thêm thành công dữ liệu vào Qdrant.")
    except Exception as e:
        print(f"Lỗi khi thêm dữ liệu vào Qdrant: {e}")
else:
    print("Dữ liệu chunks hoặc embeddings không hợp lệ hoặc không khớp số lượng.")

```

**Giải thích:**

*   Phương thức `upsert_documents` (hoặc tên tương tự) của `QdrantDb` nhận vào danh sách các đối tượng `Document` (chunk) và danh sách các `embeddings` tương ứng.
*   Nó sẽ tạo các điểm dữ liệu (points) trong Qdrant. Mỗi điểm thường bao gồm:
    *   **ID:** Một định danh duy nhất cho chunk (Agno có thể tự tạo hoặc sử dụng ID từ `Document`).
    *   **Vector:** Embedding của chunk đó.
    *   **Payload:** Metadata của chunk (ví dụ: nội dung text gốc, tên tài liệu nguồn, số thứ tự chunk). Điều này rất quan trọng để có thể hiển thị nội dung chunk khi truy xuất.
*   `upsert` có nghĩa là nếu một điểm với ID đã tồn tại, nó sẽ được cập nhật; nếu không, nó sẽ được tạo mới.

## 4. Truy xuất (Tìm kiếm) Thông tin từ Qdrant

Khi người dùng đặt câu hỏi (`query`), bạn cần tạo embedding cho câu hỏi đó và sử dụng nó để tìm kiếm các chunk tương đồng nhất trong Qdrant.

```python
# Giả sử bạn đã có:
# - query: str = "Câu hỏi của người dùng"
# - embedder: OllamaEmbedder = Embedder đã khởi tạo (dùng để tạo query embedding)
# - vector_db: QdrantDb = Đối tượng QdrantDb đã khởi tạo

# Số lượng kết quả muốn truy xuất
num_results = 5

try:
    # Tạo embedding cho câu hỏi
    print(f"Đang tạo embedding cho query: 	'{query}	'")
    query_embedding = embedder.embed_query(query=query)

    # Thực hiện tìm kiếm tương đồng trong Qdrant
    print(f"Đang tìm kiếm {num_results} kết quả tương đồng trong Qdrant...")
    # Phương thức tìm kiếm có thể là search, query, find_similar, etc.
    # Cần kiểm tra tài liệu Agno hoặc mã nguồn.
    # Giả sử tên là search:
    search_results = vector_db.search(query_embedding=query_embedding, limit=num_results)

    print("Kết quả tìm kiếm từ Qdrant:")
    if search_results:
        for i, result in enumerate(search_results):
            # Mỗi kết quả thường chứa ID, điểm số tương đồng (score), và payload
            # Payload chứa thông tin gốc của chunk
            print(f"--- Kết quả {i+1} ---")
            print(f"Score: {result.score:.4f}")
            # Truy cập payload để lấy nội dung chunk
            if result.payload:
                print(f"Content: {result.payload.get(	'content	', 'N/A')[:200]}...") # In 200 ký tự đầu
                print(f"Metadata: {result.payload.get(	'metadata	', {})}")
            else:
                print("Không có payload.")
            # Lưu ý: Cấu trúc của `result` có thể khác nhau tùy phiên bản qdrant_client và cách Agno trả về.
            # Bạn có thể cần print(result) để xem cấu trúc chi tiết.

        # Danh sách các chunk truy xuất này (cần tái tạo đối tượng Document nếu cần)
        # sẽ được đưa vào Reranker ở bước tiếp theo.
        retrieved_chunks_for_reranker = [] # Cần xây dựng lại từ search_results

    else:
        print("Không tìm thấy kết quả nào.")

except Exception as e:
    print(f"Lỗi khi tìm kiếm trong Qdrant: {e}")

```

**Giải thích:**

1.  Tạo embedding cho `query` bằng cùng một `embedder` đã dùng để tạo embedding cho các chunk.
2.  Gọi phương thức `search` (hoặc tên tương tự) của `QdrantDb`, cung cấp `query_embedding` và số lượng kết quả mong muốn (`limit`).
3.  Kết quả trả về (`search_results`) là một danh sách các điểm dữ liệu tìm thấy, thường được sắp xếp theo độ tương đồng giảm dần.
4.  Mỗi kết quả chứa điểm số (`score`) và `payload`. Payload chứa thông tin metadata của chunk gốc, bao gồm cả nội dung văn bản.
5.  Bạn cần trích xuất thông tin cần thiết từ `payload` (đặc biệt là nội dung chunk) để sử dụng trong các bước tiếp theo (ví dụ: đưa vào reranker hoặc LLM).

## Kết luận

Việc tích hợp Qdrant local vào Agno Framework khá đơn giản nhờ các lớp trừu tượng mà Agno cung cấp. Bằng cách chạy Qdrant qua Docker, khởi tạo `QdrantDb` trong Agno để kết nối, sử dụng các phương thức như `upsert_documents` để lưu trữ và `search` để truy xuất, bạn có thể xây dựng một nền tảng vững chắc cho các ứng dụng RAG. Đảm bảo rằng kích thước vector được cấu hình chính xác và xử lý payload từ kết quả tìm kiếm để lấy lại nội dung chunk gốc là những điểm quan trọng cần lưu ý.




---



# Hướng dẫn Sử dụng Agent Teams (Nhóm Agent) trong Agno Framework

## Giới thiệu

Một trong những tính năng mạnh mẽ của Agno Framework là khả năng tạo và quản lý các **Agent Teams** (Nhóm Agent). Thay vì chỉ có một agent đơn lẻ thực hiện nhiệm vụ, bạn có thể tập hợp nhiều agent (hoặc thậm chí các team khác) lại với nhau để giải quyết các vấn đề phức tạp hơn thông qua sự phối hợp và chuyên môn hóa.

Khái niệm Agent Team cho phép bạn:

*   **Phân chia công việc:** Giao các nhiệm vụ con cụ thể cho các agent chuyên biệt (ví dụ: một agent chuyên viết code, một agent chuyên phân tích dữ liệu, một agent chuyên tìm kiếm thông tin).
*   **Phối hợp:** Các agent trong team có thể giao tiếp, chia sẻ thông tin và kết quả công việc với nhau.
*   **Quản lý luồng công việc:** Một agent điều phối (hoặc chính team) có thể quyết định agent nào sẽ thực hiện bước tiếp theo dựa trên trạng thái hiện tại.

Agno cung cấp lớp `Team` trong module `agno.team` để định nghĩa và vận hành các nhóm agent này.

## 1. Các Thành phần Chính của `Team`

Lớp `Team` trong Agno có nhiều tham số cấu hình quan trọng. Dưới đây là một số thành phần cốt lõi:

*   **`members`**: (Bắt buộc) Một danh sách chứa các thành viên của team. Mỗi thành viên có thể là một đối tượng `Agent` hoặc một đối tượng `Team` khác (cho phép tạo cấu trúc team phân cấp).
*   **`mode`**: Xác định cách team điều phối công việc giữa các thành viên. Các chế độ phổ biến có thể bao gồm:
    *   `coordinate` (Mặc định): Một agent điều phối (thường là LLM của chính `Team`) sẽ quyết định agent thành viên nào phù hợp nhất để xử lý yêu cầu hoặc bước tiếp theo, và chuyển giao nhiệm vụ.
    *   `route`: Có thể dựa trên quy tắc hoặc mô hình để định tuyến yêu cầu đến một thành viên cụ thể.
    *   `collaborate`: Các thành viên có thể cùng làm việc song song hoặc tuần tự trên một nhiệm vụ, chia sẻ kết quả.
    *   *(Lưu ý: Các chế độ cụ thể và hành vi chính xác có thể thay đổi tùy phiên bản Agno, cần tham khảo mã nguồn `team.py` để biết chi tiết)*
*   **`model`**: (Tùy chọn) Một đối tượng `Model` (LLM) cho chính team. Mô hình này thường đóng vai trò điều phối viên, quyết định cách phân công nhiệm vụ cho các thành viên dựa trên mô tả và khả năng của họ.
*   **`name`**: Tên của team.
*   **`role`**: Mô tả vai trò hoặc mục đích chung của team.
*   **`instructions`**: Hướng dẫn cho agent điều phối của team về cách quản lý các thành viên và luồng công việc.
*   **`tools`**: Các công cụ có sẵn cho chính agent điều phối của team (khác với công cụ của từng agent thành viên).
*   **`memory`**: Bộ nhớ dùng chung cho team hoặc cho agent điều phối (`TeamMemory`).
*   **`storage`**: Lưu trữ trạng thái của team (`TeamSession`).
*   **`share_member_interactions`**: (Boolean) Nếu `True`, lịch sử tương tác của các thành viên khác sẽ được chia sẻ khi gọi một thành viên, cung cấp ngữ cảnh rộng hơn.

## 2. Định nghĩa một Agent Team

Giả sử chúng ta có hai agent đơn giản:

*   `CoderAgent`: Chuyên viết mã Python.
*   `ReviewerAgent`: Chuyên đánh giá và đề xuất cải tiến mã.

Chúng ta có thể tạo một team để phối hợp hai agent này.

```python
from agno.agent import Agent
from agno.team import Team
from agno.models.ollama import Ollama # Ví dụ sử dụng Ollama

# --- Định nghĩa Agent Thành viên ---

coder_agent = Agent(
    name="Coder",
    role="Viết mã Python dựa trên yêu cầu.",
    instructions=["Chỉ viết mã Python, không giải thích dài dòng."],
    model=Ollama(model="codellama:7b"), # Model chuyên code
    debug_mode=False
)

reviewer_agent = Agent(
    name="Reviewer",
    role="Đánh giá mã Python và đề xuất cải tiến.",
    instructions=[
        "Nhận mã Python và đưa ra nhận xét, gợi ý cụ thể.",
        "Tập trung vào tính đúng đắn, hiệu quả và khả năng đọc."
    ],
    model=Ollama(model="qwen2:1.5b"), # Model tổng quát hơn
    debug_mode=False
)

# --- Định nghĩa Team ---

# Sử dụng một model làm điều phối viên cho team
team_coordinator_model = Ollama(model="qwen2:7b") # Model mạnh hơn để điều phối

coding_team = Team(
    name="CodingTeam",
    role="Nhóm chuyên phát triển và đánh giá mã Python.",
    members=[coder_agent, reviewer_agent],
    model=team_coordinator_model,
    mode="coordinate", # Điều phối viên sẽ quyết định ai làm gì
    instructions=[
        "Bạn là điều phối viên của một nhóm lập trình.",
        "Thành viên Coder sẽ viết mã theo yêu cầu.",
        "Thành viên Reviewer sẽ đánh giá mã do Coder viết.",
        "Khi nhận yêu cầu viết code, hãy giao cho Coder.",
        "Khi Coder hoàn thành, hãy lấy kết quả và giao cho Reviewer đánh giá.",
        "Trả về kết quả cuối cùng sau khi Reviewer hoàn thành."
    ],
    # memory=TeamMemory(...), # Cấu hình bộ nhớ nếu cần
    # storage=Storage(...), # Cấu hình lưu trữ nếu cần
    share_member_interactions=True, # Chia sẻ ngữ cảnh giữa các agent
    debug_mode=True
)

# --- Chạy Team ---
if __name__ == "__main__":
    print("--- Bắt đầu chạy Team ---")
    # Yêu cầu Team thực hiện nhiệm vụ phối hợp
    request = "Viết một hàm Python đơn giản để tính tổng hai số và sau đó đánh giá hàm đó."
    team_response = coding_team.run(request)

    print("\n--- Phản hồi cuối cùng từ Team ---")
    coding_team.print_response(team_response, markdown=True)

    # In lịch sử tương tác (nếu memory được cấu hình)
    # print("\n--- Lịch sử tương tác Team ---")
    # print(coding_team.memory.get_history())
```

**Giải thích:**

1.  Chúng ta định nghĩa hai `Agent` riêng biệt (`coder_agent`, `reviewer_agent`) với vai trò, hướng dẫn và mô hình LLM phù hợp.
2.  Chúng ta tạo một đối tượng `Team` tên là `coding_team`.
3.  `members` được gán danh sách chứa `coder_agent` và `reviewer_agent`.
4.  Chúng ta cung cấp một `model` riêng cho `Team` để đóng vai trò điều phối viên.
5.  `mode` được đặt là `coordinate`.
6.  `instructions` hướng dẫn cho điều phối viên cách phân công công việc: yêu cầu code -> Coder, kết quả của Coder -> Reviewer.
7.  `share_member_interactions=True` cho phép Reviewer biết được yêu cầu ban đầu và mã do Coder viết.
8.  Khi `coding_team.run(request)` được gọi, agent điều phối của team sẽ:
    *   Nhận `request`.
    *   Dựa vào `instructions`, gọi `coder_agent.run("Viết hàm Python tính tổng hai số")` (hoặc tương tự).
    *   Nhận kết quả (mã Python) từ `coder_agent`.
    *   Gọi `reviewer_agent.run("Đánh giá mã sau: [mã từ coder]")`.
    *   Nhận kết quả đánh giá từ `reviewer_agent`.
    *   Tổng hợp và trả về phản hồi cuối cùng cho người dùng.

## 3. Luồng Hoạt động và Giao tiếp

Cơ chế giao tiếp và phối hợp bên trong `Team` thường diễn ra như sau (đặc biệt với mode `coordinate`):

1.  **Nhận Yêu cầu:** `Team` nhận yêu cầu từ người dùng hoặc từ một agent/team cấp cao hơn.
2.  **Điều phối:** Agent điều phối của `Team` (sử dụng `model` của team) phân tích yêu cầu, trạng thái hiện tại, và `instructions` để quyết định hành động tiếp theo. Hành động này thường là chọn một `member` phù hợp và tạo ra một yêu cầu/nhiệm vụ con cho member đó.
3.  **Thực thi Thành viên:** `Team` gọi phương thức `run()` (hoặc `arun()`) của `member` được chọn với nhiệm vụ con. Ngữ cảnh từ các tương tác trước đó (nếu `share_member_interactions=True`) có thể được truyền vào.
4.  **Nhận Kết quả:** `Team` nhận kết quả trả về từ `member`.
5.  **Lặp lại/Tổng hợp:** Agent điều phối của `Team` nhận kết quả từ member, cập nhật trạng thái, và quyết định hành động tiếp theo (có thể là gọi một member khác, gọi lại cùng member với thông tin mới, hoặc tổng hợp kết quả cuối cùng).
6.  **Trả lời:** Khi luồng công việc hoàn tất theo `instructions` hoặc một điều kiện dừng được đáp ứng, `Team` trả về kết quả cuối cùng.

## 4. Lưu ý và Thực tiễn Tốt

*   **Mô hình Điều phối:** Chọn một LLM đủ mạnh và phù hợp cho vai trò điều phối của `Team`. Mô hình này cần hiểu rõ `instructions` và khả năng của từng `member`.
*   **Hướng dẫn Rõ ràng:** `instructions` cho `Team` cần cực kỳ rõ ràng và chi tiết về luồng công việc mong muốn, cách phân công nhiệm vụ, và khi nào nên kết thúc.
*   **Mô tả Thành viên:** Vai trò (`role`) và tên (`name`) của các `Agent` thành viên rất quan trọng để agent điều phối biết được khả năng của từng người.
*   **Chia sẻ Ngữ cảnh:** Cân nhắc kỹ việc bật `share_member_interactions`. Nó cung cấp nhiều ngữ cảnh hơn nhưng cũng làm tăng độ dài của prompt gửi đến các thành viên.
*   **Xử lý Lỗi:** Cần có cơ chế xử lý khi một agent thành viên thất bại hoặc trả về kết quả không mong muốn.
*   **Bộ nhớ và Trạng thái:** Sử dụng `TeamMemory` và `Storage` để lưu trữ lịch sử tương tác và trạng thái của team, giúp duy trì ngữ cảnh qua nhiều lượt chạy.
*   **Debug:** Sử dụng `debug_mode=True` cho cả `Team` và các `Agent` thành viên để theo dõi chi tiết quá trình điều phối và thực thi.

## Kết luận

Agent Teams là một cấu trúc mạnh mẽ trong Agno Framework, cho phép xây dựng các hệ thống AI phức tạp bằng cách kết hợp nhiều agent chuyên biệt. Bằng cách định nghĩa rõ ràng các thành viên, vai trò, hướng dẫn điều phối, và cơ chế chia sẻ thông tin, bạn có thể tạo ra các nhóm agent có khả năng giải quyết các bài toán đa bước, đòi hỏi sự phối hợp cao một cách hiệu quả.

