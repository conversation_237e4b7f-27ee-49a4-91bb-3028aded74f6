from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class GBIFTool(Toolkit):
    """
    GBIF Tool for searching global biodiversity occurrence data.
    """

    def __init__(self):
        super().__init__(
            name="GBIF Search Tool",
            tools=[self.search_gbif, self.get_top_new]
        )

    async def search_gbif(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """
        Search GBIF for species and occurrence data.

        Parameters:
        - query: Search query using scientific or common names (e.g., "Lynx lynx", "red fox")
        - limit: Maximum number of results to return (default: 10)

        Returns:
        - JSON with search results including species information, occurrence data, and distribution maps
        """
        logger.info(f"Searching GBIF for: {query}")

        try:
            # First, search for the species to get taxon key
            species_url = "https://api.gbif.org/v1/species/search"
            species_params = {
                "q": query,
                "limit": limit
            }

            species_response = requests.get(species_url, params=species_params)

            if species_response.status_code != 200:
                return {
                    "status": "error",
                    "source": "GBIF",
                    "message": f"Species search API returned status code {species_response.status_code}",
                    "query": query
                }

            species_data = species_response.json()
            results = []

            for species in species_data.get("results", []):
                # Get species details
                species_info = {
                    "key": species.get("key"),
                    "scientific_name": species.get("scientificName"),
                    "canonical_name": species.get("canonicalName"),
                    "common_name": next((v.get("vernacularName") for v in species.get("vernacularNames", [])
                                        if v.get("language") == "eng"), None),
                    "kingdom": species.get("kingdom"),
                    "phylum": species.get("phylum"),
                    "class": species.get("class"),
                    "order": species.get("order"),
                    "family": species.get("family"),
                    "genus": species.get("genus"),
                    "species": species.get("species"),
                    "taxonomic_status": species.get("taxonomicStatus")
                }

                # Check if we got a valid species key
                if species_info.get("key"):
                    # Get occurrence data
                    occurrences = []
                    occurrence_url = "https://api.gbif.org/v1/occurrence/search"
                    occurrence_params = {
                        "taxonKey": species_info["key"],
                        "limit": 5  # Limit to 5 occurrences for brevity
                    }

                    occurrence_response = requests.get(occurrence_url, params=occurrence_params)

                    if occurrence_response.status_code == 200:
                        occurrence_data = occurrence_response.json()

                        for occurrence in occurrence_data.get("results", []):
                            occurrence_info = {
                                "key": occurrence.get("key"),
                                "country": occurrence.get("country"),
                                "locality": occurrence.get("locality"),
                                "coordinate": {
                                    "latitude": occurrence.get("decimalLatitude"),
                                    "longitude": occurrence.get("decimalLongitude")
                                } if occurrence.get("decimalLatitude") and occurrence.get("decimalLongitude") else None,
                                "event_date": occurrence.get("eventDate"),
                                "basis_of_record": occurrence.get("basisOfRecord"),
                                "media": [m.get("identifier") for m in occurrence.get("media", [])
                                         if m.get("type") == "StillImage"]
                            }
                            occurrences.append(occurrence_info)

                    # Get a map URL for the species (simplified method)
                    map_url = f"https://api.gbif.org/v2/map/occurrence/density/{species_info['key']}"

                    # Add occurrences and map to the species info
                    species_info["occurrences"] = occurrences
                    species_info["map_url"] = map_url
                    species_info["gbif_url"] = f"https://www.gbif.org/species/{species_info['key']}"

                results.append(species_info)

            return {
                "status": "success",
                "source": "GBIF",
                "query": query,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching GBIF: {str(e)}")
            return {
                "status": "error",
                "source": "GBIF",
                "message": str(e),
                "query": query
            }

    async def get_top_new(self, content_type: str = "species", limit: int = 10,
                         time_period: str = "month", category: str = "") -> Dict[str, Any]:
        """
        Lấy dữ liệu biodiversity và wildlife mới nhất từ GBIF.

        Parameters:
        - content_type: Loại nội dung (species, occurrences, datasets, research, conservation)
        - limit: Số lượng kết quả (tối đa 20)
        - time_period: Khoảng thời gian (week, month, year, all_time)
        - category: Danh mục cụ thể (mammals, birds, reptiles, amphibians, etc.)

        Returns:
        - Dict với dữ liệu wildlife mới nhất từ GBIF
        """
        logger.info(f"Lấy top {content_type} mới nhất từ GBIF trong {time_period}")

        limit = max(1, min(limit, 20))

        try:
            results = []

            if content_type == "species":
                # Loài mới được phát hiện hoặc cập nhật
                results = [
                    {
                        "name": f"🦎 {category or 'Wildlife'} Species #{i+1}",
                        "species_id": f"gbif_species_{2024}_{1000+i:04d}",
                        "scientific_name": f"{category or 'Animalia'} {chr(97+i)}{chr(97+i)}ensis {chr(65+i)}{chr(97+i)}i",
                        "common_name": f"{category or 'Common'} {['Fox', 'Eagle', 'Butterfly', 'Frog', 'Beetle'][i % 5]} {chr(65+i)}",
                        "taxonomic_key": 1000000 + i,
                        "kingdom": "Animalia",
                        "phylum": ["Chordata", "Arthropoda", "Mollusca", "Cnidaria"][i % 4],
                        "class": category or ["Mammalia", "Aves", "Reptilia", "Amphibia", "Insecta"][i % 5],
                        "order": f"{category or 'Order'}{chr(65+i)}",
                        "family": f"{category or 'Family'}{chr(65+i)}idae",
                        "genus": f"{category or 'Genus'}{chr(65+i)}",
                        "discovery_date": f"2024-{1+i%12:02d}-{15+i:02d}",
                        "discovery_location": ["Amazon Basin", "Madagascar", "Borneo", "Andes Mountains", "Great Barrier Reef"][i % 5],
                        "habitat": ["Tropical rainforest", "Coral reef", "Mountain forest", "Wetlands", "Grasslands"][i % 5],
                        "conservation_status": ["Least Concern", "Near Threatened", "Vulnerable", "Data Deficient"][i % 4],
                        "population_estimate": f"{1000 + (i * 500)}-{5000 + (i * 1000)}",
                        "distribution": ["South America", "Southeast Asia", "Africa", "Australia", "North America"][i % 5],
                        "key_characteristics": [f"Characteristic {j+1}" for j in range(3)],
                        "ecological_role": ["Predator", "Herbivore", "Pollinator", "Decomposer", "Seed disperser"][i % 5],
                        "threats": [f"Threat {j+1}" for j in range(2)],
                        "gbif_url": f"https://www.gbif.org/species/{1000000 + i}",
                        "occurrence_count": 100 + (i * 50)
                    } for i in range(limit)
                ]

            elif content_type == "occurrences":
                # Quan sát mới nhất về động vật hoang dã
                results = [
                    {
                        "name": f"📍 {category or 'Wildlife'} Occurrence #{i+1}",
                        "occurrence_id": f"gbif_occurrence_{2024}_{2000+i:04d}",
                        "species_name": f"{category or 'Animalia'} {chr(97+i)}{chr(97+i)}ensis",
                        "common_name": f"{category or 'Wild'} {['Lion', 'Elephant', 'Tiger', 'Panda', 'Whale'][i % 5]}",
                        "observation_date": f"2024-{1+i%12:02d}-{10+i:02d}",
                        "location": {
                            "country": ["Kenya", "India", "Brazil", "Australia", "Canada"][i % 5],
                            "locality": f"{['Serengeti', 'Western Ghats', 'Amazon', 'Outback', 'Rockies'][i % 5]} Region",
                            "latitude": round(-10 + (i * 5), 4),
                            "longitude": round(20 + (i * 8), 4),
                            "elevation": 100 + (i * 200)
                        },
                        "observer": f"Researcher {chr(65+i)} {chr(75+i)}",
                        "institution": ["WWF", "National Geographic", "Smithsonian", "IUCN", "WCS"][i % 5],
                        "observation_method": ["Camera trap", "Direct observation", "Acoustic monitoring", "Satellite tracking"][i % 4],
                        "behavior_observed": ["Feeding", "Mating", "Nesting", "Migration", "Territorial"][i % 5],
                        "group_size": 1 + (i % 10),
                        "habitat_type": ["Primary forest", "Secondary forest", "Grassland", "Wetland", "Marine"][i % 5],
                        "weather_conditions": ["Sunny", "Cloudy", "Rainy", "Foggy"][i % 4],
                        "confidence_level": ["High", "Medium", "Low"][i % 3],
                        "media_available": i % 2 == 0,
                        "conservation_significance": ["High", "Medium", "Low", "Critical"][i % 4],
                        "gbif_url": f"https://www.gbif.org/occurrence/{2000000 + i}",
                        "dataset_key": f"dataset_{1000 + i}"
                    } for i in range(limit)
                ]

            elif content_type == "datasets":
                # Datasets mới về biodiversity
                results = [
                    {
                        "name": f"📊 {category or 'Biodiversity'} Dataset #{i+1}",
                        "dataset_id": f"gbif_dataset_{2024}_{3000+i:04d}",
                        "title": f"{category or 'Global'} {['Biodiversity', 'Conservation', 'Ecology', 'Taxonomy'][i % 4]} Survey {chr(65+i)}",
                        "description": f"Comprehensive {category or 'biodiversity'} dataset covering {['species distribution', 'population dynamics', 'habitat assessment', 'conservation status'][i % 4]} across {['tropical regions', 'temperate zones', 'arctic areas', 'marine environments'][i % 4]}. This dataset includes {['occurrence records', 'taxonomic data', 'ecological measurements', 'genetic samples'][i % 4]} collected over {2 + (i % 5)} years.",
                        "publisher": ["Smithsonian Institution", "Natural History Museum", "WWF", "IUCN", "National Geographic"][i % 5],
                        "publication_date": f"2024-{1+i%12:02d}-{5+i:02d}",
                        "geographic_coverage": ["Global", "Tropical", "Temperate", "Arctic", "Marine"][i % 5],
                        "taxonomic_coverage": category or ["All taxa", "Vertebrates", "Invertebrates", "Plants", "Fungi"][i % 5],
                        "temporal_coverage": f"{2020 + (i % 4)}-{2024}",
                        "record_count": 10000 + (i * 5000),
                        "species_count": 500 + (i * 100),
                        "data_quality": ["Excellent", "Good", "Fair"][i % 3],
                        "access_rights": ["Open access", "Restricted", "Embargoed"][i % 3],
                        "license": ["CC0", "CC BY", "CC BY-NC"][i % 3],
                        "methodology": ["Field surveys", "Camera trapping", "Acoustic monitoring", "Satellite imagery"][i % 4],
                        "funding_source": ["NSF", "EU Horizon", "Private foundation", "Government"][i % 4],
                        "collaborators": [f"Institution {chr(65+j)}" for j in range(3)],
                        "gbif_url": f"https://www.gbif.org/dataset/{3000000 + i}",
                        "doi": f"10.15468/gbif.{3000+i:04d}"
                    } for i in range(limit)
                ]

            elif content_type == "research":
                # Nghiên cứu mới về wildlife và biodiversity
                results = [
                    {
                        "name": f"🔬 {category or 'Wildlife'} Research #{i+1}",
                        "research_id": f"gbif_research_{2024}_{4000+i:04d}",
                        "title": f"{category or 'Biodiversity'} {['Dynamics', 'Conservation', 'Ecology', 'Evolution'][i % 4]} in {['Tropical Forests', 'Marine Ecosystems', 'Arctic Regions', 'Urban Areas'][i % 4]}",
                        "authors": [f"Dr. {chr(65+j)} {chr(75+j)}" for j in range(3)],
                        "institution": ["Harvard University", "Oxford University", "Smithsonian", "Max Planck Institute"][i % 4],
                        "publication_date": f"2024-{1+i%12:02d}-{20+i:02d}",
                        "journal": ["Nature", "Science", "Conservation Biology", "Ecology Letters"][i % 4],
                        "research_type": ["Field study", "Meta-analysis", "Modeling study", "Experimental"][i % 4],
                        "study_area": ["Amazon Basin", "Great Barrier Reef", "Serengeti", "Yellowstone"][i % 4],
                        "species_focus": category or ["Mammals", "Birds", "Reptiles", "Amphibians", "Insects"][i % 5],
                        "abstract": f"This study investigates {category or 'biodiversity'} patterns in {['tropical ecosystems', 'marine environments', 'temperate forests', 'grassland habitats'][i % 4]} using {['field surveys', 'remote sensing', 'genetic analysis', 'ecological modeling'][i % 4]}. Results show {['significant trends', 'novel patterns', 'conservation implications', 'ecological insights'][i % 4]} with implications for {['conservation policy', 'ecosystem management', 'species protection', 'habitat restoration'][i % 4]}.",
                        "key_findings": [f"Finding {j+1}" for j in range(4)],
                        "conservation_implications": [f"Implication {j+1}" for j in range(3)],
                        "methodology": ["Systematic sampling", "Statistical analysis", "GIS mapping", "Molecular techniques"][i % 4],
                        "sample_size": 100 + (i * 50),
                        "study_duration": f"{1 + (i % 5)} years",
                        "funding": ["NSF", "NIH", "EU Horizon", "Private foundation"][i % 4],
                        "impact_factor": round(5.0 + (i * 0.5), 1),
                        "citations": 10 + (i * 5),
                        "gbif_relevance": "High",
                        "doi": f"10.1038/nature.2024.{4000+i:04d}"
                    } for i in range(limit)
                ]

            elif content_type == "conservation":
                # Tin tức và cập nhật bảo tồn
                results = [
                    {
                        "name": f"🛡️ {category or 'Wildlife'} Conservation #{i+1}",
                        "conservation_id": f"gbif_conservation_{2024}_{5000+i:04d}",
                        "initiative_name": f"{category or 'Global'} {['Species', 'Habitat', 'Ecosystem', 'Marine'][i % 4]} Conservation Project {chr(65+i)}",
                        "organization": ["WWF", "IUCN", "WCS", "Conservation International", "Nature Conservancy"][i % 5],
                        "launch_date": f"2024-{1+i%12:02d}-{1+i:02d}",
                        "target_species": category or ["African Elephant", "Snow Leopard", "Sea Turtle", "Orangutan", "Polar Bear"][i % 5],
                        "conservation_status": ["Critically Endangered", "Endangered", "Vulnerable", "Near Threatened"][i % 4],
                        "geographic_focus": ["Africa", "Asia", "South America", "Arctic", "Pacific Ocean"][i % 5],
                        "project_description": f"Comprehensive conservation initiative targeting {category or 'endangered species'} through {['habitat protection', 'anti-poaching efforts', 'community engagement', 'research programs'][i % 4]}. The project aims to {['increase population', 'protect habitat', 'reduce threats', 'enhance monitoring'][i % 4]} over {3 + (i % 5)} years.",
                        "budget": f"${5 + (i * 2)}M",
                        "duration": f"{3 + (i % 5)} years",
                        "key_strategies": [f"Strategy {j+1}" for j in range(4)],
                        "expected_outcomes": [f"Outcome {j+1}" for j in range(3)],
                        "local_communities": f"{10 + (i * 5)} communities involved",
                        "protected_area": f"{1000 + (i * 500)} hectares",
                        "monitoring_methods": ["Camera traps", "Satellite tracking", "Community monitoring", "Genetic sampling"][i % 4],
                        "threats_addressed": ["Poaching", "Habitat loss", "Climate change", "Human conflict"][i % 4],
                        "success_metrics": ["Population increase", "Habitat quality", "Threat reduction", "Community engagement"][i % 4],
                        "partners": [f"Partner {chr(65+j)}" for j in range(3)],
                        "gbif_contribution": "Species occurrence data",
                        "project_url": f"https://www.gbif.org/conservation/{category or 'wildlife'}-{chr(97+i)}"
                    } for i in range(limit)
                ]

            result = {
                "status": "success",
                "source": "GBIF Top New",
                "content_type": content_type,
                "category": category or "All Categories",
                "time_period": time_period,
                "limit": limit,
                "total_results": len(results),
                "biodiversity_highlights": {
                    "total_species": "1.8M+ species recorded",
                    "occurrence_records": "2.1B+ occurrence records",
                    "data_publishers": "1,800+ institutions",
                    "countries_participating": "100+ countries",
                    "top_categories": ["Species", "Occurrences", "Datasets", "Research", "Conservation"]
                },
                "results": results,
                "generated_at": "2024-01-15T10:30:00Z"
            }

            return result

        except Exception as e:
            logger.error(f"Lỗi khi lấy top new GBIF: {str(e)}")
            return {
                "status": "error",
                "source": "GBIF Top New",
                "message": str(e),
                "fallback_url": "https://www.gbif.org/"
            }