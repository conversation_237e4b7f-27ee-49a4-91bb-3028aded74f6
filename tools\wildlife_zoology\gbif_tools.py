from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class GBIFTool(Toolkit):
    """
    GBIF Tool for searching global biodiversity occurrence data.
    """
    
    def __init__(self):
        super().__init__(
            name="GBIF Search Tool",
            tools=[self.search_gbif]
        )

    async def search_gbif(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """
        Search GBIF for species and occurrence data.
        
        Parameters:
        - query: Search query using scientific or common names (e.g., "Lynx lynx", "red fox")
        - limit: Maximum number of results to return (default: 10)
        
        Returns:
        - JSON with search results including species information, occurrence data, and distribution maps
        """
        logger.info(f"Searching GBIF for: {query}")
        
        try:
            # First, search for the species to get taxon key
            species_url = "https://api.gbif.org/v1/species/search"
            species_params = {
                "q": query,
                "limit": limit
            }
            
            species_response = requests.get(species_url, params=species_params)
            
            if species_response.status_code != 200:
                return {
                    "status": "error",
                    "source": "GBIF",
                    "message": f"Species search API returned status code {species_response.status_code}",
                    "query": query
                }
            
            species_data = species_response.json()
            results = []
            
            for species in species_data.get("results", []):
                # Get species details
                species_info = {
                    "key": species.get("key"),
                    "scientific_name": species.get("scientificName"),
                    "canonical_name": species.get("canonicalName"),
                    "common_name": next((v.get("vernacularName") for v in species.get("vernacularNames", []) 
                                        if v.get("language") == "eng"), None),
                    "kingdom": species.get("kingdom"),
                    "phylum": species.get("phylum"),
                    "class": species.get("class"),
                    "order": species.get("order"),
                    "family": species.get("family"),
                    "genus": species.get("genus"),
                    "species": species.get("species"),
                    "taxonomic_status": species.get("taxonomicStatus")
                }
                
                # Check if we got a valid species key
                if species_info.get("key"):
                    # Get occurrence data
                    occurrences = []
                    occurrence_url = "https://api.gbif.org/v1/occurrence/search"
                    occurrence_params = {
                        "taxonKey": species_info["key"],
                        "limit": 5  # Limit to 5 occurrences for brevity
                    }
                    
                    occurrence_response = requests.get(occurrence_url, params=occurrence_params)
                    
                    if occurrence_response.status_code == 200:
                        occurrence_data = occurrence_response.json()
                        
                        for occurrence in occurrence_data.get("results", []):
                            occurrence_info = {
                                "key": occurrence.get("key"),
                                "country": occurrence.get("country"),
                                "locality": occurrence.get("locality"),
                                "coordinate": {
                                    "latitude": occurrence.get("decimalLatitude"),
                                    "longitude": occurrence.get("decimalLongitude")
                                } if occurrence.get("decimalLatitude") and occurrence.get("decimalLongitude") else None,
                                "event_date": occurrence.get("eventDate"),
                                "basis_of_record": occurrence.get("basisOfRecord"),
                                "media": [m.get("identifier") for m in occurrence.get("media", []) 
                                         if m.get("type") == "StillImage"]
                            }
                            occurrences.append(occurrence_info)
                    
                    # Get a map URL for the species (simplified method)
                    map_url = f"https://api.gbif.org/v2/map/occurrence/density/{species_info['key']}"
                    
                    # Add occurrences and map to the species info
                    species_info["occurrences"] = occurrences
                    species_info["map_url"] = map_url
                    species_info["gbif_url"] = f"https://www.gbif.org/species/{species_info['key']}"
                
                results.append(species_info)
            
            return {
                "status": "success",
                "source": "GBIF",
                "query": query,
                "results_count": len(results),
                "results": results
            }
        
        except Exception as e:
            log_debug(f"Error searching GBIF: {str(e)}")
            return {
                "status": "error",
                "source": "GBIF",
                "message": str(e),
                "query": query
            }