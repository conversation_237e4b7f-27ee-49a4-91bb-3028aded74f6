from typing import Dict, Any, List, Optional, Union
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime, timedelta
import random

class LiquipediaTool(Toolkit):
    """
    Công cụ tìm kiếm Liquipedia để tra cứu thông tin esports, độ<PERSON> tuyển, giải đấu và thống kê người chơi.
    
    <PERSON><PERSON><PERSON> từ khóa tìm kiếm gợi ý:
    - Thông tin đội tuyển (team info, team roster)
    - <PERSON><PERSON><PERSON> qu<PERSON> gi<PERSON>i đấu (tournament results, match history)
    - <PERSON><PERSON><PERSON> thi đấu (tournament schedule, upcoming matches)
    - <PERSON>hống kê người chơi (player stats, player profiles)
    - Gi<PERSON>i đấu theo game (Dota 2, League of Legends, CS2, Valorant)
    - <PERSON><PERSON><PERSON> x<PERSON><PERSON> hạng (rankings, DPC rankings, circuit points)
    - <PERSON><PERSON><PERSON> sử giải đấu (tournament history, past events)
    """
    
    def __init__(self):
        super().__init__(
            name="<PERSON><PERSON>ng cụ tìm kiếm Liquipedia",
            tools=[self.search_liquipedia]
        )
        self.base_url = "https://liquipedia.net"
        self.games = ["dota2", "lol", "cs2", "valorant", "pubg", "apexlegends", "rainbowsix", "rocketleague"]
        self.regions = ["international", "na", "eu", "sea", "china", "cis", "sa", "oce"]
        self.tournament_tiers = ["1", "2", "3", "4", "premier", "major", "international", "dpcleague"]

    def search_liquipedia(self, query: str, game: str = "dota2", region: str = "international", 
                         year: Optional[int] = None, limit: int = 5) -> str:
        """
        Tìm kiếm thông tin esports trên Liquipedia.
        
        Args:
            query: Từ khóa tìm kiếm (tên đội, người chơi, giải đấu, v.v.)
            game: Tựa game (dota2, lol, cs2, valorant, v.v.)
            region: Khu vực (international, na, eu, sea, china, cis, sa, oce)
            year: Năm tổ chức giải đấu (None cho tất cả các năm)
            limit: Số lượng kết quả trả về (tối đa 10)
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm Liquipedia với từ khóa: {query}, game: {game}")
        
        # Xác thực tham số
        if game not in self.games:
            game = "dota2"
        if region not in self.regions:
            region = "international"
            
        limit = max(1, min(limit, 10))  # Giới hạn trong khoảng 1-10
        current_year = datetime.now().year
        
        try:
            # Giả lập kết quả tìm kiếm
            results = []
            
            # Hàm tạo ngẫu nhiên
            def random_prize_pool() -> str:
                prize = random.choice([
                    f"${random.randint(1, 40)}M",
                    f"${random.randint(100, 999)}K",
                    f"${random.randint(10, 99)}K",
                    "TBD"
                ])
                return prize
                
            def random_date(year: Optional[int] = None) -> str:
                if year is None:
                    year = random.randint(2020, current_year + 1)
                month = random.randint(1, 12)
                day = random.randint(1, 28)
                return f"{year}-{month:02d}-{day:02d}"
                
            def random_team_name() -> str:
                prefixes = ["Team ", "", "Evil ", "Natus ", "T1", "Fnatic ", "OG ", "Team Secret", "PSG.LGD ", "Virtus.pro "]
                names = ["Spirit", "Liquid", "Alliance", "EG", "LGD", "VP", "OG", "Secret", "Fnatic", "T1", "Nigma", "Navi"]
                return f"{random.choice(prefixes)}{random.choice(names)}"
            
            # Tạo kết quả tìm kiếm
            for i in range(limit):
                # Xác định loại kết quả (tournament, team, player)
                result_type = random.choice(["tournament", "team", "player"])
                
                if result_type == "tournament":
                    t_year = year if year else current_year - random.randint(0, 2)
                    t_end_date = random_date(t_year)
                    t_start_date = (datetime.strptime(t_end_date, "%Y-%m-%d") - timedelta(days=random.randint(1, 14))).strftime("%Y-%m-%d")
                    
                    results.append({
                        "type": "tournament",
                        "name": f"{query} {t_year} {region.upper()} Championship",
                        "game": game.upper(),
                        "tier": random.choice(["S-Tier", "A-Tier", "B-Tier"]),
                        "prize_pool": random_prize_pool(),
                        "dates": f"{t_start_date} - {t_end_date}",
                        "location": f"{region.upper()}",
                        "participants": random.randint(8, 20),
                        "winner": random_team_name(),
                        "runner_up": random_team_name(),
                        "url": f"{self.base_url}/{game}/{query.replace(' ', '_')}_{t_year}"
                    })
                    
                elif result_type == "team":
                    team_name = f"{query} {random.choice(['Esports', 'Gaming', 'Team'])}" if i % 2 == 0 else query
                    
                    results.append({
                        "type": "team",
                        "name": team_name,
                        "game": game.upper(),
                        "region": region.upper(),
                        "formed": str(current_year - random.randint(1, 10)),
                        "total_earnings": f"${random.randint(10000, 5000000):,}",
                        "active_players": [
                            f"Player{i+1}" for i in range(random.randint(5, 7))
                        ],
                        "coach": f"Coach_{random.randint(1, 10)}",
                        "recent_tournaments": [
                            {"name": f"{game.upper()} {current_year-1} Championship", "placement": random.choice(["1st", "2nd", "3-4th", "5-8th"])}
                            for _ in range(random.randint(1, 3))
                        ],
                        "url": f"{self.base_url}/{game}/{team_name.replace(' ', '_')}"
                    })
                    
                else:  # player
                    player_name = f"{query}Player{i+1}" if i % 2 == 0 else query
                    
                    results.append({
                        "type": "player",
                        "name": player_name,
                        "game": game.upper(),
                        "nationality": random.choice(["Vietnam", "United States", "China", "South Korea", "Russia", "Sweden"]),
                        "age": random.randint(18, 35),
                        "team": random_team_name(),
                        "role": random.choice(["Carry", "Mid", "Offlane", "Support", "Jungle", "Rifler", "AWPer", "IGL"]),
                        "total_earnings": f"${random.randint(1000, 5000000):,}",
                        "active_years": f"{current_year - random.randint(1, 10)} - Present",
                        "recent_achievements": [
                            f"{random.choice(['1st', '2nd', '3rd'])} place at {random_team_name()} {current_year-1}"
                            for _ in range(random.randint(1, 3))
                        ],
                        "url": f"{self.base_url}/{game}/{player_name}"
                    })
            
            # Thêm thông tin chi tiết cho kết quả đầu tiên nếu có
            if results:
                first_result = results[0]
                if first_result["type"] == "tournament":
                    first_result["bracket"] = {
                        "format": "Double Elimination",
                        "participants": [random_team_name() for _ in range(8)],
                        "prize_distribution": {
                            "1st": "45%",
                            "2nd": "22%",
                            "3rd": "10%",
                            "4th": "8%",
                            "5-8th": "15%"
                        }
                    }
                elif first_result["type"] == "team":
                    first_result["roster"] = [
                        {
                            "name": f"Player{i+1}",
                            "role": random.choice(["Carry", "Mid", "Offlane", "Support"]),
                            "join_date": random_date(current_year - 1)
                        } for i in range(5)
                    ]
                else:  # player
                    first_result["stats"] = {
                        "total_matches": random.randint(100, 1000),
                        "win_rate": f"{random.randint(45, 75)}%",
                        "KDA_ratio": round(random.uniform(2.0, 5.0), 2),
                        "signature_heroes": [f"Hero_{i}" for i in range(1, 4)]
                    }
            
            result = {
                "status": "success",
                "source": "Liquipedia",
                "query": query,
                "game": game,
                "region": region,
                "year": year if year else "All Years",
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Liquipedia: {str(e)}")
            result = {
                "status": "error",
                "source": "Liquipedia",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"Tìm kiếm {query} trên Liquipedia {game.upper()}",
                        "url": f"{self.base_url}/{game}/index.php?search={query.replace(' ', '+')}",
                        "summary": f"Tìm kiếm thông tin về {query} trong cộng đồng {game.upper()}"
                    },
                    {
                        "title": f"Giải đấu {game.upper()} {current_year}",
                        "url": f"{self.base_url}/{game}/Tournaments",
                        "summary": f"Xem tất cả các giải đấu {game.upper()} trong năm {current_year}"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
