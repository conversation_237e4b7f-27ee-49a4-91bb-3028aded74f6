from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json

class PitchforkTool(Toolkit):
    """
    Công cụ tìm kiếm Pitchfork giúp tìm kiếm đánh giá, xếp hạng và bài viết đặc sắc về âm nhạc.
    
    Các từ khóa tìm kiếm gợi ý:
    - Tên nghệ sĩ, album, bài hát
    - Thể loại âm nhạc (indie, rock, hip hop, electronic, v.v.)
    - <PERSON><PERSON><PERSON> bài đánh giá mới nhất (best new music, best new reissue)
    - <PERSON><PERSON><PERSON> bài phỏng vấn nghệ sĩ
    - <PERSON><PERSON><PERSON> bài viết đặc sắc theo năm, thập kỷ
    """
    def __init__(self):
        super().__init__(
            name="Công cụ tìm kiếm Pitchfork",
            tools=[self.search_pitchfork]
        )
        self.base_url = "https://pitchfork.com"
        self.search_types = ["all", "reviews", "features", "news", "videos", "best"]

    def search_pitchfork(self, query: str, search_type: str = "all", limit: int = 5) -> str:
        """
        Tìm kiếm trên Pitchfork theo từ khóa và loại nội dung.
        
        Args:
            query: Từ khóa tìm kiếm (tên nghệ sĩ, album, bài hát...)
            search_type: Loại nội dung (all, reviews, features, news, videos, best)
            limit: Số lượng kết quả trả về (tối đa 10)
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        logger.info(f"Đang tìm kiếm Pitchfork với từ khóa: {query}, loại: {search_type}")
        
        if search_type not in self.search_types:
            search_type = "all"
            
        limit = max(1, min(limit, 10))  # Giới hạn trong khoảng 1-10
        
        try:
            # Giả lập kết quả tìm kiếm
            results = []
            
            if search_type in ["all", "reviews"]:
                results.extend([
                    {
                        "title": f"{query} Album Review",
                        "artist": f"{query} Artist",
                        "type": "review",
                        "score": 8.5,
                        "author": "Pitchfork Staff",
                        "publish_date": "2025-01-01",
                        "summary": f"A thoughtful and engaging review of {query}'s latest work.",
                        "url": f"https://pitchfork.com/reviews/albums/{query.lower().replace(' ', '-')}",
                        "image_url": f"https://media.pitchfork.com/photos/{600000 + i}/square.jpg"
                    } for i in range(min(limit, 3))
                ])
                
            if search_type in ["all", "features"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"The Evolution of {query}",
                        "type": "feature",
                        "author": "Pitchfork Staff",
                        "publish_date": "2025-01-01",
                        "summary": f"An in-depth look at how {query} has evolved over the years.",
                        "url": f"https://pitchfork.com/features/{query.lower().replace(' ', '-')}",
                        "image_url": f"https://media.pitchfork.com/photos/{700000 + i}/square.jpg"
                    } for i in range(min(limit - len(results), 2))
                ])
                
            if search_type in ["all", "news"] and len(results) < limit:
                results.extend([
                    {
                        "title": f"{query} Announces New Album and World Tour",
                        "type": "news",
                        "author": "Pitchfork News",
                        "publish_date": "2025-01-01",
                        "summary": f"{query} has announced a new album and accompanying world tour dates.",
                        "url": f"https://pitchfork.com/news/{query.lower().replace(' ', '-')}-new-album-tour",
                        "image_url": f"https://media.pitchfork.com/photos/{800000 + i}/square.jpg"
                    } for i in range(min(limit - len(results), 2))
                ])
                
            if search_type == "best" and not results:
                results = [
                    {
                        "title": f"The Best {query} of 2025",
                        "type": "best",
                        "author": "Pitchfork Staff",
                        "publish_date": "2025-12-01",
                        "summary": f"Our picks for the best {query} of 2025.",
                        "url": f"https://pitchfork.com/best/{query.lower().replace(' ', '-')}-2025",
                        "image_url": "https://media.pitchfork.com/photos/999999/square.jpg"
                    }
                ]
            
            result = {
                "status": "success",
                "source": "Pitchfork",
                "query": query,
                "search_type": search_type,
                "limit": limit,
                "results": results[:limit]  # Đảm bảo không vượt quá giới hạn
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Pitchfork: {str(e)}")
            result = {
                "status": "error",
                "source": "Pitchfork",
                "message": str(e),
                "query": query,
                "results": [
                    {
                        "title": f"{query} Reviews",
                        "url": f"https://pitchfork.com/search/?query={query}",
                        "summary": f"Tìm kiếm đánh giá về {query} trên Pitchfork"
                    },
                    {
                        "title": f"Bài viết về {query}",
                        "url": f"https://pitchfork.com/search/features/?query={query}",
                        "summary": f"Các bài viết đặc sắc về {query} trên Pitchfork"
                    }
                ]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
