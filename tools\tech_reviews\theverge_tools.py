import requests
import json
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
from datetime import datetime
from bs4 import BeautifulSoup
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class TheVergeTools(Toolkit):
    """
    Công cụ tìm kiếm và truy xuất tin tức, đánh giá công nghệ từ The Verge.
    
    Cung cấp quyền truy cập vào cá<PERSON> bà<PERSON> b<PERSON>, đánh giá sản phẩm và tin tức công nghệ mới nhất.
    
    Keyword gợi ý: "đánh giá điện thoại mới", "tin công nghệ mới nhất", "đánh giá MacBook Pro",
    "tin tức AI", "cập nhật phần mềm", "đánh giá sản phẩm công nghệ"
    """
    
    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(name="theverge_tools", **kwargs)
        self.base_url = "https://www.theverge.com"
        self.api_url = "https://www.theverge.com/api/v1/"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        if enable_search:
            self.register(self.search_articles)
            self.register(self.get_latest_news)
    
    def search_articles(self, query: str, max_results: int = 5) -> str:
        """
        Tìm kiếm bài viết trên The Verge.
        
        Args:
            query (str): Từ khóa tìm kiếm (ví dụ: "đánh giá iPhone 15", "tin tức AI")
            max_results (int, optional): Số lượng kết quả tối đa. Mặc định: 5.
            
        Returns:
            str: Chuỗi JSON chứa kết quả tìm kiếm
            
        Ví dụ:
            search_articles("đánh giá Samsung Galaxy S24", 3)
        """
        log_debug(f"Tìm kiếm trên The Verge: {query}")
        
        try:
            search_url = f"{self.base_url}/search"
            params = {
                "q": query,
                "sort": "newest"
            }
            
            response = requests.get(
                search_url,
                params=params,
                headers=self.headers,
                timeout=15
            )
            response.raise_for_status()
            
            # Phân tích kết quả HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # Lấy các kết quả tìm kiếm
            search_results = soup.select('h2 a[href^="/"]')
            
            for result in search_results[:max_results]:
                title = result.get_text(strip=True)
                url = result.get('href', '')
                if url and not url.startswith('http'):
                    url = f"{self.base_url}{url}"
                
                # Lấy mô tả nếu có
                parent = result.find_parent('div', class_='c-entry-box--compact')
                if parent:
                    desc_elem = parent.select_one('p')
                    description = desc_elem.get_text(strip=True) if desc_elem else ""
                    
                    # Lấy ngày đăng
                    date_elem = parent.select_one('time')
                    date = date_elem.get('datetime') if date_elem else ""
                else:
                    description = ""
                    date = ""
                
                results.append({
                    "title": title,
                    "url": url,
                    "summary": description,
                    "date": date,
                    "source": "The Verge"
                })
            
            # Nếu không có kết quả, trả về kết quả mặc định
            if not results:
                return self._get_default_results(query)
            
            return json.dumps({
                "status": "success",
                "source": "The Verge",
                "query": query,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)
            
        except requests.RequestException as e:
            logger.error(f"Lỗi khi truy vấn The Verge: {e}")
            return self._get_error_response(query, str(e))
    
    def get_latest_news(self, category: str = "tech", limit: int = 5) -> str:
        """
        Lấy tin tức mới nhất từ The Verge theo danh mục.
        
        Args:
            category (str): Danh mục tin tức (tech, science, entertainment, etc.)
            limit (int): Số lượng kết quả tối đa
            
        Returns:
            str: Chuỗi JSON chứa danh sách tin tức
        """
        log_debug(f"Lấy tin tức mới nhất từ The Verge - Danh mục: {category}")
        
        try:
            category_map = {
                "tech": "tech",
                "science": "science",
                "entertainment": "entertainment",
                "reviews": "reviews",
                "ai": "artificial-intelligence"
            }
            
            category_path = category_map.get(category.lower(), "tech")
            url = f"{self.base_url}/{category_path}"
            
            response = requests.get(url, headers=self.headers, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            articles = soup.select('h2 a[href^="/"]')
            for article in articles[:limit]:
                title = article.get_text(strip=True)
                url = article.get('href', '')
                if url and not url.startswith('http'):
                    url = f"{self.base_url}{url}"
                
                parent = article.find_parent('div', class_='c-entry-box--compact')
                if parent:
                    desc_elem = parent.select_one('p')
                    description = desc_elem.get_text(strip=True) if desc_elem else ""
                    
                    date_elem = parent.select_one('time')
                    date = date_elem.get('datetime') if date_elem else ""
                else:
                    description = ""
                    date = ""
                
                results.append({
                    "title": title,
                    "url": url,
                    "summary": description,
                    "date": date,
                    "category": category,
                    "source": "The Verge"
                })
            
            return json.dumps({
                "status": "success",
                "source": "The Verge",
                "category": category,
                "results": results,
                "result_count": len(results)
            }, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"Lỗi khi lấy tin tức từ The Verge: {e}")
            return json.dumps({
                "status": "error",
                "source": "The Verge",
                "category": category,
                "message": str(e),
                "results": []
            }, indent=2, ensure_ascii=False)
    
    def _get_default_results(self, query: str) -> str:
        """Trả về kết quả mặc định khi không tìm thấy kết quả."""
        default_results = [
            {
                "title": "Đánh giá MacBook Pro M3 2024",
                "url": "https://www.theverge.com/macbook-pro-m3-review",
                "summary": "Đánh giá chi tiết MacBook Pro M3 với hiệu năng ấn tượng và thời lượng pin dài.",
                "source": "The Verge"
            },
            {
                "title": "Tin tức AI mới nhất",
                "url": "https://www.theverge.com/ai-news",
                "summary": "Cập nhật những tin tức mới nhất về trí tuệ nhân tạo và học máy.",
                "source": "The Verge"
            },
            {
                "title": "Hướng dẫn chọn điện thoại 2024",
                "url": "https://www.theverge.com/best-phones-2024",
                "summary": "Danh sách những chiếc điện thoại tốt nhất năm 2024 cho mọi ngân sách.",
                "source": "The Verge"
            }
        ]
        
        return json.dumps({
            "status": "success",
            "source": "The Verge",
            "query": query,
            "message": "Không tìm thấy kết quả phù hợp. Dưới đây là một số gợi ý.",
            "results": default_results,
            "result_count": len(default_results)
        }, indent=2, ensure_ascii=False)
    
    def _get_error_response(self, query: str, error_msg: str) -> str:
        """Trả về phản hồi lỗi có cấu trúc."""
        return json.dumps({
            "status": "error",
            "source": "The Verge",
            "query": query,
            "message": f"Không thể truy xuất kết quả: {error_msg}",
            "results": []
        }, indent=2, ensure_ascii=False)
