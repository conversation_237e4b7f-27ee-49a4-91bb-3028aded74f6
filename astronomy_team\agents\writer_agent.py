from typing import Dict, Any, List, Optional
from agno.agent import Agent
from agno.models.ollama import Ollama
from ..config import MODEL_CONFIG
import logging
import json

logger = logging.getLogger(__name__)

class WriterAgent(Agent):
    """Agent chuyên viết và tổng hợp câu trả lời."""
    
    def __init__(self, **kwargs):
        super().__init__(
            model=Ollama(
                id=MODEL_CONFIG["base_model"],
            ),
            name="Writer Agent",
            description="""
            Bạn là một nhà văn khoa học chuyên nghiệp.
            Nhiệm vụ của bạn là tổng hợp thông tin từ các nguồn khác nhau 
            và viết câu trả lời rõ ràng, ch<PERSON>h xác và dễ hiểu.
            """,
            **kwargs
        )
        
        logger.info("Khởi tạo Writer Agent thành công")
    
    async def arun(self, message: dict, **kwargs) -> dict:
        """Tổ<PERSON> hợp và viết câu trả lời dựa trên thông tin đã thu thập."""
        query = message.get("query")
        analysis = message.get("analysis")
        search_results = message.get("search_results")
        evaluation = message.get("evaluation")
        
        logger.info("Bắt đầu viết câu trả lời")
        
        try:
            # Chuẩn bị dữ liệu cho prompt
            prompt = self._create_writing_prompt(query, analysis, search_results, evaluation)
            
            # Gọi model để tạo câu trả lời
            response = await self.model.agenerate(
                messages=[{"role": "user", "content": prompt}]
            )
            
            # Lấy nội dung phản hồi
            answer = response.choices[0].message.content
            
            # Định dạng lại câu trả lời
            formatted_answer = self._format_response(answer, search_results, evaluation)
            
            logger.info("Hoàn thành viết câu trả lời")
            
            return {
                "status": "success",
                "answer": formatted_answer,
                "sources": self._extract_sources(search_results)
            }
            
        except Exception as e:
            logger.error(f"Lỗi khi viết câu trả lời: {str(e)}")
            return {
                "status": "error",
                "message": f"Không thể tạo câu trả lời: {str(e)}",
                "answer": "Xin lỗi, tôi không thể tạo câu trả lời lúc này. Vui lòng thử lại sau.",
                "sources": []
            }
    
    async def run(self, message: dict, **kwargs) -> dict:
        return await self.arun(message, **kwargs)

    def _create_writing_prompt(
        self, 
        query: str,
        analysis: Dict[str, Any],
        search_results: Dict[str, Any],
        evaluation: Optional[Dict[str, Any]] = None
    ) -> str:
        """Tạo prompt cho việc viết câu trả lời."""
        prompt_parts = [
            "# YÊU CẦU VIẾT CÂU TRẢ LỜI",
            f"## Câu hỏi: {query}\n",
            
            "## Thông tin phân tích:",
            f"- Từ khóa chính: {', '.join(analysis.get('keywords', []))}",
            f"- Phân tích chi tiết: {analysis.get('analysis', 'Không có thông tin')}\n",
            
            "## Kết quả tìm kiếm:",
            self._format_search_results_for_prompt(search_results),
            
            "## Đánh giá chất lượng (nếu có):",
            self._format_evaluation_for_prompt(evaluation) if evaluation else "Không có thông tin đánh giá",
            "\n## Yêu cầu:",
            "1. Viết câu trả lời ngắn gọn, rõ ràng, dễ hiểu",
            "2. Sử dụng thông tin từ các nguồn đáng tin cậy",
            "3. Trích dẫn nguồn rõ ràng dưới dạng [số] tương ứng với danh sách nguồn",
            "4. Nếu thông tin không đủ hoặc không chắc chắn, hãy nêu rõ",
            "5. Định dạng câu trả lời bằng Markdown với các tiêu đề phù hợp",
            "\n## Câu trả lời:"
        ]
        
        return "\n".join(part for part in prompt_parts if part)
    
    def _format_search_results_for_prompt(self, search_results: Dict[str, Any]) -> str:
        """Định dạng kết quả tìm kiếm cho prompt."""
        if not search_results.get("papers"):
            return "Không tìm thấy bài báo nào phù hợp."
            
        formatted = []
        for i, paper in enumerate(search_results.get("papers", []), 1):
            formatted.append(
                f"{i}. {paper.get('title', 'Không có tiêu đề')}\n"
                f"   Tác giả: {paper.get('authors', 'Không rõ')}\n"
                f"   Năm: {paper.get('year', 'N/A')}\n"
                f"   Tóm tắt: {paper.get('abstract', 'Không có tóm tắt')}\n"
            )
            
        return "\n".join(formatted)
    
    def _format_evaluation_for_prompt(self, evaluation: Dict[str, Any]) -> str:
        """Định dạng đánh giá cho prompt."""
        if not evaluation:
            return "Không có thông tin đánh giá."
            
        metrics = evaluation.get("evaluation_metrics", {})
        suggestions = evaluation.get("suggestions", [])
        
        formatted = [
            "### Chất lượng kết quả:",
            f"- Độ phù hợp: {metrics.get('relevance', 'N/A')}/10",
            f"- Độ tin cậy: {metrics.get('reliability', 'N/A')}/10",
            f"- Độ cập nhật: {metrics.get('recency', 'N/A')}/10",
            f"- Độ đầy đủ: {metrics.get('completeness', 'N/A')}/10",
            f"- Tổng thể: {evaluation.get('overall_quality', 'N/A')}/10",
            "\n### Đề xuất cải thiện:",
            "\n".join(f"- {sug}" for sug in suggestions) if suggestions else "Không có đề xuất"
        ]
        
        return "\n".join(formatted)
    
    def _format_response(self, answer: str, search_results: Dict[str, Any], evaluation: Optional[Dict[str, Any]] = None) -> str:
        """Định dạng lại câu trả lời cuối cùng."""
        formatted_parts = [answer]
        
        # Thêm phần nguồn tham khảo nếu có
        sources = self._extract_sources(search_results)
        if sources:
            formatted_parts.append("\n## Nguồn tham khảo:")
            formatted_parts.extend(sources)
        
        # Thêm đánh giá độ tin cậy nếu có
        if evaluation:
            score = evaluation.get("evaluation_metrics", {}).get("overall_quality", 0)
            formatted_parts.append(
                f"\n*Độ tin cậy: {score:.1f}/10*"
            )
        
        return "\n".join(formatted_parts)
    
    def _extract_sources(self, search_results: Dict[str, Any]) -> List[str]:
        """Trích xuất danh sách nguồn tham khảo."""
        sources = []
        for i, paper in enumerate(search_results.get("papers", []), 1):
            source_parts = [f"{i}."]
            
            if paper.get("title"):
                source_parts.append(f"{paper['title']}")
            
            if paper.get("authors"):
                source_parts.append(f"{paper['authors']}")
            
            if paper.get("year"):
                source_parts.append(f"({paper['year']})")
            
            if paper.get("url"):
                source_parts.append(f"[[Link]]({paper['url']})")
            
            sources.append(" ".join(part for part in source_parts if part))
            
        return sources
