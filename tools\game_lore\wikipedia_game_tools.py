from typing import Dict, Any, Optional, List
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from datetime import datetime

class WikipediaGameTools(Toolkit):
    """
    Wikipedia Game Tools for searching games, series, characters, and fictional universes on Wikipedia.
    """

    def __init__(self, enable_search: bool = True, **kwargs):
        super().__init__(
            name="wikipedia_game_tools",
            **kwargs
        )

        if enable_search:
            self.register(self.search_wikipedia_game)
            self.register(self.get_top_new_game_articles)
            self.register(self.search_game_categories)
            self.register(self.get_game_series_info)
            self.register(self.search_game_developers)

    def search_wikipedia_game(self, query: str, language: str = "en") -> str:
        """
        Search Wikipedia for games, series, characters, or fictional universes.

        Args:
        - query: Game title, series, character, or universe (e.g., 'Final Fantasy', 'Elden Ring Ranni', 'Halo UNSC')
        - language: Wikipedia language code (default: 'en')

        Returns:
        - JSON string with summary, page URL, thumbnail, and related topics
        """
        log_debug(f"Searching Wikipedia ({language}) for: {query}")

        try:
            # Wikipedia API endpoint
            api_url = f"https://{language}.wikipedia.org/api/rest_v1/page/summary/{query.replace(' ', '_')}"
            response = requests.get(api_url, timeout=10)

            if response.status_code == 404:
                return json.dumps({
                    "status": "error",
                    "source": "Wikipedia",
                    "message": "No article found for query",
                    "query": query
                }, indent=2)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "source": "Wikipedia",
                    "message": f"Wikipedia API returned status code {response.status_code}",
                    "query": query
                }, indent=2)

            data = response.json()
            summary = data.get("extract")
            page_url = data.get("content_urls", {}).get("desktop", {}).get("page")
            title = data.get("title")
            thumbnail = data.get("thumbnail", {}).get("source")

            # Get related topics (using search API)
            related = self._get_related_topics(query, language, title)

            # Get additional game-specific information
            game_info = self._extract_game_info(summary, title)

            return json.dumps({
                "status": "success",
                "source": "Wikipedia",
                "query": query,
                "title": title,
                "summary": summary,
                "page_url": page_url,
                "thumbnail": thumbnail,
                "related_topics": related,
                "game_info": game_info,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Error searching Wikipedia Game: {str(e)}")
            return json.dumps({
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "query": query
            }, indent=2)

    def get_top_new_game_articles(self, category: str = "video_games", limit: int = 10) -> str:
        """
        Lấy các bài viết game mới nhất từ Wikipedia.

        Args:
        - category: Category game ('video_games', 'game_series', 'game_characters')
        - limit: Số lượng bài viết

        Returns:
        - JSON string với danh sách bài viết game mới nhất
        """
        log_debug(f"Lấy top new game articles cho category: {category}")

        try:
            # Wikipedia category mapping
            category_mapping = {
                "video_games": "Category:Video games",
                "game_series": "Category:Video game series",
                "game_characters": "Category:Video game characters",
                "game_companies": "Category:Video game companies"
            }

            category_title = category_mapping.get(category, "Category:Video games")

            # Get recent changes in category
            api_url = "https://en.wikipedia.org/w/api.php"
            params = {
                "action": "query",
                "list": "categorymembers",
                "cmtitle": category_title,
                "format": "json",
                "cmlimit": limit,
                "cmsort": "timestamp",
                "cmdir": "desc"
            }

            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to get new articles: {response.status_code}"
                }, indent=2)

            data = response.json()
            members = data.get("query", {}).get("categorymembers", [])

            articles = []
            for member in members:
                title = member.get("title")
                articles.append({
                    "title": title,
                    "url": f"https://en.wikipedia.org/wiki/{title.replace(' ', '_')}",
                    "page_id": member.get("pageid"),
                    "category": category
                })

            return json.dumps({
                "status": "success",
                "source": "Wikipedia",
                "category": category,
                "results_count": len(articles),
                "top_new_game_articles": articles,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy top new game articles: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_game_categories(self, search_term: str = "", limit: int = 15) -> str:
        """
        Tìm kiếm game categories trên Wikipedia.

        Args:
        - search_term: Từ khóa tìm kiếm category
        - limit: Số lượng categories

        Returns:
        - JSON string với danh sách game categories
        """
        log_debug(f"Tìm kiếm game categories: {search_term}")

        try:
            api_url = "https://en.wikipedia.org/w/api.php"

            if search_term:
                # Search for specific categories
                params = {
                    "action": "query",
                    "list": "allcategories",
                    "acprefix": search_term,
                    "format": "json",
                    "aclimit": limit
                }
            else:
                # Get popular game categories
                popular_categories = [
                    "Video games", "Video game series", "Video game characters",
                    "Video game companies", "Video game genres", "Video game platforms",
                    "Role-playing video games", "Action video games", "Strategy video games",
                    "Simulation video games", "Sports video games", "Racing video games",
                    "Fighting video games", "Puzzle video games", "Adventure video games"
                ]

                return json.dumps({
                    "status": "success",
                    "source": "Wikipedia",
                    "search_term": search_term or "Popular categories",
                    "results_count": len(popular_categories),
                    "game_categories": [{"category": cat, "type": "popular"} for cat in popular_categories],
                    "timestamp": datetime.now().isoformat()
                }, indent=2)

            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return json.dumps({
                    "status": "error",
                    "message": f"Failed to search categories: {response.status_code}"
                }, indent=2)

            data = response.json()
            categories = data.get("query", {}).get("allcategories", [])

            category_list = []
            for cat in categories:
                category_list.append({
                    "category": cat.get("*"),
                    "size": cat.get("size", 0),
                    "type": "search_result"
                })

            return json.dumps({
                "status": "success",
                "source": "Wikipedia",
                "search_term": search_term,
                "results_count": len(category_list),
                "game_categories": category_list,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm game categories: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def get_game_series_info(self, series_name: str) -> str:
        """
        Lấy thông tin về một game series.

        Args:
        - series_name: Tên game series

        Returns:
        - JSON string với thông tin game series
        """
        log_debug(f"Lấy thông tin game series: {series_name}")

        try:
            # Search for series page
            series_info = self._get_page_info(series_name)

            # Search for games in the series
            series_games = self._search_series_games(series_name)

            # Get series statistics
            series_stats = self._calculate_series_stats(series_games)

            return json.dumps({
                "status": "success",
                "source": "Wikipedia",
                "series_name": series_name,
                "series_info": series_info,
                "series_games": series_games,
                "series_statistics": series_stats,
                "timestamp": datetime.now().isoformat()
            }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi lấy thông tin game series: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    def search_game_developers(self, developer_name: str = "", limit: int = 10) -> str:
        """
        Tìm kiếm thông tin về game developers.

        Args:
        - developer_name: Tên developer (để trống để lấy popular developers)
        - limit: Số lượng kết quả

        Returns:
        - JSON string với thông tin developers
        """
        log_debug(f"Tìm kiếm game developers: {developer_name}")

        try:
            if developer_name:
                # Search for specific developer
                developer_info = self._get_page_info(developer_name)
                developer_games = self._search_developer_games(developer_name)

                return json.dumps({
                    "status": "success",
                    "source": "Wikipedia",
                    "developer_name": developer_name,
                    "developer_info": developer_info,
                    "developer_games": developer_games,
                    "timestamp": datetime.now().isoformat()
                }, indent=2)
            else:
                # Get popular developers
                popular_developers = [
                    "Nintendo", "Sony Interactive Entertainment", "Microsoft Studios",
                    "Electronic Arts", "Activision Blizzard", "Ubisoft", "Square Enix",
                    "Capcom", "Konami", "Sega", "Bandai Namco", "Take-Two Interactive"
                ]

                developer_list = []
                for dev in popular_developers[:limit]:
                    dev_info = self._get_basic_info(dev)
                    developer_list.append(dev_info)

                return json.dumps({
                    "status": "success",
                    "source": "Wikipedia",
                    "search_type": "popular_developers",
                    "results_count": len(developer_list),
                    "developers": developer_list,
                    "timestamp": datetime.now().isoformat()
                }, indent=2)

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm game developers: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": str(e)
            }, indent=2)

    # Helper methods
    def _get_related_topics(self, query: str, language: str, exclude_title: str) -> list:
        """Get related topics from Wikipedia search."""
        try:
            search_url = f"https://{language}.wikipedia.org/w/api.php"
            search_params = {
                "action": "query",
                "list": "search",
                "srsearch": f"{query} video game OR series OR character OR universe OR developer",
                "format": "json",
                "srlimit": 5
            }
            search_resp = requests.get(search_url, params=search_params, timeout=5)
            if search_resp.status_code == 200:
                search_data = search_resp.json()
                related = []
                for item in search_data.get("query", {}).get("search", []):
                    if item.get("title") != exclude_title:
                        related.append(item.get("title"))
                return related
        except:
            pass
        return []

    def _extract_game_info(self, summary: str, title: str) -> dict:
        """Extract game-specific information from summary."""
        if not summary:
            return {}

        summary_lower = summary.lower()
        game_info = {
            "is_video_game": any(term in summary_lower for term in ["video game", "game", "gaming"]),
            "is_series": "series" in summary_lower,
            "is_character": "character" in summary_lower,
            "has_developer_info": any(term in summary_lower for term in ["developed", "developer", "studio"]),
            "has_release_info": any(term in summary_lower for term in ["released", "launch", "debut"])
        }
        return game_info

    def _get_page_info(self, page_title: str) -> dict:
        """Get basic page information."""
        try:
            api_url = f"https://en.wikipedia.org/api/rest_v1/page/summary/{page_title.replace(' ', '_')}"
            response = requests.get(api_url, timeout=5)
            if response.status_code == 200:
                data = response.json()
                return {
                    "title": data.get("title"),
                    "extract": data.get("extract", "")[:300] + "..." if data.get("extract") else "",
                    "url": data.get("content_urls", {}).get("desktop", {}).get("page"),
                    "thumbnail": data.get("thumbnail", {}).get("source")
                }
        except:
            pass
        return {"title": page_title, "extract": "", "url": "", "thumbnail": None}

    def _search_series_games(self, series_name: str) -> list:
        """Search for games in a series."""
        try:
            api_url = "https://en.wikipedia.org/w/api.php"
            params = {
                "action": "query",
                "list": "search",
                "srsearch": f"{series_name} video game",
                "format": "json",
                "srlimit": 10
            }
            response = requests.get(api_url, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                games = []
                for item in data.get("query", {}).get("search", []):
                    games.append({
                        "title": item.get("title"),
                        "snippet": item.get("snippet", "")
                    })
                return games
        except:
            pass
        return []

    def _calculate_series_stats(self, games: list) -> dict:
        """Calculate statistics for a game series."""
        return {
            "total_games": len(games),
            "series_span": "Multiple years" if len(games) > 3 else "Limited series",
            "popularity": "High" if len(games) > 5 else "Medium" if len(games) > 2 else "Low"
        }

    def _search_developer_games(self, developer: str) -> list:
        """Search for games by a developer."""
        try:
            api_url = "https://en.wikipedia.org/w/api.php"
            params = {
                "action": "query",
                "list": "search",
                "srsearch": f"{developer} video game developed",
                "format": "json",
                "srlimit": 8
            }
            response = requests.get(api_url, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                games = []
                for item in data.get("query", {}).get("search", []):
                    games.append({
                        "title": item.get("title"),
                        "snippet": item.get("snippet", "")
                    })
                return games
        except:
            pass
        return []

    def _get_basic_info(self, name: str) -> dict:
        """Get basic information about an entity."""
        return {
            "name": name,
            "type": "Game Developer",
            "url": f"https://en.wikipedia.org/wiki/{name.replace(' ', '_')}",
            "status": "Active"
        }
