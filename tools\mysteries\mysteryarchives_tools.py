from typing import Dict, List, Any, Optional
from datetime import datetime
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests
import json
from urllib.parse import quote_plus

class MysteryArchivesTools(Toolkit):
    """
    Công cụ tìm kiếm và truy xuất tài liệu từ Mystery Archives.
    
    Công cụ này cung cấp khả năng tìm kiếm các bài viết, hồ sơ vụ án từ Mystery Archives,
    một nguồn tài nguyên phong phú về các bí ẩn chưa có lời giải.
    
    <PERSON><PERSON><PERSON> từ khóa tìm kiếm phổ biến:
    - "Zodiac Killer"
    - "Black Dahlia"
    - "Jack the Ripper"
    - "Unresolved Mysteries"
    - "Cold Cases"
    """
    
    def __init__(self):
        super().__init__(
            name="mystery_archives_tools",
            tools=[self.search_mystery_archives, self.get_popular_archives]
        )
        self.base_url = "https://www.mysteryarchives.com/api/v1"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'MysteryResearchTool/1.0',
            'Accept': 'application/json'
        })

    def search_mystery_archives(self, query: str, limit: int = 5, category: str = None) -> str:
        """
        Tìm kiếm tài liệu từ Mystery Archives dựa trên từ khóa.
        
        Args:
            query (str): Từ khóa tìm kiếm
            limit (int, optional): Số lượng kết quả trả về. Mặc định là 5.
            category (str, optional): Danh mục tìm kiếm (ví dụ: "unsolved", "historical", "cryptids").
            
        Returns:
            str: Kết quả tìm kiếm dưới dạng JSON
            
        Example:
            >>> tool = MysteryArchivesTools()
            >>> result = tool.search_mystery_archives("Zodiac Killer", limit=3)
            >>> print(json.loads(result)['results'][0]['title'])
            'Zodiac Killer: The Complete Case Files'
        """
        logger.info(f"Searching Mystery Archives for: {query}")
        
        try:
            params = {
                'q': query,
                'limit': min(limit, 20),  # Giới hạn tối đa 20 kết quả
                'category': category if category else ''
            }
            
            # Thực hiện yêu cầu tìm kiếm
            response = self.session.get(
                f"{self.base_url}/search",
                params=params,
                timeout=10
            )
            response.raise_for_status()
            
            # Xử lý kết quả
            data = response.json()
            results = []
            
            for item in data.get('items', [])[:limit]:
                results.append({
                    'title': item.get('title', 'No title'),
                    'url': item.get('url', ''),
                    'summary': item.get('excerpt', ''),
                    'date': item.get('date', ''),
                    'category': item.get('category', '')
                })
            
            return json.dumps({
                'status': 'success',
                'query': query,
                'total_results': len(results),
                'results': results,
                'timestamp': datetime.now().isoformat()
            }, indent=2, ensure_ascii=False)
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Lỗi kết nối đến Mystery Archives: {str(e)}"
            logger.error(error_msg)
            return json.dumps({
                'status': 'error',
                'message': error_msg,
                'query': query,
                'results': []
            }, indent=2)
            
        except Exception as e:
            error_msg = f"Lỗi khi xử lý kết quả tìm kiếm: {str(e)}"
            logger.error(error_msg)
            return json.dumps({
                'status': 'error',
                'message': error_msg,
                'query': query,
                'results': []
            }, indent=2)
    
    def get_popular_archives(self, limit: int = 5) -> str:
        """
        Lấy danh sách các tài liệu phổ biến từ Mystery Archives.
        
        Args:
            limit (int, optional): Số lượng kết quả trả về. Mặc định là 5.
            
        Returns:
            str: Danh sách tài liệu phổ biến dưới dạng JSON
        """
        try:
            response = self.session.get(
                f"{self.base_url}/popular",
                params={'limit': min(limit, 10)},
                timeout=10
            )
            response.raise_for_status()
            
            data = response.json()
            return json.dumps({
                'status': 'success',
                'results': data.get('items', [])[:limit],
                'timestamp': datetime.now().isoformat()
            }, indent=2, ensure_ascii=False)
            
        except Exception as e:
            error_msg = f"Không thể lấy danh sách tài liệu phổ biến: {str(e)}"
            logger.error(error_msg)
            return json.dumps({
                'status': 'error',
                'message': error_msg,
                'results': []
            }, indent=2)
