from agno.team import Team
from agno.models.ollama import Ollama
from agno.memory.v2.memory import Memory
from agno.storage.agent.sqlite import SqliteAgentStorage
from textwrap import dedent
import asyncio

from agents.astronomy.writer_thesis_agent import writer_thesis_agent
from agents.astronomy.writer_status_agent import writer_status_agent
from agents.astronomy.writer_youtube_agent import writer_youtube_agent

from utils.chunking import chunk_text
from utils.reranker import rerank_chunks

# --- RERANKER BAAI/bge-reranker-v2-m3 ---
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import torch

class BGEReRanker:
    def __init__(self, model_name="BAAI/bge-reranker-v2-m3"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForSequenceClassification.from_pretrained(model_name)
        self.model.eval()

    def rerank(self, query: str, docs: list, top_k: int = 8):
        # docs: list[str]
        pairs = [(query, doc) for doc in docs]
        with torch.no_grad():
            inputs = self.tokenizer.batch_encode_plus(
                pairs, padding=True, truncation=True, return_tensors="pt", max_length=512
            )
            scores = self.model(**inputs, return_dict=True).logits.view(-1,).float()
            sorted_indices = torch.argsort(scores, descending=True)
            reranked = [docs[i] for i in sorted_indices[:top_k]]
        return reranked

bge_reranker = BGEReRanker()
from utils.qdrant_optimization import get_qdrant_optimizer

# --- EMBEDDING bằng Ollama (nomic-embed-text:latest) ---
import requests

def embed_text(text: str) -> list:
    """
    Sử dụng Ollama embedding model nomic-embed-text:latest (chạy local).
    """
    url = "http://localhost:11434/api/embeddings"
    data = {
        "model": "nomic-embed-text:latest",
        "prompt": text
    }
    try:
        response = requests.post(url, json=data, timeout=30)
        response.raise_for_status()
        result = response.json()
        return result["embedding"]
    except Exception as e:
        print(f"Lỗi embedding với Ollama: {e}")
        return [0.0] * 768

# --- Qdrant client thực tế ---
from qdrant_client import QdrantClient

QDRANT_COLLECTION = "astronomy_knowledge"
qdrant_client = QdrantClient(host="localhost", port=6333)

MODEL = Ollama(id="qwen3:4b")
memory = Memory(model=MODEL)
team_storage = SqliteAgentStorage(table_name="team_sessions", db_file="tmp/persistent_memory.db")
qdrant_optimizer = get_qdrant_optimizer()

class AstronomyContentCreatorTeam(Team):
    def __init__(self, writer_type: str = "thesis"):
        if writer_type == "thesis":
            self.writer_agent = writer_thesis_agent
        elif writer_type == "status":
            self.writer_agent = writer_status_agent
        elif writer_type == "youtube":
            self.writer_agent = writer_youtube_agent
        else:
            self.writer_agent = writer_thesis_agent

        super().__init__(
            name="Astronomy Content Creator Team",
            mode="coordinate",
            model=MODEL,
            members=[self.writer_agent],
            instructions=dedent("""
                Nhận chủ đề hoặc câu hỏi về thiên văn học, tìm kiếm kiến thức liên quan từ Qdrant, tổng hợp và sáng tạo nội dung mới (luận văn, status, kịch bản Youtube).
                Sau khi tạo content, lưu lại nội dung mới vào Qdrant để làm giàu tri thức cho hệ thống.
                Luôn ưu tiên tiếng Việt, nội dung chính xác, truyền cảm hứng.
            """),
            memory=memory,
            storage=team_storage,
            show_tool_calls=True,
            markdown=True
        )

    async def aprint_response(self, query, **kwargs):
        # Nếu print_response là async
        if asyncio.iscoroutinefunction(super().print_response):
            result = await super().print_response(query)
        else:
            result = super().print_response(query)
        if result is None or (isinstance(result, str) and result.strip() == ""):
            print("⚠️ Không có nội dung trả lời từ AI (None hoặc rỗng).")
        return result

    async def apredict_response(self, query, **kwargs):
        return await self.aprint_response(query, **kwargs)

    async def create_content(self, query: str, top_k: int = 8, chunk_strategy: str = "auto", writer_kwargs: dict = None):
        # 1. Tạo embedding cho query
        query_vector = embed_text(query)

        # 2. Truy vấn Qdrant lấy các chunk liên quan
        if qdrant_client is None:
            raise RuntimeError("Qdrant client chưa được khởi tạo.")
        search_results = await qdrant_optimizer.optimized_search(
            client=qdrant_client,
            collection_name=QDRANT_COLLECTION,
            query_vector=query_vector,
            limit=top_k
        )
        # 3. Rerank các chunk (dùng BGE reranker)
        chunks = []
        for item in search_results:
            payload = item.get("payload", {})
            text = payload.get("text", "")
            if text:
                chunks.append({"text": text, "id": item.get("id", None)})
        chunk_texts = [c["text"] for c in chunks]
        reranked_texts = bge_reranker.rerank(query, chunk_texts, top_k=top_k)
        reranked_chunks = [c for c in chunks if c["text"] in reranked_texts]

        # 4. Tổng hợp nội dung cho writer agent
        context_text = "\n\n".join([c["text"] for c in reranked_chunks])
        prompt = f"Chủ đề: {query}\n\nThông tin tham khảo:\n{context_text}\n\nYêu cầu: Viết nội dung phù hợp dạng {self.writer_agent.name}."
        writer_input = prompt
        if writer_kwargs is None:
            writer_kwargs = {}
        content = await self.writer_agent.apredict_response(writer_input, **writer_kwargs)

        # Kiểm tra nội dung trả về từ agent/model
        if not content or (isinstance(content, str) and content.strip() == ""):
            print("Không có nội dung trả lời từ AI (content rỗng). Không lưu lên Qdrant.")
            return content

        # 5. Chunk content mới và lưu lên Qdrant
        new_chunks = chunk_text(content, strategy=chunk_strategy)
        if not new_chunks:
            print("Không có chunk nào được sinh ra từ nội dung trả về. Không lưu lên Qdrant.")
            return content
        points = []
        for idx, chunk in enumerate(new_chunks):
            points.append({
                "id": None,  # Qdrant sẽ tự sinh id nếu None
                "vector": embed_text(chunk),
                "payload": {
                    "text": chunk,
                    "source": "content_creator",
                    "writer_type": self.writer_agent.name,
                    "query": query,
                    "chunk_index": idx
                }
            })
        # Kiểm tra points trước khi upsert
        if not points:
            print("Không có dữ liệu để lưu lên Qdrant (points rỗng). Bỏ qua upsert.")
            return content
        try:
            await qdrant_optimizer.optimized_upsert(
                client=qdrant_client,
                collection_name=QDRANT_COLLECTION,
                points=points
            )
        except Exception as e:
            print(f"Lỗi upsert Qdrant: {e}")
        return content

if __name__ == "__main__":
    import sys
    import nest_asyncio
    nest_asyncio.apply()
    print("Chọn kiểu writer agent:")
    print("1. Thesis (luận văn)")
    print("2. Status Facebook")
    print("3. Youtube script")
    choice = input("Nhập số (1/2/3): ").strip()
    if choice == "1":
        writer_type = "thesis"
    elif choice == "2":
        writer_type = "status"
    elif choice == "3":
        writer_type = "youtube"
    else:
        writer_type = "thesis"
    team = AstronomyContentCreatorTeam(writer_type=writer_type)
    print("Nhập chủ đề hoặc câu hỏi về thiên văn học (hoặc 'exit' để thoát):")
    while True:
        query = input("\nBạn: ")
        if query.lower() in ["exit", "quit", "thoát"]:
            print("Tạm biệt!")
            break
        try:
            content = asyncio.get_event_loop().run_until_complete(team.create_content(query))
            print("\nNội dung được tạo:\n")
            print(content)
        except Exception as e:
            print(f"Lỗi: {e}")
